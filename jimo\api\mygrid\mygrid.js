import {
  request
} from '@/common/net/request.js';

export function statisticsQuota(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsQuota",
      method: "POST",
      params
    });
  }
  
  
  export function statisticsMission(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsMissionByStatus",
      method: "POST",
      params
    });
  }
  export function gridOrg(){
    return request({
        url: "/village/grid/gridOrg",
        method: "GET"
      });
  }
  export function statisticsMissionByType(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsMissionByType",
      method: "POST",
      params
    });
  }
  export function gridCompleteSort(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/gridCompleteSort",
      method: "POST",
      params
    });
  }
  export function findEventNumApp(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/flow/tbFlowApply/findEventNumApp",
      method: "POST",
      params
    });
  }
  export function gridOrgAndMemberApp(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/gridOrgAndMemberApp",
      method: "POST",
      params
    });
  }
  //判断是否有权限使用大喇叭
  export function permissions(){
    return request({
        url: "/village/broadcast/cadres/findByVillage",
        method: "GET"
      });
  }
  // 查询网格个人信息
  export function getGridMemberInfowg(params){
    return request({
        url: "/village/gridMember/findOne",
        method: "GET",
        params
    });
  }
  
// 获取事务下发数量汇总数据
export function getMissions(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/mission/getMissions' ,
        method: 'GET',
		params
    })
}
// 获取工作上报数量汇总数据
export function getWorkReport(params) {
	params['uniContentType'] = 'json'
    return request({
        url:'/village/flow/tbFlowApply/getWorkReportBy',
        method: 'GET',
		params
    })
}
// 获取工作任务数量汇总数据
export function getWorkTask(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/missionPeople/countMissionPeople',
        method: 'GET',
		params
    })
}
// 获取下户走访数量汇总数据
export function getVisitPlan(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/getVisitPlan',
        method: 'GET',
		params
    })
}
// 获取事务下发信息
export function getCountMission(params) {
    return request({
        url: '/village/mission/getCountMission' ,
        method: 'GET',
		params
    })
}
// 获取经常下发的事项类型
export function getMissionType(params) {
    return request({
        url: '/village/mission/getMissionSequencing' ,
        method: 'GET',
		params
    })
}
// 获取累计下户走访
export function getCountVisit(params) {
    return request({
        url: '/village/mission/getCountHouseholdVisitPlan',
        method: 'GET',
		params
    })
}
// 获取累计任务完成
export function getCountTask(params) {
    return request({
        url:  '/village/mission/getMissionCompleted',
        method: 'GET',
		params
    })
}
// 获取累计上报事项
export function getCountReport(params) {
    return request({
        url: '/village/mission/getcountWorkReport',
        method: 'GET',
		params
    })
}
// 获取同网格下的其他网格员
export function getColleague(params) {
    return request({
        url: '/village/grid/gridMemberMissionList',
        method: 'GET',
		params
    })
}
// 获取所有网格
export function getAllGrid() {
    return request({
        url:  '/village/grid_organization_management/management/findAllList',
        method: 'GET',
    })
}
// 获取网格下的成员
export function getAllPeople(params) {
	params['uniContentType'] = 'json'
    return request({
        url:  '/village/grid_organization_management/information/findAll',
        method: 'POST',
		params
    })
}
// 获取村统计数据
export function getVillageStatistical() {
    return request({
        url: '/user/people/statisticalQuery',
        method: 'GET'
    })
}
// 获取农户详情
export function getVillagerDetail(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/user/people/findOneByGrid',
        method: 'GET',
		params
    })
}
// 获取网格员信息
export function getGridMemberInfo() {
    return request({
        url: '/village/grid/gridMissionStatistics',
        method: 'GET'
    })
}