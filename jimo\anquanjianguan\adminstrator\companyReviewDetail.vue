<template>
  <view class="review-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="验收详情" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>

    <view class="container">
      <!-- 上半部分：企业与隐患信息 -->
      <view class="detail-section">
        <view class="detail-item">
          <text class="label">企业名称：</text>
          <text class="value">{{detail.enterpriseName}}</text>
        </view>
        <view class="detail-item">
          <text class="label">企业性质：</text>
          <text class="value">{{detail.enterpriseNature}}</text>
        </view>
        <view class="detail-item">
          <text class="label">巡检时间：</text>
          <text class="value">{{ detail.inspectTime }}</text>
        </view>
        <view class="detail-item">
          <text class="label">上报人：</text>
          <text class="value">{{detail.uploader}}</text>
        </view>
        <view class="detail-item">
          <text class="label">隐患描述：</text>
          <text class="value">{{detail.problemDesc}}</text>
        </view>
        <view class="detail-item">
          <text class="label">隐患图片：</text>
          <view class="img-list">
           <image v-for="(img, idx) in detail.dangerimglist" :key="idx" :src="img" style="width: 80rpx; height: 80rpx; margin-right: 16rpx; background: #eee;" />

           </view>
        </view>
        <view class="detail-item">
          <text class="label">整改描述：</text>
          <text class="value">{{detail.reviewDesc}}</text>
        </view>
       
        <view class="detail-item">
          <text class="label">整改图片：</text>
          <view class="img-list">
             <image v-for="(img, idx) in detail.reviewImgs" :key="idx" :src="img" style="width: 80rpx; height: 80rpx; margin-right: 16rpx; background: #eee;" />

           </view>
        </view>
        <view class="detail-item">
          <text class="label">整改时间：</text>
          <text class="value">{{detail.reviewTime}}</text>
        </view>
         <view class="detail-item">
          <text class="label">复核状态：</text>
          <text class="value review-status" :class="'review-' + detail.reviewStatus">{{ getCheckTypeText(detail.reviewStatus) }}</text>

        </view>
      </view>
      <!-- 下半部分：表单 -->
      <view class="form-section" v-if="detail.auditStatus=='PENDING'">
        <u--form ref="formRef" :model="form"  :label-width="120" :labelStyle="{fontWeight: 'bold', paddingBottom: '10rpx'}" :rules="rules">
          <u-form-item label="验收审核" prop="auditStatus" required @click="openReviewStatusPicker = true">
            <u-input v-model="auditValueLabel" placeholder="请选择验收审核" readonly >
              <template #suffix>
                <u-icon name="arrow-down" style="margin-right: 10rpx;"></u-icon>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="验收意见" prop="auditOpinion">
            <u-textarea v-model="form.auditOpinion" placeholder="请输入整改意见" maxlength="100" showWordLimit />
          </u-form-item>
        </u--form>
      </view>
      <view class="bottom u-border-top" v-if="detail.auditStatus=='PENDING'">
        <view class="btn pjBtn" @click="submit">提交</view>
      </view>
      <u-picker @close="openReviewStatusPicker = false" :show="openReviewStatusPicker" closeOnClickOverlay :columns="[reviewStatusOptions]" keyName="label" @confirm="onReviewStatusConfirm" @cancel="openReviewStatusPicker = false" />
    </view>
  </view>
</template>
<script setup>
import { ref, reactive } from 'vue'
import {LIVE_INVENTORY_BG} from "@/common/net/staticUrl.js"
import { onLoad } from '@dcloudio/uni-app'
import { AJYService } from "../../api/anquanjianguan/companyList.js";
import {AdminService} from "../../api/anquanjianguan/adminstrator.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const detail = ref({})
const openReviewStatusPicker = ref(false)
const form = reactive({
  reviewId:'',
  auditStatus: '',
  auditOpinion: ''
})
const formRef = ref(null)
const rules = ref({
  auditStatus: [{ required: true, message: '请选择验收审核', trigger: ['blur', 'change'] }]
})  
 const checkTypeList = ref([
  { text: '重大隐患', value: 'PENDING', color: '#FF8C42' },
  { text: '已完成', value: 'COMPLETED', color: '#2ED573' },
  { text: '已移交执法', value: 'TRANSFERRED', color: '#5352ED' },
  { text: '延期整改', value: 'DELAYED', color: '#FFA726' },
])
const reviewStatusOptions = [
  { label: '通过', value: 'PASSED' },
  { label: '驳回', value: 'REJECTED' }
]
const auditValueLabel = ref('')
const isdisabled = ref(false)
function onReviewStatusConfirm(e) {
  form.auditStatus = e.value[0].value
  auditValueLabel.value = e.value[0].label
  openReviewStatusPicker.value = false
}
function getCheckTypeText(type) {
  const typeItem = checkTypeList.value.find(item => item.value === type)
  return typeItem ? typeItem.text : '未知'
}
function submit() {
  formRef.value.validate().then((valid) => {
       if (isdisabled.value) return;
      isdisabled.value = true
      AdminService.auditReview(form).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title:'提交成功！', icon: 'none' })
          setTimeout(()=>{
            uni.navigateBack()
          })
        } else {
          uni.showToast({ title: res.message, icon: 'none' })
        }
      }).catch(e=>{
        isdisabled.value = false
        uni.showToast({
				title: '提交失败！',
				icon: 'none',
			})
      })
  
  })
}
onLoad((options) => {
  form.reviewId = options.id
  AJYService.findInspectReviewById(form.reviewId).then(res => {
    if (res.success) {
      detail.value = res.data
      console.log(res)
      detail.value.reviewImgs = detail.value.reviewImages?detail.value.reviewImages.split(','):[]
      detail.value.dangerimglist = detail.value.riskImages?detail.value.riskImages.split(','):[]
    }
  })
})
</script>
<style lang="scss" scoped>
.review-detail {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  .container {
    padding: 0 22rpx 22rpx 22rpx;
    box-sizing: border-box;
    .detail-section {
      background-color: #fff;
      border-radius: 20rpx;
      margin: 30rpx 0 0 0;
      height: calc(100vh - 620rpx);
      box-sizing: border-box;
      overflow: scroll;
      padding: 30rpx 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
      .detail-item {
        display: flex;
        align-items: flex-start;
        padding: 18rpx 0;
        font-size: 28rpx;
        border-bottom: 1px solid #f0f0f0;
        &:last-child { border-bottom: none; }
        .label {
          color: #666;
          min-width: 180rpx;
          font-size: 28rpx;
          flex: 1;
        }
        .value {
          color: #333;
          font-weight: 500;
          text-align: right;
          word-break: break-all;
            &.review-status {
            font-weight: 600;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            font-size: 28rpx;

            &.review-PENDING {
              color: #FF8C42;
              background: rgba(255, 140, 66, 0.1);
              border: 1rpx solid rgba(255, 140, 66, 0.3);
              
            }
            &.review-COMPLETED {
              color: #2ED573;
              background: rgba(46, 213, 115, 0.1);
              border: 1rpx solid rgba(46, 213, 115, 0.3);
            }
            &.review-TRANSFERRED {
              color: #5352ED;
              background: rgba(83, 82, 237, 0.1);
              border: 1rpx solid rgba(83, 82, 237, 0.3);
            }
            &.review-DELAYED {
              color: #FFA726;
              background: rgba(255, 167, 38, 0.1);
              border: 1rpx solid rgba(255, 167, 38, 0.3);
            }
            &.review-REJECTED {
              color: #FF5252;
              background: rgba(255, 82, 82, 0.1);
              border: 1rpx solid rgba(255, 82, 82, 0.3);
            }
          }
        }
        .img-list {
          display: flex;
          gap: 20rpx;
          flex-wrap: wrap;
          .img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            background: #eee;
            border: 1px solid #eee;
          }
        }
      }
    }
    .form-section {
      margin-top: 20rpx;
      background: #fff;
      border-radius: 20rpx;
      padding: 34rpx 24rpx 53rpx 28rpx;
      box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
      .u--form {
        width: 100%;
      }
    }
    .blank {
      width: 100%;
      height: 180rpx;
    }
    .bottom {
      position: fixed;
      width: 100%;
      background: #fff;
      bottom: 0;
      left: 0;
      align-items: center;
      padding: 20rpx 38rpx;
      z-index: 100;
      box-sizing: border-box;
      .btn {
        line-height: 80rpx;
        border-radius: 40rpx;
        flex: 1;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 36rpx;
      }
      .pjBtn {
        background: #3cc16c;
        color: #fff;
      }
    }
  }
}
</style> 