import {
    request
} from '../request.js'
import {
    isDef
} from '@/common/uniUtils.js'

// 获取消息列表
export function getMessageList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/message/findPage',
        method: 'GET',
        params,
    })
}

//修改已读
export function updateRead(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/message/update',
        method: "POST",
        params,
    });
}

//点击最新消息-已读单条（订阅的…）
export function readMsg(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/staffSubs/readMsg',
        method: "POST",
        params,
    });
}

//查看消息详情
export function getMessageInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/msgSubs/findInfo',
        method: 'GET',
        params,
    })
}
 