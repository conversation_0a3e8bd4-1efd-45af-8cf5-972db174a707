import request from '@/common/request.js'
import { BASE_URL } from '@/common/net/config.js'

// 模拟数据
const mockData = {
  knowledgeInfo: {
    id: 1,
    knowledgeTitle: "水稻种植技术指南",
    knowledgeType: 1,
    createDate: "2024-01-15 14:30",
    knowledgeDetail: `
      <p>水稻种植技术要点：</p>
      <p>1. 育苗准备</p>
      <img src="https://example.com/rice1.jpg" />
      <p>2. 插秧技术</p>
      <video src="https://example.com/rice-planting.mp4"></video>
      <p>3. 水分管理</p>
      <p>4. 病虫害防治</p>
    `,
    author: "张农技"
  },
  fileList: {
    records: [
      {
        fileTitle: "水稻种植完整指南.pdf",
        fileAddress: "https://example.com/files/rice-guide.pdf"
      },
      {
        fileTitle: "病虫害防治手册.doc",
        fileAddress: "https://example.com/files/pest-control.doc"
      }
    ]
  },
  comments: {
    records: [
      {
        id: 1,
        avatar: "https://example.com/avatar1.jpg",
        userName: "张**",
        content: "这个技术指南非常实用！",
        createTime: "2024-01-16 10:30"
      },
      {
        id: 2,
        avatar: "https://example.com/avatar2.jpg",
        userName: "李**",
        content: "视频讲解很清晰，学会了很多！",
        createTime: "2024-01-16 11:20"
      }
    ]
  }
}

// 获取知识详情
export function getKnowledgeInfo(params) {
  // return request.get(`${BASE_URL}/knowledge/detail`, params)
  return Promise.resolve({
    success: true,
    data: mockData.knowledgeInfo
  })
}

// 获取知识附件
export function getKnowledgeFile(params) {
  // return request.get(`${BASE_URL}/knowledge/files`, params)
  return Promise.resolve({
    success: true,
    data: mockData.fileList
  })
}

// 获取评论列表
export function getKnowledgeComments(params) {
  // return request.get(`${BASE_URL}/knowledge/comments`, params)
  return Promise.resolve({
    success: true,
    data: mockData.comments
  })
}

// 添加评论
export function addKnowledgeComment(params) {
  // return request.post(`${BASE_URL}/knowledge/comment`, params)
  return Promise.resolve({
    success: true,
    message: "评论成功"
  })
}