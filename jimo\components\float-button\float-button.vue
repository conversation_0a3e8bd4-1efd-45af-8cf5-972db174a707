<template>
    <view 
        class="float-button" 
        :class="{
            'edge-left': isLeft && !isDragging && !isActive,
            'edge-right': !isLeft && !isDragging && !isActive,
            'active': isDragging || isActive,
            'with-transition': !isDragging
        }"
        :style="buttonStyle" 
        @touchstart="touchStart" 
        @touchmove.stop.prevent="touchMove" 
        @touchend="touchEnd"
        @tap.self="onClick"
    >
        <slot>
            <text v-if="iconClass" :class="iconClass" :style="iconStyle"></text>
            <image v-else-if="icon" :src="icon" mode="aspectFit" :style="iconStyle" class="button-icon"></image>
            <text v-else-if="text" class="button-text">{{ text }}</text>
        </slot>
    </view>
</template>

<script>
export default {
    name: 'FloatButton',
    props: {
        // 按钮文字
        text: {
            type: String,
            default: ''
        },
        // 按钮图片
        icon: {
            type: String,
            default: ''
        },
        // 图标类名（支持 iconfont 等字体）
        iconClass: {
            type: String,
            default: ''
        },
        // 图标大小
        iconSize: {
            type: String,
            default: '60%'
        },
        // 图标颜色
        iconColor: {
            type: String,
            default: '#ffffff'
        },
        // 初始位置
        initialPosition: {
            type: Object,
            default: () => ({
                bottom: '100px',
                left: '0px'
            })
        },
        // 按大小
        size: {
            type: String,
            default: '100rpx'
        }
    },
    data() {
        return {
            position: {
                left: '30px',
                top: '100px'
            },
            startPosition: {
                x: 0,
                y: 0
            },
            endPosition: {
                x: 0,
                y: 0
            },
            isDragging: false,
            isActive: false,
            isLeft: true,
            opacity: 0.7,
            hideTimer: null,
            sysInfo: uni.getSystemInfoSync()
        }
    },
    computed: {
        buttonStyle() {
            return {
                left: this.position.left,
                top: this.position.top,
                opacity: this.opacity,
                width: this.size,
                height: this.size
            }
        },
        // 图标样式
        iconStyle() {
            return {
                fontSize: this.iconSize,
                color: this.iconColor,
                width: this.iconSize,
                height: this.iconSize
            }
        }
    },
    methods: {
        touchStart(e) {
            e.stopPropagation()
            this.isDragging = true
            this.isActive = true
            this.opacity = 1
            if (this.hideTimer) {
                clearTimeout(this.hideTimer)
            }

            const touch = e.touches[0]
            const currentLeft = parseFloat(this.position.left)
            const buttonSize = parseInt(this.size)
            
            // 考虑当前是否处于贴边状态
            let offsetX = 0
            if (this.isLeft && !this.isActive) {
                offsetX = buttonSize * 0.6 // 60% 的偏移
            } else if (!this.isLeft && !this.isActive) {
                offsetX = -buttonSize * 0.6 // 60% 的偏移
            }
            
            // 计算触摸点相对于按钮实际位置的偏移
            this.startPosition = {
                x: touch.clientX - currentLeft - offsetX,
                y: touch.clientY - parseFloat(this.position.top)
            }
        },
        touchMove(e) {
            if (!this.isDragging) return

            const touch = e.touches[0]
            
            // 计算新位置，考虑触摸点在按钮内的偏移
            let newLeft = touch.clientX - this.startPosition.x
            let newTop = touch.clientY - this.startPosition.y

            // 限制边界
            const buttonSize = parseInt(this.size)
            // 允许按钮贴右边，但保留40%可见
            const maxLeft = this.sysInfo.windowWidth - (buttonSize * 0.4)
            const maxTop = this.sysInfo.windowHeight - buttonSize

            // 确保不超出屏幕边界
            newLeft = Math.max(0, Math.min(maxLeft, newLeft))
            newTop = Math.max(0, Math.min(maxTop, newTop))

            // 更新位置
            this.position.left = `${newLeft}px`
            this.position.top = `${newTop}px`

            this.endPosition = {
                x: touch.clientX,
                y: touch.clientY
            }
        },
        touchEnd() {
            this.isDragging = false

            // 获取屏幕信息
            const screenWidth = this.sysInfo.windowWidth
            const buttonSize = parseInt(this.size)

            // 计算距屏幕中心点的距离
            const distanceToCenter = this.endPosition.x - (screenWidth / 2)
            
            // 根据距离判断贴边
            if (distanceToCenter > 0) {
                // 距离右边更近，贴右边，保留40%可见
                this.position.left = `${screenWidth - (buttonSize * 0.4)}px`
                this.isLeft = false
            } else {
                // 距离左边更近，贴左边
                this.position.left = '0px'
                this.isLeft = true
            }

            // 延迟后隐藏
            this.hideTimer = setTimeout(() => {
                this.opacity = 0.7
                this.isActive = false
            }, 2000)

            // 保持垂直位置不变
            this.position.top = `${parseFloat(this.position.top)}px`
        },
        onClick() {
            console.log('444')
            this.isActive = true
            if (this.hideTimer) {
                clearTimeout(this.hideTimer)
            }
            this.hideTimer = setTimeout(() => {
                this.isActive = false
            }, 2000)
            console.log('--------------点击事件触发')
            this.$emit('tuichu')
        }
    },
    created() {
        // 初始化位置，将 bottom 转换为 top
        const initPosition = { ...this.initialPosition }
        if (initPosition.bottom) {
            initPosition.top = `${this.sysInfo.windowHeight - parseInt(initPosition.bottom) - parseInt(this.size)}px`
            delete initPosition.bottom
        }
        this.position = initPosition
        
        // 初始化后延迟隐藏
        this.hideTimer = setTimeout(() => {
            this.isActive = false
        }, 3000)
    },
    beforeDestroy() {
        if (this.hideTimer) {
            clearTimeout(this.hideTimer)
        }
    }
}
</script>

<style scoped>
.float-button {
    position: fixed;
  
    z-index: 999;
}

.float-button::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    /* 扩大点击区域 */
    left: -100%;
    right: -100%;
}
.float-button.active {
    transition: transform 0.3s ease-in-out;
}

.float-button.with-transition {
    transition: all 0.3s ease-in-out;
}   

.float-button.edge-left {
    transform: translateX(-60%);
}

.float-button.edge-left::after {
    left: 0;
    width: 200%;
}

.float-button.edge-right {
    transform: translateX(60%);
}

.float-button.edge-right::after {
    right: 0;
    left: -100%;
    width: 200%;
}

.float-button.active {
    transform: translateX(0);
}

.button-icon {
    display: block;
    margin: 0 auto;
}

.button-text {
    color: #ffffff;
    font-size: 24rpx;
    text-align: center;
    width: 100%;
    line-height: 1;
}
</style>