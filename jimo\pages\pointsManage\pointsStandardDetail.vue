<template>
	<view class="main-container">
		<view class="detail-container">
			<view class="label">
				标题
			</view>
			<view class="content">
				{{title}}
			</view>
			<view class="label">
				具体标准
			</view>
			<view class="content">
				{{content}}
			</view>
			<view class="label">
				加减状态
			</view>
			<view class="content">
				{{addSubtractedStateName}}
			</view>
			<view class="label">
				积分范围
			</view>
			<view class="content" v-if="!!pointStandardId">
				{{minimum}}-{{maximum}}积分
			</view>
			<view class="content" v-else>
				{{scope}}
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, nextTick, computed, defineExpose } from 'vue'
	import { onShow, onLoad, onReachBottom } from '@dcloudio/uni-app'
	import { findPointsStandDetail,findPartyPointsStandDetail, findPointsStandDetailHistory,findPartyPointsStandDetailHistory } from "../../api/points/points"
	const content = ref('') // 内容
	const addSubtractedStateName = ref('') // 加减状态
	const minimum = ref('') // 积分最小值
	const maximum = ref('') // 积分最大值
	const pointStandardId = ref('') // 积分标准id
	const pointApplyId = ref('') // 审批id
	const scope = ref('') // 积分范围
	let sourceType = ref('1')  //1为家庭 2为党员
	onLoad((options) => {
		sourceType.value = options?.sourceType || '1'
		// 发起时查看积分标准，查看的是实时标准，需要传标准id
		if (!!options.pointStandardId) {
			pointStandardId.value = options.pointStandardId
			getStandardDetailCurrent()
		}
		// 如果是审批详情页查看积分标准，则查看的是发起审批时的标准详情，不是实时的
		if (!!options.pointApplyId) {
			pointApplyId.value = options.pointApplyId
			getStandardDetailHistory()
		}
		
	})
	// 获取实时评分标准详情
	function getStandardDetailCurrent() {
		let param = {
			pointStandardId: pointStandardId.value
		}
		uni.showLoading({
			title: '加载中...',
			mask: true,
		})
		let api = sourceType.value == '2' ? findPartyPointsStandDetail : findPointsStandDetail
		api(param).then(res => {
			if (res.success && res.data) {
				title.value = res.data.title || ''
				content.value = res.data.content || ''
				addSubtractedStateName.value = res.data.addSubtractedStateName || ''
				if (res.data.addSubtractedState == 'add') {
					addSubtractedStateName.value = '加分'
				} else if (res.data.addSubtractedState == 'subtracted') {
					addSubtractedStateName.value = '减分'
				}
				minimum.value = res.data.minimum || ''
				maximum.value = res.data.maximum || ''
				uni.hideLoading()
			} else {
				uni.hideLoading()
				uni.showLoading({
					title: res.message || '评分标准信息获取失败',
					mask: true,
					complete: () => {
						setTimeout(() => {
							uni.navigateBack()
						},2000)
					}
				})
			}
		}).catch(err => {
			console.log(err);
			uni.hideLoading()
			uni.showLoading({
				title: res.message || '评分标准信息获取失败',
				mask: true,
				complete: () => {
					setTimeout(() => {
						uni.navigateBack()
					},2000)
				}
			})
		})
	}
	// 获取历史评分标准详情
	function getStandardDetailHistory() {
		let param = {
			pointApplyId: pointApplyId.value
		}
		uni.showLoading({
			title: '加载中...',
			mask: true,
		})
		let api = sourceType.value == '2' ? findPartyPointsStandDetailHistory : findPointsStandDetailHistory
		api(param).then(res => {
			if (res.success && res.data) {
				title.value = res.data.standardTitle || ''
				content.value = res.data.standardContent || ''
				addSubtractedStateName.value = res.data.addSubtractedState || ''
				scope.value = res.data.scope
				uni.hideLoading()
			} else {
				uni.hideLoading()
				uni.showLoading({
					title: res.message || '评分标准信息获取失败',
					mask: true,
					complete: () => {
						setTimeout(() => {
							uni.navigateBack()
						},2000)
					}
				})
			}
		}).catch(err => {
			console.log(err);
			uni.hideLoading()
			uni.showLoading({
				title: res.message || '评分标准信息获取失败',
				mask: true,
				complete: () => {
					setTimeout(() => {
						uni.navigateBack()
					},2000)
				}
			})
		})
	}
</script>

<style lang="scss" scoped>
	.main-container {
		box-sizing: border-box;
		padding: 20rpx;
		width: 100%;

		.detail-container {
			padding: 20rpx;
			background-color: #fff;
			border-radius: 20rpx;

			.label,
			.content {
				width: 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #000;
				text-align: left;
				font-style: normal;
				word-wrap: break-word;
			}

			.label {
				margin-bottom: 20rpx;
				height: 40rpx;
				line-height: 40rpx;
				color: #999999;
			}

			.content {
				margin-bottom: 30rpx;
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F0F7F7;
	}
</style>