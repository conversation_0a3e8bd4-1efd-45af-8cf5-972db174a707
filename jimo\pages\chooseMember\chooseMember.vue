<template>
	<view class="container">
		<u-navbar title="选择成员" :title-style="{'fontSize': '36rpx'}" placeholder border auto-back>
		</u-navbar>
		<view class="content-container">
			<u-search placeholder="请输入关键字" v-model="keyword" shape="round" @search='filterMember' :showAction='false'></u-search>
			<template v-if="memberList && memberList.length > 0">
				<view class="article-list">
					<view :class="{'article-item':true, 'articleDisabled':member.disabled}" v-for="member in memberList" :key="member.peopleId"
						@click="chooseMember(member)">
						<view class="article-radio" v-if="chooseType == 'multi'">
							<view style="position: relative;">
								<view class="radio-img"></view>
								<view v-if="isChoosed(member)" class="active"></view>
							</view>
						</view>
						<view class="article-content">
							<view class="article-avatar">
								<u-avatar :src="member.photo" size="98rpx"
									:default-url="DEFAULT_AVATOR_IMAGES"></u-avatar>
							</view>
							<view class="article-text">
								<view style="font-size: 32rpx;margin-bottom: 10rpx;">{{ member.name }}</view>
								<view style="font-size: 28rpx;;color: #999999;">{{ member.dept }}</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 底部加载，三个状态：more、loading、nomore -->
				<!-- <u-loadmore :status="loadStatus"></u-loadmore> -->
			</template>
			<view v-if='noData' class="empty-status">
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
		</view>
		<!-- 占位块，使上一个view的marginbottom生效 -->
		<view style="height: 1rpx;"></view>
	</view>
	<view class="submit-container paddingbottom" v-if="chooseType == 'multi'">
		<view class="submit" @click="submit">
			确认
		</view>
	</view>
</template>

<script setup>
	import { reactive, ref } from "vue";
	import { onLoad } from '@dcloudio/uni-app';
	import { getFlowNodePeople, updateReferPeople } from '../../api/flowPage/flowPage.js'
	import { NO_MORE_IMG, DEFAULT_AVATOR_IMAGES } from '@/common/net/staticUrl.js'
	import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';

	const memberList = ref([]);
	const originalList = ref([]) // 人员列表原始数据，用于过滤人员
	const total = ref(0);
	const isLoading = ref(false);
	const loadStatus = ref("loading");
	const flowId = ref('')
	const eventType = ref('')
	const chooseType = ref('single') // 单选还是多选
	const noData = ref(false)
	const keyword = ref('') // 搜索关键字
	let flowFlag = ref('')
	let isEditReferee = ref('') //新增是已选择的转阅人
	let applyId = ref('') //申请id
	let applyPeopleId = ref('') //申请人id
	onLoad((options) => {
		if (!!options.flowId) {
			flowId.value = options.flowId;
		}
		if (!!options.chooseType) {
			chooseType.value = options.chooseType;
		}
		if (!!options.choosedMembers) {
			choosedMembers.value = JSON.parse(options.choosedMembers);
		}
		if(!!options.eventType) {
			eventType.value = options.eventType
		}
		if(!!options.flowFlag){
			flowFlag.value = options.flowFlag
		}
		if(!!options.isEditReferee){
			isEditReferee.value = options.isEditReferee
		}
		if(!!options.applyId){
			applyId.value = options.applyId
		}
		if(!!options.applyPeopleId){
			applyPeopleId.value = options.applyPeopleId
		}
		getList();
	})

	// 获取成员列表
	function getList() {
		//显示加载中动画
		// uni.showLoading({
		// 	title: '加载中...',
		// 	mask: true,
		// })
		let queryParams = {
			flowId: flowId.value,
			isDetail: '0',
			eventType: eventType.value,
			isFlowSetting: '0'
		};
		getFlowNodePeople(queryParams).then((res) => {
			handleView();
			if (res.success) {
				// total.value = res.data.total;
				// if (queryParams.currentPage > 1) {
				// 	memberList.value = [...memberList.value, ...res.data.records];
				// } else {
				// 	memberList.value = res.data.records;
				// }
				// loadStatus.value = total.value === memberList.value.length ? "nomore" : "more";
				if(chooseType.value == 'single') {
					// 审批人
					memberList.value = res.data.approver;
					originalList.value = res.data.approver;
				}else if (chooseType.value == 'multi') {
					// 工作上报--转阅人
					if(flowFlag.value === 'gongzuoshangbao'){
						let list = res.data.approver
						if(choosedMembers.value && choosedMembers.value.length > 0 && !!isEditReferee.value){
							// 审核过程中只能增加转阅人，不能删除原本已选定的
							list.forEach(x => {
								if(choosedMembers.value.some(y => y.peopleId === x.peopleId)){
									x.disabled = true
								}
							})
						}
						memberList.value = list;
						originalList.value = list;
						return
					}
					// 抄送人
					memberList.value = res.data.sendPeople;
					originalList.value = res.data.sendPeople;
				}
				if (memberList.value.length == 0) {
					noData.value = true
				}
			} else {
				uni.showToast({
					title: res.message || '查询数据失败',
					icon: 'none'
				});
				// loadStatus.value = "more";
			}
		}).catch(() => {
			handleView();
			uni.showToast({
				title: '查询数据失败',
				icon: 'none'
			});
			// loadStatus.value = "more";
		})
	}

	// 请求成功后更新视图
	function handleView() {
		//成功获取数据后隐藏加载动画
		// uni.hideLoading()
		//成功获取数据后结束下拉刷新
		uni.stopPullDownRefresh();
		isLoading.value = false;
	}

	//下拉刷新
	onPullDownRefresh(() => {
		// 触发下拉刷新时执行
		if (isLoading.value == false) {
			isLoading.value = true;
			// queryParams.currentPage = 1;
			// loadStatus.value = "loading";
			getList();
		}
	});

	//下拉监听方法
	onReachBottom(() => {
		// if (loadStatus.value == 'nomore') {
		// 	return;
		// }
		// queryParams.currentPage += 1;
		// getList();
	});

	// 选中的成员列表
	const choosedMembers = ref([]);

	// 选中成员
	function chooseMember(memberInfo) {
		if(memberInfo.disabled){
			return console.log('无法取消该人员')
		}
		// 单选时选中即返回，多选时选中后只保存选项，需要通过按钮触发返回
		if (chooseType.value == 'single') {
			choosedMembers.value = [];
			choosedMembers.value.push(memberInfo)
			// 返回上一页
			uni.navigateBack({
				delta: 1
			});
			// 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
			uni.$emit('chooseMemberCallback', choosedMembers.value);
		} else {
			// 先判断当前的点击的是否已被选中，已被选中的点击是取消选中，未被选中的点击是选中
			let tmpIndex = choosedMembers.value.findIndex(member => {
				return member.peopleId == memberInfo.peopleId
			})
			// 不存在则添加
			if (tmpIndex == -1) {
				choosedMembers.value.push(memberInfo)
			} else {
				// 已存在则删除
				choosedMembers.value.splice(tmpIndex, 1)
			}
			console.log("choosedMembers.value------", choosedMembers.value);
		}
	}
	// 判断当前选项是否已选中
	function isChoosed(memberInfo) {
		let tmpObj = choosedMembers.value.find(item => {
			return item.peopleId == memberInfo.peopleId
		})
		if (!!tmpObj) {
			return true
		} else {
			return false;
		}
	}
	// 提交选中人员
	function submit() {
		if(!!isEditReferee.value){
			// 修改时，添加新的转阅人 直接接口保存
			let referPeopleIds = []
			choosedMembers.value.forEach(item => {
				referPeopleIds.push(item.peopleId)
			})
			let params = {
				referPeopleIdList: referPeopleIds,
				applyId: applyId.value,
				flowId: flowId.value,
				applyPeopleId: applyPeopleId.value
			}
			updateReferPeople(params).then(res => {
				if(res.success){
					// 返回上一页
					uni.navigateBack({
						delta: 1
					});
					// 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
					uni.$emit('chooseMemberCallback', choosedMembers.value);
				}else{
					uni.showToast({
						title:res.message,
						icon:'none'
					})
				}
			}).catch(e => {
				uni.showToast({
					title: e,
					icon:'none'
				})
			})
			return
		}
		// 返回上一页
		uni.navigateBack({
			delta: 1
		});
		// 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
		uni.$emit('chooseMemberCallback', choosedMembers.value);
	}
	// 过滤人员
	function filterMember() {
		if (keyword.value == '') {
			memberList.value = originalList.value
		} else {
			memberList.value = originalList.value.filter((item) => {
				return item.name.indexOf(keyword.value) != -1
			})
		}
		if (memberList.value.length == 0) {
			noData.value = true
		} else {
			noData.value = false
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background: #F0F7F7;
		padding-bottom: calc(env(safe-area-inset-bottom) + 135rpx);

		.content-container {
			display: block;
			margin: 20rpx;
			padding: 25rpx;
			background: #fff;
			border-radius: 10rpx;
			box-shadow: 1rpx 2rpx 8rpx 6rpx rgba(79, 139, 250, 0.05);
			min-height: 100rpx;

			.article-list {
				margin-top: 20rpx;
				width: 100%;

				.article-item {
					display: flex;
					align-items: center;
					width: 100%;
					height: 120rpx;
					margin-bottom: 18rpx;
					padding-top: 18rpx;

					&:last-child {
						padding-bottom: 24rpx;
					}

					.article-radio {
						width: 60rpx;
						padding-bottom: 18rpx;

						.radio-img {
							width: 30rpx;
							height: 30rpx;
							margin: auto;
							background: #FFFFFF;
							border: 2rpx solid #C3C3C3;
							border-radius: 50%;
						}

						.active {
							position: absolute;
							top: 50%;
							left: 50%;
							width: 20rpx;
							height: 20rpx;
							background: #0BBD88;
							border-radius: 50%;
							transform: translate(-50%, -50%);
						}
					}

					.article-content {
						display: flex;
						align-items: center;
						height: 100%;
						border-bottom: 1rpx solid #E0E0E0;
						flex: 1;
						padding-bottom: 18rpx;
						margin-left: 25rpx;

						.article-text {
							margin-left: 20rpx;
							display: flex;
							flex-direction: column;
							align-items: flex-start;
							line-height: 1.5;
						}
					}
				}
				.articleDisabled{
					.radio-img{
						background: #ebedf0;
					}
					.active{
						background: #c8c9cc !important;
					}
					.article-text{
						color: #c8c9cc;
					}
					.article-avatar{
						filter: opacity(0.6);
					}
				}
			}

			.empty-status {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 50rpx 0;

				.empty-icon {
					display: flex;
					flex-direction: column;
					justify-content: center;

					.no-more {
						margin-top: 20rpx;
						display: flex;
						justify-content: center;
						margin-top: 10rpx;

						.txt {
							text-align: center;
							height: 37rpx;
							font-size: 26rpx;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #333333;
							line-height: 37rpx;
						}
					}
				}
			}
		}
	}

	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 125rpx;
		background: #FFFFFF;

		.submit {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-size: 36rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>