<template>
    <!-- 活动详情 -->
    <view class="container" v-if="dataInfo">
        <!-- 交通通行 -->
        <view class="traffic" v-if="parentKey == 'traffic'">
            <view class="traffic-top">
                <u-navbar bgColor="transparent" placeholder title="交通通行" leftIconSize="24" :title-style="titleStyle"
                    @leftClick="leftClick">
                </u-navbar>
                <view class="item-content">
                    <view class="con-title">
                        <view class="title">{{dataInfo?.title}}</view>
                        <view class="price-part">
                            <view class="money">¥</view>
                            <view class="price">
                                {{dealPrice(dataInfo.price)}}
                            </view>
                            <view class="txt">起</view>
                        </view>
                    </view>
                    <view class="desc">{{dataInfo.position}}</view>
                    <view class="time-part">
                        <view class="label">开放时间</view>
                        <view class="value">{{dataInfo.openingStartTime}}-{{dataInfo.openingEndTime}}</view>
                    </view>
                </view>
                <view class="traffic-detail">
                    <view class="active">详细介绍</view>
                    <view class="content-detail1">
                        <scroll-view class="traffic-scroll-style" scroll-y="true">
                            <view v-for="(content, index) in contentArr" :key="index">
                                <rich-text class="rich-text ql-editor" :nodes="content"
                                    @click="showImg(content)"></rich-text>
                                <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                    :style="{ width }"></video>
                            </view>
                        </scroll-view>
                    </view>

                </view>
            </view>
        </view>
        <view v-else class="inner-container">
            <view class="cover-text" v-if="!isshow">
                <u-navbar bgColor="transparent" placeholder leftIconSize="24" autoBack @leftClick="leftClick">
                </u-navbar>
            </view>
            <view class="village-tour-content">
                <view class="bg-img">
                    <!-- 轮播图展示-->
                    <u-swiper :list="swiperList" indicator indicatorMode="line" height="435rpx" circular radius="0rpx"
                        @click="clickSwiper" keyName="url"></u-swiper>
                    <!-- <u-image v-if="dataInfo?.coverPhoto" :src="dataInfo?.coverPhoto" mode="aspectFill"
                            width="750rpx" height="435rpx"></u-image> -->
                </view>
                <view class="lists">
                    <!-- 旅游路线 -->
                    <template v-if="parentKey == 'touristroutes'">
                        <view class="tour-title">{{dataInfo?.title}}</view>
                        <view class="content-detail">
                            <view class="active">路线详情</view>
                            <scroll-view class="tour-scroll-style" scroll-y="true">
                                <view class="content-detail1">
                                    <view v-for="(content, index) in contentArr" :key="index">
                                        <rich-text class="rich-text ql-editor" :nodes="content"
                                            @click="showImg(content)"></rich-text>
                                        <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                            :style="{ width }"></video>
                                    </view>
                                </view>
                            </scroll-view>
                        </view>
                    </template>
                    <!-- 酒店民宿 -->
                    <template v-else-if="parentKey == 'hotelshomestays'">
                        <view class="hotel">
                            <view class="title-part">
                                <view class="con-title">{{dataInfo?.title}}</view>
                                <view class="price-part">
                                    <view class="money">¥</view>
                                    <view class="price">
                                        {{dealPrice(dataInfo.price)}}
                                    </view>
                                    <view class="txt">起</view>
                                </view>
                            </view>
                            <view class="intro">{{dataInfo.introduction}}</view>
                            <view class="address" :style="addressStyle">
                                <view class="position">
                                    <view class="map">{{dataInfo.position}}</view>
                                    <view v-if="dataInfo.hotline" class="hotline">
                                        <view class="label">联系酒店</view>
                                        <view class="value">{{dataInfo.hotline}}</view>
                                    </view>
                                </view>
                                <view v-if="dataInfo.hotline || dataInfo.longitude" class="tel">
                                    <u-image @tap.stop="handlePhone(dataInfo.hotline)" width="52rpx" height="52rpx"
                                        mode="aspectFill" :src="VILLAGE_TOUR_PHONE_ICON">
                                    </u-image>
                                    <u-image class="map-icon" v-if="dataInfo.latitude"
                                        @tap.stop="handleNavigate(dataInfo.contentId)" width="52rpx" height="52rpx"
                                        mode="aspectFill" :src="VILLAGE_TOUR_MAP_ICON">
                                    </u-image>
                                </view>

                            </view>
                            <view class="hotel-detail">
                                <view class="active">详细介绍</view>
                                <scroll-view class="hotel-scroll-style" scroll-y="true">
                                    <view class="hotel-detail1">
                                        <view v-for="(content, index) in contentArr" :key="index">
                                            <rich-text class="rich-text ql-editor" :nodes="content"
                                                @click="showImg(content)"></rich-text>
                                            <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                                :style="{ width }"></video>
                                        </view>
                                    </view>
                                </scroll-view>
                            </view>
                        </view>
                    </template>
                    <!-- 门票玩乐 -->
                    <template v-else-if="parentKey == 'ticketsfun'">
                        <view class="ticket">
                            <view class="title-part">
                                <view class="con-title">{{dataInfo?.title}}</view>
                                <view class="price-part">
                                    <view class="money">¥</view>
                                    <view class="price">
                                        {{dealPrice(dataInfo.price)}}
                                    </view>
                                    <view class="txt">起</view>
                                </view>
                            </view>
                            <view class="middle">
                                <view class="time-part">
                                    <view class="label">开放时间</view>
                                    <view class="value">{{dataInfo.openingStartTime}}-{{dataInfo.openingEndTime}}
                                    </view>
                                </view>
                                <view class="address" :style="addressStyle1">
                                    <view class="position">
                                        <view class="map">{{dataInfo.position}}</view>
                                        <view class="hotline">
                                            <view class="label">联系电话</view>
                                            <view class="value">{{dataInfo.hotline}}</view>
                                        </view>
                                    </view>
                                    <view class="tel" :class="{'tickets-tel':dataInfo.typeName != '景区介绍'}">
                                        <u-image @tap.stop="handlePhone(dataInfo.hotline)" width="52rpx" height="52rpx"
                                            mode="aspectFill" :src="VILLAGE_TOUR_PHONE_ICON">
                                        </u-image>
                                        <u-image class="map-icon"
                                            v-if="dataInfo.latitude && dataInfo.typeName == '景区介绍'"
                                            @tap.stop="handleNavigate(dataInfo.contentId)" width="52rpx" height="52rpx"
                                            mode="aspectFill" :src="VILLAGE_TOUR_MAP_ICON">
                                        </u-image>
                                    </view>
                                </view>

                            </view>
                            <view class="ticket-detail">
                                <view class="active">详细介绍</view>
                                <scroll-view class="ticket-scroll-style" scroll-y="true">
                                    <view class="content-detail1">
                                        <view v-for="(content, index) in contentArr" :key="index">
                                            <rich-text class="rich-text ql-editor" :nodes="content"
                                                @click="showImg(content)"></rich-text>
                                            <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                                :style="{ width }"></video>
                                        </view>
                                    </view>
                                </scroll-view>
                            </view>
                        </view>
                    </template>
                    <!-- 美食指南 -->
                    <template v-else-if="parentKey == 'food'">
                        <view class="food">
                            <view class="title-part">
                                <view class="con-title">{{dataInfo?.title}}</view>
                                <view class="price-part">
                                    <view class="money">¥</view>
                                    <view class="price">
                                        {{dealPrice(dataInfo.price)}}
                                    </view>
                                    <view class="txt">/人均</view>
                                </view>
                            </view>
                            <view class="address" :style="addressStyle">
                                <view class="position">
                                    <view class="map">{{dataInfo.position}}</view>
                                    <view v-if="dataInfo.hotline" class="hotline">
                                        <view class="label">联系电话</view>
                                        <view class="value">{{dataInfo.hotline}}</view>
                                    </view>
                                </view>
                                <view v-if="dataInfo.hotline" class="tel">
                                    <u-image @tap.stop="handlePhone(dataInfo.hotline)" width="52rpx" height="52rpx"
                                        mode="aspectFill" :src="VILLAGE_TOUR_PHONE_ICON">
                                    </u-image>
                                </view>
                            </view>
                            <view class="food-detail">
                                <view class="active">详细介绍</view>
                                <scroll-view class="food-scroll-style" scroll-y="true">
                                    <view class="content-detail1">
                                        <view v-for="(content, index) in contentArr" :key="index">
                                            <rich-text class="rich-text ql-editor" :nodes="content"
                                                @click="showImg(content)"></rich-text>
                                            <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                                :style="{ width }"></video>
                                        </view>
                                    </view>
                                </scroll-view>
                            </view>
                        </view>
                    </template>
                    <!-- 本地特产 -->
                    <template v-else-if="parentKey == 'localspecialties'">
                        <view class="local">
                            <view class="local-title">{{dataInfo?.title}}</view>
                            <view class="intro">{{dataInfo.introduction}}</view>
                            <view v-if="dataInfo.hotline" class="contact">
                                <view class="hotline">
                                    <view class="label">联系电话</view>
                                    <view class="value">{{dataInfo.hotline}}</view>
                                </view>
                                <view class="tel">
                                    <u-image @tap.stop="handlePhone(dataInfo.hotline)" width="52rpx" height="52rpx"
                                        mode="aspectFill" :src="VILLAGE_TOUR_PHONE_ICON">
                                    </u-image>
                                </view>

                            </view>
                            <view class="local-detail">
                                <view class="active">详细展示</view>
                                <scroll-view class="local-scroll-style" scroll-y="true">
                                    <view class="content-detail1">
                                        <view v-for="(content, index) in contentArr" :key="index">
                                            <rich-text class="rich-text ql-editor" :nodes="content"
                                                @click="showImg(content)"></rich-text>
                                            <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
                                                :style="{ width }"></video>
                                        </view>
                                    </view>
                                </scroll-view>
                            </view>
                        </view>
                    </template>

                </view>

            </view>
        </view>

    </view>
</template>
<script setup>
    import {
        VILLAGE_TOUR_MAP_BG,
        VILLAGE_TOUR_PHONE_ICON,
        VILLAGE_TOUR_MAP_ICON
    } from '@/common/net/staticUrl.js'
    import {
        ref,
        onMounted,
        reactive,
        computed
    } from 'vue'
    import {
        onLoad,
        onShareAppMessage,
        onShareTimeline
    } from '@dcloudio/uni-app'
    import {
        findOneContentNew,
    } from "../../api/villageCodeTour/villageCodeTour.js"
    import {
        responsibility
    } from '@/common/net/my/my.js'
    import {
        loadImage
    } from '@/common/getImage.js'
    import {
        goldTask
    } from '../../api/api.js'
    import {
        hasLogin,
        objToStr
    } from './villageCodeTourUnit.js'
    import kvStore from '@/common/store/uniKVStore.js'

    const isLogin = kvStore.get('hasLogin', true)
    let contentId = ref('');
    let parentKey = ref('')
    let dataInfo = ref(null)
    let isshow = ref(false);
    let contentArr = reactive([]);
    let videoArr = reactive([]);
    let show = ref(false)
    const titleStyle = {
        height: '50rpx',
        fontSize: '36rpx',
        fontFamily: 'Source Han Sans CN, Source Han Sans CN',
        color: '#000',
        fontWeight: 600,
        lineHeight: '50rpx',
    }
    const addressStyle = {
        backgroundImage: "url(" + VILLAGE_TOUR_MAP_BG + ")",
        backgroundSize: '691rpx 140rpx',
        backgroundRepeat: 'no-repeat'
    }
    const addressStyle1 = {
        backgroundImage: "url(" + VILLAGE_TOUR_MAP_BG + ")",
        backgroundSize: '480rpx 140rpx',
        backgroundRepeat: 'no-repeat'
    }

    const query = ref({})
    const scanFlag = ref(false)
    const cur_longitude = ref(''); //当前人位置经度
    const cur_latitude = ref(''); //当前人位置纬度
    onLoad((options) => {
        getUserLocation()
        console.log('----------options', options)
        // 判断是否登录
        query.value = {
            from: 'villageCodeTour',
            contentId: options.contentId,
            parentKey: options.parentKey,
        }
        if (!hasLogin('detail', query.value, isLogin)) return

        if (!!options.from && options.from == 'villageCodeTour') {
            // 扫码跳转的会携带参数from=villageCodeTour
            scanFlag.value = true
        }

        contentId.value = options.contentId;
        parentKey.value = options.parentKey;
        if (parentKey.value == 'ticketsfun') {
            show.value = true;
        }
        getInfo()
        getGoldTask("1")
    })

    function leftClick() {
        console.log('leftClick触发了scanFlag', scanFlag.value);
        const pages = getCurrentPages();
        console.log('currentPages----', pages.length);
        if (scanFlag.value) {
            uni.switchTab({
                url: '/pages/home/<USER>',
            })
        } else {
            const pages = getCurrentPages();
            if (pages.length === 1) {
                uni.switchTab({
                    url: '/pages/home/<USER>',
                })
            } else {
                uni.navigateBack();
            }
        }
    }

    const swiperList = ref([])
    // 获取文件的后缀名
    const getFileName = (item) => {
        let index = item.lastIndexOf(".")
        let name = item.substring(index + 1);
        return name;
    }
    async function getSwiper() {
        if (dataInfo.value) {
            const {
                coverPhoto
            } = dataInfo.value
            let phtotArr = coverPhoto && coverPhoto.includes('[') ? JSON.parse(coverPhoto) : [coverPhoto]
            let arr = []
            for (let i = 0; i < phtotArr.length; i++) {
                let tempimg = await loadImage(phtotArr[i])
                let suffixName = getFileName(phtotArr[i])
                let isVideo = ['mp4', 'mov', 'avi', 'flv'].indexOf(suffixName) >= 0
                arr.push({
                    url: tempimg,
                    type: isVideo ? 'video' : 'image'
                })
            }
            swiperList.value = arr
        }
    }

    function getInfo() {
        //显示加载中动画
        uni.showNavigationBarLoading();
        findOneContentNew({
            contentId: contentId.value
        }).then((res) => {
            console.log(res.data)
            if (res.success) {
                console.log(res.data);
                dataInfo.value = res.data;
                getSwiper()
                parseVideo()
                //成功获取数据后隐藏加载动画
                uni.hideNavigationBarLoading();
            }
        }).catch(() => {
            //成功获取数据后隐藏加载动画
            uni.hideNavigationBarLoading();
        })
    }
    //监听滑动事件
    function onScroll(e) {
        console.log('---------------------onscroll')
        console.log(e)
        console.log(e.detail)
        console.log(e.detail.scrollTop)
        if (e.detail.scrollTop < 50) { //滚动距离为50就显示
            isshow.value = false
        } else {
            isshow.value = true
        }
    }

    function getUserLocation() {
        uni.getLocation({
            type: "gcj02",
            success: function(res) {
                // 暂时
                cur_longitude.value = res.longitude; //118.787575;
                cur_latitude.value = res.latitude; //32.05024;
                console.log("获取当前的用户经度", cur_longitude.value);
                console.log("获取当前的用户纬度", cur_latitude.value);
            }
        })
    }
    //发送给朋友
    onShareAppMessage((from) => {
        getGoldTask("2")
        return {
            title: dataInfo.value.title, // 标题
            path: `/jimo/pages/villageCodeTour/detail?${objToStr(query.value)}`, // 要分享的页面
        }
    })
    // 分享到朋友圈
    onShareTimeline(() => {
        getGoldTask("2")
        return {
            title: dataInfo.value.title,
            path: `/jimo/pages/villageCodeTour/detail?${objToStr(query.value)}`, // 要分享的页面
        }
    })
    // 富文本点击查看大图
    function showImg(content) {
        // 富文本
        const richContent = content;
        // 判断含有图片
        if (richContent.indexOf("src") >= 0) {
            const imgs = [];
            richContent.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function(match, capture) {
                imgs.push(capture);
            })
            uni.previewImage({
                current: imgs[0], // 当前显示图片的链接
                urls: imgs
            })
        }
    }

    //处理富文本的数据包含视频
    function parseVideo() {
        console.log('----------act')
        console.log(dataInfo.value)
        const content = dataInfo.value.content
        if (typeof content != 'string') {
            //不是HTML字符串格式的暂不处理
            contentArr[0] = content;
            videoArr[0] = null;
            return false;
        }

        //同步解决如果图片太大超出手机显示界面的问题
        let nodes = content.replace(/\<img/g, '<img style="max-width:98%!important;height:auto;"');
        let arr = nodes.split('</video>');
        let reg = /<video([\s\S]*)/g;

        for (let i in arr) {
            var item = arr[i];
            var urlMatch = item.match(/<video[\s\S]*src=\"(.*?)\"/);
            if (urlMatch && urlMatch.length > 1) {
                videoArr[i] = urlMatch[1];
            } else {
                videoArr[i] = null;
            }
            contentArr[i] = item.replace(reg, '');
        }
    }

    function dealPrice(value) {
        value = value.replace(/[^\d.]/g, '');
        value = value.replace(/^\./g, '');
        value = value.replace(/\.{2,}/g, '.');
        value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
        value = value.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        if (value.length > 8) {
            value = value.substring(0, 8);
        }
        if (value > 100000) {
            value = '99999';
        }
        return value;
    }

    function handlePhone(value) {
        console.log('-----------value')
        console.log(value)
        // #ifdef H5
        window.open(`tel:${value}`)
        // #endif
        //#ifdef MP-WEIXIN

        uni.makePhoneCall({
            phoneNumber: value, //item.phone,
            success: (res) => {
                console.log(res);
            },
        });
        //#endif
        //#ifdef APP-PLUS
        plus.device.dial(value, true);
        //#endif
    }

    function handleNavigate(id) {
        openMap();
       
    }

    // 金币任务-乡村一码游-分享
    async function getGoldTask(type) {
        console.log("场景ID-信息contentId", contentId.value)
        let taskType = type == '1' ? '5' : '8'
        try {
            let res = await goldTask({
                taskId: "",
                taskType,
                appKey: 'villageCodeTour'
            });
            if (res && res.success) {
                let goldMessage = `获得${res.data.coin}金币`
                uni.showToast({
                    title: goldMessage,
                    icon: 'none',
                    duration: 1500,
                    success: () => {
                        if (type == '1') {
                            const timer = setTimeout(() => {
                                clearTimeout(timer)
                                // 家庭任务积分获取-阅读信息
                                responsibility({
                                    taskType: '4'
                                })
                            }, 1500)
                        }
                    }
                })
            } else {
                // 家庭任务积分获取-阅读信息
                if (type == '1') responsibility({
                    taskType: '4'
                })
                console.log('获取金币失败', res)
            }
        } catch (err) {
            console.error('获取金币异常', err)
        }
    }
    // 打开地图
    function openMap() {
        let lon = dataInfo.value.longitude;
        let lat = dataInfo.value.latitude;
        const name = dataInfo.value.title;
        const address = dataInfo.value.position;
        console.log("获取经纬度ssssfff", lon, lat);
        //打开地图
        // #ifdef MP-WEIXIN
        uni.openLocation({
            latitude: parseFloat(lat),
            longitude: parseFloat(lon),
            name,
            address,
            scale: 18
        })
        // #endif
        // #ifdef APP-PLUS
        // openMapApp(lat, lon, currentInfo.value.title);
        handleNavigation()
        // #endif
    }

    function handleNavigation() {
        // #ifdef APP-PLUS
        // 判断系统安装的地图应用有哪些, 并生成菜单按钮
        let mapList = [{
                title: '高德地图',
                name: 'amap',
                androidName: 'com.autonavi.minimap',
                iosName: 'iosamap://'
            },
            {
                title: '百度地图',
                name: 'baidumap',
                androidName: 'com.baidu.BaiduMap',
                iosName: 'baidumap://'
            },
            {
                title: '腾讯地图',
                name: 'qqmap',
                androidName: 'com.tencent.map',
                iosName: 'qqmap://'
            }
        ];
        // 根据真机有的地图软件 生成的 操作菜单
        let buttons = [];
        let platform = uni.getSystemInfoSync().platform;
        platform === 'android' &&
            mapList.forEach(item => {
                if (plus.runtime.isApplicationExist({
                        pname: item.androidName
                    })) {
                    buttons.push(item);
                }
            });
        platform === 'ios' &&
            mapList.forEach(item => {
                console.log(item.iosName);
                if (plus.runtime.isApplicationExist({
                        action: item.iosName
                    })) {
                    buttons.push(item);
                }
            });
        if (buttons.length) {
            plus.nativeUI.actionSheet({
                    //选择菜单
                    title: '选择地图应用',
                    cancel: '取消',
                    buttons: buttons
                },
                e => {
                    let _map = buttons[e.index - 1];
                    openURL(_map, platform);
                }
            );
        } else {
            uni.showToast({
                title: '请安装地图软件',
                icon: 'none'
            });
            return;
        }
        // #endif
    }

    // 打开第三方程序实际应用
    function openURL(map, platform) {
        console.log('----------------current', dataInfo.value)
        const name = dataInfo.value.title;
        const latitude = dataInfo.value.latitude;
        const longitude = dataInfo.value.longitude;
        let urls = {
            android: {
                amap: `amapuri://route/plan/?sid=&did=&dlat=${latitude}&dlon=${longitude}&dname=${name}&dev=0&t=0`,
                qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${latitude},${longitude}&referer=ixinyi_client`,
                baidumap: `baidumap://map/direction?origin=${cur_latitude.value},${cur_longitude.value}&destination=name:${name}|latlng:${latitude.value},${longitude.value}&coord_type=wgs84&mode=driving&src=andr.baidu.openAPIdemo"`
            },
            ios: {
                amap: `iosamap://path?sourceApplication=ixinyi_client&dlat=${latitude}&dlon=${longitude}&dname=${name}&dev=0&t=0`,
                qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${latitude},${longitude}&referer=ixinyi_client`,
                baidumap: `baidumap://map/direction?origin=${cur_latitude.value},${cur_longitude.value}&destination=name:${name}|latlng:${latitude},${longitude}&mode=driving&src=ios.baidu.openAPIdemo`
            }
        };
        let newurl = encodeURI(urls[platform][map.name]);
        console.log(newurl);
        plus.runtime.openURL(
            newurl,
            function(res) {
                uni.showModal({
                    content: res.message
                });
            },
            map.androidName ? map.androidName : ''
        );
    }
</script>

<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .scroll-style {
        box-sizing: border-box;
        height: 100vh;
    }

    .active {
        position: relative;
        width: 128rpx;
        height: 60rpx;
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        color: #33374D;
        line-height: 45rpx;

        &:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 42rpx;
            width: 43rpx;
            height: 6rpx;
            background: #0BBD88;
            border-radius: 3rpx;
        }
    }

    .content-detail {
        margin-top: 16rpx;
    }

    .content-detail1 {
        padding: 16rpx 0;
    }

    .container {
        width: 750rpx;
        background: #F4F8F7;
        height: 100vh;
        overflow: hidden;
        position: relative;

        .traffic-top {
            width: 750rpx;
            height: 307rpx;
            // background: linear-gradient(180deg, #69E88B 0%, #F4F8F7 100%);
            background: linear-gradient(180deg, rgba(105, 232, 139, 0.34) 0%, rgba(244, 248, 247, 0.34) 100%);
            // opacity: 0.34;

            .item-content {
                margin-top: 16rpx;
                padding: 0 35rpx;

                .con-title {
                    display: flex;
                    justify-content: space-between;

                    .title {
                        width: 520rpx;
                        height: 100rpx;
                        font-size: 36rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 50rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        /* 将容器以弹性盒子形式布局 */
                        -webkit-line-clamp: 2;
                        /* 限制文本显示为两行 */
                        -webkit-box-orient: vertical;
                        /* 将弹性盒子的主轴方向设置为垂直方向 */
                        overflow: hidden;
                        /* 隐藏容器中超出部分的内容 */
                        text-overflow: ellipsis;
                        /* 超出容器范围的文本显示省略号 */
                    }

                    .price-part {
                        display: flex;
                        margin-left: 26rpx;

                        .price {
                            max-width: 120rpx;
                            height: 50rpx;
                            font-size: 36rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #666666;
                            line-height: 50rpx;
                            background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .money {
                            height: 50rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #666666;
                            line-height: 53rpx;
                            background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .txt {
                            width: 24rpx;
                            height: 42rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 50rpx;
                        }
                    }

                }

                .desc {
                    margin-top: 16rpx;
                    width: 671rpx;
                    height: 84rpx;
                    font-size: 24rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #999999;
                    line-height: 42rpx;
                    word-break: break-all;
                    display: -webkit-box;
                    /* 将容器以弹性盒子形式布局 */
                    -webkit-line-clamp: 2;
                    /* 限制文本显示为两行 */
                    -webkit-box-orient: vertical;
                    /* 将弹性盒子的主轴方向设置为垂直方向 */
                    overflow: hidden;
                    /* 隐藏容器中超出部分的内容 */
                    text-overflow: ellipsis;
                    /* 超出容器范围的文本显示省略号 */
                }

                .time-part {
                    margin-top: 24rpx;
                    width: 671rpx;
                    height: 140rpx;
                    background: #FFFFFF;
                    border-radius: 8rpx;
                    padding: 24rpx 16rpx 16rpx;

                    .label {
                        width: 112rpx;
                        height: 42rpx;
                        font-size: 28rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #333333;
                        line-height: 42rpx;
                    }

                    .value {
                        margin-top: 16rpx;
                        height: 42rpx;
                        font-size: 24rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #333333;
                        line-height: 42rpx;
                    }
                }
            }

        }

        .traffic-scroll-style {
            height: calc(100vh - 670rpx - env(safe-area-inset-top) - env(safe-area-inset-bottom));
        }

        .traffic-detail {
            margin-top: 16rpx;
            padding: 10rpx 36rpx;

            .content-detail1 {
                padding-top: 16rpx;
                // height: calc(100vh - 480rpx - env(safe-area-inset-bottom) - env(safe-area-inset-top));
                // overflow-y: auto;
            }
        }

        .inner-container {
            position: relative;

            .cover-text {
                z-index: 9;
                position: absolute;
                top: 6vh;
                left: 30rpx;
                display: flex;
                flex-direction: column;

                .title {
                    margin-top: 75rpx;
                    height: 50rpx;
                    font-size: 36rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    color: #fff;
                    line-height: 50rpx;
                }
            }

        }

        .bg-img {
            z-index: 1;
            width: 750rpx;
        }

        .village-tour-content {
            position: relative;
            width: 100%;
            height: 100vh;

            .lists {
                position: absolute;
                z-index: 10;
                top: calc(374rpx + env(safe-area-inset-top));
                height: calc(100vh - 308rpx - env(safe-area-inset-top) - env(safe-area-inset-bottom));
                left: 0;
                width: 750rpx;
                padding: 0 36rpx 24rpx;
                // background: linear-gradient(180deg, #EFFFF9 0%, #FFFFFF 5%, #FFFFFF 100%);
                background: linear-gradient(180deg, #EFFFF9 0.1%, #FFFFFF 100%);
                box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                border-radius: 20rpx;

                .tour-title {
                    margin-top: 24rpx;
                    font-size: 36rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    color: #000000;
                    line-height: 50rpx;
                }

                .tour-scroll-style {
                    height: calc(100vh - 540rpx - env(safe-area-inset-top) - env(safe-area-inset-bottom));
                }

                .tour-detail1 {
                    padding-top: 16rpx;
                    padding-bottom: calc(env(safe-area-inset-bottom) + 10rpx);
                }

                .title-part {
                    display: flex;
                    justify-content: space-between;

                    .con-title {
                        width: 480rpx;
                        height: 100rpx;
                        font-size: 36rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 50rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        /* 将容器以弹性盒子形式布局 */
                        -webkit-line-clamp: 2;
                        /* 限制文本显示为两行 */
                        -webkit-box-orient: vertical;
                        /* 将弹性盒子的主轴方向设置为垂直方向 */
                        overflow: hidden;
                        /* 隐藏容器中超出部分的内容 */
                        text-overflow: ellipsis;
                        /* 超出容器范围的文本显示省略号 */
                    }

                    .price-part {
                        margin-top: 13rpx;
                        margin-left: 24rpx;
                        display: flex;
                        width: 160rpx;
                        display: flex;
                        justify-content: flex-end;

                        .price {
                            max-width: 160rpx;
                            height: 50rpx;
                            font-size: 36rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #666666;
                            line-height: 50rpx;
                            background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .money {
                            height: 50rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #666666;
                            line-height: 53rpx;
                            background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                        }

                        .txt {
                            width: 24rpx;
                            height: 42rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 50rpx;
                        }
                    }
                }

                .hotel {
                    margin-top: 24rpx;

                    .intro {
                        margin-top: 16rpx;
                        width: 671rpx;
                        height: 84rpx;
                        font-size: 24rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #999999;
                        line-height: 42rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        /* 将容器以弹性盒子形式布局 */
                        -webkit-line-clamp: 2;
                        /* 限制文本显示为两行 */
                        -webkit-box-orient: vertical;
                        /* 将弹性盒子的主轴方向设置为垂直方向 */
                        overflow: hidden;
                        /* 隐藏容器中超出部分的内容 */
                        text-overflow: ellipsis;
                        /* 超出容器范围的文本显示省略号 */
                    }

                    .address {
                        display: flex;
                        margin-top: 24rpx;
                        padding: 24rpx 24rpx 16rpx;

                        .position {
                            width: 655rpx;

                            .map {
                                width: 500rpx;
                                height: 42rpx;
                                font-size: 28rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #333333;
                                line-height: 42rpx;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                word-break: break-all;
                                white-space: nowrap;
                            }

                            .hotline {
                                margin-top: 16rpx;
                                display: flex;

                                .label {
                                    width: 120rpx;
                                    height: 42rpx;
                                    border-radius: 8rpx;
                                    border: 1rpx solid #0BBD88;
                                    padding: 4rpx 10rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #0BBD88;
                                    line-height: 35rpx;
                                }

                                .value {
                                    margin-left: 16rpx;
                                    height: 42rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #333333;
                                    line-height: 42rpx;
                                }
                            }
                        }

                        .tel {
                            margin-top: -4rpx;

                            .map-icon {
                                margin-top: 6rpx;
                            }
                        }
                    }

                    .hotel-detail {
                        margin-top: 28rpx;

                        .hotel-scroll-style {
                            height: calc(100vh - 840rpx - env(safe-area-inset-bottom) - env(safe-area-inset-top))
                        }

                        .hotel-detail1 {
                            padding: 16rpx 0;
                        }
                    }

                }

                .ticket {
                    margin-top: 24rpx;

                    .middle {
                        display: flex;
                    }

                    .time-part {
                        width: 180rpx;
                        height: 140rpx;
                        background: #F8F8F8;
                        border-radius: 8rpx;
                        padding: 24rpx 12rpx;

                        .label {
                            height: 42rpx;
                            font-size: 28rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #333333;
                            line-height: 42rpx;
                        }

                        .value {
                            height: 42rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #333333;
                            line-height: 42rpx;
                        }
                    }

                    .address {
                        margin-left: 16rpx;
                        display: flex;
                        padding: 24rpx 16rpx 16rpx;
                        justify-content: flex-end;

                        .position {
                            width: 400rpx;

                            .map {
                                width: 400rpx;
                                height: 42rpx;
                                font-size: 28rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #333333;
                                line-height: 42rpx;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                word-break: break-all;
                                white-space: nowrap;
                            }

                            .hotline {
                                margin-top: 16rpx;
                                display: flex;

                                .label {
                                    width: 120rpx;
                                    height: 42rpx;
                                    border-radius: 8rpx;
                                    border: 1rpx solid #0BBD88;
                                    padding: 4rpx 10rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #0BBD88;
                                    line-height: 35rpx;
                                }

                                .value {
                                    margin-left: 16rpx;
                                    height: 42rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #333333;
                                    line-height: 42rpx;
                                }
                            }
                        }

                        .tel {
                            margin-top: -4rpx;

                            .map-icon {
                                margin-top: 6rpx;
                            }
                        }

                        .tickets-tel {
                            margin-top: 25rpx;
                        }
                    }

                    .ticket-detail {
                        margin-top: 28rpx;

                        .ticket-scroll-style {
                            height: calc(100vh - 710rpx - env(safe-area-inset-bottom) - env(safe-area-inset-top))
                        }
                    }
                }

                .food {
                    margin-top: 24rpx;

                    .con-title {
                        width: 520rpx;
                        height: 50rpx;
                        font-size: 36rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 50rpx;
                    }

                    .price-part {
                        width: 200rpx;
                        text-align: end;

                        .txt {
                            width: 60rpx;
                        }
                    }

                    .address {
                        margin-top: 16rpx;
                        display: flex;
                        padding: 24rpx 16rpx 16rpx;
                        justify-content: flex-end;

                        .position {
                            width: 670rpx;

                            .map {
                                width: 593rpx;
                                height: 42rpx;
                                font-size: 28rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #333333;
                                line-height: 42rpx;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                word-break: break-all;
                                white-space: nowrap;
                            }

                            .hotline {
                                margin-top: 16rpx;
                                display: flex;

                                .label {
                                    width: 120rpx;
                                    height: 42rpx;
                                    border-radius: 8rpx;
                                    border: 1rpx solid #0BBD88;
                                    padding: 4rpx 10rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #0BBD88;
                                    line-height: 35rpx;
                                }

                                .value {
                                    margin-left: 16rpx;
                                    height: 42rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #333333;
                                    line-height: 42rpx;
                                }
                            }
                        }

                        .tel {
                            margin-top: 20rpx;
                        }
                    }


                    .food-detail {
                        margin-top: 28rpx;

                        .food-scroll-style {
                            height: calc(100vh - 690rpx - env(safe-area-inset-bottom) - env(safe-area-inset-top))
                        }

                    }

                }

                .local {
                    .local-title {
                        margin-top: 24rpx;
                        font-size: 36rpx;
                        height: 100rpx;
                        width: 660rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 50rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        /* 将容器以弹性盒子形式布局 */
                        -webkit-line-clamp: 2;
                        /* 限制文本显示为两行 */
                        -webkit-box-orient: vertical;
                        /* 将弹性盒子的主轴方向设置为垂直方向 */
                        overflow: hidden;
                        /* 隐藏容器中超出部分的内容 */
                        text-overflow: ellipsis;
                        /* 超出容器范围的文本显示省略号 */
                    }

                    .intro {
                        margin-top: 16rpx;
                        width: 671rpx;
                        height: 84rpx;
                        font-size: 24rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #999999;
                        line-height: 42rpx;
                        word-break: break-all;
                        display: -webkit-box;
                        /* 将容器以弹性盒子形式布局 */
                        -webkit-line-clamp: 2;
                        /* 限制文本显示为两行 */
                        -webkit-box-orient: vertical;
                        /* 将弹性盒子的主轴方向设置为垂直方向 */
                        overflow: hidden;
                        /* 隐藏容器中超出部分的内容 */
                        text-overflow: ellipsis;
                        /* 超出容器范围的文本显示省略号 */
                    }

                    .contact {
                        width: 671rpx;
                        height: 140rpx;
                        background: #FFFFFF;
                        border-radius: 8rpx;
                        display: flex;
                        justify-content: space-between;
                        margin-top: 10rpx;
                        padding: 24rpx 16rpx 0 16rpx;

                        .hotline {
                            margin-top: 16rpx;
                            display: flex;

                            .label {
                                width: 120rpx;
                                height: 42rpx;
                                border-radius: 8rpx;
                                border: 1rpx solid #0BBD88;
                                padding: 4rpx 10rpx;
                                font-size: 24rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                color: #0BBD88;
                                line-height: 35rpx;
                            }

                            .value {
                                margin-left: 16rpx;
                                height: 42rpx;
                                font-size: 24rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                color: #333333;
                                line-height: 42rpx;
                            }
                        }

                        .tel {
                            margin-top: 20rpx;
                        }

                    }

                    .local-detail {
                        margin-top: 21rpx;

                        .local-scroll-style {
                            height: calc(100vh - 800rpx - env(safe-area-inset-bottom) - env(safe-area-inset-top))
                        }
                    }

                }
            }
        }
    }
</style>