// app配置相关js文件，开发配置文件：生产环境和开发环境隔离
import { isDev } from '@/common/EnvUtils.js'

// 安卓APP打包证书sha-1值 从平台获取到的信息字母为小写，这里注意改成小写字母，并去掉“:”
// 安卓端开发环境证书cerSha1值
let androidCerDev = "7321d00003876b824ec41cc2cfdbf12cb6972a51" 
// 安卓端生产环境证书cerSha1值
let androidCerBuild = "47bb2a17354cbdc3d2521fb983e8dc2734e19903"

// IOS端开发环境证书md5值
let iosMd5Dev = "2713e49198024aa6572030c8509febc1"
// IOS端生产环境证书md5值
let iosMd5Build = "2713e49198024aa6572030c8509febc1"

export const cerSha1 = isDev() ? androidCerDev : androidCerBuild;
export const iosMd5 = isDev() ? iosMd5Dev : iosMd5Build
// sm2 doCrypt公钥
export const publicKey =
    '0428D625CEEB71CE823BD7D78DFEE7B122F2DA5C4D21E32253AD684D0FE21810394A799639C0CDFBFEB535A1DFD6A366A637E582CE0B1466A5FE7858841135DE6B'

function randomStr() {
    let max = 12,
    min = 9,
    stra = "",
    arr =
        "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"; // 随机值的长度
    const range = Math.round(Math.random() * (max - min)) + min;

    //随机数值的产生
    for (var i = 0; i < range; i++) {
        const random = Math.round(Math.random() * (arr.length - 1));
        stra = stra + arr[random];
    }
    return stra;
}

export { randomStr }