<template>
	<view class="content-boxes">
		<view class="formBox">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="uForm" label-position="top"
				label-width="120" :borderBottom="true" labelStyle="font-weight: 600;font-size: 32rpx;">
				<u-form-item label="申报对象" prop="peopleName" :borderBottom="true" @click="openPeople" key="peopleName">
					<u--input v-if="sourceType=='1'" v-model="form.peopleName" disabledColor="#ffffff" placeholder="请选择申报对象"
						:suffixIcon="userStore.currentRole === 'villager' ? '' : 'arrow-right'" border="none" 
						disabled>
					</u--input>
					<u--input v-else v-model="form.peopleName" disabledColor="#ffffff" placeholder="请选择申报对象"
						:suffixIcon="!isDeclarant ? '' : 'arrow-right'" border="none" 
						disabled>
					</u--input>
				</u-form-item>
				<u-form-item label="申报积分标准" prop="pointsStandard" :borderBottom="true" @click="selectStandard"
					key="pointsStandard">
					<u--input v-model="form.pointsStandard" disabled disabledColor="#ffffff" placeholder="请选择积分标准"
						suffixIcon="arrow-right" border="none">
					</u--input>
				</u-form-item>
				<u-form-item label="申报积分" prop="pointsValue" :borderBottom="true" ref="item1" key="pointsValue">
					<u--input v-model="form.pointsValue" border="none" placeholder="请填写积分数值"
						type="number" maxlength="4" :prefixIcon="prefixIcon"
						prefixIconStyle="font-size: 30rpx;"></u--input>
				</u-form-item>
				<u-form-item label="申报说明" prop="pointsDescribe" :borderBottom="true" ref="item1" key="pointsDescribe">
					<u--textarea v-model="form.pointsDescribe" border="none"
						placeholder="请填写申报说明" maxlength="200" :count="true"></u--textarea>
				</u-form-item>
				<u-form-item label="申报凭证" prop="document" :borderBottom="false" ref="item1" key="document">
					<view class="img-upload p30">
						<u-upload :auto-upload="false" :fileList="imgList"
							:deletable='tyrue' :maxCount="6" :mutiple="true"
							width="150rpx" height="150rpx" @afterRead="afterRead($event)"
							@delete="deletePic($event)" @clickPreview='previewPic'></u-upload>
					</view>
				</u-form-item>
			</u--form>
		</view>
		<view class="flow-container">
			<view class="main-title">
				申报流程
			</view>
			<view class="flow-content">
				<view class="flow-content-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								申报人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people">
								{{ applyPeopleName }}
							</view>
						</view>
					</view>
				</view>
				<!-- 需要手动选择一个审批人 -->
				<view class="flow-content-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/handling.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								审批人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people" v-if="form.approver" @click="chooseApprover('single')">
								{{form.approver}}
							</view>
							<view v-else class="people">
								<u-image src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
									height="48rpx" @click="chooseApprover('single')"></u-image>
							</view>
						</view>
					</view>
				</view>
				<!-- 只有审批类流程才展示抄送人 -->
				<view class="flow-content-item send-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/handling.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								抄送人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="noline"></view>
							</view>
							<view class="people send-people u-line-1">
								<!-- 如果有配置好的抄送人则直接展示，不能修改 -->
								{{sendPeopleArr}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
<!-- 		<u-action-sheet :show="showType" :actions="actions" title="请选择加减状态" @close="showType = false"
			@select="typeSelect">
		</u-action-sheet> -->
	</view>
	<view class="bottom-box paddingbottom">
		<view class="submit-btn" @click.stop="submit"> 提交</view>
	</view>
</template>

<script setup>
	import { onMounted, reactive, ref, computed } from 'vue'
	import { onReady, onShow, onHide, onLoad, onUnload, onBackPress } from '@dcloudio/uni-app'
	import { findInfo, findPeople, findApplicantSendPeople,findPartyApplicantSendPeople, addApply,addPartyApply,isDeclarantInfo } from "../../api/points/points"
	import { useTokenStore } from '@/store/token.js'
	import { useUserStore } from '@/store/user.js'
	import { fileUpload } from '@/common/api.js'
	import { setPoints} from '../../api/burialPoint.js'
	const tokenStore = useTokenStore()
	const userStore = useUserStore()
	const tenantId = tokenStore.tenantId

	const recordsId = ref("")
	let sourceType = ref('1')  //1为家庭 2为党员
	let isDeclarant = ref(false) //党员是否为申报人
	const peopleInfo = userStore.userInfo.customParam.peopleInfo
	const form = ref({
		peopleIds:'',
		peopleName: '',	// 申报对象
		// pointsName: "",
		pointsStandard: '', // 积分标准
		pointsStandardId: '', // 积分标准id
		pointsValue: "",	// 积分值
		pointsDescribe: "",	// 申报说明
		document: '',	// 申报凭证图片
		approver: '', // 审批人
		nodePeopleId: '' ,// 审批人ID
		standardUpdateDate: '' // 积分标准更新时间
	})
	onLoad(async(options) => {
		sourceType.value = options?.sourceType || '1'
		await getUserInfo()
		let phone = peopleInfo?.cellphone.slice(-4)
		if(sourceType.value == '2'){
			form.value.peopleIds = !isDeclarant.value ? peopleInfo.villagePeopleId : ''
			form.value.peopleName = !isDeclarant.value ? peopleInfo.name + phone : ''
		}else{
			form.value.peopleIds = userStore.currentRole === 'villager' ? peopleInfo.villagePeopleId : ''
			form.value.peopleName = userStore.currentRole === 'villager' ?  peopleInfo.name + phone : ''	// 申报对象
		}
		if (!!options.recordId) {
			recordsId.value = options.recordId
			findInfo({ recordId: options.recordId }).then(res => {
				form.value = res.data
			})
		}
		// 埋点-进入积分申报填写页
		let param = {
			eventId : 'enter_integral_reporting_form',
			attributeValue: '/jimo/pages/pointsManage/report'
		}
		setPoints(param)
		getApplySendPeople()
	})
	
	const sendPeopleArr = ref('') // 抄送人
	const rules = ref({
		"peopleName": {
			type: 'string',
			required: true,
			message: '请选择申报对象',
			trigger: ['change'],
		},
		"pointsStandard": {
			type: 'string',
			required: true,
			message: '请选择积分标准',
			trigger: ['change'],
		},
		"pointsName": {
			type: 'string',
			required: true,
			message: '请选择加减状态',
			trigger: ['change'],
		},
		"pointsValue": [{
				type: 'number',
				required: true,
				message: '请填写正确的积分数值',
				trigger: ['change'],
			},
			{
				pattern: /^[1-9]\d*$/,
				transform(value) {
					return String(value);
				},
				message: '请填写正确的积分数值',
				trigger: ['change']
			}
		],
		"pointsDescribe": {
			type: 'string',
			required: true,
			message: '请填写申报说明',
			trigger: ['change'],
		},
	})
	onMounted(() => {
		uForm.value.setRules(rules)
	})
	const uForm = ref(null)

	function submit() {
		if (imgList.value.findIndex(o => o.status === 'uploading') > -1) {
			uni.showToast({
				title: '图片上传中...',
				icon: 'none'
			})
			return
		}
		// 埋点-点击积分申报填写页-提交按钮
		let param = {
			eventId : 'submit_integral_reporting_form',
			attributeValue: 'submit_btn'
		}
		setPoints(param)
		uForm.value.validate().then((res) => {
			if (res) {
				if (form.value.pointsValue > maximum.value || form.value.pointsValue < minimum.value) {
					uni.showToast({
						title: '申报积分超出可申报范围',
						icon: 'none',
						duration: 2000
					})
					return false
				}
				if (!form.value.nodePeopleId) {
					uni.showToast({
						title: '请选择审批人',
						icon: 'none',
						duration: 2000
					})
					return false
				}
				uni.showLoading({
					title: '处理中...',
					mask: true,
				})
				let imageUrls = []
				imgList.value.forEach(item => {
					imageUrls.push(item.url)
				})
				const params = {
					peopleIds: form.value.peopleIds,
					pointStandardId: form.value.pointsStandardId,
					point: form.value.pointsValue,
					content: form.value.pointsDescribe.substring(0, 200),
					document:imageUrls.join(),
					nodePeopleId: form.value.nodePeopleId,
					standardUpdateDate: form.value.standardUpdateDate,
					role: userStore.currentRole
				}
				let api = sourceType.value == '2' ? addPartyApply : addApply
				api(params).then(res => {
					if (res.success) {
						// uni.showToast({
						//   title: '新增成功！',
						//   icon: 'none'
						// });
						uni.showLoading({
							title: '提交成功',
							mask: true,
						})
						setTimeout(() => {
							// uni.redirectTo({
							// 	url: '/jimo/pages/pointsManage/pointsReport' 
							// })
							uni.navigateBack()
						}, 1000)
					} else {
						uni.hideLoading();
						console.log("添加失败")
						uni.showToast({
							title: res.message,
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err,
						icon: 'none'
					});
				})
			}

		})
	}

	//加减状态相关
	const actions = ref([{
			name: '加分',
			value: 'add',
		},
		{
			name: '减分',
			value: "sub"
		},
	])
	// const showType = ref(false)

	// function typeSelect(e) {
	// 	form.value['pointsType'] = e.value
	// 	form.value['pointsName'] = e.name
	// }

	//人员相关
	onReady(() => {
		// getPeople()
	})
	const peopleShow = ref(false)
	const peopleList = ref([])

	function close() {
		peopleShow.value = false
	}

	function getPeople() {
		findPeople({ tenantId: tenantId }).then(res => {
			if (res.success) {
				peopleList.value = res.data
			}
		})
	}
	const choosedMembers = ref([])
	function openPeople() {
		if (sourceType.value == '1' && (!!recordsId.value || userStore.currentRole === 'villager')) {
			return
		}
		if(sourceType.value == '2' && !isDeclarant.value){
			return
		}
		uni.$once('choosePeopleCallback', (data) => {
			if (data.length > 0) {
				choosedMembers.value = data;
				let nameArr = [];
				let idArr = [];
				data.forEach(item => {
					nameArr.push(item.name + item.phone)
					idArr.push(item.peopleId)
				})
				form.value.peopleName = nameArr.join(',')
				form.value.peopleIds = idArr.join(',')
			}else{
				form.value.peopleName = ''
				form.value.peopleIds = ''
			}
			uForm.value && uForm.value.validateField('peopleName', () => {})
		})
		let choosedMembersJson = JSON.stringify(choosedMembers.value) // 已选择的
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/choosePeople?chooseType=multi&choosedMembers=${choosedMembersJson}&sourceType=${sourceType.value}`
		})
	}

	function peopleChange(e, item) {
		form.value['peopleName'] = item.peopleName
		peopleShow.value = false
	}

	function openType() {
		if (!!recordsId.value) {
			return
		}
		showType.value = true;
	}
	// 选择积分标准
	const maximum = ref('') // 申报积分最大值
	const minimum = ref('')	// 申报积分最小值
	function selectStandard() {
		uni.$once('selectStandardCallback', (data) => {
			if (!!data) {
				if (form.value['pointsStandardId'] != data.pointStandardId ) {
					form.value['pointsStandard'] = data.title
					form.value['pointsStandardId'] = data.pointStandardId
					if ( data.addSubtractedState == 'add') {
						prefixIcon.value = 'plus'
					} else if ( data.addSubtractedState == 'subtracted') {
						prefixIcon.value = 'minus'
					}
					form.value['pointsValue'] = ''
					maximum.value = data.maximum
					minimum.value = data.minimum
					form.value['standardUpdateDate'] = data.updateDate
					uForm.value && uForm.value.validateField('pointsStandard', () => {})
				}
			}
		})
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/pointsStandardList?selectedId=${form.value.pointsStandardId}&sourceType=${sourceType.value}`
		})
	}
	// 申报积分输入框前置图标 加分plus 减分minus
	const prefixIcon = ref('plus')
	
	// 申报凭证图片
	const imgList = ref([])
	const afterRead = async (event) => {
		console.log('afterRead---------', event);
		// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
		let lists = [].concat(event.file);
		let fileListLen = imgList.value.length;
		lists.map((item) => {
			imgList.value.push({
				...item,
				status: 'uploading',
				message: '上传中',
			});
		});
		for (let i = 0; i < lists.length; i++) {
			const result = await uploadFilePromise(lists[i].url);
			let item = imgList.value[fileListLen];
			imgList.value.splice(fileListLen, 1, {
				...item,
				status: 'success',
				message: '',
				url: result,
			});
			fileListLen++;
		}
	};
	// 上传图片
	const uploadFilePromise = (url) => {
		return new Promise((resolve, reject) => {
			fileUpload({
				filePath: url
			}).then((res) => {
				if (res.success) {
					resolve(res.data.url);
				}
			}).catch((err) => {
				uni.showToast({
					title: err,
					icon: 'none',
					duration: 2000
				});
			})
		});
	};
	// 删除图片
	const deletePic = (event) => {
		imgList.value.splice(event.index, 1);
	}
	// 新增时的预览图片
	const previewPic = (event) => {
		uni.previewImage({
			urls: event.url,
		})
	}
	// 查看详情时的预览图片
	const previewPics = (index) => {
		let pics = []
		imgList.value.forEach((item) => {
			pics.push(item.url)
		})
		uni.previewImage({
			urls: pics
		})
	}
	// 申报人
	const applyPeopleName = computed(() => {
         return userStore.userInfo.customParam.peopleInfo?.name || ''
    })
	// 审批人
	// const approverList = ref([])
	// 选择审批人
	function chooseApprover() {
		uni.$once('chooseApproverCallback', (data) => {
			console.log('chooseApproverCallback------', data.name);
			if (!!data) {
				form.value.approver = data.name
				form.value.nodePeopleId = data.peopleId
			}
		})
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/choosePeople?chooseType=single&sourceType=${sourceType.value}`
		})
	}
	// 查询审批人
	function getApplySendPeople() {
		let api = sourceType.value == '2' ? findPartyApplicantSendPeople : findApplicantSendPeople
		api().then(res => {
			if (res.success && res.data) {
				sendPeopleArr.value = res.data.sendPeople
			}
		}).catch(err => {
			console.log(err);
		})
	}
	// 查询是否为党员积分申报人
	async function getUserInfo(){
		try{
			let res = await isDeclarantInfo()
			if(res.success){
				isDeclarant.value = res.data
			}
		}catch(e){
			
		}
	}
</script>

<style lang="scss" scoped>
	.content-boxes {
		background: #F0F7F7;
		//width: 100vw;
		// height: calc(100vh - 40rpx);
		padding: 20rpx;

	}

	.formBox {
		margin-bottom: 20rpx;
		background: #ffffff;
		padding: 20rpx 20rpx 0rpx 20rpx;
		border-radius: 20rpx;
		// min-height: calc(100vh - 350rpx);
	}

	.bottom-box {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100vw;
		height: 126rpx;
		background: #ffffff;
		.submit-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 94%;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 40rpx;
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #FFFFFF;
		}
	}

	.peopleBox {
		padding: 40rpx;
		height: calc(100vh - 110rpx);

		.top {
			.title {}

			.calcelBtn {
				float: right;
				display: block;
				color: #909399;
			}
		}

		.radioClass {
			border-bottom: #F0F7F7 1px solid;
			padding: 20rpx;
		}

		.r-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: #F0F7F7 1px solid;
		}

		.photoImg {
			margin: 0 18rpx;
		}
	}
	.flow-container {
		margin: 20rpx 0;
		padding: 20rpx;
		box-sizing: border-box;
		background: #fff;
		border-radius: 20rpx;
		.main-title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
		}
		.flow-content-item {
			margin-top: 20rpx;
			width: 100%;
			height: 118rpx;
	
			.step-box,
			.people-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
	
			}
	
			.people-box {
				min-height: 72rpx;
	
				.left-box {
					display: flex;
					justify-content: center;
					width: 46rpx;
	
					.line {
						width: 2rpx;
						height: 72rpx;
						background: #DDDDDD;
					}
	
					.noline {
						width: 2rpx;
						height: 72rpx;
						background: transparent;
					}
				}
	
				.people {
					margin-left: 20rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #555555;
				}
	
				.send-people {
					width: 100%;
				}
			}
	
			.title-box {
				display: flex;
				align-items: center;
				width: 100%;
	
				.title {
					margin-left: 20rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #333333;
				}
			}
	
			.time {
				font-size: 26rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}
		}
	
		.send-item {
			height: auto;
		}
	}
	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	::v-deep .u-textarea.data-v-b6c174a6 {
		padding: 12rpx 0 0 0;
		
		textarea {
			color: #303133 !important;
		}
	}
</style>