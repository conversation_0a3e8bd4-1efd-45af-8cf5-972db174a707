import {
    request
} from '../request.js';

export function getDiaryList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/partyMemberDiaryNotebook/findPage',
        method: 'GET',
        params
    })
}

export function saveDiary(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/partyMemberDiaryNotebook/add',
        method: 'POST',
        params
    })
}

export function updateDiary(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/partyMemberDiaryNotebook/update',
        method: 'POST',
        params
    })
}