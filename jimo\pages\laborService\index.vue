<template>
	<view class="labor-service">
		<view class="header">
			<u-navbar bgColor="rgba(0, 0, 0, 0)" :autoBack="true" :placeholder="true"
				leftIconColor='#000' titleStyle="font-size: 36rpx;color: #000">
			</u-navbar>
			<image class="bg-image" :src="LABOR_BGIMG" mode=""></image>
			<view class="title">劳务用工</view>
			<view class="my-tabs">
				<view class="my-tab-item" v-for="item in tabsData" :key="item.value" 
					:class="{'is-active': item.value === activeName }"
					@click="activeName = item.value">
					<view class="text">{{ item.name }}</view>
					<view class="my-tabs-wrapper"></view>
				</view>
			</view>
			<view class="more" @click="moreShow = true">
				<image :src="LABOR_MORE" mode=""></image>
			</view>
		</view>
		<view class="select-view">
			<view class="work-type-select">
				<uni-data-select
					v-model="workType"
					:localdata="workTypesData"
					@change="handleWorkType">
				</uni-data-select>
			</view>
			<view class="fliter-view" @click="goRegionSelect">
				<uni-data-select
					v-model="villageSelect"
					placeholder="筛选" 
					:localdata="villageData"
					:showPopper="false"
					:clear="true"
					@change="handleVillageClear()">
				</uni-data-select>
			</view>
		</view>
		<view class="data-body" v-show="activeName === '1'">
			<u-list height="100%" @scrolltolower="scrolltolower1">
				<view class="work-item" v-for="item in dataList1" :key="item.text"
					@click="goRecruitWorkDetails(item)">
					<view class="first">
						<view>{{ item.positionName }}</view>
						<view>{{ item.salaryRange }}</view>
					</view>
					<view class="second ellipsis-2">{{ item.positionDescription }}</view>
					<view class="third">{{ item.workPlace }}</view>
					<view class="call" :style="{'background-image': `url(${LABOR_BUTTONBG})`}"
						@click.stop="handleCallPhone(item.positionId)">
						立即沟通
					</view>
				</view>
				<u-empty text="暂无招工信息" :icon="NO_MORE_IMG" v-if="!dataList1.length" />
			</u-list>
			
			<view class="add-work" @click="handleAdd()">
				<image :src="LABOR_RESUME_EDIT"></image>
			</view>
		</view>
		
		<view class="data-body" v-show="activeName === '2'">
			<u-list height="100%" @scrolltolower="scrolltolower2">
				<view class="work-item" v-for="item in dataList2" :key="item.text"
					@click="goResumeDetails(item.resumeId)">
					<view class="first">
						<view>{{ item.resumeName }}</view>
						<view>{{ item.salaryRangeLow }}-{{ item.salaryRangeHigh }}元</view>
					</view>
					<view class="second second2">
						<view>想找</view>
						<view>{{ item.countyName }} ｜ {{ item.positionName }}</view>
					</view>
					<view class="third">{{ item.introduction }}</view>
					<view class="call" :style="{'background-image': `url(${LABOR_BUTTONBG})`}"
						@click.stop="handleCallPhone2(item.resumeId)">
						立即沟通
					</view>
				</view>
				<u-empty text="暂无找工信息" :icon="NO_MORE_IMG" v-if="!dataList2.length" />
			</u-list>
		</view>
	
		<u-popup :show="moreShow" round="20rpx" mode="bottom" @close="handleClose()">
			<view>
				<view class="my-resume" @click="goMyResume()">我的简历</view>
				<u-line></u-line>
				<view class="my-recruit" @click="goMyRecruitWorks()">我的招工</view>
				<view class="close" @click="handleClose()">取消</view>
			</view>
		</u-popup>
		
		<!-- 行政区划选择 -->
		<region-select ref="refRegionSelect" @get="handleRegionSelect" :is-user-area="true" />
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad,onShow } from '@dcloudio/uni-app'
import { 
	LABOR_BGIMG, 
	LABOR_MORE, 
	LABOR_BUTTONBG,
	LABOR_RESUME_EDIT, 
	NO_MORE_IMG ,
} from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'
import RegionSelect from '../../components/regionSelect/index.vue'

/** 更多弹窗显示 */
const moreShow = ref(false)
/** 行政区划筛选 */
const refRegionSelect = ref()
/** 标签页数据 */
const tabsData = ref([
	{ name: '招工信息', value: '1' },
	{ name: '找工信息', value: '2' }
])
/** 标签页选择 */
const activeName = ref('1')
/** 工种 */
const workTypesData = ref([
	{ text: '全部', value: '' },
	{ text: '计算机', value: '1' },
	{ text: '厨师', value: '2' },
	{ text: '老师', value: '3' },
	{ text: '木工技师', value: '4' },
	{ text: '瓦工技师', value: '5' },
	{ text: '管道工技师', value: '6' },
	{ text: '绿化工技师', value: '7' },
	{ text: '林木种苗技师', value: '8' },
	{ text: '抚育间伐技师', value: '9' },
	{ text: '特岗护理技师', value: '10' },
	{ text: '闸门运行技师', value: '11' },
	{ text: '果树工技师', value: '12' },
	{ text: '其他', value: '13' },
])
/** 工种选择 */
const workType = ref('')
/** 筛选 */
const villageSelect = ref()
const villageData = ref([])
const villageAddress = ref()
/** 招工数据列表 */
const dataList1 = ref([])
const dataList1Over = ref(false)
/** 招工数据分页 */
const page1 = ref({
	pageNum: 1,
	pageSize: 20
})
/** 找工数据列表 */
const dataList2 = ref([])
const dataList2Over = ref(false)
/** 找工数据分页 */
const page2 = ref({
	pageNum: 1,
	pageSize: 20
})
const taskId = ref('') //金币任务ID
onLoad((options) =>{
	const { taskId: taskIdFromOptions = '' } = options;
	taskId.value = taskIdFromOptions

})
onShow(() => {
	handleReset()
})

const handleReset = () => {
	page1.pageNum = 1
	page2.pageNum = 1
	dataList1.value = []
	dataList2.value = []
	dataList1Over.value = false
	dataList2Over.value = false
	getDataList1()
	getDataList2()
}

/**
 * @description 弹窗关闭
 */
const handleClose = () => {
	moreShow.value = false
}
/**
 * @description 我的简历
 */
const goMyResume = () => {
	uni.navigateTo({
		url: '/jimo/pages/laborService/myResume'
	})
	handleClose()
}
/**
 * @description 我的招工
 */
const goMyRecruitWorks = () => {
	uni.navigateTo({
		url: '/jimo/pages/laborService/myRecruitWorks'
	})
	handleClose()
}
/**
 * @description 职位名称查询
 */
const handleWorkType = () => {
	handleReset()
}
/**
 * @description 获取招工分页数据
 */
const getDataList1 = () => {
	uni.showLoading({
		title: '数据加载中...'
	})
		
	const positionName = workType.value ? workTypesData.value.find(o => o.value === workType.value).text : ''
	
	const params = {
		...page1.value,
		positionName,
		workPlace: villageAddress.value
	}
	LaborService.getRecruitWorksPageList(params).then(r => {
		if (r.success) {
			dataList1Over.value = !r.data.records.length
			dataList1.value.push(...r.data.records)
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 招工-下拉加载
 */
const scrolltolower1 = () => {
	if (!dataList1Over.value) {
		page1.value.pageNum += 1
		getDataList1()
	}
}
/**
 * @description 跳转选择村庄
 */
const goRegionSelect = () => {
	refRegionSelect.value.init()
}
/**
 * @description 行政区划选择回调
 * @param {type} params
 */
const handleRegionSelect = ({ selectRegion }) => {
	const regionFullName = selectRegion[0].regionFullName
	const regionName = selectRegion[0].regionName
	villageSelect.value = regionName
	villageData.value = [{ text: regionName, value: regionName }]
	villageAddress.value = regionFullName
	
	handleReset()
}
/**
 * @description 清空回调
 */
const handleVillageClear = () => {
	if (!villageSelect.value) {
		villageAddress.value = ''
		handleReset()
	}
}
/**
 * @description 招工详情页面
 * @param {Object} item
 */
const goRecruitWorkDetails = (item) => {
	uni.navigateTo({
		url: `/jimo/pages/laborService/recruitWorkDetails?positionId=${item.positionId}&taskId=${taskId.value}`
	})
}
/**
 * @description 立即沟通
 * @@param {String} positionId
 */
const handleCallPhone = (positionId) => {
	const params = {
		positionId
	}
	LaborService.getRecruitWorkInfo(params).then(r => {
		if (r.success) {
			uni.makePhoneCall({
				phoneNumber: r.data.contactsPhone
			})
		}
	})
	
}
/**
 * @description 立即沟通
 * @@param {String} resumeId
 */
const handleCallPhone2 = (resumeId) => {
	const params = {
		resumeId
	}
	LaborService.getResumeInfo(params).then(r => {
		if (r.success) {
			uni.makePhoneCall({
				phoneNumber: r.data.telephone
			})
		}
	})
	
}
/**
 * @description 新增我的招工
 */
const handleAdd = () => {
	uni.navigateTo({
		url: `/jimo/pages/laborService/recruitWorkAddEdit`,
	})
}

/**
 * @description 获取找工分页列表
 */
const getDataList2 = () => {
	uni.showLoading({
		title: '数据加载中...'
	})
		
	const positionName = workType.value ? workTypesData.value.find(o => o.value === workType.value).text : ''
	
	const params = {
		...page2.value,
		positionName,
		workPlace: villageAddress.value
	}
	LaborService.getResumePageList(params).then(r => {
		if (r.success) {
			dataList2Over.value = !r.data.records.length
			dataList2.value.push(...r.data.records)
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 找工-下拉加载
 */
const scrolltolower2 = () => {
	if (!dataList2Over.value) {
		page2.value.pageNum += 1
		getDataList2()
	}
}
/**
 * @description 去找工详情页面
 * @param {type} id
 */
const goResumeDetails = (id) => {
	uni.navigateTo({
		url: `/jimo/pages/laborService/resumeDetails?resumeId=${id}&taskId=${taskId.value}`
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}
.labor-service {
	width: 100%;
	height: 100%;
	position: fixed;
	background: #F0F7F7;
	
	.header {
		width: 100%;
		height: 364rpx;
		position: fixed;
		top: 0;
		
		.bg-image {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			z-index: -1;
		}
		
		.title {
			position: absolute;
			top: 194rpx;
			left: 20rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 42rpx;
			color: #000000;
		}
		
		.my-tabs {
			width: 100%;
			position: absolute;
			bottom: 0;
			left: 0;
			z-index: 1;
			
			.my-tab-item {
				float: left;
				width: 205rpx;
				height: 84rpx;
				line-height: 80rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				font-size: 36rpx;
				color: #333333;
				position: relative;
				text-align: center;
				
				.my-tabs-wrapper {
					width: 43rpx;
					height: 6rpx;
					background: #0BBD88;
					border-radius: 3rpx;
					position: absolute;
					bottom: 0;
					left: calc(50% - 21rpx);
					display: none;
				}
				
				&.is-active {
					color: #0BBD88;
					
					.my-tabs-wrapper {
						display: block;
					}
				}
			}
		}
	
		.more {
			position: absolute;
			right: 20rpx;
			bottom: 24rpx;
			z-index: 2;
			
			image {
				width: 33rpx;
				height: 33rpx;
			}
		}
	}

	.select-view {
		width: 100%;
		height: 92rpx;
		position: fixed;
		top: 364rpx;
		display: flex;
		align-items: center;
		z-index: 10;
		padding: 0 20rpx;
		
		.work-type-select, .fliter-view {
			min-width: 220rpx;
			margin-right: 12rpx;
		}
	}
	
	::v-deep.data-body {
		width: 100%;
		height: calc(100% - 364rpx - 92rpx);
		position: fixed;
		bottom: 0;
		left: 0;
		padding: 0 20rpx 44rpx 20rpx;
		
		.u-empty {
			height: 600rpx;
			
			image {
				width: 160px !important;
				height: 88px !important;
			}
		}
		
		.work-item {
			width: 100%;
			padding: 22rpx 19rpx 28rpx 27rpx;
			margin-bottom: 20rpx;
			background: #FFFFFF;
			border-radius: 20rpx;
			position: relative;
			
			.first {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				font-size: 32rpx;
				line-height: 45rpx;
				display: flex;
				justify-content: space-between;
				
				view:nth-child(1) {
					color: #000000;
				}
				view:nth-child(2) {
					color: #0BBD88;
				}
			}
			
			.second {
				width: 530rpx;
				margin-top: 14rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				line-height: 42rpx;
			}
			
			.second2 {
				display: flex;
				
				view:nth-child(1) {
					width: 77rpx;
					height: 40rpx;
					border-radius: 3rpx;
					border: 1rpx solid #9C9C9C;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 15rpx;
				}
			}
			
			.third {
				width: 344rpx;
				margin-top: 13rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 33rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			
			.call {
				width: 132rpx;
				height: 48rpx;
				position: absolute;
				right: 19rpx;
				bottom: 23rpx;
				background-repeat: no-repeat;
				background-size: 100% 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-size: 24rpx;
				color: #FFFFFF;
				text-align: center;
				line-height: 48rpx;
			}
		}
	
		.add-work {
			position: fixed;
			bottom: 262rpx;
			right: 20rpx;
			width: 80rpx;
			height: 80rpx;
			
			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.my-resume {
		padding: 41rpx 0 20rpx 0;
		text-align: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
		line-height: 45rpx;
	}
	.my-recruit {
		padding: 24rpx 0 27rpx 0;
		text-align: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
		line-height: 45rpx;
	}
	
	.close {
		height: 99rpx;
		border-top: 10rpx solid #F1F1F1;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 28rpx;
		color: #000000;
		line-height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}

.ellipsis-2 {
	-webkit-line-clamp: 2;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>