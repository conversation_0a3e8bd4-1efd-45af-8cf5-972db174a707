<template>
	<view>
		<web-view :src="url" width="100%" height="100%" frameborder=0></web-view>
	</view>
</template>

<script>
import gcoord from 'gcoord'
	export default {
		data() {
			return {
				url:"",
				txKey:"Q5JBZ-BSDW3-PTL3B-RDAQA-OJO7S-GZFJY"
			}
		},
		onLoad(options) {
            
            if (navigator.geolocation){
            console.log(options)
			// var result = gcoord.transform(
			//   [options.lng?options.lng:120.367355, options.lat?options.lat:36.538773],    // 经纬度坐标
			//   gcoord.WGS84,               // 当前坐标系
			//   gcoord.GCJ02                 // 目标坐标系
			// );
           // console.log(result)
			this.url = "https://apis.map.qq.com/tools/locpicker?coord="+options.lat+","+options.lng+"&search=1&type=1&key="+this.txKey+"&referer=jimo"
			window.addEventListener('message', event=> {
			  // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
			    var loc = event.data;
				if (loc && loc.module == 'locationPicker') {
			        //防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
			        console.log('location', loc);
					// uni.redirectTo({
					// 	url:"../editAddress/editAddress?loc="+JSON.stringify(loc)+"&query="+options.query+"&isedit="+options.isedit+"&id="+options.id
					// })
					uni.$emit('selectlocation', loc)
					uni.navigateBack()
				}
			}, false);
        }
        else{
        }
		},
		onHide() {
		},
		methods: {
		}
	}
	
</script>

<style>

</style>


