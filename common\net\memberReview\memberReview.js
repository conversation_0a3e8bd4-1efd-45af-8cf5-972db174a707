import {
	request
} from '../request.js'
import {
	API_MEMBERREVIEW_URL,
	API_MEMBERREVIEWDETAIL_URL,
	API_MEMBERREVIEWPASS_URL
} from '@/common/net/netUrl.js'

// 获取成员审核列表
export function getReviewMemberList(params) {
	params['uniContentType'] = 'json'
    return request({
        url: API_MEMBERREVIEW_URL,
        method: 'POST',
		params
    })
}

// 获取成员审核详情
export function getReviewMemberInfo(params) {
	params['uniContentType'] = 'json'
    return request({
        url: API_MEMBERREVIEWDETAIL_URL,
        method: 'GET',
		params
    })
}

// 同意申请
export function passApply(params) {
	params['uniContentType'] = 'json'
    return request({
        url: API_MEMBERREVIEWPASS_URL,
        method: 'POST',
		params
    })
}