<template>
  <view class="notification-list" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="短信通知" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
       <logoutbtn></logoutbtn>

    <view class="container">
      <view class="searchCont">
        <u-input v-model="keyword" placeholder="请输入短信标题" border="none" height="68rpx" />
        <view class="date-select">
          <u-input v-model="dateText" placeholder="2025-07-11" readonly border="none" >
            <template #suffix>
              <u-icon name="calendar" @click="showDatePicker = true" style="margin-right: 10rpx;"></u-icon>
            </template>
          </u-input>
        </view>
      </view>
      <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="getList">

<view class="empty-status" v-if="loaded && dataList.length === 0">
  <view class="empty-icon">
    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
    <view class="no-more">
      <span class="txt">暂无数据</span>
    </view>
  </view>
</view>
 <view class="assetList">
          <view class="list" v-for="item in dataList" :key="item.id" @click="goDetail">
            <view class="row row-title">
              <text class="label">短信标题：</text>
              <text class="value">{{ item.title }}</text>
            </view>
            <view class="row">
              <text class="label">短信内容：</text>
              <text class="value">{{ item.content }}</text>
            </view>
            <view class="row">
              <text class="label">发送时间：</text>
              <text class="value">{{ item.time }}</text>
            </view>
            <view class="row">
              <text class="label">接收企业：</text>
              <text class="value">{{ item.company }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <u-datetime-picker
      closeOnClickOverlay
      @close="showDatePicker = false"
      v-model="dateValue"
      :show="showDatePicker"
      mode="date"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
    />
    <view class="alarm-confirm">
      <view class="btn2" @click="handleAddSms">新增短信</view>
    </view>
  </view>
</template>

<script setup>
import { NO_MORE_IMG, LIVE_INVENTORY_BG } from '@/common/net/staticUrl.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

import { ref, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getNotificationList } from "../../api/anquanjianguan/companyList.js"
const keyword = ref('')
const dateText = ref('2025-07-11')
const dateValue = ref('2025-07-11')
const showDatePicker = ref(false)
const dataList = ref([])
const loadStatus = ref('more')
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(6)
const loaded = ref(false)
watch(() => keyword.value,(newVal)=>{
  search()
})
function goDetail() {
  uni.navigateTo({
    url: './notificationDetail'
  })
}
function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }
  getNotificationList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    title: keyword.value,
    date: dateText.value
  }).then(res => {
    if (res.success) {
      total.value = res.data.total
      if (pageNum.value > 1) {
        dataList.value = [...dataList.value, ...res.data.records]
      } else {
        dataList.value = res.data.records
      }
      if (total.value === dataList.value.length) {
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'more'
        pageNum.value = pageNum.value + 1
      }
    } else {
      uni.showToast({
        title: res.message || '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    }
    loaded.value = true
  }).catch(() => {
    uni.showToast({
      title: '查询数据失败',
      icon: 'none',
    })
    loadStatus.value = 'more'
  })
}

function search() {
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}



function onDateConfirm(e) {
  dateText.value = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showDatePicker.value = false
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}
function handleAddSms() {
  uni.navigateTo({
    url: './addNotification'
  })
  // TODO: 跳转到新增短信页面
}
onLoad(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.notification-list {
  width: 100%;
  min-height: 100vh;
  background: #fff;
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC-Regular, PingFang-SC;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.container {
  height: calc(100vh - 90rpx);
  .searchCont {
    padding: 0 20rpx;
    height: 68rpx;
    border-radius: 34rpx;
    display: flex;
    align-items: center;
    margin: 22rpx 22rpx 0 22rpx;
    box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
    background: #fff;
    box-sizing: border-box;
    .u-input {
      flex: 2;
      margin-right: 16rpx!important;
    }
    .date-select{
      flex: 1;
    }
  }
  .scroll-style {
			/* background-color: #fff; */
			height: calc(100% - 200rpx - env(safe-area-inset-bottom));
  
  .assetList {
    margin: 30rpx 16rpx 0 16rpx;
    .list {
      background: #fff;
      border-radius: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
      margin-bottom: 24rpx;
      padding: 24rpx 20rpx;
      position: relative;
      overflow: hidden;
      .row-title {
        height: 71rpx;
        background: linear-gradient(270deg, #FFFFFF 0%, #E9FFF6 100%);
        display: flex;
        align-items: center;
        border-radius: 20rpx 20rpx 0 0;
        padding-left: 11rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 0;
        position: relative;
        .label {
          font-size: 28rpx;
          color: #222;
          font-weight: 600;
        }
        .value {
          font-size: 28rpx;
          color: #0CBE88;
          font-weight: 700;
          margin-left: 12rpx;
        }
      }
      .row {
        display: flex;
        align-items: center;
        margin-bottom: 18rpx;
        .label {
          font-size: 28rpx;
          color: #999;
          font-weight: 400;
        }
        .value {
          font-size: 30rpx;
          color: #222;
          margin-left: 12rpx;
          font-weight: 500;
        }
      }
    }
  }
}
}
.alarm-confirm {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: calc(123rpx + env(safe-area-inset-bottom));
  padding: 22rpx 23rpx calc(21rpx + env(safe-area-inset-bottom)) 33rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  z-index: 10;
  box-sizing: border-box;
  .btn2 {
    width: calc(100% - 80rpx);
    height: 80rpx;
    margin-left: 35rpx;
    margin-right: 45rpx;
    background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
    border-radius: 48rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 36rpx;
    color: #FFFFFF;
  }
}
</style> 