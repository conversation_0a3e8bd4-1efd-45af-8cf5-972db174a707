<template>
  <view class="remind-list" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="待处理提醒" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
      <logoutbtn></logoutbtn>

    <view class="container">
      <u-tabs :list="tabsList" lineWidth="78rpx" :itemStyle="{ width: '28%', height: '86rpx' }" @click="handleTabClick" :current="tabActive" />
      <view class="searchCont">
        <u-search v-model="keyword" placeholder="请输入企业名称" :showAction="false" :clearabled="false" bgColor="#FFFFFF" height="78rpx" @search="search" />
        <view class="status-select" v-if="tabActive!=2">
          <u-input v-model="statusText" placeholder="待处理" readonly border="none">
            <template #suffix>
           
                <u-icon name="arrow-down" @click="showStatusPicker = true" style="margin-right: 10rpx;"></u-icon>
         
              </template>
          </u-input>
        </view>
      </view>
      <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="getList">

        <view class="empty-status" v-if="loaded && dataList.length === 0">
          <view class="empty-icon">
            <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
            <view class="no-more">
              <span class="txt">暂无数据</span>
            </view>
          </view>
        </view>
        <view class="assetList">
          <template v-if="tabActive==0">
          <view class="list" v-for="item in dataList" :key="item.recordId" @click="gotoDetail(item)">
            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn" :class="parseStatus(item.inspectStatus)">{{ parseStatusText(item.inspectStatus)}}</view>
            </view>
            <view class="row">
              <text class="label">企业性质：</text>
              <text class="value">{{ item.enterpriseNature }}</text>
            </view>
            <view class="row" v-if="item.inspectStatus!='PROCESSED'">
              <text class="label">最新巡检时间：</text>
              <text class="value">{{ item.lastInspectTime }}</text>
            </view>
             <view class="row" v-if="item.inspectStatus=='PROCESSED'">
              <text class="label">巡检时间：</text>
              <text class="value">{{ item.inspectTime }}</text>
            </view>
            <!-- <view class="row" >
              <text class="label">隐患时间：</text>
              <text class="value">{{ item.inspectTime  }}</text>
            </view>
            <view class="row" >
              <text class="label">整改期限：</text>
              <text class="value">{{ item.rectifyDeadline }}</text>
            </view> -->
          </view>
          </template>   
            <template v-if="tabActive==1">
          <view class="list" v-for="item in dataList" :key="item.reviewId" @click="gotoDetail(item)">
            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn" :class="parseStatus(item.reviewStatus)">{{ parseStatusText(item.reviewStatus)}}</view>
            </view>
            <view class="row">
              <text class="label">企业性质：</text>
              <text class="value">{{ item.enterpriseNature }}</text>
            </view>
            <view class="row" v-if="item.reviewStatus=='PENDING'">
              <text class="label">隐患时间：</text>
              <text class="value">{{ item.inspectTime }}</text>
            </view>
            <view class="row" v-if="item.reviewStatus!='PENDING'">
              <text class="label">复核时间：</text>
              <text class="value">{{ item.reviewTime  }}</text>
            </view>
            <view class="row" >
              <text class="label">整改期限：</text>
              <text class="value">{{ item.rectifyDeadline }}</text>
            </view>
          </view>
          </template>   
            <template v-if="tabActive==2">
          <view class="list" v-for="(item,index) in dataList" :key="index" @click="gotoOverdueDetail(item)">
            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn overdue">
                {{ item.status }}
              </view>
            </view>
            <view class="row">
              <text class="label">证书名称：</text>
              <text class="value">{{ item.credentialName }}</text>
            </view>
          
            <view class="row" >
              <text class="label">证书有效期：</text>
              <text class="value">{{ item.startDate }}-{{ item.endDate }}</text>
            </view>
          </view>
          </template>   
        </view>
        <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && dataList.length === 0)"
      ></u-loadmore>
      </scroll-view>
    </view>
    <u-picker closeOnClickOverlay @close="showStatusPicker = false" :show="showStatusPicker" keyName="label" :columns="[tabActive==0?inspectStatusList:reviewStatusList]" @confirm="onStatusConfirm" @cancel="showStatusPicker = false" />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { LIVE_INVENTORY_BG,NO_MORE_IMG } from '@/common/net/staticUrl.js';
import { AJYService } from "../../api/anquanjianguan/companyList.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'
const tabsList = ref([
  { name: '巡检打卡',badge:{value:''} },
  { name: '整改复核',badge:{value:''} },
  {name:'证书逾期',badge:{value:''}}
])
const tabActive = ref(0)
const keyword = ref('')
const statusText = ref('待处理')
const showStatusPicker = ref(false)
// 状态列表，加入已超期
const inspectStatusList = ref([

  { label: '待处理', value: 'PENDING' },
  { label: '已处理', value: 'PROCESSED' },
])
const reviewStatusList = ref([
  
  { label: '待整改', value: 'PENDING' },
  { label: '已完成', value: 'COMPLETED' },
  { label: '已移交执法', value: 'TRANSFERRED' },
  { label: '已延期', value: 'DELAYED' }
])
const dataList = ref([])
const loadStatus = ref('more')
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(6)
const loaded = ref(false)
const statusValue = ref('pending')
function initnum(){
  AJYService.inspectwillRecordList({pageNum:1,pageSize:1}).then(res => {
    if (res.success) {
      tabsList.value[0].badge.value = res.data.total
    }
  })
  AJYService.inspectReviewList({pageNum:1,pageSize:1,reviewStatus:'PENDING'}).then(res => {
    if (res.success) {
      tabsList.value[1].badge.value = res.data.total
    }
  })
  AJYService.findExpiringCredentials({pageNum:1,pageSize:1}).then(res => {
    if (res.success) {
      tabsList.value[2].badge.value = res.data.total
    }
  })
}
function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }
  let api = ''
  let params = {}
  if(tabActive.value==0){
    if(statusValue.value == 'PROCESSED'){
      api = 'inspectedRecordList'
    }
    else{
      api = 'inspectwillRecordList'
    }
    params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      enterpriseName: keyword.value,
      // inspectStatus: statusValue.value,
    }
  }
  else if(tabActive.value==1){
   api = 'inspectReviewList'
   params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      enterpriseName: keyword.value,
      reviewStatus: statusValue.value,
    }
  }
  else{
     api = 'findExpiringCredentials'
   params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      enterpriseName: keyword.value,
    }
  }
  AJYService[api](params).then(res => {
    if (res.success) {
     
      total.value = res.data.total
      if (pageNum.value > 1) {
        dataList.value = [...dataList.value, ...res.data.records]
      } else {
        dataList.value = res.data.records
      }
      if (total.value === dataList.value.length) {
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'more'
        pageNum.value = pageNum.value + 1
      }
    } else {
      uni.showToast({
        title: res.message || '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    }
    loaded.value = true
  }).catch(() => {
    uni.showToast({
      title: '查询数据失败',
      icon: 'none',
    })
    loadStatus.value = 'more'
  })
}

function search() {
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}
function parseStatus(statusvalue){
 return statusvalue.toLowerCase();
}
function parseStatusText(statusvalue){
  if(tabActive.value==0){
    return inspectStatusList.value.find(item=>item.value==statusvalue).label
  }
  else{
    return reviewStatusList.value.find(item=>item.value==statusvalue).label
  }
}

function handleTabClick(item) {
  if(tabActive.value==item.index) return;

  tabActive.value = item.index
  if(tabActive.value==0){
    statusText.value = inspectStatusList.value[0].label
    statusValue.value = inspectStatusList.value[0].value
  }
  else{
    statusText.value = reviewStatusList.value[0].label
    statusValue.value = reviewStatusList.value[0].value
  }
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}
function onStatusConfirm(e) {
  statusText.value = e.value[0].label
  statusValue.value = e.value[0].value
  showStatusPicker.value = false
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}
function gotoDetail(item) {
  if (tabActive.value === 0) {
    if(item.inspectStatus=='PENDING'){
    uni.navigateTo({ url: `./companyCheck?id=${item.enterpriseId}` })
    }
    else{
    uni.navigateTo({ url: `../adminstrator/companyCheckDetail?id=${item.recordId}` })
    
    }
  } else {
     if(item.reviewStatus=='PENDING'){
    uni.navigateTo({ url: `./companyChange?id=${item.reviewId}` })
     }
     else{
          uni.navigateTo({ url: `../adminstrator/companyChangeDetail?id=${item.reviewId}` })

     }
  }
}
function gotoOverdueDetail(item){

    const detailData = {
      enterpriseName: item.enterpriseName || '',
      enterpriseNature: item.enterpriseNature || '',
      enterpriseScale: item.enterpriseScale || '',
      industryCategory: item.industryCategory || '',
      communityName: item.communityName || '',
      supervisorName: item.supervisorName || '',
      credentialName: item.credentialName || '',
      credentialImage: item.credentialImage || '',
      startDate: item.startDate || '',
      endDate: item.endDate || '',
      overdueDays: item.overdueDays || 0,
      status: item.status || ''
    }
    uni.navigateTo({
      url: '/jimo/anquanjianguan/adminstrator/certificateDetail?detail=' + encodeURIComponent(JSON.stringify(detailData))
    })
 
}
onShow(() => {
  search()
  initnum()
})
</script>

<style lang="scss" scoped>
.remind-list {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 1135rpx;
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC-Regular, PingFang-SC;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.container {
  ::v-deep.u-tabs {
			padding: 0 25rpx;
			
			.u-tabs__wrapper__nav__line {
				// width: 78rpx !important;
				height: 8rpx !important;
				background: linear-gradient( 117deg, #0CBE88 0%, #3ADB97 100%) !important;
				border-radius: 5rpx !important;
			}
		}
  .searchCont {
    height: 78rpx;
    border-radius: 39rpx;
    display: flex;
    align-items: center;
    margin: 22rpx 22rpx 0 22rpx;
    box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
    background: #fff;
   
    .u-search {
      flex: 2;
      margin-right: 16rpx!important;
    }
    .status-select{
        flex: 1;
       ::v-deep .uni-input-input{
        text-align: right;
       }
    }
  }
  .filterSection {
    display: flex;
    align-items: center;
    margin: 20rpx 22rpx 0 22rpx;
    .select {
      flex: 1;
      min-width: 178rpx;
      height: 68rpx;
      background: #FFFFFF;
      border-radius: 34rpx;
      margin-right: 8rpx;
      box-shadow: 0 2rpx 8rpx rgba(12,190,136,0.08);
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      .u--input {
        width: 100%;
        font-size: 28rpx;
        color: #333;
      }
      .u-icon {
        margin-left: 8rpx;
        color: #999;
      }
    }
  }
  .scroll-style {
			/* background-color: #fff; */
			height: calc(100vh - 280rpx - env(safe-area-inset-bottom));
  .assetList {
    margin: 30rpx 16rpx 0 16rpx;
    .list {
      background: #fff;
      border-radius: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
      margin-bottom: 24rpx;
      padding: 24rpx 20rpx;
      position: relative;
      overflow: hidden;
      .row-title {
        height: 71rpx;
        background: linear-gradient(270deg, #FFFFFF 0%, #E9FFF6 100%);
        display: flex;
        align-items: center;
        border-radius: 20rpx 20rpx 0 0;
        padding-left: 11rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 0;
        position: relative;
        .label {
          font-size: 28rpx;
          color: #222;
          font-weight: 600;
        }
        .value {
          font-size: 28rpx;
          color: #0CBE88;
          font-weight: 700;
          margin-left: 12rpx;
        }
      }
      .row {
        display: flex;
        align-items: center;
        margin-bottom: 18rpx;
        .label {
          font-size: 28rpx;
          color: #999;
          font-weight: 400;
        }
        .value {
          font-size: 30rpx;
          color: #222;
          margin-left: 12rpx;
          font-weight: 500;
        }
      }
    }
  }
}
}
.status-btn {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 26rpx;
  border-radius: 0 20rpx 0 32rpx;
  padding: 0 24rpx 0 29rpx;
  height: 48rpx;
  line-height: 48rpx;
  z-index: 2;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  background: #909399;
  color: #fff;
}
   .status-btn.overdue{
            background: linear-gradient(135deg, #FF4757 0%, #FF3838 100%);
            box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
          }

// 添加所有状态的样式
  .status-btn.pending {
    background: #fff7e6;
    color: #ff9500;
  }
  .status-btn.processed {
    background: #e6f7e6;
    color: #00b050;
  }
  .status-btn.completed {
    background: #e6f7e6;
    color: #00b050;
  }
  .status-btn.transferred {
    background: #e6f2ff;
    color: #1976FF;
  }
  .status-btn.delayed {
    background: #fff7e6;
    color: #ff9500;
  }
  .status-btn.rejected {
    background: #fff1f0;
    color: #ff4d4f;
  }
</style> 