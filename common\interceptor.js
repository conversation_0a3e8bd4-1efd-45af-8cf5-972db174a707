import kvStore from '@/common/store/uniKVStore.js'
// 页面白名单，不受拦截
const whiteList = [
	'/pages/index/index',
	'/pages/login/login',
	'/pages/generalPage/forgetPwd/forgetPwd',
	'/pages/generalPage/userAgreement/userAgreement',
	'/pages/generalPage/privacyPolicy/privacyPolicy',
	'/pages/home/<USER>',
	'/pages/homeland/homeland',
	'/pages/work/work',
	'/pages/listPage/detail',
	'/pages/listPage/list',
	'/pages/listPage/appList',
	'/pages/UNI/webview/webview'
]
// 判断目标页面是否允许访问
function hasPermission(url) {
	url = url.split("?")[0]	//去除url中的参数
	let hasLogin = kvStore.get('hasLogin', true); //hasLogin是登录成功后在本地存储的登录标识
	hasLogin = Boolean(Number(hasLogin)); //返回布尔值
	// 在白名单中或有登录判断条件可以直接跳转
	if (whiteList.indexOf(url) !== -1 || hasLogin) {
		return true
	}
	return false
}
uni.addInterceptor('navigateTo', {
	// 页面跳转前进行拦截, invoke根据返回值进行判断是否继续执行跳转
	invoke(e) {
		if (!hasPermission(e.url)) {
			backToLogin();
			return false
		}
		return true
	},
	success(e) {}
})

uni.addInterceptor('switchTab', {
	// tabbar页面跳转前进行拦截
	invoke(e) {
		if (!hasPermission(e.url)) {
			backToLogin();
			return false
		}
		return true
	},
	success(e) {}
})

uni.addInterceptor('reLaunch', {
	// 页面跳转前进行拦截
	invoke(e) {
		if (!hasPermission(e.url)) {
			backToLogin();
			return false
		}
		return true
	},
	success(e) {}
})

// 跳转登录页
function backToLogin() {
  uni.reLaunch({
  	url: '/pages/index/index'
  })
}