<template>
    <!-- 通用的列表组件 -->
    <template v-if="!loading">
        <view class="article-list" v-if="contentList.length > 0">
            <view class="article-item" v-for="(item, index) in contentList" :key="index" @click="gotoDetail(item)">
                <!-- 左图右标题形式 -->
                <view class="pic-list" v-if="listType == 'picList'">
                    <view class="article-info">
                        <u-image v-if="item?.coverImg" width="116" height="75" radius="3" :src="item.coverImg"
                            mode="aspectFill"></u-image>
                        <u-image v-else :src="COTENT_EMPTY_IMG" width="116" height="75" mode="aspectFill"></u-image>
                        <view class="pic-content">
                            <view class="content">
                                <text class="content-text">
                                    {{ item.contentTitle }}
                                </text>
                            </view>
                            <view class="time">{{ timeChange(item.createDate) }}</view>
                        </view>
                    </view>
                </view>
                <template v-else>
                    <!-- 评论列表 -->
                    <view class="comment-list" v-if="listType == 'commentList'">
                        <view class="article-info">
                            <u-avatar :src="item?.headPhoto" size="96rpx" :default-url="COMMENT_HEAD_IMG"></u-avatar>
                            <!-- <u-image :src="COMMENT_HEAD_IMG" shape="circle" mode="aspectFit" width="96rpx"
                                height="96rpx"></u-image> -->
                            <view class="author-info">
                                <view class="article-author">{{ item.createByName }}</view>
                                <view class="article-time">{{timeChange(item.createDate) }}</view>
                            </view>
                        </view>
                        <view class="article-content">
                            <text class="content-text">
                                {{ item.contentTitle }}
                            </text>
                        </view>
                        <view class="article-option">
                            <view class="art-icons-list">
                                <view class="option view-info">
                                    <u-icon name="eye" size="32rpx"></u-icon>
                                    <span class="comment"> {{ item.readCount }}</span>
                                </view>
                                <view class="option like-list" :class="{activeList:item.contentType=='url' }"
                                    @click.stop="handleLike(index, item)">
                                    <u-icon name="thumb-up-fill" v-if="item.upvoteFlag == 1" color="#f00"
                                        size="32rpx"></u-icon>
                                    <u-icon name="thumb-up" v-else size="32rpx"></u-icon>
                                    <span class="like">{{item.upvoteCount ? item.upvoteCount : '点赞'}}</span>
                                </view>
                                <view class="option icon-list" v-if="item.contentType == 'text'">
                                    <u-icon name="chat" size="32rpx"></u-icon>
                                    <span class="comment">
                                        {{
                                          item.commentCount
                                            ? item.commentCount > 999
                                              ? '999+'
                                              : item.commentCount
                                            : '评论'
                                        }}
                                    </span>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 普通列表 -->
                    <view class="custom-list" v-else>
                        <view class="article-content">
                            <text class="content-text">
                                {{ item.contentTitle }}
                            </text>
                        </view>
                        <view class="article-item-option">
                            <span class="item-view">
                                <u-icon name="eye" size="32rpx"></u-icon>
                                <span class="txt"> {{ item.readCount }}</span>
                            </span>
                            <view class="item-time">{{ timeChange(item.createDate) }} </view>
                        </view>
                    </view>
                </template>
            </view>
        </view>
        <view class="empty-status" :class="{ 'empty-status1': height200 }" v-else>
            <view class="empty-icon">
                <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
                <view class="no-more">
                    <span class="txt">暂无数据 </span>
                </view>
            </view>
        </view>
    </template>
    <!-- <u-loadmore :status="status" /> -->
    <view class="nomore" v-if="status && contentList.length > 0">没有更多数据啦</view>

    <!-- 悬浮按钮 -->
    <view v-if="
      columnInfo &&
      ['zuzhishenghuo', 'dangyuanzhijia'].includes(columnInfo.columnKey)
    " @click="gotoWrite">
        <floating-button></floating-button>
    </view>
</template>
<script setup>
    import {
        COTENT_EMPTY_IMG,
        COMMENT_HEAD_IMG,
        NO_MORE_IMG,
    } from '@/common/net/staticUrl.js'
    import {
        ref,
        reactive,
        onMounted,
        onUnmounted,
        watchEffect,
        watch,
        defineExpose,
    } from 'vue'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        useMainStore
    } from '@/store/index.js'
    import {
        onShow
    } from '@dcloudio/uni-app'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        getContentList,
        getContentListAnonymous,
        updateContentLike,
        addReadNum,
        addReadNumAnonymous,
    } from '@/common/net/listPage/listPage.js'
    import {
        timeChange
    } from '@/common/date/dateTimeFormat.js'
    const userStore = useUserStore()
    const tokenStore = useTokenStore()
    const mainStore = useMainStore()
    const tenantId = tokenStore.tenantId
    import {
        onPullDownRefresh,
        onReachBottom
    } from '@dcloudio/uni-app'
    import {
        getColumnInfo,
        getColumnInfoAnonymous,
    } from '@/common/net/listPage/listPage.js'
    // 接收父组件传入的参数
    const props = defineProps({
        // 是普通列表:list，还是带评论的列表:commentList,还是左边图片的列表 picList
        listType: {
            type: String,
            default: 'list',
        },
        // 栏目id
        columnId: {
            type: String,
            required: true,
            default: '',
        },
        //是否分页
        isShowPage: {
            type: Boolean,
            default: true,
        },
        pageSize: {
            type: Number,
            default: 10,
        },
        from: {
            type: String,
            default: '',
        },
    })
    let columnInfo = ref(null)
    // 接收父组件传入的参数
    let contentList = ref([])
    let currentPage = ref(1)
    let totalPage = ref(0)
    let loadStatus = ref(false)
    let height200 = ref(false)
    let loading = ref(false)
    let status = ref(false)
    onShow(() => {
        height200.value = props.from == 'appList'
        contentList.value = []
        currentPage.value = 1
        getList()
    })

    watch(
        () => props.columnId,
        (newVal, oldVal) => {
            if (newVal) {
                status.value = false
                height200.value = props.from == 'appList'
                getColumnDetail()
                contentList.value = []
                currentPage.value = 1
                getList()
            }
        }, {
            immediate: true,
        }
    )
    //下拉刷新
    onPullDownRefresh(() => {
        // 触发下拉刷新时执行
        contentList.value = []
        currentPage.value = 1
        loadStatus.value = true
        getList()
    })
    //下拉监听方法
    onReachBottom(() => {
        console.log('------------------onReachBottom')
        if (props.isShowPage) {
            if (loadStatus.value) {
                status.value = false
                currentPage.value += 1
                getList()
            } else {
                status.value = true
            }
        }
    })
    // 获取列表
    function getList() {
        if (currentPage.value == 1) {
            //显示加载中动画
            // uni.showLoading({
            //     title: '加载中...',
            // });
            loading.value = true
        }
        if (mainStore.hasLogin) {
            getContentList({
                    columnId: props.columnId,
                    tenantId: tenantId,
                    pageSize: props.pageSize,
                    pageNum: currentPage.value,
                })
                .then((res) => {
                    //成功获取数据后隐藏加载动画
                    handleData(res)
                })
                .finally(() => {
                    if (currentPage.value == 1) {
                        uni.hideLoading()
                        loading.value = false
                    }
                })
        } else {
            getContentListAnonymous({
                    columnId: props.columnId,
                    tenantId: tenantId,
                    pageSize: props.pageSize,
                    pageNum: currentPage.value,
                })
                .then((res) => {
                    //成功获取数据后隐藏加载动画
                    handleData(res)
                })
                .finally(() => {
                    if (currentPage.value == 1) {
                        uni.hideLoading()
                        loading.value = false
                    }
                })
        }
    }

    function handleData(res) {
        const {
            success,
            data
        } = res
        if (success) {
            if (data.records.length > 0) {
                //如果页数>1，需要拼接返回的数据
                if (currentPage.value > 1) {
                    contentList.value = [...contentList.value, ...data.records]
                } else {
                    contentList.value = data.records
                }
                const flag = data.records.length >= props.pageSize ? true : false
                loadStatus.value = flag
                status.value = !flag
            } else {
                status.value = true
                // contentList.value = [...contentList.value]
                loadStatus.value = false
            }

        } else {
            status.value = true
            loadStatus.value = false
            // contentList.value = [...contentList.value]
        }
    }
    // scroll-view 下拉加载更多
    function scrollToLower() {
        console.log('-----------list.vue-scrollToLower')
        if (props.isShowPage) {
            if (loadStatus.value) {
                status.value = false
                currentPage.value += 1
                getList()
            } else {
                status.value = true
            }
        }
    }

    function reloadList() {
        // contentList.value = []
        // currentPage.value = 1
        // getList()
        this.scrollToLower()
    }

    function getColumnDetail() {
        if (mainStore.hasLogin) {
            getColumnInfo({
                columnId: props.columnId,
            }).then((res) => {
                console.log(res.data)
                columnInfo.value = res.data
            })
        } else {
            getColumnInfoAnonymous({
                columnId: props.columnId,
            }).then((res) => {
                console.log(res.data)
                columnInfo.value = res.data
            })
        }
    }
    // 党员之家、组织生活
    function gotoWrite() {
        uni.navigateTo({
            url: '/pages/generalPage/columnContent/publishColumnContent?columnId=' +
                props.columnId,
        })
    }

    function gotoDetail(item) {
        console.log(item)
        if (item && item.contentType == 'url') {
            addReadData(item)
            let url = encodeURIComponent(item.contentUrl)
            uni.navigateTo({
                url: '/pages/UNI/webview/webview?url=' + url,
                fail() {
                    uni.showToast({
                        title: '打开失败',
                        icon: 'none',
                        duration: 2000,
                    })
                },
            })
        } else if (item && item.contentType == 'text') {
            addReadData(item)
            uni.navigateTo({
                url: '/pages/listPage/detail?contentId=' +
                    item.contentId +
                    '&listType=' +
                    props.listType,
            })
        }
    }

    // 新增一条阅读内容
    async function addReadData(item) {
        console.log('-------addReadData')
        console.log(mainStore.hasLogin)
        if (mainStore.hasLogin) {
            await addReadNum({
                contentId: item.contentId,
            }).finally(() => {
                console.log('finally')
            })
        } else {
            await addReadNumAnonymous({
                contentId: item.contentId,
            }).finally(() => {
                console.log('finally')
            })
        }
    }

    // 点赞
    function handleLike(index, item) {
        //游客身份不能点赞、评论
        // isVisitor()
        const isVisitorFlag = isVisitor()
        if (isVisitorFlag) {
            return;
        }
        const arrList = contentList.value
        updateContentLike({
                contentId: item.contentId,
                upvoteFlag: item.upvoteFlag == 1 ? 0 : 1,
            })
            .then((res) => {
                uni.showToast({
                    title: item.upvoteFlag == 1 ? '取消点赞成功' : '点赞成功',
                    icon: 'none',
                })
                if (arrList && arrList.length > index) {
                    arrList[index] = {
                        ...item,
                        upvoteCount: item.upvoteFlag == 1 ? --item.upvoteCount : ++item.upvoteCount,
                        upvoteFlag: item.upvoteFlag == 1 ? 0 : 1,
                    }
                }
            })
            .catch((e) => {
                console.log(e)
                uni.showToast({
                    title: e,
                    icon: 'none',
                })
            })
    }
    //游客身份不能点赞、评论
    function isVisitor() {
        if (userStore.userInfo.customParam.userType == 'Visitor') {
            uni.showModal({
                title: '提示',
                content: '加入该村庄后才能点赞或评论',
                confirmText: '加入',
                success: function(res) {
                    if (res.confirm) {
                        const customParam = userStore.userInfo.customParam;
                        uni.navigateTo({
                            url: "/pages/generalPage/join/join?tenantId=" + customParam.tenantId +
                                '&optionType=add&tenantName=' + customParam.tenantName +
                                '&fullName=' +
                                customParam.tenantName
                        })
                    }
                }
            });
            return true;
        } else {
            return false
        }
    }

    defineExpose({
        reloadList,
        scrollToLower,
    })
</script>
<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .nomore {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC, PingFang-SC;
        font-weight: 400;
        color: #999999;
        line-height: 37rpx;
    }

    .article-list {
        height: auto;
        width: 100%;
        margin: 20rpx 0;
        padding: 0 20rpx;

        .article-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 20rpx;
            margin-bottom: 20rpx;
            width: 100%;
            background-color: #fff;
            border-radius: 6rpx;
            position: relative;

            &:last-child {
                // padding-bottom: 20rpx;
                display: inline-block;
            }

            .comment-list {
                height: 260rpx;
            }

            .custom-list {
                width: 100%;
            }

            .article-info {
                display: flex;
                justify-content: flex-start;
                margin-bottom: 20rpx;

                .article-image {
                    border-radius: 8rpx;
                }

                .pic-content {
                    height: 135rpx;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: space-between;
                    margin-left: 20rpx;

                    .content {
                        height: 120rpx;
                        display: flex;
                        align-items: center;

                        .content-text {
                            font-size: 28rpx;
                            font-family: PingFangSC-Medium, PingFang SC;
                            font-weight: 600;
                            color: #222222;
                            line-height: 48rpx;
                            display: -webkit-box;
                            justify-content: center;
                            /* 将容器以弹性盒子形式布局 */
                            -webkit-line-clamp: 2;
                            /* 限制文本显示为两行 */
                            -webkit-box-orient: vertical;
                            /* 将弹性盒子的主轴方向设置为垂直方向 */
                            overflow: hidden;
                            /* 隐藏容器中超出部分的内容 */
                            text-overflow: ellipsis;
                            /* 超出容器范围的文本显示省略号 */
                        }
                    }

                    .time {
                        margin-top: 10rpx;
                        font-size: 26rpx;
                        font-family: PingFang-SC-Regular, PingFang-SC;
                        font-weight: 400;
                        color: #999999;
                    }
                }

                .author-info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    margin-left: 20rpx;

                    .article-author {
                        margin-top: 6rpx;
                        font-size: 28rpx;
                        font-weight: 600;
                        color: #000000;
                        line-height: 40rpx;
                    }

                    .article-time {
                        margin-top: 14rpx;
                        font-size: 26rpx;
                        font-family: PingFang-SC-Regular, PingFang-SC;
                        font-weight: 400;
                        color: #999999;
                    }
                }
            }

            .article-content {
                height: 80rpx;
                display: flex;
                align-items: center;

                .content-text {
                    font-size: 28rpx;
                    line-height: 44rpx;
                    font-family: PingFang-SC-Regular, PingFang-SC;
                    font-weight: 500;
                    color: #000000;
                    display: -webkit-box;
                    /* 将容器以弹性盒子形式布局 */
                    -webkit-line-clamp: 2;
                    /* 限制文本显示为两行 */
                    -webkit-box-orient: vertical;
                    /* 将弹性盒子的主轴方向设置为垂直方向 */
                    overflow: hidden;
                    /* 隐藏容器中超出部分的内容 */
                    text-overflow: ellipsis;
                    /* 超出容器范围的文本显示省略号 */
                }
            }

            .article-item-option {
                display: flex;
                justify-content: space-between;
                margin-top: 20rpx;
                width: 100%;

                .item-time {
                    margin: 0 10rpx 0 0;
                    font-size: 28rpx;
                    color: #999999;
                }

                .item-view {
                    font-size: 28rpx;
                    color: #999999;
                    display: flex;

                    .txt {
                        margin-left: 8rpx;
                    }
                }
            }

            .article-option {
                position: absolute;
                bottom: 20rpx;
                left: 10rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 710rpx;

                .art-icons-list {
                    position: relative;
                    width: 100%;
                }

                .option {
                    width: 33.3%;
                }

                .view-info {
                    position: absolute;
                    bottom: 0;
                    left: 10rpx;
                    display: flex;
                    align-items: center;
                    margin-left: 10rpx;
                }

                .like-list {
                    position: absolute;
                    left: 42%;
                    bottom: 0;
                    display: flex;
                    align-items: baseline;
                }

                .activeList {
                    bottom: 0;
                    left: calc(100vw - 140rpx - 40rpx);
                    display: flex;
                    align-items: baseline;
                    width: 140rpx;
                }

                .icon-list {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    display: flex;
                    align-items: baseline;
                    width: 140rpx;
                }

                .comment {
                    margin: 0 34rpx 0 8rpx;
                    font-size: 28rpx;
                    color: #999999;
                }

                .like {
                    margin-left: 8rpx;
                    font-size: 28rpx;
                    color: #999999;
                }
            }
        }

        &::after {
            content: '';
            flex: 1;
        }
    }

    .empty-status {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 50rpx;

        .empty-icon {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .no-more {
                margin-top: 20rpx;
                display: flex;
                justify-content: center;
                margin-top: 10rpx;

                .txt {
                    text-align: center;
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: PingFang-SC-Regular, PingFang-SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 37rpx;
                }
            }
        }
    }

    .empty-status1 {
        margin-top: 200rpx;
    }
</style>