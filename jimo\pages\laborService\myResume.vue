<!-- 我的简历 -->
<template>
	<view class="my-resume" v-if="resumeInfo">
		<u-navbar title="我的简历" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000" />
			
		<view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<view class="name">{{ resumeInfo.resumeName }}</view>
				<view class="info">
					<text>{{ resumeInfo.sex === 'woman' ? '女' : '男' }}</text>
					<text v-if="resumeInfo.age">｜{{ resumeInfo.age }}</text>
					<text v-if="resumeInfo.positionName">｜{{ resumeInfo.positionName }}</text>
				</view>
				<view class="money">
					<image :src="LABOR_MONEY" />
					<text>{{ resumeInfo.salaryRangeLow || '0' }}-{{ resumeInfo.salaryRangeHigh || '0' }}元</text>
				</view>
				<view class="address" v-if="resumeInfo.regionPlace">
					<image :src="LABOR_DZ" />
					<view style="width: calc(100% - 46rpx);">
						{{ resumeInfo.regionPlace || '' }}
					</view>
				</view>
				<view class="self-introduction">
					<view class="title">个人介绍</view>
					<view class="content">{{ resumeInfo.introduction }}</view>
				</view>
				<view class="certificate" v-if="resumeInfo.img">
					<view class="title">证书照片</view>
					<view class="content">
						<view v-for="item in resumeInfo.img.split(',')" :key="item.text">
							<image :src="item" />
						</view>
					</view>
				</view>
				<view class="eye">
					<u-icon name="eye" color="#000" size="26rpx"></u-icon>
					<text>{{ resumeInfo.number || '0' }}</text>
				</view>
			</view>
		</view>
		
		<view class="edit-btn" @click="goEditResume()">
			<image :src="LABOR_RESUME_EDIT" />
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { LABOR_MONEY, LABOR_DZ, LABOR_RESUME_EDIT } from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 数据加载 */
const loading = ref(false)
/** 简历详情 */
const resumeInfo = ref(null)
/** 是否进入编辑页面 */
const isToEdit = ref(false)

onShow(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	getResumeInfo()
})

/**
 * @description 获取简历详情
 */
const getResumeInfo = () => {
	uni.showLoading({ title: '加载中...', mask: true })
	
	LaborService.getMyResumeInfo({}).then(r => {
		if (r.data) {
			resumeInfo.value = r.data
			getWorkPlace()
		} else if (!isToEdit.value) {
			isToEdit.value = true
			uni.navigateTo({
				url: '/jimo/pages/laborService/myResumeEdit'
			})
		} else {
			uni.navigateBack()
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
const getWorkPlace = () => {
	const { workPlace, countyName } = resumeInfo.value
	if (!workPlace) return
	const strList = workPlace.split(',')
	const str = strList[0].split(countyName)[0] + countyName
	
	resumeInfo.value.regionPlace = str + ' ' + strList.map(item => item.replace(new RegExp(str, 'g'), '')).join('、')
}
/**
 * @description 编辑简历
 */
const goEditResume = () => {
	uni.navigateTo({
		url: '/jimo/pages/laborService/myResumeEdit'
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 34rpx 19rpx 20rpx 21rpx;
	
	.name {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		font-size: 42rpx;
		color: #000000;
		line-height: 59rpx;
	}
	
	.info {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 26rpx;
		color: #111111;
		line-height: 37rpx;
		margin-top: 23rpx;
	}
	
	.money {
		display: flex;
		margin-top: 27rpx;
		
		image {
			width: 32rpx;
			height: 32rpx;
			margin-top: 1rpx;	
			margin-right: 12rpx;
		}
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #111111;
			line-height: 37rpx;
		}
	}
	
	.address {
		display: flex;
		margin-top: 28rpx;
		
		image {
			width: 32rpx;
			height: 32rpx;
			margin-top: 3rpx;
			margin-right: 14rpx;
		}
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 33rpx;
		}
	}
	
	.self-introduction {
		margin-top: 34rpx;
		border-top: 1rpx solid #E8E8E8;
		padding: 21rpx 7rpx 30rpx 5rpx;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}
		
		.content {
			margin-top: 11rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 48rpx;
			padding-left: 13rpx;
			white-space: pre-wrap;
			word-break: break-all;
		}
	}
	
	.certificate {
		margin-top: 20rpx;
		display: flow-root;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}
		
		.content {
			margin-top: 16rpx;
			
			> view {
				width: calc((100% - 20rpx) / 2);
				height: 243rpx;
				border: 1rpx solid #E3E3E3;
				padding: 8rpx;
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				float: left;
				
				&:nth-child(2n) {
					margin-right: 0 !important;
				}
				
				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
	
	.eye {
		display: flex;
		padding-left: 13rpx;
		margin-top: 100rpx;
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 33rpx;
			margin-left: 10rpx;
		}
	}
}

.edit-btn {
	position: fixed;
	bottom: 262rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	
	image {
		width: 100%;
		height: 100%;
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>