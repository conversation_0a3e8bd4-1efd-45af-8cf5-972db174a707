import {
	request
} from '@/common/net/request.js'
import {
	API_VILLAGES_URL,
	API_INDUSTRY_URL,
	API_TYPELIST_URL,
	API_EVENTCOUNT_URL,
	API_EVENTLIST_URL
} from '@/common/net/netUrl.js'

// 获取村镇基础信息
export function getVillages(params) {
	return request({
		url: API_VILLAGES_URL,
		method: 'GET',
		params,
	})
}

// 查询某个产业类型的所有数据
export function getIndustry(params) {
	return request({
		url: API_INDUSTRY_URL,
		method: 'GET',
		params,
	})
}

// 查询所有产业类型
export function typeList(params) {
	return request({
		url: API_TYPELIST_URL,
		method: 'GET',
		params,
	})
}

// 获取代办在办已办数量
export function getEventCount() {
	return request({
		url: API_EVENTCOUNT_URL,
		method: 'POST',
	})
}

// 获取代办在办已办列表
export function getEventList(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_EVENTLIST_URL,
		method: 'POST',
		params
	})
}