import {
    request
} from '../request.js'
import {
	API_ADDSTATISTICS_URL
} from '@/common/net/netUrl.js'

// 获取活动列表
export function getActivityList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/activities/findPageMobile',
        method: 'GET',
        params,
    })
}

// 获取活动说明
export function getActivityShortInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/ballot/candidate/findActivitiesInfo',
        method: 'POST',
        params,
    })
}

// 获取详情
export function findInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/activities/findInfoMobile',
        method: 'GET',
        params,
    })
}

// 新增访问量
export function addStatistics(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDSTATISTICS_URL,
		method: 'POST',
		params,
	})
}