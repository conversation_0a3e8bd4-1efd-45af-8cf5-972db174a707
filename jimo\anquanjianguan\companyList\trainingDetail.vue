<template>
  <view class="training-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="活动详情" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
      <logoutbtn></logoutbtn>

    <view class="container">
      <view class="tab-content" ref="tabContentRef">
          <view 
          class="triangle-status-tag"
          v-if="training.trainingStatus"
          :class="{
            'triangle-ing': training.trainingStatus === '进行中'
          }"
        >
          <text class="triangle-text">
            {{ training.trainingStatus}}
          </text>
        </view>
        <u--form :model="training" errorType="toast" :rules="isReadOnly?[]:rules" ref="formRef" :label-width="'100px'" :labelStyle="labelStyle" :borderBottom="false">
          <div class="collapse-title">活动信息</div>
          <!-- 活动信息表单项 -->
          <u-form-item label="活动标题" prop="trainingTitle" required>
            <u--input v-model="training.trainingTitle" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入活动标题'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="活动开始时间" prop="trainingTrainingStartTime" @click.stop="isReadOnly ? null : (showStartDatePicker = true)" required>
            <u--input v-model="training.trainingTrainingStartTime" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择活动开始时间'" border="none" clearable/>
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <u-form-item label="活动结束时间" prop="trainingTrainingEndTime" @click.stop="isReadOnly ? null : (showEndDatePicker = true)" required>
            <u--input v-model="training.trainingTrainingEndTime" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择活动结束时间'" border="none" clearable/>
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <u-form-item label="活动地点" prop="trainingLocation" required>
            <u--input v-model="training.trainingLocation" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入活动地点'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="参与企业" prop="enterpriseIds" @click.stop="gotoCompanyList" required>
            <u--input v-model="enterpriseNames" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择参与企业'" border="none" clearable/>
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <!-- <u-form-item label="活动状态" prop="trainingStatus" v-if="isReadOnly">
            <u--input v-model="training.trainingStatus" readonly border="none" />
          </u-form-item> -->
          <u-form-item label="活动内容" prop="trainingContent">
            <u-textarea v-model="training.trainingContent" :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请输入活动内容'" border="none" ></u-textarea>
          </u-form-item>
          
          <div class="collapse-title">活动图片</div>
          <u-form-item label="" prop="imageUrl">
            <view class="image-section" style="width:100%">
              <view class="imgcell">
                <template v-if="training.imageUrl">
                  <view class="imgitem" v-for="(img, imgIndex) in training.imageUrl.split(',')" :key="imgIndex">
                    <view class="img">
                      <image :src="img"></image>
                    </view>
                    <image class="closebtn" :src="CLOSE_BTN" @click="isReadOnly ? null : deleteActivityImage(imgIndex)" v-if="!isReadOnly"></image>
                  </view>
                </template>
                <image :src="IMG_ADD" style="width: 80rpx; height: 80rpx" @click="isReadOnly ? null : uploadActivityImage()" v-if="!isReadOnly"/>
              </view>
            </view>
          </u-form-item>
        </u--form>
      </view>
    </view>
   
    <template v-if="!isReadOnly">
      <view class="blank"></view>
      <view class="bottom">
        <view class="btn" @click="reset" style="margin-right: 40rpx;">取消</view>
        <view class="btn pjBtn" @click="handleSubmit">保存</view>
      </view>
    </template>
    
    <u-tabbar :value="barActive" v-if="training.trainingStatus=='未开始'&&isReadOnly" @change="handlerBarChange" fixed safeAreaInsetBottom active-color="#0CBE88" inactive-color="#999999" >
      <u-tabbar-item text="编辑" icon="info-circle" />
      <u-tabbar-item text="删除" icon="trash" />
    </u-tabbar>
    
    <u-datetime-picker closeOnClickOverlay @close="showStartDatePicker = false" :show="showStartDatePicker" @confirm="onStartDateConfirm" format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showStartDatePicker = false" />
    <u-datetime-picker closeOnClickOverlay @close="showEndDatePicker = false" :show="showEndDatePicker" @confirm="onEndDateConfirm" format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showEndDatePicker = false" />
    
    <u-popup :show="showPop" mode="center" @close="cancelHandler" closeable :safeAreaInsetBottom="false" round="5">
      <view class="tktip">
        <view class="content">确定要删除该活动吗？</view>
        <view class="btngroup">
          <view class="cancel" @click="cancelHandler">取消</view>
          <view class="okbtn" @click="deleteHandler">确定</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { TrainingService } from '../../api/anquanjianguan/training'
import {LIVE_INVENTORY_BG, IMG_ADD, CLOSE_BTN} from "@/common/net/staticUrl.js"
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const showPop = ref(false)
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)
const training = ref({})
const enterpriseNames = ref('')

// 保存初始数据的副本
const originalTrainingData = ref({})
const barActive = ref(-1)
const formRef = ref(null)
const tabContentRef = ref(null)


const labelStyle = ref({ color: '#333', lineHeight: '50rpx' })
const isReadOnly = computed(() => type.value === 'info')
const type = ref('info')
const isdisabled = ref(false)

// 动态计算 tab-content 高度
const tabContentHeight = computed(() => {
  if (training.value.trainingStatus === '未开始') {
    return 'calc(100vh - 250rpx)'
  } else if (training.value.trainingStatus === '已结束' || training.value.trainingStatus === '进行中') {
    return 'calc(100vh - 120rpx)'
  } else {
    // 默认高度
    return 'calc(100vh - 250rpx)'
  }
})

// 企业验证函数
const validateCompany = (rule, value, callback) => {
  if(!value || !value.length){
    callback(new Error('请选择参与企业'))
  }else{
    callback()
  }
}

// 开始时间验证函数
const validateStartTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error('活动开始时间不能为空'))
    return
  }

  if (training.value.trainingTrainingEndTime) {
    const startTime = new Date(value)
    const endTime = new Date(training.value.trainingTrainingEndTime)

    if (startTime >= endTime) {
      callback(new Error('活动开始时间不能大于或等于结束时间'))
      return
    }
  }

  callback()
}

// 结束时间验证函数
const validateEndTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error('活动结束时间不能为空'))
    return
  }

  if (training.value.trainingTrainingStartTime) {
    const startTime = new Date(training.value.trainingTrainingStartTime)
    const endTime = new Date(value)

    if (endTime <= startTime) {
      callback(new Error('活动结束时间不能小于或等于开始时间'))
      return
    }
  }

  callback()
}

const rules = ref({
  trainingTitle: [{ required: true, message: '活动标题不能为空', trigger: ['blur', 'change'] }],
  trainingTrainingStartTime: {
    validator: validateStartTime,
    trigger: ['blur', 'change']
  },
  trainingTrainingEndTime: {
    validator: validateEndTime,
    trigger: ['blur', 'change']
  },
  trainingLocation: [{ required: true, message: '活动地点不能为空', trigger: ['blur', 'change'] }],
  enterpriseIds: {
    validator: validateCompany
  },
  trainingStatus: [{ required: true, message: '活动状态不能为空', trigger: ['blur', 'change'] }]
})

// 跳转到企业列表页面
function gotoCompanyList(){
  console.log('---')
  if(isReadOnly.value) return;
  uni.navigateTo({
    url: '/jimo/anquanjianguan/SMSNotification/columncontent'
  })
}

onLoad(async (options) => {
  const id = options.id || ''

  // 监听企业选择结果
  uni.$on('selectcompanys', (res) => {
    console.log(res)
    training.value.enterpriseIds = res.map(item=>item.enterpriseId).join()
    enterpriseNames.value = res.map(item=>item.enterpriseName).join('、')
  })
  // 使用真实API调用
  TrainingService.findTrainingById({trainingId:id}).then(res => {
    if (res.success) {
      training.value = res.data
      // 处理企业名称显示
      if (res.data.enterpriseIds && res.data.enterpriseIds.length > 0) {
        enterpriseNames.value = res.data.enterpriseNames
      }
      // 保存初始数据的深拷贝
      originalTrainingData.value = JSON.parse(JSON.stringify(res.data))
    }
  })
})

function reset() {
  type.value = 'info';
  barActive.value = -1
  // 恢复初始数据
  if (originalTrainingData.value && Object.keys(originalTrainingData.value).length > 0) {
    try {
      training.value = JSON.parse(JSON.stringify(originalTrainingData.value))
      // 恢复企业名称显示
      if (training.value.enterpriseIds && training.value.enterpriseIds.length > 0) {
        enterpriseNames.value = training.value.enterpriseNames;
      }
    } catch (error) {
      console.error('数据恢复失败:', error)
    }
  }
}

function onStartDateConfirm(e) {
  training.value.trainingTrainingStartTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showStartDatePicker.value = false

  // 如果结束时间已选择，重新验证结束时间
  if (training.value.trainingTrainingEndTime && formRef.value) {
    formRef.value.validateField('trainingTrainingEndTime')
  }
}

function onEndDateConfirm(e) {
  training.value.trainingTrainingEndTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showEndDatePicker.value = false

  // 如果开始时间已选择，重新验证开始时间
  if (training.value.trainingTrainingStartTime && formRef.value) {
    formRef.value.validateField('trainingTrainingStartTime')
  }
}


function handleSubmit() {
  formRef.value.validate().then(valid => {
    if (valid) {
      if (isdisabled.value) return;
      isdisabled.value = true

      // 使用真实API调用
      TrainingService.updateTraining(training.value).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title: '保存成功', icon: 'success' })
          setTimeout(() => {
            uni.navigateBack()
          }, 1000)
        } else {
          uni.showToast({ title: res.message || '保存失败', icon: 'none' })
        }
      }).catch(() => {
        isdisabled.value = false
        uni.showToast({ title: '保存失败', icon: 'none' })
       
      })
    }
  })
}

function cancelHandler() {
  showPop.value = false
  reset()
}

async function deleteHandler() {
  if (isdisabled.value) return;
  isdisabled.value = true
  try {
    // 使用真实API调用
    let res = await TrainingService.deleteTraining({trainingId:training.value.trainingId})
    isdisabled.value = false
    if (res.success) {
      showPop.value = false
      uni.showToast({
        title: '删除成功',
        icon: 'none',
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      uni.showToast({
        title: res.message || '删除失败',
        icon: 'none',
      })
    }
  } catch (e) {
    isdisabled.value = false
    showPop.value = false
    uni.showToast({
      title: '删除失败',
     icon: 'none',
    })

  }
}

const handlerBarChange = (index) => {
  barActive.value = index
  if(index === 0) {
    type.value = 'edit'
    formRef.value.setRules(rules)
  }
  if(index === 1) {
    showPop.value = true
  } 
}

// 上传活动图片
function uploadActivityImage() {
  uni.chooseImage({
    count: 9 - (training.value.imageUrl ? training.value.imageUrl.split(',').length : 0),
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera', 'album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = training.value.imageUrl ? training.value.imageUrl.split(',') : []
      training.value.imageUrl = [...arr, ...list].join(',')
      console.log(training.value.imageUrl, '活动图片上传结果')
      uni.hideLoading()
    }
  })
}

// 批量上传文件函数
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url
          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) {
        uni.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 2000,
        })
      }
    }
    resolve(filelist)
  })
}

// 删除活动图片
function deleteActivityImage(imageIndex) {
  let arr = training.value.imageUrl.split(',')
  arr.splice(imageIndex, 1)
  training.value.imageUrl = arr.length ? arr.join(',') : ''
}
</script>

<style lang="scss" scoped>
  .triangle-status-tag {
      position: absolute;
      top: 0;
      right: 0;
      width: 160rpx;
      height: 160rpx;
      z-index: 10;
      background: transparent;
      .triangle-text {
        position: absolute;
        top: 38rpx;
        right: -40rpx;
        width: 160rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
        transform: rotate(50deg);
        text-align: center;
        pointer-events: none;
        white-space: nowrap;
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
          border-top: 160rpx solid #e5e5e5;
        border-left: 140rpx solid transparent;

      }
    }
     .triangle-img .triangle-text {
      color: #fff;
    }
    .triangle-ing::before {
      border-top-color: #0cbe88 !important;
    }
 
.training-detail {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #0CBE88 0%, #3ADB97 100%);
  ::v-deep .tktip {
    padding: 80rpx 60rpx 60rpx 60rpx;
    width: 650rpx;
    box-sizing: border-box;
    border-radius: 24rpx;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20rpx);
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);

    .content {
      font-size: 32rpx;
      color: #2c3e50;
      line-height: 55rpx;
      margin-bottom: 40rpx;
      text-align: center;
      font-weight: 500;
    }

    .btngroup {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20rpx;

      .cancel {
        width: 240rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 40rpx;
        border: 2rpx solid rgba(108, 117, 125, 0.2);
        font-size: 28rpx;
        font-weight: 500;
        color: #6c757d;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        }
      }

      .okbtn {
        width: 240rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        border-radius: 40rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 28rpx;
        font-weight: 600;
        color: #ffffff;
        transition: all 0.3s ease;
        box-shadow: 0 8rpx 20rpx rgba(238, 90, 36, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(238, 90, 36, 0.4);
        }
      }
    }
  }
  .container {
    //overflow: scroll;
    //height: calc(100vh - 200rpx);
    box-sizing: border-box;
    padding: 20rpx;

    .tab-content {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20rpx);
      border-radius: 32rpx 32rpx 0 0;
      padding:0 20rpx;
      box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
      height: v-bind(tabContentHeight);
      box-sizing: border-box;
      overflow: scroll;
      border: 1rpx solid rgba(255, 255, 255, 0.2);
      position: relative;

    }
  }
}

.imgcell {
  padding: 30rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20rpx;

  .imgitem {
    width: 200rpx;
    height: 150rpx;
    border: none;
    margin: 0;
    position: relative;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    &:hover {
      transform: translateY(-4rpx);
      box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.18);
    }

    .img {
      width: 100%;
      height: 100%;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(12, 190, 136, 0.1) 0%, rgba(58, 219, 151, 0.1) 100%);
        z-index: 1;
      }

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }

    .closebtn {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      top: 8rpx;
      right: 8rpx;
      z-index: 2;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 1);
      }
    }
  }
}

.image-section {
  margin-top: 40rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  border: 1rpx solid rgba(12, 190, 136, 0.1);
}

.blank {
  width: 100%;
  height: 140rpx;
}

.bottom {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  z-index: 100;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);

  .btn {
    line-height: 88rpx;
    border-radius: 44rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    font-weight: 600;
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 20rpx rgba(127, 140, 141, 0.3);

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 3rpx 12rpx rgba(127, 140, 141, 0.4);
    }
  }

  .pjBtn {
    background: linear-gradient(135deg, #0CBE88 0%, #3ADB97 100%);
    box-shadow: 0 8rpx 24rpx rgba(12, 190, 136, 0.4);
    margin-left: 20rpx;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 16rpx rgba(12, 190, 136, 0.5);
    }
  }
}

.collapse-title {
  font-size: 28rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #0CBE88 0%, #3ADB97 100%);
  color: white;
  letter-spacing: 1rpx;
  padding: 20rpx 30rpx;
  text-align: left;
  border-radius: 16rpx;
  margin: 30rpx 0 20rpx 0;
  box-shadow: 0 6rpx 20rpx rgba(12, 190, 136, 0.25);
  position: relative;
  overflow: hidden;

 
}


// 表单项样式优化
::v-deep .u-form-item {
  margin-bottom: 30rpx;

  .u-form-item__body {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
    padding: 20rpx;
    border: 1rpx solid rgba(12, 190, 136, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.9);
      border-color: rgba(12, 190, 136, 0.2);
      box-shadow: 0 4rpx 16rpx rgba(12, 190, 136, 0.1);
    }
  }

  .u-form-item__body__left__content__label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 28rpx;
  }
}

// 输入框样式优化
::v-deep .u-input__content__field-wrapper__field,
::v-deep .u-textarea__content__field {
  color: #34495e;
  font-size: 28rpx;

  &::placeholder {
    color: #95a5a6;
  }
}

// 图标样式优化
::v-deep .u-icon {
  color: #0CBE88;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    color: #0A9B6B;
  }
}

// tabbar 样式优化
::v-deep .u-tabbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);

  .u-tabbar-item {
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
