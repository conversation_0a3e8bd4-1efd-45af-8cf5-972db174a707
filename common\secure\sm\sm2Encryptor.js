// #ifdef H5||APP-PLUS
import { sm2, sm3 } from 'sm-crypto'
// #endif

// #ifdef MP-WEIXIN||MP-ALIPAY
import { sm2, sm3 } from 'miniprogram-sm-crypto'
// #endif
import { publicKey } from '@/common/confound.js'
/**
 * 生成密钥对
 */
export function doGenerate() {
  let keypair = sm2.generateKeyPairHex()
  return {
    publicKey: keypair.publicKey,
    privateKey: keypair.privateKey,
  }
}

/**
 * sm2加密方法
 * @param publickey
 * @param msg
 * @returns {*}
 */
export function doCrypt(msg) {
  let encryptData =
    '04' +
    sm2.doEncrypt(
      msg,publicKey,1
    )
  return encryptData
}

/**
 * sm2解密方法
 * @param publickey
 * @param msg
 * @returns {*}
 */
export function doDecrypt(prvkey, encryptData, cipherMode) {
  let decryptData = sm2.doDecrypt(encryptData, prvkey, cipherMode) // 解密结果
  return decryptData
}

/**
 * sm3加密
 */
export function doSM3(msg) {
  return sm3(msg)
}
