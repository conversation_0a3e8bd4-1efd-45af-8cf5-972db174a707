import {
    request
} from '../request.js';

export function findOrderById(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/productAppoint/findById",
      method: "GET",
      params
    });
  }

  export function findOrderPage(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/productAppoint/findOfPage",
      method: "POST",
      params
    });
  }
  export function addOrder(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/productAppoint/add",
      method: "POST",
      params
    });
  }

  export function refundUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/productAppoint/cancelUpdate",
      method: "POST",
      params
    });
  }

