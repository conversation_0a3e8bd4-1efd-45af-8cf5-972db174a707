<template>
	<view class="content-boxes">
		<view class="formBox">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="uForm" label-position="top"
				label-width="100" :borderBottom="true" labelStyle="font-weight: 600;font-size: 32rpx;">
				<view class="prompt">村民任务积分的使用或消耗，由村干部在此处进行积分登记，登记后当前村民的任务积分相应减少。</view>
				<u-form-item label="积分使用对象" prop="peopleName" :borderBottom="true" @click="openPeople()"
					key="peopleName">
					<u--input v-model="form.peopleName" disabled disabledColor="#ffffff" placeholder="请选择成员"
						border="none" placeholder-class="plaClass">
					</u--input>
					<template #right>
						<u-icon name="arrow-right"></u-icon>
					</template>
				</u-form-item>
				<u-form-item label="当前积分" prop="currentPoints" :borderBottom="true" key="currentPoints">
					<u--input v-model="form.currentPoints" disabled disabledColor="#ffffff" placeholder=""
						border="none">
					</u--input>
				</u-form-item>
				<u-form-item label="积分减少数值" prop="pointsValue" :borderBottom="true" ref="item1" key="pointsValue">
					<u--input v-if="recordsId == ''" v-model="form.pointsValue" border="none" placeholder="请填写积分减少数值"
						type="number" maxlength="4" placeholder-class="plaClass"></u--input>
					<text v-else>{{form.pointsValue}}</text>
				</u-form-item>
				<u-form-item label="积分使用说明" prop="pointsDescribe" :borderBottom="true" ref="item1" key="pointsDescribe">
					<u--textarea v-if="recordsId == ''" v-model="form.pointsDescribe" border="none" 
						placeholder-class="plaClass" placeholder="请填写积分使用说明" 
						autoHeight maxlength="100" count disableDefaultPadding style="color: #303133;" />
					<text v-else> {{form.pointsDescribe}} </text>
				</u-form-item>

			</u--form>
		</view>
	</view>

	<view v-if="recordsId == ''">
		<view class="blank"></view>
		<view class="bottom u-border-top">
			<view class="btn pjBtn" @click.stop="render">提交</view>
			<view class="paddingbottom"></view>
		</view>
	</view>
	<u-modal :show="polish" :content='"确定为" + form.peopleName + "减少" + form.pointsValue + "任务积分吗？"'
		:closeOnClickOverlay="true" :showCancelButton="true" @confirm="submit" @cancel="polish = false"></u-modal>
</template>

<script setup>
	import {
		DEFAULT_AVATOR_IMAGES
	} from '@/common/net/staticUrl.js'
	import {
		onMounted,
		reactive,
		ref,
		computed
	} from 'vue'
	import {
		onReady,
		onShow,
		onHide,
		onLoad,
		onUnload,
		onBackPress
	} from '@dcloudio/uni-app'
	import {
		findInfo,
		findPeople
	} from  '../../api/taskPoints/points';
	import {
		addTask,
		taskPoints,
	} from '@/common/net/my/my.js'
	import {
		useTokenStore
	} from '@/store/token.js'

	const tokenStore = useTokenStore()
	const tenantId = tokenStore.tenantId
	const recordsId = ref("")
	const polish = ref(false)
	onLoad((options) => {

		if (!!options.recordId) {
			recordsId.value = options.recordId
			findInfo({
				recordId: options.recordId
			}).then(res => {
				form.value = res.data
			})
		}

		getPeople()
	})

	const form = ref({
		villagePeopleId: "",
		peopleName: "",
		pointsValue: "",
		pointsDescribe: "",
		currentPoints: "0",
	})
	const rules = ref({
		"peopleName": {
			type: 'string',
			required: true,
			message: '请选择成员',
			trigger: ['change'],
		},
		"pointsValue": [{
				type: 'number',
				required: true,
				message: '填写正确的积分数值',
				trigger: ['change'],
			},
			{
				pattern: /^\+?[1-9]\d*$/,
				transform(value) {
					return String(value);
				},
				message: '填写正确的积分数值',
				trigger: ['change']
			}
		],
		"pointsDescribe": {
			type: 'string',
			required: true,
			message: '填写积分说明',
			trigger: ['change'],
		},
	})
	onMounted(() => {
		uForm.value.setRules(rules)
	})

	function render() {
		uForm.value.validate().then((res) => {
			if (parseInt(form.value.pointsValue) > parseInt(form.value.currentPoints)) {
				uni.showToast({
					title: '减少积分不能大于当前村民拥有的积分',
					icon: 'none'
				});
			} else {
				polish.value = true
			}
		})
	}

	const uForm = ref(null)

	function submit() {
		uForm.value.validate().then((res) => {
			if (res) {
				polish.value = false
				uni.showLoading({
					title: '处理中...',
					mask: true,
				})
				const params = {
					pointsUsePeopleId: form.value.villagePeopleId,
					pointsValue: form.value.pointsValue,
					pointsUsageDesc: form.value.pointsDescribe.substring(0, 100)
				}

				addTask(params).then(res => {
					if (res.success) {
						// uni.showToast({
						//   title: '新增成功！',
						//   icon: 'none'
						// });
						uni.showLoading({
							title: '积分记录成功',
							mask: true,
						})
						setTimeout(() => {
							// uni.redirectTo({
							// 	url: '/jimo/pages/pointsManage/pointsReport' 
							// })
							uni.navigateBack()
						}, 1000)
					} else {
						uni.hideLoading();
						console.log("添加失败")
						uni.showToast({
							title: res.message,
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err,
						icon: 'none'
					});
				})
			}

		})
	}

	//人员相关
	onReady(() => {
		getPeople()
	})
	const peopleShow = ref(false)
	const peopleList = ref([])

	function close() {
		peopleShow.value = false
	}

	function getPeople() {
		findPeople({
			tenantId: tenantId
		}).then(res => {
			if (res.success) {
				peopleList.value = res.data
			}
		})
	}
	const villagePeopleId = ref('')

	function openPeople() {
		if (!!recordsId.value) {
			return
		}
		//peopleShow.value = true
		uni.$once('chooseMemberCallback', (data) => {
			form.value['peopleName'] = data[0].peopleName
			form.value['villagePeopleId'] = data[0].villagePeopleId
			// uForm.value.validateField('peopleName', () => {})
			villagePeopleId.value = data[0].villagePeopleId
			integral()
			uForm.value.validateField('peopleName', () => {})
		})
		uni.navigateTo({
			url: '/jimo/pages/taskPoints/choosePeople?chooseType=single'
		})

	}

	//村民积分查询
	function integral() {
		let obj = {
			villagePeopleId: villagePeopleId.value
		};
		taskPoints(obj).then((res) => {
			if (res.success) {
				form.value.currentPoints = res.data
				
			} else {
				form.value.currentPoints = 0
			}
		})
	}

	function peopleChange(e, item) {
		form.value['peopleName'] = item.peopleName
		peopleShow.value = false
	}

	function groupChange(e, item) {

	}
</script>

<style lang="scss" scoped>
	.content-boxes {
		background: #F0F7F7;
		//width: 100vw;
		height: 100%;
		padding: 20rpx;

	}

	.formBox {
		background: #ffffff;
		padding: 20rpx;
		border-radius: 20rpx;
		min-height: calc(100vh - 350rpx);
	}

	.prompt {
		font-size: 26rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #666666;
		line-height: 39rpx;
	}

	.bottom-box {
		bottom: 0;
		position: absolute;
		width: 100vw;
		height: 126rpx;
		background: #ffffff;

		//padding:20rpx;
		.btn {
			margin: 0 auto;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			width: 90%;
			margin: 20rpx auto;
		}
	}

	.peopleBox {
		padding: 40rpx;
		height: calc(100vh - 110rpx);

		.top {
			.title {}

			.calcelBtn {
				float: right;
				display: block;
				color: #909399;
			}
		}

		.radioClass {
			border-bottom: #F0F7F7 1px solid;
			padding: 20rpx;
		}

		.r-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: #F0F7F7 1px solid;
		}

		.photoImg {
			margin: 0 18rpx;
		}
	}

	.submit-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 94%;
		height: 80rpx;
		background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 40rpx;
		font-size: 32rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #FFFFFF;
		margin: 20rpx auto;
	}

	::v-deep .u-textarea.data-v-b6c174a6 {
		padding: 12rpx 0 0 0;
		
		textarea {
			color: #303133 !important;
		}
	}

	::v-deep .u-form-item__body__left {
		width: 260rpx !important;
	}

	.bottom {
		position: fixed;
		width: 100%;
		background: #fff;
		bottom: 0;
		left: 0;
		//display: flex;
		align-items: center;
		padding: 20rpx 38rpx;
		z-index: 100;
		box-sizing: border-box;

		.btn {
			line-height: 80rpx;
			border-radius: 40rpx;
			flex: 1;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36rpx;
		}

		.pjBtn {
			background: #3cc16c;
			color: #fff;
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.blank {
		width: 100%;
		background: #F0F7F7;
		height: 180rpx;
	}
	::v-deep .plaClass {
		text-align: left;
		color: #B1B1B1;
		font-size: 26rpx;
	}
</style>