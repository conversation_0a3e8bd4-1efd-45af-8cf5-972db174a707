<template>
  <view
    class="formcontainer"
    :style="{ backgroundImage: `url(${LIVE_INVENTORY_BG})` }"
  >
    <u-navbar
      title="巡检打卡"
      bgColor="rgba(0,0,0,0)"
      :autoBack="true"
      :placeholder="true"
      leftIconColor="#000"
      titleStyle="font-size: 36rpx;color: #000"
    />
    <logoutbtn></logoutbtn>

    <view class="infocontainer">
      <view class="form">
        <view class="formitem">
          <view class="label">企业名称</view>
          <view class="value">{{ detail.enterpriseName }}</view>
        </view>
        <view class="formitem">
          <view class="label">企业性质</view>
          <view class="value">{{ detail.enterpriseNature }}</view>
        </view>
        <view class="formitem">
          <view class="label">企业规模</view>
          <view class="value">{{ detail.enterpriseScale }}</view>
        </view>
        <view class="formitem">
          <view class="label">最新巡检时间</view>
          <view class="value">{{ detail.lastInspectTime }}</view>
        </view>
      </view>
      <view class="form">
        <u--form
          errorType="none"
          ref="formRef"
          :model="form"
          :label-width="120"
          :labelStyle="{ fontWeight: 'bold', fontSize: '32rpx' }"
          :rules="rules"
        >


          <u-form-item label="巡检时间" prop="inspectTime">
            <u--input
              v-model="form.inspectTime"
              placeholder="请输入巡检时间"
              border="none"
              readonly
            />
          </u-form-item>
          <u-form-item label="巡检定位" prop="location" @click="openMap">
            <view class="formitem">
              <u--input
                v-model="form.location"
                placeholder="请输入巡检定位"
                border="none"
              />
              <u-icon name="map" ></u-icon>
            </view>
          </u-form-item>
          <view class="label">检查记录</view>
          <view class="imgcell">
            <template v-if="form.inspectImages">
            <view class="imgitem" v-for="item in form.inspectImages.split(',')" :key="item">
              <view class="img">
                <image
                  :src="item"
               
                ></image>
              </view>
              <image
                class="closebtn"
                :src="CLOSE_BTN"
                @click="deleteHanlder(item)"
              ></image>
            </view>
            </template>
            <image
              :src="IMG_ADD"
              style="width: 80rpx; height: 80rpx"
              @click="uploadimgHandler('location')"
            />
          </view>
          <!-- <view class="label">定位图片</view>
          <view class="imgcell">
            <template v-if="form.checkImages">
            <view class="imgitem" v-for="item in form.checkImages.split(',')" :key="item">
              <view class="img">
                <image
                  :src="item"
               
                ></image>
              </view>
              <image
                class="closebtn"
                :src="CLOSE_BTN"
                @click="deleteCheckHanlder(item)"
              ></image>
            </view>
            </template>
            <image
              :src="IMG_ADD"
              style="width: 80rpx; height: 80rpx"
              @click="uploadimgHandler('check')"
            />
          </view> -->

          <view class="label">
            <text style="color: #f00">*</text>隐患图片：
          </view>
          <view class="imgcell">
            <template v-if="form.riskImages">
            <view
              class="imgitem"
              v-for="item in form.riskImages.split(',')"
              :key="item"
            >
              <!-- <image class="img" :src="item" @click="previewHandler(index, imglist)"></image> -->
              <view class="img">
                <image
                  :src="item"
               
                ></image>
              </view>
              <image
                class="closebtn"
                :src="CLOSE_BTN"
                @click="deleteDangerImageHanlder(item)"
              ></image>
            </view>
            </template>
            <image
              :src="IMG_ADD"
              style="width: 80rpx; height: 80rpx"
              @click="uploadimgHandler('risk')"
            />
          </view>
          <u-form-item label="" prop="problemDesc">
            <view style="width: 100%">
              <view class="label">
                <text style="color: #f00">*</text>隐患描述：
              </view>
              <u--textarea
                v-model="form.problemDesc"
                placeholder="请输入隐患描述"
                border="surround"
              ></u--textarea>
            </view>
          </u-form-item>
          <u-form-item
            required
            label="整改期限"
            prop="rectifyDeadline"
          >
            <u--input
              v-model="form.rectifyDeadline"
              placeholder="请输入日期，格式：yyyy-mm-dd"
              border="none"
              @blur="validateDate(form.rectifyDeadline, (value)=>{form.rectifyDeadline = value} )"
            />
          </u-form-item>

          <u-form-item label="上传人" prop="uploader" required>
            <u--input
              v-model="form.uploader"
              placeholder="请输入上传人"
              border="none"
              clearable
            />
          </u-form-item>
          <view class="flexitem">
            <view class="label">是否存在重大隐患：</view>
            <u-radio-group
              v-model="form.isMajorRisk"
              size="20"
              activeColor="#1BC78D"
            >
            <u-radio :name="'NO'" label="否"></u-radio>
            <u-radio :name="'YES'" label="是"></u-radio>
             
            </u-radio-group>
          </view>
        </u--form>
      </view>
    </view>
    <view class="bottom u-border-top">
      <view class="btn pjBtn" @click="submit">提交</view>
    </view>
    <u-datetime-picker closeOnClickOverlay
      @close="showDatePicker = false"
      :show="showDatePicker"
      @confirm="onDateConfirm"
      format="YYYY-MM-DD"
      mode="date"
      @cancel="showDatePicker = false"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { IMG_ADD, LIVE_INVENTORY_BG, CLOSE_BTN } from '@/common/net/staticUrl'
import { onShow, onReady,onLoad } from '@dcloudio/uni-app'
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import { AJYService } from "../../api/anquanjianguan/companyList.js";

import {AdminService} from "../../api/anquanjianguan/adminstrator"
import logoutbtn from '@/jimo/components/logoutbtn.vue'
const detail = ref({})
const form = ref({})
const rules = ref({
  problemDesc: [
    {
      required: true,
      message: '问题描述不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  rectifyDeadline: [
    {
      required: true,
      message: '整改期限不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  uploader: [
    { required: true, message: '上传人不能为空', trigger: ['blur', 'change'] },
  ],
})
const showDatePicker = ref(false)
const companyId = ref('')
onLoad((options)=>{
form.value.enterpriseId = options.id
AdminService.findCompanyById(options.id).then(res => {
    if (res.success) {
      detail.value = res.data
      }
  })
})
const validateDate = (value, callback) => {
 
  if (!value) return;
  // 检查日期格式是否正确
  const datePattern = /^\d{4}-\d{1,2}-\d{1,2}$/;
  if (!datePattern.test(value)) {
    uni.showToast({
      title: '请输入正确的日期格式：yyyy-mm-dd',
      icon: 'none'
    });
    callback('')
    
    return;
  }
  
  // 分割日期
  const [year, month, day] = value.split('-').map(Number);
  
  // 验证日期是否有效
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
   callback('')
    return;
  }
  
  // 格式化日期，确保月份和日期是两位数
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay}`)
}
onShow(() => {

  getUserLocation()
  // 设置checkTime为当前时间
  form.value.inspectTime = uni.$u.timeFormat(Date.now(), 'yyyy-mm-dd hh:MM:ss')
})
const formRef = ref(null)
const longitude = ref(0)
const latitude = ref(0)
onReady(() => {
  formRef.value.setRules(rules)
  form.value.isMajorRisk = 'NO'
})

function getUserLocation(){
  uni.getLocation({
            type: "gcj02",
            success(res){
              console.log(res,'0----------000')
                // 暂时
                form.value.longitude = res.longitude; //118.787575;
                form.value.latitude = res.latitude; //32.05024;
                console.log("获取当前的用户经度", longitude.value);
                console.log("获取当前的用户纬度", latitude.value);
            },
            fail(err){
              console.log(err,'dd-----------d')
            }
        })
}
function uploadimgHandler(type) {
  let  length = 0
  if(type=='location'){
   
    length = form.value.inspectImages?form.value.inspectImages.split(',').length:0
  }else if(type=='check'){
    
    length = form.value.checkImages?form.value.checkImages.split(',').length:0
  }
  else{
  
    length = form.value.riskImages?form.value.riskImages.split(',').length:0
  }
  uni.chooseImage({
    count: 9 - length, //默认9
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
    sourceType: ['camera'], //从相册选择
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      if (type == 'location') {
        const arr = form.value.inspectImages?form.value.inspectImages.split(','):[]
       form.value.inspectImages = [...arr, ...list].join(',')
    
      }
      else if (type == 'check') {
        const arr = form.value.checkImages?form.value.checkImages.split(','):[]
        form.value.checkImages = [...arr, ...list].join(',')
      } else {
        const arr = form.value.riskImages?form.value.riskImages.split(','):[]
        form.value.riskImages = [...arr, ...list].join(',')
      }

      uni.hideLoading()
    },
  })
}
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url

          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) {}
    }
    resolve(filelist)
  })
}
function deleteHanlder(item) {
 
  const imageIndex = form.value.inspectImages.split(',').indexOf(item)
   let arr = form.value.inspectImages.split(',')
  arr.splice(imageIndex, 1).join(',')
  form.value.inspectImages = arr.length?arr.join(','):''

}
function deleteCheckHanlder(item) {
  const imageIndex = form.value.checkImages.split(',').indexOf(item)
   let arr = form.value.checkImages.split(',')
  arr.splice(imageIndex, 1).join(',')
  form.value.checkImages = arr.length?arr.join(','):''

}
function deleteDangerImageHanlder(item) {
  const imageIndex = form.value.riskImages.split(',').indexOf(item)
   let arr = form.value.riskImages.split(',')
  arr.splice(imageIndex, 1).join(',')
  form.value.riskImages = arr.length?arr.join(','):''
  l
}

function onDateConfirm(e) {
  form.value.rectifyDeadline = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  console.log(form.value.rectifyDeadline)
  showDatePicker.value = false
}
const isdisabled = ref(false)
function submit() {
  formRef.value.validate().then( async (valid) => {
     if (valid) {
    if (isdisabled.value) return;
      isdisabled.value = true
      AJYService.addInspectRecord(form.value).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title: '打卡成功', icon: 'success' })
          setTimeout(()=>{
           uni.navigateBack()
          },600)
        } else {
          uni.showToast({ title: res.message, icon: 'none' })
        }
      }).catch(e=>{
        console.log(e)
        isdisabled.value = false
        uni.showToast({
				title: '打卡失败！',
				icon: 'none',
			})
      })
    }
    else{
     uni.showToast({ title: '请填写完整信息', icon: 'none' })
  
    }
  })
   .catch((e) => {
      uni.showToast({ title: '请填写完整信息', icon: 'none' })
    })
}

function openMap(){
  try{
		uni.getSetting({
			success(res) {
				console.log(res)
				if (res.authSetting['scope.userLocation']) {
					console.log('获取位置已授权')
					getLocation()
				}else {
					uni.showModal({
						title: '请求授权当前位置',
						content: "需要获取您的地理位置，请确认授权！",
						confirmColor: "#01BD5D",
						success(res1) {
				
							if (res1.confirm) {
								uni.authorize({
									scope: 'scope.userLocation',
									success(res) {
										uni.showToast({
											title: "授权成功",
											icon: "success",
											duration: 3000
										});
										getLocation()
									},
									fail() {
										uni.showToast({
											title: "授权失败",
											icon: "none",
											duration: 3000
										});
									}
								})
							}
						}
					})
				}
			},
			fail() {
				console.log("获取授权信息授权失败")
			}
		})
  }catch(err){
    getLocation()
  }
	}

	function getLocation() {
		uni.chooseLocation({
			latitude: form.value.latitude, /* 纬度 */
			longitude: form.value.longitude, /* 经度 */
			success(t) {
				console.log(t)
			
					form.value.location = t.address + t.name
			    form.value.longitude = t.longitude
          form.value.latitude = t.latitude
				
				
				
			}
		});
	}
	
</script>
<style scoped lang="scss">
.flexitem {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.formitem {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  ::v-deep .u-icon {
    margin-left: 20rpx;
  }
}
.value {
  color: #666;
  word-wrap: break-word;
}
::v-deep .redplaceholder {
  color: red !important;
  font-size: 28rpx;
}
.cellcomplete {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 0 26rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-family: PingFang-SC, PingFang-SC;
  color: #000000;
  .rightarrow {
    width: 30rpx;
    height: 30rpx;
  }
}
.workgetcon {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 23rpx 26rpx;
  .workgetarea {
    position: relative;
    .btngroup {
      position: absolute;
      right: 0;
      top: -46rpx;
      display: flex;
      align-items: center;
      .btn {
        width: 111rpx;
        height: 48rpx;
        border-radius: 31rpx;
        border: 0.5px solid #cccccc;
        line-height: 48rpx;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        margin-left: 7rpx;
      }
      .greenbtn {
        border: 0.5px solid #0bbd88;
        color: #0bbd88;
      }
    }
    .areacon {
      padding-top: 30rpx;
    }
  }
  .label {
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
  }
  .addWorkget {
    padding: 36rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #0bbd88;
    line-height: 45rpx;
    .plus {
      margin-right: 20rpx;
    }
  }
}
.imgcell {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .imgitem {
    width: 233rpx;
    height: 179rpx;
    border: 0.5px solid #e2e2e2;
    margin-right: 40rpx;
    margin-bottom: 20rpx;
    position: relative;

    image {
      margin: 7rpx;
      width: 219rpx;
      height: 164rpx;
    }

    .closebtn {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: -16rpx;
      right: -16rpx;
    }
  }
}
.gridcontent {
  padding: 15rpx 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 28rpx;
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 400;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 56rpx;
}
.fileitem {
  display: flex;
  flex-wrap: wrap;
  white-space: nowrap;
  position: relative;
  text {
    font-size: 24rpx;
    color: #ccc;
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .closebtn {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.imgitem {
  width: 233rpx;
  height: 179rpx;
  border: 0.5px solid #e2e2e2;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
  position: relative;
  image {
    margin: 7rpx;
    width: 219rpx;
    height: 164rpx;
  }
  .closebtn {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.uploadcon {
  width: 100%;
  overflow: hidden;
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 180rpx;
}
.arrow {
  top: 50rpx;
  width: 16rpx;
  height: 30rpx;
  position: absolute;
  right: 0;
}
.formcontainer {
  height: 100vh;
  .infocontainer {
    margin: 20rpx;

    height: calc(100vh - 220rpx);
    box-sizing: border-box;
    overflow: scroll;
    .form {
      padding: 0 26rpx;
      background: #ffffff;
      border-radius: 20rpx;
      padding-bottom: 26rpx;
      ::v-deep .uni-input-wrapper {
        text-align: right;
      }
      ::v-deep .u-form-item__body__left__content__required {
        position: inherit;
        left: auto;
      }
      ::v-deep .u-radio-group {
        justify-content: end;
      }
      ::v-deep .u-radio {
        margin-right: 20rpx;
      }
      &:nth-child(1) {
        margin-bottom: 26rpx;
      }
    }
  }
  ::v-deep .u-form-item {
    position: relative;
  }
  .label {
    font-size: 32rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 600;
    color: #333333;
    line-height: 45rpx;
    padding: 23rpx 0;
  }
  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  ::v-deep .u-input__content__field-wrapper__field {
    font-size: 28rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 400;
    color: #b1b1b1;
    line-height: 40rpx;
  }
}
</style>
