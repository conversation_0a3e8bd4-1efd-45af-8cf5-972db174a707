<template>
  <view class="bg">
    <u-navbar
      title="积分商城"
      :autoBack="true"
      bgColor="rgb(66,206,147,0)"
      :placeholder="true"
      titleStyle="color:#fff"
      leftIconColor="#fff"
    >
    </u-navbar>
    <!-- 背景 -->
    <image class="navBg" mode="widthFix" :src="POINTMALL_BG1"></image>
    <view class="firstBox">
		<view v-if="isParty" class="toggleItem" @click="toggleTab">
			<image mode="widthFix" :src="TOOGLE_ICON"></image>
			<text>{{sourceType == 1 ? '切换党员积分' : '切换家庭积分'}}</text>
		</view>
      <view class="flexbetween">
        <u-avatar size="132rpx" :src="sculpture"></u-avatar>
        <view class="pointdetail">
          <view class="title"> {{ sourceType == 1 ? '家庭积分' : '党员积分'}} </view>
          <view class="point">{{ sourceType == 1 ? point : partyPoints }}</view>
          <!-- <view class="enddate"> 25积分将于2024-03-05过期 </view> -->
        </view>
      </view>
      <view class="flexbetween btngroup">
        <view class="btn flexbetween" @click="gotopointsaccount">
          <image class="img" :src="POINTMALL_ACCOUNT"></image>
          <text class="label">积分账单</text>
        </view>
        <view class="diliver"></view>
        <view class="btn flexbetween" @click="gotoorderlist">
          <image class="img" :src="POINTMALL_ORDERLIST"></image>
          <text class="label">订单记录</text>
        </view>
      </view>
    </view>
    <view class="topline">
      <image class="leftimg" :src="LINE_LEFT"></image>
      <text class="linelabel">精选商品</text>
      <image class="rightimg" :src="LINE_RIGHT"></image>
    </view>

    <scroll-view class="secondBox" scroll-y @scrolltolower="getList">
      <view
        class="empty-status"
        v-if="loaded === true && commoditylist.length === 0"
      >
        <view class="empty-icon">
          <u-image
            :src="NO_MORE_IMG"
            shape="circle"
            mode="aspectFit"
            width="300rpx"
            height="150rpx"
          ></u-image>
          <view class="no-more">
            <span class="txt">暂无数据</span>
          </view>
        </view>
      </view>
      <!-- <u-empty
        v-if="loaded === true && commoditylist.length === 0"
        mode="data"
      ></u-empty> -->
      <block v-if="commoditylist.length > 0">
        <view
          class="good"
          v-for="(item, index) in commoditylist"
          :key="index"
          @click="getData(item)"
        >
          <view class="goodimg">
            <safe-image
              :lazyLoad='true'
              radius="5"
              :src="item.commodityPicture.split(',')[0]"
              mode="widthFix"
              width="100%"
              height="219rpx"
            ></safe-image>
          </view>
          <view class="goodtitle u-line-2">
            {{ item.commodityTitle }}
          </view>
          <view class="goodprice u-line-1 flexbetween">
            <image :src="JF"></image>
            <text>{{ sourceType == '2' ? item.integralParty : item.integral }}积分</text>
          </view>
        </view>
      </block>
      <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && commoditylist.length === 0)"
      ></u-loadmore>
    </scroll-view>
    <!-- 占位块，使上一个view的marginbottom生效 -->
    <view style="height: 1rpx"></view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { onLoad, onShow, onReady } from '@dcloudio/uni-app'
import { NO_MORE_IMG } from '@/common/net/staticUrl'
import { findOfPage, findOne, checkUserIsParty, getPartyAllInfo } from "../../api/pointsmall/pointsmall"
import { findRedeemablePoints } from  '../../api/pointsmall/hypermarketOrder'
import { useUserStore } from '@/store/user.js'
import { setPoints} from '../../api/burialPoint.js'
import {
  DEFAULT_AVATOR_IMAGES,
  POINTMALL_BG1,
  POINTMALL_ACCOUNT,
  POINTMALL_ORDERLIST,
  LINE_LEFT,
  JF,
  LINE_RIGHT,
  TOOGLE_ICON,
} from '@/common/net/staticUrl.js'
const userStore = useUserStore()
const sculpture = userStore.userInfo
  ? ref(userStore.userInfo.headPhoto)
  : ref(DEFAULT_AVATOR_IMAGES)
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  shelfStatus: 1,
})
const commoditylist = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
const point = ref(0)
let sourceType = ref('1')  //1为家庭 2为党员
let isParty = ref(false)
let partyPoints = ref(0)
const villagePeopleId = userStore?.userInfo?.customParam.peopleInfo.villagePeopleId
onLoad((option) => {
	sourceType.value = option?.sourceType || '1'
  getList()
  getUserInfo()
  getPartyPoints()
})
onShow(() => {
  getUserPoint()
  // 埋点-进入积分商城页
  let param = {
  	eventId : 'enter_integral_mall_page',
  	attributeValue: '/jimo/pages/pointsmall/pointsmall'
  }
  setPoints(param)
})
onReady(() => {
  uni.$on('refreshList', refreshList)
})

async function refreshList() {
  commoditylist.value = []
  loadStatus.value = 'loading'
  queryParams.pageNum = 1
  await getList()
}
async function getList() {
  if (loadStatus.value == 'nomore') return
  if (loadStatus.value == 'more') {
    loadStatus.value = 'loading'
  }
  try {
	queryParams.role = sourceType.value
    const res = await findOfPage(queryParams)
    if (res.success) {
      total.value = res.data.total
      if (queryParams.pageNum > 1) {
        commoditylist.value = [...commoditylist.value, ...res.data.records]
      } else {
        commoditylist.value = res.data.records
      }
      if (total.value === commoditylist.value.length) {
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'more'
        queryParams.pageNum = queryParams.pageNum + 1
      }
    } else {
      uni.showToast({
        title: res.message || '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    }
    loaded.value = true
  } catch (error) {
    uni.showToast({
      title: '查询数据失败',
      icon: 'none',
    })
    loadStatus.value = 'more'
  }
}

function gotoorderlist() {
  uni.navigateTo({
    url: `/jimo/pages/pointsmall/orderlist?sourceType=${sourceType.value}`,
  })
}

function gotopointsaccount() {
	if(sourceType.value == 1){
		uni.navigateTo({
			url: '/jimo/pages/pointsmall/pointsaccount',
		})
	}else{
		uni.navigateTo({
			url: '/jimo/pagesOne/partyMemberPoints/pointsaccount',
		})
	}
 
}
async function getUserPoint() {
  try {
    let res = await findRedeemablePoints()
    if (res.success) {
      point.value = res.data
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '获取用户积分失败！',
      icon: 'none',
    })
  }
}

function getData(data) {
  uni.navigateTo({
    url: `/jimo/pages/pointsmall/gooddetail?id=${data.commodityId}&sourceType=${sourceType.value}`,
  })
}
// 查询是否为党员
function getUserInfo(){
	checkUserIsParty().then(res=>{
		isParty.value = res.data
	})
}
// 切换积分
function toggleTab(){
	if(sourceType.value == '1'){
		sourceType.value = '2'
	}else{
		sourceType.value = '1'
	}
	refreshList()
}
  function getPartyPoints() {
		getPartyAllInfo({villagePeopleId: villagePeopleId}).then(res=>{
			if(res.success){
				partyPoints.value = parseInt(res.data?.totalPointsRemain) > 0 ? parseInt(res.data?.totalPointsRemain) : 0
			}else{
				uni.showToast({
					title: res.message,
					icon:'none'
				})
				partyPoints.value = 0
			}
			
		})
	}
</script>

<style lang="scss" scoped>
::v-deep .u-loadmore {
  padding-bottom: 60rpx;
}

.bg {
  background-color: white;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
}

.navBg {
  width: 100%;
  position: absolute;
  /* 使用 z-index 调整上下层级需要脱离文档流 */
  top: 0;
  z-index: 1;
  /* z-index 设低一点，让页面元素可以覆盖它 */
}


.firstBox {
  top: 177rpx;
  width: calc(100% - 114rpx);
  margin: 0 57rpx;
  padding-top: 16rpx;
  z-index: 2;
  position: absolute;
  line-height: 1;

  box-sizing: border-box;

  .flexbetween {
    display: flex;
    align-items: center;

    .pointdetail {
      margin-left: 20rpx;
      margin-bottom: 12rpx;

      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #292c39;
        margin-bottom: 14rpx;
      }

      .point {
        font-size: 43rpx;
        font-weight: 600;
        color: #666666;
        background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 14rpx;
      }

      .enddate {
        font-size: 26rpx;

        color: #666666;
      }
    }
  }
  .toggleItem{
  	position: absolute;
  	top: 5rpx;
  	right: -15rpx;
  	width: 212rpx;
  	height: 48rpx;
  	border-radius: 29rpx;
  	border: 1rpx solid #17C289;
  	display: flex;
  	align-items: center;
  	justify-content: center;
	z-index: 3;
	image{
		display: block;
		width: 32rpx;
		height: 32rpx;
	}
  	text{
  		font-family: Source Han Sans CN, Source Han Sans CN;
  		font-weight: 400;
  		font-size: 26rpx;
  		line-height: 48rpx;
  		font-style: normal;
		color: #17C289;
  	}
  }

  .btngroup {
    justify-content: space-between;
    padding: 20rpx 30rpx 0 30rpx;

    .btn {
      .img {
        width: 31rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }

      .label {
        font-size: 28rpx;
        color: #333333;
      }
    }

    .diliver {
      width: 1rpx;
      height: 36rpx;
    }
  }
}

.topline {
  position: absolute;
  top: 430rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 40rpx;
  right: 40rpx;

  .leftimg {
    flex: 1;
    margin-right: 16rpx;
    height: 2px;
  }

  .linelabel {
    font-size: 36rpx;

    color: #000000;
  }

  .rightimg {
    flex: 1;
    height: 2px;
    margin-left: 16rpx;
  }
}

.secondBox {
  background: #ffffff;
  z-index: 2;
  position: absolute;
  top: 490rpx;
  height: calc(100% - 490rpx);
  margin: 0 25rpx;
  box-sizing: border-box;

  width: calc(100% - 50rpx);

  .good {
    display: inline-block;
    margin: 11rpx 0;

    background: #ffffff;
    border-radius: 16rpx;
    border: 0.5px solid #e0e0e0;
    position: relative;

    padding: 15rpx;
    width: calc(50% - 46rpx);

    &:nth-child(2n + 1) {
      margin-right: 23rpx;
    }

    .goodimg {
      width: 100%;
      height: 219rpx;
      border-radius: 8rpx;
      margin-bottom: 10rpx;
      overflow: hidden;
      position: relative;

      ::v-deep u-image {
        position: relative;
      }

      ::v-deep image {
        position: absolute;
        left: 0;
        width: 100%;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .goodtitle {
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #333333;
      line-height: 40rpx;
      margin-bottom: 12rpx;
      height: 80rpx;
    }

    .goodprice {
      font-size: 32rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
      color: #666666;
      line-height: 45rpx;

      background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      image {
        width: 20rpx;
        height: 22rpx;
        margin-right: 12rpx;
      }
    }
  }

  .demo-title {
    font-size: 28rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
  }

  .demo-price {
    font-size: 32rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 600;
    color: #666666;
    line-height: 45rpx;
    background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>
