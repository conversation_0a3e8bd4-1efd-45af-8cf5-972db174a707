<template>
  <view class="remind-list" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="处理提醒" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>

    <view class="container">
      <u-tabs :list="tabsList" lineWidth="78rpx" :itemStyle="{ width: '28%', height: '86rpx' }" @click="handleTabClick" :current="tabActive" />
      <view class="searchCont">
        <u-search v-model="keyword" @search="init" :placeholder='请输入企业名称' :showAction="false" :clearabled="false" bgColor="#FFFFFF" height="78rpx" />
        <view class="status-select" >
          <u-input v-model="statusText" :placeholder="tabActive === 0 ? '未审核' :tabActive === 1? '未验收':'巡检逾期'" readonly border="none" >
            <template #suffix>
                <u-icon name="arrow-down" @click="showStatusPicker = true" style="margin-right: 10rpx;"></u-icon>
           </template>
          </u-input>
        </view>
      </view>
      <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="getList">
        <view class="empty-status" v-if='loaded === true && dataList.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
      <view class="assetList">
        <template v-if="tabActive === 0">
          <view class="list" v-for="item in dataList" :key="item.id" @click="gotoCompanyDetail(item)">

            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn" :class="item.auditStatus">{{companystatusList.find(type=>item.auditStatus==type.value)?companystatusList.find(type=>item.auditStatus==type.value).text:'未知' }}</view>
            </view>
            <view class="row">
              <text class="label">企业性质：</text>
              <text class="value">{{ item.enterpriseNature ||'未知'}}</text>
            </view>
            <view class="row">
              <text class="label">成立时间：</text>
              <text class="value">{{ item.establishDate ||'未知' }}</text>
            </view>
          </view>
        </template>
        <template v-else-if="tabActive === 2">
          <view class="list" v-for="item in dataList" :key="item.id" @click="gotoOverdueDetail(item)">
            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn overdue" >
                {{ getOverdueTypeText(item.overdueType) }}
              </view>
        
            </view>
            <template v-if="statusValue=='certificate'">
              <view class="row">
              <text class="label">证书名称：</text>
              <text class="value">{{ item.credentialName }}</text>
            </view>
            <view class="row">
              <text class="label">证书有效期：</text>
              <text class="value">{{ item.startDate }}-{{ item.endDate }}</text>
            </view>
            </template>
            <view class="row" v-else>
              <text class="label">截止日期：</text>
              <text class="value">{{ item.deadline }}</text>
            </view>
          </view>
        </template>
        <template v-else>
          <view class="list" v-for="item in dataList" :key="item.id" :class="'status-' + item.status" @click="gotoreviewDetail(item)">
            <view class="row row-title">
              <text class="label">企业名称：</text>
              <text class="value">{{ item.enterpriseName }}</text>
              <view class="status-btn" :class="parseStatus(item.auditStatus)">{{  parseStatusText(item.auditStatus) }}</view>
            </view>
             <view class="row">
      <text class="label">复核状态：</text>
      <text class="value review-status" :class="'review-' + item.reviewStatus">{{ getCheckTypeText(item.reviewStatus) }}</text>
    </view>
            <view class="row">
              <text class="label">企业性质：</text>
              <text class="value">{{ item.enterpriseNature }}</text>
            </view>
            <view class="row">
              <text class="label">整改时间：</text>
              <text class="value">{{ item.reviewTime }}</text>
            </view>
          </view>
        </template>
      </view>
      <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && dataList.length === 0)"
      ></u-loadmore>
      </scroll-view>
    </view>
    <u-picker @close="showStatusPicker = false" :show="showStatusPicker" closeOnClickOverlay :columns="[tabActive === 0 ? companystatusList:tabActive === 1? checkStatusList:overdueTypeList]" @confirm="onStatusConfirm" @cancel="showStatusPicker = false" />
  </view>
</template>
<script setup>
import { ref, computed } from 'vue'
import { NO_MORE_IMG,LIVE_INVENTORY_BG } from '@/common/net/staticUrl';
import { onShow } from '@dcloudio/uni-app'
import { AdminService } from "../../api/anquanjianguan/adminstrator"
import { AJYService } from "../../api/anquanjianguan/companyList.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const tabsList = ref([
  { name: '企业审核',badge:{value:''}  },
  { name: '隐患验收' ,badge:{value:''} },
  { name: '逾期提醒',badge:{value:''}  }
])
const overdueTypeList = ref([
  { text: '巡检逾期', value: 'check', color: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)' },
  { text: '整改逾期', value: 'reform', color: 'linear-gradient(135deg, #FF4757 0%, #FF3838 100%)' },
  { text: '证件逾期', value: 'certificate', color: 'linear-gradient(135deg, #3742FA 0%, #2F3542 100%)' }
])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
const tabActive = ref(0)
const keyword = ref('')
const statusText = ref('未审核')
const statusValue = ref('pending')
const showStatusPicker = ref(false)
const queryParams = ref({
  pageNum: 1,
  pageSize: 6,
  enterpriseName: undefined
})
const dataList = ref([])
const companystatusList = ref([
  // 企业审核tab
  { text: '未审核', value: 'pending' },
  { text: '审核通过', value: 'approved' },
  { text: '已驳回', value: 'rejected' }
])
const checkStatusList = ref([
  // 隐患审核tab
  { text: '待审核', value: 'PENDING' },
  { text: '已通过', value: 'PASSED' },
  { text: '已驳回', value: 'REJECTED' }
])
 const checkTypeList = ref([
  { text: '重大隐患', value: 'PENDING', color: '#FF8C42' },
  { text: '已完成', value: 'COMPLETED', color: '#2ED573' },
  { text: '已移交执法', value: 'TRANSFERRED', color: '#5352ED' },
  { text: '延期整改', value: 'DELAYED', color: '#FFA726' },
])
onShow(()=>{
  initnum()
  init()
})
async function initnum(){
  AdminService.findCompanyChecklist({pageNum:1,pageSize:1,auditStatus:'pending'}).then(res => {
    if (res.success) {
      tabsList.value[0].badge.value = res.data.total
    }
  })
  AJYService.inspectReviewList({pageNum:1,pageSize:1,auditStatus:'PENDING'}).then(res => {
    if (res.success) {
      tabsList.value[1].badge.value = res.data.total
    }
  })
 const res1 =  await AdminService.findOverdueInspections({pageNum:1,pageSize:1})
 const res2 =  await AdminService.findOverdueReviews({pageNum:1,pageSize:1})
 const res3 =  await AdminService.findOverdueCredentials({pageNum:1,pageSize:1})
 tabsList.value[2].badge.value  = res1.data.total + res2.data.total + res3.data.total
//  .then(res => {
//     if (res.success) {
//       tabsList.value[2].badge.value = res.data.total
//     }
//   })
}
function getList() {
  if (loadStatus.value == 'nomore') return
  if (loadStatus.value == 'more') {
    loadStatus.value = 'loading'
  }
  queryParams.value.enterpriseName = keyword.value

  if(tabActive.value==0){
  queryParams.value.auditStatus = statusValue.value
  AdminService.findCompanyChecklist(queryParams.value)
    .then((res) => {
      console.log(res)
      if (res.success) {
        total.value = res.data.total
        if (queryParams.value.pageNum > 1) {
          dataList.value = [...dataList.value, ...res.data.records]
        } else {
          dataList.value = res.data.records
        }
        if (total.value === dataList.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.value.pageNum = queryParams.value.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
  }
  else if(tabActive.value==1){

    queryParams.value.auditStatus = statusValue.value
  AJYService.inspectReviewList(queryParams.value)
    .then((res) => {
      if (res.success) {
        total.value = res.data.total
        if (queryParams.value.pageNum > 1) {
          dataList.value = [...dataList.value, ...res.data.records]
        } else {
          dataList.value = res.data.records
        }
        if (total.value === dataList.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.value.pageNum = queryParams.value.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
  }
  else{
    // 逾期提醒：根据statusValue调用不同的接口
    let apiPromise;
    // 清理查询参数，只保留分页和企业名称
    const overdueParams = {
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      enterpriseName: queryParams.value.enterpriseName
    };
    if (statusValue.value === 'check') {
      // 巡检逾期
      apiPromise = AdminService.findOverdueInspections(overdueParams);
    }
    if (statusValue.value === 'reform') {
      // 整改逾期
      apiPromise = AdminService.findOverdueReviews(overdueParams);
    }
    if (statusValue.value === 'certificate') {
      // 证件逾期
      apiPromise = AdminService.findOverdueCredentials(overdueParams);
    } 
    apiPromise.then((res) => {
      if (res.success) {
        // 处理不同接口返回的数据，统一截止日期字段名
        let records = res.data.records || [];
        records = records.map(item => {
          let deadline = '';
          if (statusValue.value === 'check') {
            deadline = item.dueInspectDate; // 巡检逾期使用dueInspectDate
          } else if (statusValue.value === 'reform') {
            deadline = item.rectifyDeadline; // 整改逾期使用rectifyDeadline
          } else if (statusValue.value === 'certificate') {
            deadline = item.endDate; // 证件逾期使用endDate
          }

          return {
            ...item,
            deadline: deadline,
            overdueType: statusValue.value
          };
        });

        total.value = res.data.total
        if (queryParams.value.pageNum > 1) {
          dataList.value = [...dataList.value, ...records]
        } else {
          dataList.value = records
        }
        if (total.value === dataList.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.value.pageNum = queryParams.value.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
      loaded.value = true
    })

  }
}
function getOverdueTypeText(type) {
  const typeItem = overdueTypeList.value.find(item => item.value === type)
  return typeItem ? typeItem.text : '未知'
}


function gotoOverdueDetail(item){
  if(statusValue.value === 'check'){
    // uni.navigateTo({
    //   url: '/jimo/anquanjianguan/adminstrator/companySHDetail?id=' + item.enterpriseId
    // })
  }
  else if(statusValue.value === 'reform'){
    uni.navigateTo({
      url: '/jimo/anquanjianguan/adminstrator/companyChangeDetail?id=' + item.reviewId
    })
  }
  else if(statusValue.value === 'certificate'){
    const detailData = {
      enterpriseName: item.enterpriseName || '',
      enterpriseNature: item.enterpriseNature || '',
      enterpriseScale: item.enterpriseScale || '',
      industryCategory: item.industryCategory || '',
      communityName: item.communityName || '',
      supervisorName: item.supervisorName || '',
      credentialName: item.credentialName || '',
      credentialImage: item.credentialImage || '',
      startDate: item.startDate || '',
      endDate: item.endDate || '',
      overdueDays: item.overdueDays || 0,
      status: item.status || ''
    }
    uni.navigateTo({
      url: '/jimo/anquanjianguan/adminstrator/certificateDetail?detail=' + encodeURIComponent(JSON.stringify(detailData))
    })
  }
}

function getCheckTypeText(type) {
 const typeItem = checkTypeList.value.find(item => item.value === type)
  return typeItem ? typeItem.text : '未知'
}
function handleTabClick(item) {
  if(tabActive.value==item.index) return;

  tabActive.value = item.index
  // 切换tab时重置筛选项
  if (tabActive.value === 0) {
    statusText.value = companystatusList.value[0].text
    statusValue.value = companystatusList.value[0].value
  } else  if (tabActive.value === 1)  {
    statusText.value = checkStatusList.value[0].text
    statusValue.value = checkStatusList.value[0].value
  }
  else {
    statusText.value = overdueTypeList.value[0].text
    statusValue.value = overdueTypeList.value[0].value
  }
  init()
}
function init(){
  dataList.value = []
  total.value = 0
  loaded.value = false
  queryParams.value.pageNum = 1
  
  loadStatus.value = 'more'
  getList()
}
function onStatusConfirm(e) {
  statusValue.value = e.value[0].value
  statusText.value = e.value[0].text
  showStatusPicker.value = false
  init()
}
function parseStatus(statusvalue){
 return statusvalue.toLowerCase();
}
function parseStatusText(statusvalue){
    return checkStatusList.value.find(item=>item.value==statusvalue).text
  
}
function gotoCompanyDetail(item) {
  uni.navigateTo({
    url: '/jimo/anquanjianguan/adminstrator/companySHDetail?id=' + item.auditId
  })
}
function gotoreviewDetail(item) {
  uni.navigateTo({
    url: '/jimo/anquanjianguan/adminstrator/companyReviewDetail?id=' + item.reviewId
  })
}
</script>
<style lang="scss" scoped>
.remind-list {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC-Regular, PingFang-SC;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.container {
  ::v-deep .u-tabs {
    padding: 0 25rpx;
    .u-tabs__wrapper__nav__line {
      height: 8rpx !important;
      background: linear-gradient(117deg, #0CBE88 0%, #3ADB97 100%) !important;
      border-radius: 5rpx !important;
    }
  }
  .searchCont {
    height: 78rpx;
    border-radius: 39rpx;
    display: flex;
    align-items: center;
    margin: 22rpx 22rpx 0 22rpx;
    box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
    background: #fff;
    .u-search {
      flex: 2;
      margin-right: 16rpx!important;
    }
    .status-select{
        flex: 1;
    }
  }
  .scroll-style {
			/* background-color: #fff; */
			height: calc(100vh - 280rpx - env(safe-area-inset-bottom));
  .assetList {
    margin: 30rpx 16rpx 0 16rpx;
    .list {
      background: #fff;
      border-radius: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
      margin-bottom: 24rpx;
      padding: 24rpx 20rpx;
      position: relative;
      overflow: hidden;
      &.status-reject {
        border: 3rpx solid #1976FF;
        box-shadow: none;
      }
      .row-title {
        height: 71rpx;
        background: linear-gradient(270deg, #FFFFFF 0%, #E9FFF6 100%);
        display: flex;
        align-items: center;
        border-radius: 20rpx 20rpx 0 0;
        padding-left: 11rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 0;
        .label {
          font-size: 28rpx;
          color: #222;
          font-weight: 600;
        }
        .value {
          font-size: 28rpx;
          color: #0CBE88;
          font-weight: 700;
          margin-left: 12rpx;
        }
        .status-btn {
          position: absolute;
          right: 0;
          top: 0;
          font-size: 24rpx;
          border-radius: 0 20rpx 0 32rpx;
          padding: 0 24rpx 0 29rpx;
          height: 48rpx;
          line-height: 48rpx;
          z-index: 2;
          font-weight: 600;
          color: #fff;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
          transition: all 0.3s ease;

          // 企业审核状态
          &.pending {
            background: linear-gradient(135deg, #FFA726 0%, #FF8F00 100%);
            box-shadow: 0 4rpx 12rpx rgba(255, 167, 38, 0.3);
          }
          &.approved {
            background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%);
            box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
          }
          &.rejected {
            background: linear-gradient(135deg, #EF5350 0%, #F44336 100%);
            box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.3);
          }

          // 隐患验收状态
          &.passed {
            background: linear-gradient(135deg, #26C6DA 0%, #00BCD4 100%);
            box-shadow: 0 4rpx 12rpx rgba(0, 188, 212, 0.3);
          }

          // 逾期提醒状态
          &.overdue{
            background: linear-gradient(135deg, #FFA726 0%, #FF8F00 100%);
            box-shadow: 0 4rpx 12rpx rgba(255, 167, 38, 0.3);
          }
        
        }
      }
      .row {
        display: flex;
        align-items: center;
        margin-bottom: 18rpx;
        .label {
          font-size: 28rpx;
          color: #999;
          font-weight: 400;
        }
        .value {
          font-size: 30rpx;
          color: #222;
          margin-left: 12rpx;
          font-weight: 500;

          &.review-status {
            font-weight: 600;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            font-size: 28rpx;

            &.review-PENDING {
              color: #FF8C42;
              background: rgba(255, 140, 66, 0.1);
              border: 1rpx solid rgba(255, 140, 66, 0.3);
              
            }
            &.review-COMPLETED {
              color: #2ED573;
              background: rgba(46, 213, 115, 0.1);
              border: 1rpx solid rgba(46, 213, 115, 0.3);
            }
            &.review-TRANSFERRED {
              color: #5352ED;
              background: rgba(83, 82, 237, 0.1);
              border: 1rpx solid rgba(83, 82, 237, 0.3);
            }
            &.review-DELAYED {
              color: #FFA726;
              background: rgba(255, 167, 38, 0.1);
              border: 1rpx solid rgba(255, 167, 38, 0.3);
            }
            &.review-REJECTED {
              color: #FF5252;
              background: rgba(255, 82, 82, 0.1);
              border: 1rpx solid rgba(255, 82, 82, 0.3);
            }
          }
        }
      }
    }
  }
 }
}
</style>