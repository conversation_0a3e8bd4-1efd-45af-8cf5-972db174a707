import {
    request
} from '../request.js'

// 获取设备列表
export function getDeviceList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/deviceInfo/findDevicePage',
        method: 'POST',
        params,
    })
}
// 获取设备详情
export function getDetailInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/deviceInfo/findOne',
        method: 'GET',
        params,
    })
}
// 获取设备类型字典项
export function getDeviceType(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/device_type',
        method: 'GET',
        params,
    })
}
// 获取设备预警列表
export function getDeviceAlarmList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/findPage',
        method: 'POST',
        params,
    })
}
// 获取设备预警类型
export function getDeviceAlarmType(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_WARN_TYPE',
        method: 'GET',
        params,
    })
}
// 获取设备预警级别
export function getDeviceAlarmLevel(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/WARN_LEVEL',
        method: 'GET',
        params,
    })
}
// 获取设备告警详情信息
export function getDeviceAlarmDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/findOne',
        method: 'GET',
        params,
    })
}
//预警处理接口
export function deviceWarnDeal(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/update',
        method: 'POST',
        params,
    })
}

