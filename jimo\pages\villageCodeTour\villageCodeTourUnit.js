

export function hasLogin (route, params, isLogin) {
	let paramsStr = objToStr(params)
	// 如果打开时是未登录状态，则一定是扫码进入的，所以需要跳转登录页，登录成功后再跳转回来
	if (!isLogin) {
	    mainStore.login_redirect_url = `/jimo/pages/villageCodeTour/${route}?${paramsStr}`
		setTimeout(() => {
	        // #ifdef H5
	        uni.reLaunch({
	            url: '/pages/login/login?loginType=password'
	        })
	        // #endif
	        // #ifndef H5
	        uni.reLaunch({
	            url: '/pages/index/index',
	        })
	        // #endif
	    }, 100)
	    return false
	}
	return true
}

export function objToStr (obj) {
	return Object.keys(obj).map(key => { return `${key}=${obj[key]}` }).join('&')
}