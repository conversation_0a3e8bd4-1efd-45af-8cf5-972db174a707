import {
  request
} from '../request.js'

// 获取内容列表
export function getContentList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/columnContent/findContentListMobile',
        method: 'GET',
        params,
    })
}
// 匿名登录-获取内容列表
export function getContentListAnonymous(params) {
    params['uniContentType'] = 'json'
    //匿名登录
    return request({
        url: '/village/columnContent/findContentListMobileAnonymous',
        method: 'GET',
        params,
    })
}
//获取栏目信息
export function getColumnInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/column/findOne/' + params.columnId,
        method: 'GET',
        // params,
    })
}
//获取栏目信息-匿名登录
export function getColumnInfoAnonymous(params) {
    params['uniContentType'] = 'json'
    //匿名登录
    return request({
        url: '/village/column/findOneAnonymous/' + params.columnId,
        method: 'GET',
        // params,
    })
}
// 获取内容详情
export function getContentDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/columnContent/findOneMobile',
        method: 'GET',
        params,
    })
}
//获取内容详情-匿名登录
export function getContentDetailAnonymous(params) {
    params['uniContentType'] = 'json'
    //匿名登录
    return request({
        url: '/village/columnContent/findOneMobileAnonymous',
        method: 'GET',
        params,
    })
}
// 删除内容
export function deleteContent(params) {
  params['uniContentType'] = 'json'
  return request({
    url: '/village/columnContent/delete',
    method: 'POST',
    params,
  })
}
// 点赞，取消点赞
export function updateContentLike(params) {
  params['uniContentType'] = 'json'
  return request({
    url: '/village/contentUpvote/upvote',
    method: 'POST',
    params,
  })
}
// 获取所有的评论列表
export function getCommentList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/contentComment/findAllComment',
        method: 'GET',
        params,
    })
}
// 获取所有的评论列表-匿名登陆
export function getCommentListAnonymous(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/contentComment/findAllCommentAnonymous',
        method: 'GET',
        params,
    })
}
// 添加评论
export function addComment(params) {
  params['uniContentType'] = 'json'
  return request({
    url: '/village/contentComment/add',
    method: 'POST',
    params,
  })
}
// 新增一条阅读记录
export function addReadNum(params) {
  params['uniContentType'] = 'json'
  return request({
    url: '/village/readCount/count/add',
    method: 'POST',
    params,
  })
}
// 新增一条阅读记录-匿名
export function addReadNumAnonymous(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/readCount/count/addAnonymous',
        method: 'POST',
        params,
    })
}
// 删除评论
export function deleteComment(params) {
  params['uniContentType'] = 'json'
  return request({
    url: '/village/contentComment/delete',
    method: 'POST',
    params,
  })
}