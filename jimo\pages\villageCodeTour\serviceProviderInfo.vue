<!-- 服务商详情 -->
<template>
	<view class="service-provider">
		<u-navbar title="服务商认证" bgColor="#FFF"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" :style="{height: `calc(100% - ${navbarHeight})`}">
			<view class="provider-status" v-if="info.disable === '0'">已禁用</view>
			<view class="provider-status" v-else-if="info.examineType !== '2'"
				:class="status.find(o => o.val === info.examineType).class">
				{{ status.find(o => o.val === info.examineType).label }}	
			</view>
			<view class="examine-refuse" v-else-if="info.examineType === '2'">
				<view class="label">审核拒绝</view>
				<mote-lines-divide :dt="info.reason" :line="1"></mote-lines-divide>
			</view>
			
			<view class="content scrollbar-none">
				<view class="form-item" v-for="item in keyList" :key="item.key">
					<view class="label">{{ item.label }}</view>
					<view class="content-imgs" v-if="item.type === 'image'">
						<image v-for="o in info[item.key].split(',')" :key="o" :src="o" />
					</view>
					<view class="content" v-else>{{ info[item.key] }}</view>
				</view>
			</view>
		</view>
		
		<view class="edit-btn" @click="goEditServiceProvider()">
			<image :src="LABOR_RESUME_EDIT" />
		</view>
		
		<u-popup :show="show" mode="center" :safeAreaInsetBottom="false" closeable @close="show = false">
			<view class="del-body">
				<text class="tip">
					服务商权限被禁用，无法修改
				</text>
				<view class="btn-view">
					<view class="cancel" @click="show = false">取消</view>
					<view class="confirm" @click="show = false">确定</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
import { ref, reactive, nextTick } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { LABOR_RESUME_EDIT } from '@/common/net/staticUrl.js'
import { SreviceProvider } from '../../api/villageCodeTour/villageCodeTour.js'
import MoteLinesDivide from './components/mote-lines-divide.vue'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 认证状态 */
const status = ref([
	{ val: '0', label: '审核中', class: 'ing' },
	{ val: '1', label: '审核通过', class: 'ok' },
	{ val: '2', label: '审核拒绝', class: 'error' },
])
/** 服务商信息 */
const info = ref({
	examineType: ''
})
/** 显示信息 */
const keyList = ref([
	{ label: '服务商名称', key: 'serveName' },
	{ label: '服务类型', key: 'serveType' },
	{ label: '姓名', key: 'name' },
	{ label: '手机号', key: 'cellphone' },
	{ label: '服务区域', key: 'fullName' },
	{ label: '详细地址', key: 'introduceAddress' },
	// { label: '法人/经营者身份证照片', key: 'identityCard' },
	{ label: '营业执照', key: 'businessLicense', type: 'image' },
])
const show = ref(false)

onShow(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	getSreviceProviderInfo()
})
/**
 * @description 获取服务商信息
 */
const getSreviceProviderInfo = () => {
	uni.showLoading({ title: '加载中' })
	SreviceProvider.findSreviceProviderInfo().then(r => {
		if (r.data) {
			info.value = r.data
			info.value.identityCard = `${r.data.identityCardFront},${r.data.identityCardOpposite}`
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 修改服务商信息
 */
const goEditServiceProvider = () => {
	if (info.value.disable === '0') {
		show.value = true
		return
	}
	uni.navigateTo({
	    url: '/jimo/pages/villageCodeTour/serviceProviderEdit'
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}


.service-provider {
	width: 100%;
	height: 100vh;
	
	.container {
		padding: 0 20rpx;
		background: #F0F7F7;
		position: relative;
		width: 100%;
		float: left;
		
		.provider-status {
			width: 100%;
			height: 64rpx;
			border-radius: 0rpx 0rpx 10rpx 10rpx;
			text-align: center;
			line-height: 64rpx;
			background-color: #f7b2b2;
			color: rgba(245, 34, 45, 1);
			font-size: 24rpx;
			font-weight: 500;
			position: absolute;
			top: 0;
			left: 0;
			
			
			&.ing {
				background-color: rgba(255, 225, 204, 1);
				color: rgba(255, 107, 0, 1);
			}
			&.ok {
				background-color: rgba(226, 250, 227, 1);
				color: rgba(1, 189, 93, 1);
			}
			&.error {
				background-color: rgba(255, 241, 240, 1);
				color: rgba(245, 34, 45, 1);
			}
		}
		
		.examine-refuse {
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			width: 100%;
			min-height: 64rpx;
			background: #FFE1CC;
			border-radius: 0rpx 0rpx 10rpx 10rpx;
			font-size: 24rpx;
			font-weight: 500;
			position: absolute;
			top: 0;
			left: 0;
		
			.label {
				width: 120rpx;
				margin-right: 10rpx;
				color: #F5222D;
				white-space: nowrap;
			}
		}
		
		>.content {
			width: 100%;
			height: calc(100% - 88rpx);
			background: #FFFFFF;
			border-radius: 20rpx;
			padding: 27rpx 25rpx;
			margin-top: 88rpx;
			
			.form-item {
				margin-bottom: 32rpx;
				
				.label {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 28rpx;
					color: #999999;
					line-height: 40rpx;
					margin-bottom: 11rpx;
				}
				
				.content {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 32rpx;
					color: #000000;
					line-height: 45rpx;
				}
				
				.content-imgs {
					margin-top: 6rpx;
					display: flex;
					justify-content: space-between;
					
					image {
						width: 308rpx;
						height: 192rpx;
					}
				}
			}
		}
	}
	
	.edit-btn {
		position: fixed;
		bottom: 72rpx;
		right: 20rpx;
		width: 80rpx;
		height: 80rpx;
		
		image {
			width: 100%;
			height: 100%;
		}
	}
}

.del-body {
	width: 650rpx;
	text-align: center;
	padding-top: 83rpx;
	padding-bottom: 52rpx;
		
	.tip {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 32rpx;
		color: #111111;
		line-height: 55rpx;
	}
	
	.btn-view {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;
		
		view {
			width: 276rpx;
			height: 80rpx;
			border-radius: 41rpx;
			line-height: 80rpx;
		}
		
		.cancel {
			background: #FFFFFF;
			border: 2rpx solid #E5E5E5;
			margin-right: 23rpx;
			
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #555555;
		}
		
		.confirm {
			background: #0CBE88;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>