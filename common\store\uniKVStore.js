import { config } from '@/config/config.js'
const UNI_SUCCESS = 'success' //成功标志
const UNI_FAIL = 'fail' //失败标志
//键值存储相关工具类:两种类型json字符串、字符串；默认字符串
export default {
  typeList: ['JSON', 'STR', 'OBJ'],
  type: 'STR',
  resetType() {
    this.type = 'STR'
  },
  changeType(val) {
    this.type = this.typeList[val]
  },
  get(key, sync = false, _type) {
    let self = this
    if (config.storeSuffix) {
      key = `${key}${config.storeSuffix}`
    }
    if (sync) {
      try {
        const value = uni.getStorageSync(key)
        switch (_type || self.type) {
          case 'JSON':
            return JSON.parse(value)
            break
          case 'STR':
            return value
            break
          case 'OBJ':
            return JSON.parse(value)
            break
          default:
            return value
            break
        }
      } catch (e) {
        return UNI_FAIL
      }
    } else {
      return new Promise((resolve, reject) => {
        uni.getStorage({
          key: key,
          success: function (res) {
            switch (_type || self.type) {
              case 'JSON':
                resolve(JSON.parse(res.data))
                break
              case 'STR':
                resolve(res.data)
                break
              case 'OBJ':
                resolve(JSON.parse(res.data))
                break
              default:
                resolve(res.data)
                break
            }
          },
          fail: function (res) {
            reject(null)
          },
        })
      })
    }
  },
  set(key, val, sync = false, _type) {
    let self = this
    if (config.storeSuffix) {
      key = `${key}${config.storeSuffix}`
    }
    if (sync) {
      let valStr
      switch (_type || self.type) {
        case 'JSON':
          valStr = JSON.stringify(val)
          break
        case 'STR':
          valStr = val
          break
        case 'OBJ':
          valStr = JSON.stringify(val)
          break
        default:
          valStr = JSON.stringify(val)
          break
      }
      try {
        uni.setStorageSync(key, valStr)
        return 'success'
      } catch (e) {
        return 'fail'
      }
    } else {
      return new Promise((resolve, reject) => {
        let valStr
        switch (_type || self.type) {
          case 'JSON':
            valStr = JSON.stringify(val)
            break
          case 'STR':
            valStr = val
            break
          case 'OBJ':
            valStr = JSON.stringify(val)
            break
          default:
            valStr = JSON.stringify(val)
            break
        }
        uni.setStorage({
          key: key,
          data: valStr,
          success: function (res) {
            resolve('success')
          },
          fail: function (res) {
            reject(null)
          },
        })
      })
    }
  },
  remove(key, sync = false) {
    if (config.storeSuffix) {
      key = `${key}${config.storeSuffix}`
    }
    if (sync) {
      try {
        uni.removeStorageSync(key)
        return 'success'
      } catch (e) {
        return 'fail'
      }
    } else {
      return new Promise((resolve, reject) => {
        uni.removeStorage({
          key: key,
          success: function (res) {
            resolve('success')
          },
          fail: function () {
            reject(null)
          },
        })
      })
    }
  },
  clear(sync = false) {
    if (sync) {
      try {
        // 仅保留 key 为 phoneNum 和 password 的缓存，清除其他所有缓存
        const keys = uni.getStorageInfoSync().keys || [];
        keys.forEach(itemKey => {
          if (itemKey !== 'phoneNum' && itemKey !== 'password') {
            uni.removeStorageSync(itemKey);
          }
        });
        return 'success'
      } catch (e) {
        return 'fail'
      }
    } else {
      return new Promise((resolve, reject) => {
        // 仅保留 key 为 phoneNum 和 password 的缓存，清除其他所有缓存
        const keys = uni.getStorageInfoSync().keys || [];
        keys.forEach(itemKey => {
          if (itemKey !== 'phoneNum' && itemKey !== 'password') {
            uni.removeStorageSync(itemKey);
          }
        });
        resolve('success')
      })
    }
  },
  has(key, sync = false) {
    if (sync) {
      try {
        const value = uni.getStorageSync(key)
        if (value) {
          return true
        } else {
          return false
        }
      } catch (e) {
        return false
      }
    } else {
      return new Promise((resolve, reject) => {
        let val = false
        uni.getStorage({
          key: key,
          success: function (res) {
            val = res.data !== null
            resolve(val)
          },
          fail: function () {
            reject(null)
          },
        })
      })
    }
  },
}
