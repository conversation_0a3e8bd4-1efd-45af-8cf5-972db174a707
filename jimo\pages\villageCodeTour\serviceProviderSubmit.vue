<!-- 服务商认证 -->
<template>
	<u-popup :show="show" mode="bottom">
		<view class="service-provider slot-content">
			<u-navbar title="服务商" bgColor="#FFF"
				:placeholder="true" leftIconColor='#000'
				titleStyle="font-size: 36rpx;color: #000" @leftClick="handleClose()">
			</u-navbar>

			<view class="container" :style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
				<view class="w-100 h-100">
					<image class="enter-bg" :src="VILLAGE_TOUR_ENTERING_BG" />
					<view class="text1">服务商认证信息已提交</view>
					<view class="text2">管理员审核中</view>
					<view class="btn" @click="handleClose()">完成</view>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script setup>
import { ref, defineExpose, defineProps, defineEmits } from 'vue'
import { 
	VILLAGE_TOUR_SERVICE_PROVIDER_BG,
	VILLAGE_TOUR_ENTERING_BG,
} from '@/common/net/staticUrl.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
const show = ref(false) 

defineExpose({
	init
})

function init () {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	show.value = true
}

const handleClose = () => {
	uni.navigateBack()
	show.value = false
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.service-provider {
	position: fixed;
	width: 100%;
	height: 100vh;
	background: #F0F7F7;
	top: 0;
	
	.container {
		border-radius: 20rpx;
		padding: 24rpx 20rpx 20rpx;
		
		>view {
			background: #FFFFFF;
		}
		
		.enter-bg {
			width: 322rpx;
			height: 409rpx;
			margin: 121rpx 178rpx 0 210rpx;
		}
		
		.text1 {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 36rpx;
			color: #333333;
			line-height: 50rpx;
			text-align: center;
		}
		
		.text2 {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #666666;
			line-height: 40rpx;
			margin-top: 24rpx;
			text-align: center;
		}
		
		.btn {
			width: 396rpx;
			height: 80rpx;
			background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 130rpx auto 0;
		}
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}
</style>