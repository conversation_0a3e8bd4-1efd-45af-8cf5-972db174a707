<template>
	<!-- 空盒子，只是为了实现页面顶部的背景色渐变 -->
	<view class="top-bg-box" :style="dynamicStyle">
	</view>
</template>

<script setup>
	import { ref } from 'vue'
	const props = defineProps({
		// 盒子高度，只传数字就可以，不需要带单位，默认rpx
		boxHeight: {
			type: String,
			default: '470'
		},
		// 背景色
		backgroundImage: {
			type: String,
			default: 'linear-gradient(180deg, rgba(10, 189, 136, 1),rgba(73, 216, 149, 0.3),rgba(255, 255, 255, 0) )'
		},
		// 控制盒子位置是否覆盖到状态栏，true则会进行计算状态栏高度然后赋值给盒子定位的top值
		boxOffset: {
			type: Boolean,
			default: false
		}
	})
	const positionTop = ref(0) // 背景盒子距离顶部的距离
	const statusBarHeight = ref(0) // 状态栏高度
	const navBarHeight = ref(0) // 导航栏高度
	uni.getSystemInfo({
		success: (e) => {
			// #ifdef MP-WEIXIN
			statusBarHeight.value = e.statusBarHeight;
			let menuInfo = uni.getMenuButtonBoundingClientRect();
			navBarHeight.value = menuInfo.height + (menuInfo.top - e.statusBarHeight) * 2;
			// 是否需要留出状态栏高度
			if (props.boxOffset) {
				positionTop.value = statusBarHeight.value + navBarHeight.value
			}
			// #endif
			
		}
	})
	// 父组件控制的样式
	const dynamicStyle = ref({
		top: positionTop.value + 'px',
		height: props.boxHeight + 'rpx',
		backgroundImage: props.backgroundImage
	})
</script>

<style lang="scss" scoped>
	.top-bg-box {
		position: absolute;
		left: 0;
		z-index: -1;
		width: 100%;
		height: 470rpx;
		// background-image: linear-gradient(to bottom , rgba(9, 189, 135,1),rgba(10, 189, 136,0.8),rgba(73, 216, 149, 0) );
	}
</style>