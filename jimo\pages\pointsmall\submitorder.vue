<template>
  <view class="orderdetail">
    <view class="gooddetail" v-if="commoditydetail">
      <view class="img">
        <view class="imgbox">
          <image
            :src="goodDetailImg"
            mode="widthFix"
          ></image>
        </view>
      </view>
      <view class="info">
        <view class="title u-line-2">
          {{ commoditydetail.commodityTitle }}
        </view>
        <view class="priceline">
          <text class="price">{{ commoditydetail?.showAmount }}</text>
          <text class="label">积分</text>
        </view>
		 <view class="stepperBox">
			<text class="name">数量</text>
			<text :class="{'labelDisabled':stepSubDisabled, 'label':true}" @click="handleStep('sub')">-</text>
			<view class="stepInput">
				<u-input type="number" border="none" v-model="number" @blur="handleStep('blur')"></u-input>
			</view>
			<text :class="{'labelDisabled':stepAddDisabled, 'label':true}" @click="handleStep('add')">+</text>
		 </view>
      </view>
    </view>
    <view class="address" v-if="addressdetail">
      <view class="titleline"> 商品兑换点 </view>
      <view class="addressdetail">
        <view class="left">
          <view class="title">{{ addressdetail.addressName }}</view>
          <view class="addresssetting">{{ addressdetail.addressDesc }}</view>
        </view>
        <image class="phone" :src="PHONE_IMG" @click="callNumber"></image>
      </view>
    </view>
    <view class="blank"></view>
    <view class="bottom u-border-top">
      <view class="btn pjBtn" @click="goSub" v-if="canbuy">提交订单</view>
      <view class="btn graybtn" v-else>积分不足</view>
      <view class="paddingbottom"></view>
    </view>
  </view>
</template>

<script setup>
import { PHONE_IMG } from '@/common/net/staticUrl.js'
import { reactive, ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { findOne, findAddressList, getPartyAllInfo } from "../../api/pointsmall/pointsmall"
import {
  findOrderById,
  findRedeemablePoints,
  payUpdateNew,
  addOrderNew
} from  '../../api/pointsmall/hypermarketOrder'
import { loadImage } from '@/common/getImage.js'
import { useUserStore } from '@/store/user.js'
const orderId = ref('')
const userpoint = ref(0)
let orderdetail = ref({})
let commoditydetail = ref(null)
let addressdetail = ref(null)
let isdisabled = ref(false)
const goodDetailImg = ref('')
const canbuy = computed(() => {
  const amount = parseFloat(commoditydetail?.value?.showAmount)
  if (isNaN(amount)) {
    return false
  }

  if (amount === 0) {
    return true
  }
	
	if(number.value && amount && userpoint.value){
		return userpoint.value - (amount * parseFloat(number.value)) >= 0
	}
	
  return false
})
let number = ref(1)  //数量
const stepSubDisabled = computed(() => {
	if(parseFloat(number.value) > 1){
		return false
	}
	return true
})
const stepAddDisabled = computed(() => {
	if(Number(number.value)){
		return false
	}
	return true
})
let sourceType = ref('1')  //1为家庭 2为党员
const userStore = useUserStore()
const villagePeopleId = userStore?.userInfo?.customParam.peopleInfo.villagePeopleId
let commodityId = ref('')
onLoad((option) => {
  commodityId.value = option.commodityId
  sourceType.value = option?.sourceType || '1'
  getGoodDetail()
  getAddressDetail()
})
onShow(() => {
  getUserPoint()
})
async function getDetail() {
  try {
    const res = await findOrderById({ id: orderId.value })
    if (res.success) {
      let detail = res.data
      orderdetail.value = detail
      getGoodDetail()
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getGoodDetail() {
  try {
    const res = await findOne({ id: commodityId.value })
    if (res.success) {
      let detail = res.data
      detail.imgs = detail.commodityPicture.split(',')
      commoditydetail.value = detail
	  if(sourceType.value == '2'){
		  commoditydetail.value.showAmount = commoditydetail.value.integralParty
	  }else{
		  commoditydetail.value.showAmount = commoditydetail.value.integral
	  }
	  goodDetailImg.value = await loadImage(detail.commodityPicture.split(',')[0])
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getAddressDetail() {
  try {
    let res = await findAddressList()
    if (res.success) {
      if (res.data.length > 0) {
        const addresslist = res.data
        addressdetail.value = addresslist[0]
      } else {
        uni.showToast({
          title: '未设置兑换点！',
          icon: 'none',
        })
      }
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getUserPoint() {
  try {
	  if(sourceType.value == '2'){
		  let res = await getPartyAllInfo({villagePeopleId: villagePeopleId})
		  if (res.success) {
		    userpoint.value = res.data?.totalPointsRemain > 0 ? ~~res.data?.totalPointsRemain : 0
		  } else {
		    uni.showToast({
				title: res.message,
				icon: 'none',
		    })
			partyPoints.value = 0
		  }
	  }else{
		let res = await findRedeemablePoints()
		if (res.success) {
		  userpoint.value = ~~res.data
		} else {
		  uni.showToast({
			title: res.message,
			icon: 'none',
		  })
		}
	  }
    
  } catch (e) {
    uni.showToast({
      title: '获取用户积分失败！',
      icon: 'none',
    })
  }
}
async function goSub() {
  if (isdisabled.value) return
  isdisabled.value = true
  try {
	  let orderRes = await addOrderNew({ commodityId: commodityId.value, role: sourceType.value, number: number.value })
	  if(orderRes.success){
		  let res = await payUpdateNew({ orderId: orderRes.data, role: sourceType.value })
		  if (res.success) {
		    if (res.status == '0') {
		      isdisabled.value = false
		      uni.showToast({
		        title: res.message,
		        icon: 'none',
		      })
		    }
		    if (res.status == '1') {
		      uni.redirectTo({
		        url:
		          '/jimo/pages/pointsmall/paydetail?cid=' +
		          res.data.commodityId +
		          '&oid=' +
		          orderRes.data,
		        complete: () => {
		          isdisabled.value = false
		        },
		      })
		    }
		  } else {
		    isdisabled.value = false
		    uni.showToast({
		      title: res.message,
		      icon: 'none',
		    })
		  }
	  }else{
		  uni.showToast({
		    title: orderRes.message,
		    icon: 'none',
		  })
	  }
  } catch (e) {
    isdisabled.value = false
    uni.showToast({
      title: '兑换失败！',
      icon: 'none',
    })
  }
}
function makeCall() {
  uni.getSetting({
    success: (res) => {
      if (res.authSetting['scope.makePhoneCall']) {
        callNumber()
      } else {
        uni.authorize({
          scope: 'scope.makePhoneCall',
          success: () => {
            callNumber()
          },
          fail: () => {
            uni.openSetting({
              success: (res) => {
                if (res.authSetting['scope.makePhoneCall']) {
                  callNumber()
                }
              },
            })
          },
        })
      }
    },
  })
}
function callNumber() {
  uni.makePhoneCall({
    phoneNumber: addressdetail.value.linkPhone,
    success: () => {
      console.log('拨打电话成功！')
    },
    fail: () => {
      console.error('拨打电话失败！')
    },
  })
}
function handleStep(type){
	let num = parseFloat(number.value)
	let limit = parseFloat(commoditydetail.value.upperLimit) //单次购买上限
	let leaveStock = parseFloat(commoditydetail.value.leaveStock)
	if(type=='sub'){
		if(num <= 1){
			return
		}
		number.value--
	}else if(type=='add'){
		if(num >= limit){
			return uni.showToast({
				title:'超过单次购买数量',
				icon:'none'
			})
		}else if(num >= leaveStock){
			return uni.showToast({
				title:'暂无库存',
				icon:'none'
			})
		}
		number.value++
	}else if(type == 'blur'){
		if(num <= 0){
			number.value = 1
		}else if(num > limit){
			number.value = limit
		}
	}
}
</script>
<style>
page {
  background-color: white;
}
</style>
<style lang="scss" scoped>
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  //display: flex;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
  .graybtn {
    background: #bdbdbd;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 120rpx;
}
.orderdetail {
  margin: 20rpx;
  .address {
    border-radius: 8rpx;
    padding: 23rpx 20rpx;

    background-color: white;
    .titleline {
      line-height: 90rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      height: 90rpx;
    }
    .addressdetail {
      background: #f8f8f8;
      border-radius: 10px;
      width: 100%;
      padding: 26rpx 20rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      .left {
        flex: 1;
        margin-right: 66rpx;
      }
      .title {
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
        line-height: 40rpx;
        margin-bottom: 13rpx;
      }
      .addresssetting {
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
      }
      .phone {
        width: 32rpx;
        height: 33rpx;
        cursor: pointer;
      }
    }
  }
  .gooddetail {
    border-radius: 8rpx;
    padding: 23rpx 20rpx;
    display: flex;
    align-items: center;
    background-color: white;

    .img {
      min-width: 241rpx;
      height: 185rpx;
      background-color: white;
      border: 0.5px solid #ccc;
      .imgbox {
        margin: 8rpx;
        box-sizing: border-box;

        overflow: hidden;
        position: relative;
        width: calc(100% - 16rpx);
        height: calc(100% - 16rpx);

        image {
          box-sizing: border-box;
          width: 100%;
          left: 0;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .info {
      padding: 10rpx 20rpx;
      .title {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 52rpx;
        margin-bottom: 10rpx;
      }
      .priceline {
        display: flex;
        align-items: center;
        .price {
          font-size: 43rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 600;
          color: #666666;
          line-height: 61rpx;
          background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-right: 10rpx;
        }
        .label {
          font-size: 30rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #999999;
          line-height: 42rpx;
        }
      }
		.stepperBox{
			display: flex;
			align-items: center;
			justify-content: center;
			.name{
				  font-family: Source Han Sans CN, Source Han Sans CN;
				  font-weight: 400;
				  font-size: 28rpx;
				  color: #333333;
				  line-height: 40rpx;
				  font-style: normal;
				  margin-right: 20rpx;
			}
			.stepInput{
				width: 80rpx;
				height: 40rpx;
				border: 1rpx solid #E1E1E1;
				margin: 0 10rpx;
				::v-deep .u-input__content__field-wrapper__field{
					line-height: 40rpx !important;
					height: 40rpx !important;
					font-size: 28rpx !important;
					text-align: center !important;
				}
			}
			.label{
				display: block;
				width: 40rpx;
				height: 40rpx;
				border: 1rpx solid #A6A6A6;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				line-height: 40rpx;
				text-align: center;
				font-style: normal;
			}
			.labelDisabled{
				border: 1rpx solid #bbb;
				color: #bbb;
			}
		}
    }
  }
}
</style>
