<template>
  <image class="floating-btn" :src="imageUrl" :style="{...customStyle, bottom: bottomHeight}"></image>
</template>

<script setup>
  import {
    computed
  } from "vue";
  const props = defineProps({
    // 悬浮按钮图片路径，默认是加号的
    imageUrl: {
      type: String,
      default: '/static/images/plus-circle.svg'
    },
    // 自定义样式，如图片width、height、bottom、right等
    customStyle: {
      type: [String, Object],
      default: ''
    },
    extraBottom: {
      type: Number,
      default: 0
    }
  });
  const bottomHeight = computed(() => {
    return (uni.$u.sys().safeAreaInsets.bottom + 60 + props.extraBottom) + 'px'
  })
</script>

<style lang="scss" scoped>
  .floating-btn {
    display: inline-block;
    position: fixed;
    bottom: 120rpx;
    right: 60rpx;
    z-index: 99;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #fff;
    outline: none;
    margin: 0;
    transform: scale(1);
    opacity: 0.9;
    transition: .2s;
  
    :active {
      transform: scale(0.97);
      opacity: 1;
    }
  }
</style>