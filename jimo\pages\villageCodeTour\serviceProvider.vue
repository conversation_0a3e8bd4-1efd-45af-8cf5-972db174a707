<!-- 服务商 -->
<template>
	<view class="service-provider">
		<u-navbar title="服务商" bgColor="#FFF"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" v-if="info.id"
			:style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
			<view class="merchant-info" @click="goDetails()">
				<view class="merchant-name ellipsis">{{ info.serveName }}</view>
				<view class="merchant-user">{{ info.name }} {{ info.cellphone }}</view>
				<view class="merchant-address">
					<image :src="VILLAGE_TOUR_MARK" />
					{{ info.fullName }}
				</view>
				<view class="merchant-status" v-if="info.disable === '0'">已禁用</view>
				<view class="merchant-status" v-else
					:class="status.find(o => o.val === info.examineType).class">
					{{ status.find(o => o.val === info.examineType).label }}
				</view>
			</view>
			
			<view class="type-list">
				<image v-for="item in serviceTypeColumns" :key="item.text"
					:src="item.img" @click="goService(item)" />
			</view>
		</view>

		<u-popup :show="show" mode="bottom">
			<view class="service-provider-rz slot-content">
				<u-navbar title="服务商" bgColor="rgba(0, 0, 0, 0)"
					:placeholder="true" leftIconColor='#000'
					titleStyle="font-size: 36rpx;color: #000" @leftClick="handleClose()">
				</u-navbar>
				<image class="bg-image" :src="VILLAGE_TOUR_SERVICE_PROVIDER_BG" mode=""></image>
				
				<image class="enter-bg" :src="VILLAGE_TOUR_ENTER_BG" />
				
				<view class="enter-btn-view">
					<view class="btn" @click="goEditServiceProvider()">去认证</view>
					<view class="tip">完成服务商认证，使用服务商权限</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { SreviceProvider } from '../../api/villageCodeTour/villageCodeTour.js'
import { 
	VILLAGE_TOUR_MARK,
	VILLAGE_TOUR_HOTEL_HOMESTAY,
	VILLAGE_TOUR_SPECIAL_PRODUCT,
	VILLAGE_TOUR_DELICIOUS_FOOD,
	VILLAGE_TOUR_SERVICE_PROVIDER_BG,
	VILLAGE_TOUR_ENTER_BG,
} from '@/common/net/staticUrl.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 服务商信息 */
const info = ref({})
/** 认证状态 */
const status = ref([
	{ val: '0', label: '未认证', class: 'ing' },
	{ val: '1', label: '已认证', class: 'ok' },
	{ val: '2', label: '已拒绝', class: 'error' },
])
/** 服务类型列表 */
const serviceTypeColumns = ref([
	{ 
		name: '酒店民宿', img: VILLAGE_TOUR_HOTEL_HOMESTAY,
		show: false, parentKey: 'hotelshomestays'
	},
	{ 
		name: '本地特产', img: VILLAGE_TOUR_SPECIAL_PRODUCT,
		show: false, parentKey: 'localspecialties'
	},
	{ 
		name: '美食指南', img: VILLAGE_TOUR_DELICIOUS_FOOD,
		show: false, parentKey: 'food'
	}
])
/** 去认证 */
const show = ref(false) 

onShow(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	getSreviceProviderInfo()
})

/**
 * @description 获取服务商信息
 */
const getSreviceProviderInfo = () => {
	SreviceProvider.findSreviceProviderInfo().then(r => {
		if (r.data) {
			info.value = r.data
			const serviceTypes = r.data.serveType.split(',')
			serviceTypeColumns.value = serviceTypeColumns.value.filter(item => {
				return serviceTypes.findIndex(o => o === item.name) !== -1
			})
			console.log('----------')
		} else {
			show.value = true
		}
	})
}
/**
 * @description 去服务商详情
 */
const goDetails = () => {
	uni.navigateTo({
	    url: `/jimo/pages/villageCodeTour/serviceProviderInfo`
	})
}
/**
 * @description 去服务类
 * @param item
 */
const goService = (item) => {
	const { disable, examineType } = info.value
	uni.navigateTo({
	    url: `/jimo/pages/villageCodeTour/serviceClassList` + 
			`?parentKey=${item.parentKey}&disable=${info.value.disable}&examineType=${info.value.examineType}`
	})
}
/**
 * @description 新增服务商信息
 */
const goEditServiceProvider = () => {
	uni.navigateTo({
	    url: '/jimo/pages/villageCodeTour/serviceProviderEdit'
	}).finally(() => {
		show.value = false
	})
}
/**
 * @description 关闭申请
 */
const handleClose = () => {
	show.value = false
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.service-provider {
	width: 100%;
	height: 100vh;
	background: #F0F7F7;

	.container {
		padding: 24rpx 20rpx;
		
		.merchant-info {
			width: 100%;
			min-height: 212rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			padding: 20rpx 25rpx;
			position: relative;
			
			.merchant-name {
				width: 312rpx;
				height: 60rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				font-size: 32rpx;
				color: #000000;
				line-height: 60rpx;
			}
			
			.merchant-user {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #000000;
				line-height: 40rpx;
				margin-top: 17rpx;
			}
			
			.merchant-address {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #999999;
				line-height: 37rpx;
				margin-top: 8rpx;
				display: flex;
				align-items: center;
				
				>image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
				}
			}
			
			.merchant-status {
				width: 128rpx;
				height: 56rpx;
				border-radius: 0rpx 8rpx 0rpx 26rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 28rpx;
				padding: 7rpx 19rpx 9rpx 25rpx;
				background-color: rgba(255, 241, 240, 1);
				color: rgba(245, 34, 45, 1);
				position: absolute;
				right: 0;
				top: 0;
				
				&.ing {
					background-color: rgba(255, 225, 204, 1);
					color: rgba(255, 107, 0, 1);
				}
				&.ok {
					background-color: rgba(226, 250, 227, 1);
					color: rgba(1, 189, 93, 1);
				}
				&.error {
					background-color: rgba(255, 241, 240, 1);
					color: rgba(245, 34, 45, 1);
				}
			}
		}
	
		.type-list {
			margin-top: 24rpx;
			
			>image {
				width: calc((100% - 20rpx) / 2);
				height: 137rpx;
				margin-right: 20rpx;
				margin-bottom: 7rpx;
				
				&:nth-child(2) {
					margin-right: 0;
				}
			}
		}
	}
}

.service-provider-rz {
	position: fixed;
	width: 100%;
	height: 100vh;
	background: #F4F8F7;
	top: 0;
	
	.bg-image {
		width: 100%;
		height: calc(358rpx + env(safe-area-inset-top));
		position: fixed;
		top: 0;
		left: 0;
		z-index: -1;
	}
	
	.enter-bg {
		width: 450rpx;
		height: 714rpx;
		position: fixed;
		bottom: calc(398rpx + env(safe-area-inset-bottom));
		left: 166rpx;
	}
	
	.enter-btn-view {
		position: fixed;
		bottom: 0;
		left: 0;
		text-align: center;
		padding-bottom: calc(57rpx + env(safe-area-inset-bottom));
		width: 100%;
		
		.btn {
			width: 480rpx;
			height: 80rpx;
			background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto;
		}
		
		.tip {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #0BBD88;
			line-height: 33rpx;
			margin-top: 24rpx;
		}
	}
}



.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>