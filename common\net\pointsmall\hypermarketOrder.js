import {
    request
} from '../request.js';

export function findOrderById(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/findById",
      method: "GET",
      params
    });
  }
  export function findRedeemablePoints(){
    let params = {}
    params['uniContentType'] = 'json'
    return request({
      url: "/user/tbFamilyPoints/points/findRedeemablePoints",
      method: "POST",
      params
    });
  }
  export function findOrderPage(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/findOfPageApp",
      method: "POST",
      params
    });
  }
  export function addOrder(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/add",
      method: "POST",
      params
    });
  }
  export function payUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/payUpdate",
      method: "POST",
      params
    });
  }
  export function refundUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/refundUpdate",
      method: "POST",
      params
    });
  }

  export function exchangeUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/exchangeUpdate",
      method: "POST",
      params
    });
  }