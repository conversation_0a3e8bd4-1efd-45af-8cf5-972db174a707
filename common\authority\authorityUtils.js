import permision from '@/common/permission.js'

// 获取麦克风权限
async function checkMicrophonePermission() {
  let status = permision.isIOS
    ? await permision.requestIOS('record')
    : await permision.requestAndroid('android.permission.RECORD_AUDIO')
  if (status === null || status === 1) {
    status = 1
  } else if (status === 2) {
    uni.showModal({
      content: '系统麦克风已关闭',
      confirmText: '确定',
      showCancel: false,
      success: function (res) {},
    })
  } else {
    uni.showModal({
      content: '需要麦克风权限',
      confirmText: '设置',
      success: function (res) {
        if (res.confirm) {
          permision.gotoAppSetting()
        }
      },
    })
  }
  return status
}

// 获取相机权限
async function checkPermission(code) {
  let status = permision.isIOS
    ? await permision.requestIOS('camera')
    : await permision.requestAndroid('android.permission.CAMERA')
  if (status === null || status === 1) {
    status = 1
  } else {
    uni.showModal({
      content: '需要相机权限',
      confirmText: '设置',
      success: function (res) {
        if (res.confirm) {
          permision.gotoAppSetting()
        }
      },
    })
  }
  return status
}

export { checkMicrophonePermission, checkPermission }
