<template>
	<view style="position: relative">
		<view class="navbar">
			<view v-for="(item, index) in navList" :key="index" class="nav-item"
				:class="{ current: tabCurrentIndex == index }" @click="tabClick(index)">
				{{ item.text }}
			</view>
		</view>
		
		<u-tabs :list="tabsList" :itemStyle="itemStyle" lineWidth="78rpx" @click="handleTabClick" />
		
		<swiper :current="tabCurrentIndex" class="swiper-box" :duration="300" @change="ontabchange">
			<swiper-item class="swiper-item" v-for="(tab, index1) in list" :key="index1">
				<scroll-view class="list-scroll-content u-flex u-flex-col" scroll-y @scrolltolower="loadMore(index1)"
					refresher-enabled="true" @refresherpulling="scrollPull(index1)"
					@refresherrefresh="scrollRefresh(index1)" refresher-background="#F8F9F9"
					:refresher-triggered="triggered" :refresher-threshold="150" @refresherabort="onAbort"
					@refresherrestore="refresherrestore">
					<!-- <u-empty
						:mode="'data'"
						class="emptyclass"
						v-if="tab.loadingType=='nomore'&&tab.data.length == 0"
					></u-empty> -->
					<view class="empty-status" v-if="tab.loadingType=='nomore' && tab.data.length === 0">
						<view class="empty-icon">
							<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
								height="150rpx"></u-image>
							<view class="no-more">
								<span class="txt">暂无数据</span>
							</view>
						</view>
					</view>
					<view class="" v-else>
						<view v-for="(item, index) in tab.data" :key="index" @click="getdetail(item)" class="item">
							<view class="tag" :class="getTagClass(item)">{{getStatusText(item)}}</view>
							<view class="title u-line-1">{{item.missionName}}</view>
							<view class="desc">
								<view class="u-line-1">工作要求：{{item.missionContent}}</view>
								<view class="u-line-1">工作类型：{{selectDictLabel(typelist,item.missionType)}}</view>
							</view>
							<view class="editbtn" @click.stop="gotoAdd('edit',item.missionId)"
								v-show="tabCurrentIndex == 0">编辑</view>
						</view>
					</view>
					<u-loadmore :status="tab.loadingType"
						v-if="!(tab.loadingType === 'nomore' && tab.data.length === 0)"></u-loadmore>
				</scroll-view>
			</swiper-item>
		</swiper>
		<view class="fab" @click="gotoAdd('add')" v-show="tabCurrentIndex==0">
			<image :src="NEW_EVENT" mode=""></image>
		</view>
	</view>
</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app'
import { reactive, ref } from 'vue'
import { NEW_EVENT, NO_MORE_IMG } from '@/common/net/staticUrl';
import { getInfoList } from '../../api/eventPublication/eventPublication.js'
import { getDicts } from '@/common/net/contacts/contacts.js'

const navList = ref([{
		text: '我下发的',

	},
	{
		text: '同事下发',
	},
])
const tabCurrentIndex = ref(0)
/** 标签页选择 */
const tabActive = ref(0)
/** 标签页数据 */
const tabsList = ref([
	{ name: '待完成' }, 
	{ name: '已完成' }, 
	{ name: '已逾期' }, 
	{ name: '逾期完成' },
	{ name: '全部' },
])
/** 标签页样式 */
const itemStyle = ref({
	width: '25%', 
	height: '86rpx',
	fontWeight: '500',
	fontSize: '32rpx',
	background: 'none'
})
const list = ref([])
const pageSize = ref(8)
const triggered = ref(false)
const typelist = ref([])
const submiting = ref(false)

onLoad((option) => {
	let arr = []
	navList.value.forEach((tabBar, index) => {
		arr.push({
			pageNum: 1,
			total: 0,
			data: [],
			refreshing: false,
			loadingType: 'more',
		})
	})
	list.value = arr
	getDicts("task_type").then((response) => {
		typelist.value = response.data;
	});
})

onShow(() => {
	refreshData(tabCurrentIndex.value)
})

/**
 * @description 标签页点击回调
 * @param {type} item 
 */
const handleTabClick = (item) => {
	if (item.index === 4) {
		tabActive.value = ''
	} else {
		tabActive.value = item.index
	}
	
	refreshData(tabCurrentIndex.value)
}

function getTagClass(item) {
	if (item.missionStatus == 0) {
		return ''
	}
	if (item.missionStatus == 1 || item.missionStatus == 3) {
		return 'wctag'
	}
	if (item.missionStatus == 2) {
		return 'yqtag'
	}
}

function getStatusText(item) {
	if (item.missionStatus == 0) {
		return '待完成'
	}
	if (item.missionStatus == 1) {
		return '已完成'
	}
	if (item.missionStatus == 2) {
		return '已逾期'
	}
	if (item.missionStatus == 3) {
		return '逾期完成'
	}
}

function selectDictLabel(datas, value) {
	var actions = [];
	Object.keys(datas).some((key) => {
		if (datas[key].dictValue == ('' + value)) {
			actions.push(datas[key].dictLabel);
			return true;
		}
	})
	return actions.join('');
}

function tabClick(index) {
	tabCurrentIndex.value = index
	refreshData(tabCurrentIndex.value)
}

function ontabchange(e) {
	let index = e.target.current || e.detail.current
	switchTab(index)
}

function switchTab(index) {
	if (tabCurrentIndex.value === index) {
		return;
	}
	if (list.value[index].data.length === 0) {
		list.value[index].total = 0
		list.value[index].pageNum = 1
		list.value[index].loadingType = 'more'
		getList(index);
	}

	tabCurrentIndex.value = index

}
async function getList() {
	const index = tabCurrentIndex.value
	if (list.value[index].loadingType == 'nomore') return;
	if (list.value[index].loadingType == 'more') {
		list.value[index].loadingType = 'loading'
	}
	let pageNum = list.value[index].pageNum


	let res = await getInfoList({
		pageNum,
		pageSize: pageSize.value,
		missionFlag: index,
		missionStatus: tabActive.value
	})


	list.value[index].data = [...list.value[index].data, ...res.data.records]

	if (list.value[index].data.length === res.data.total) {
		list.value[index].loadingType = 'nomore'
	} else {
		list.value[index].loadingType = 'more'
	}
	//给不同的swiper赋值total
	list.value[index].total = res.data.total
}

function loadMore(e) {
	let pageNum = list.value[e].pageNum
	let total = list.value[e].total
	if (pageNum * pageSize.value >= total) {
		list.value[e].loadingText = 'nomore'
		return
	} else {
		list.value[e].pageNum = list.value[e].pageNum + 1
		getList(e)
	}
}

function refreshData(e) {
	list.value[e].data = []
	list.value[e].total = 0
	list.value[e].pageNum = 1
	list.value[e].loadingType = 'more'
	getList(e)
}

function scrollPull(e) {
	var tab = list.value[e]
	if (tab.refreshing) {
		return
	}
}

function scrollRefresh(e) {
	var tab = list.value[e]
	tab.refreshing = true
	triggered.value = true
	setTimeout(() => {
		if (e == tabCurrentIndex.value) {
			refreshData(tabCurrentIndex.value)
		}
		tab.refreshing = false
		triggered.value = false
	}, 2000)
}

function refresherrestore() {
	triggered.value = false
}

function onAbort() {
	console.log('终止了')
}

function gotoAdd(flag, id = '') {
	let idstr = ''
	if (id) {
		idstr = "&id=" + id
	}
	uni.navigateTo({
		url: './addNew?flag=' + flag + idstr,
	})
}

function getdetail(item) {
	uni.navigateTo({
		url: './detail?id=' + item.missionId + "&flag=" + tabCurrentIndex.value
	})
}
</script>

<style>
	page {
		background-color: #F8F8FA;
	}
</style>

<style lang="scss" scoped>
::v-deep.u-tabs {
	position: fixed;
	top: 80rpx;
	width: 100%;
	
	.u-tabs__wrapper__nav__line {
		// width: 78rpx !important;
		height: 8rpx !important;
		background: linear-gradient(117deg, #0CBE88 0%, #3ADB97 100%) !important;
		border-radius: 5rpx !important;
	}
}
.empty-status {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 50rpx 0;

	.empty-icon {
		display: flex;
		flex-direction: column;
		justify-content: center;

		.no-more {
			margin-top: 20rpx;
			display: flex;
			justify-content: center;
			margin-top: 10rpx;

			.txt {
				text-align: center;
				height: 37rpx;
				font-size: 26rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #333333;
				line-height: 37rpx;
			}
		}
	}
}

::v-deep .u-loadmore {
	padding-bottom: 60rpx;
}

.navbar {
	position: fixed;
	width: 100%;
	left: 0;
	top: var(--window-top);
	display: flex;
	height: 80rpx;
	background: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
	z-index: 10;

	.nav-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 32rpx;
		color: #333;
		position: relative;

		&.current {
			color: rgba(11, 189, 136, 1);

			&:after {
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				width: 43rpx;
				height: 0;
				border-bottom: 6rpx solid rgba(11, 189, 136, 1);
			}
		}
	}
}

.swiper-box {
	position: absolute;
	bottom: 0;
	width: 100%;
	top: 160rpx;
	height: calc(100vh - 80rpx);
}

.list-scroll-content {
	height: 100%;
	width: 100%;

	.item {
		margin: 20rpx;
		background: #ffffff;
		box-shadow: 0px 4rpx 18rpx 0px rgba(0, 0, 0, 0.12);
		border-radius: 8rpx;
		padding: 19rpx 8rpx 22rpx 26rpx;
		position: relative;

		.title {
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #000000;
			line-height: 60rpx;
			width: 82%;
		}

		.desc {
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #666666;
			line-height: 56rpx;
			width: 82%;
		}

		.tag {
			position: absolute;
			top: 0;
			right: 0;
			width: 128rpx;
			height: 56rpx;
			background: rgba(255, 200, 168, 0.4);
			border-radius: 0 8rpx 0 26rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			text-align: center;
			color: #FF6D1C;
			line-height: 56rpx;
		}

		.wctag {
			background: rgba(180, 242, 183, 0.4);
			color: rgba(1, 189, 93, 1);
		}

		.yqtag {
			background-color: rgba(242, 180, 180, 0.38);
			color: #E90000;
		}

		.editbtn {
			position: absolute;
			bottom: 22rpx;
			right: 8rpx;
			width: 108rpx;
			height: 50rpx;
			line-height: 50rpx;
			font-size: 24rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #0BBD88;
			text-align: center;
			border-radius: 25rpx;
			border: 0.5px solid rgba(11, 189, 136, 1);
		}
	}
}

.fab {
	position: fixed;
	bottom: calc(var(--window-bottom) + 57rpx);
	right: 60rpx;
	cursor: pointer;
	z-index: 99;

	image {
		width: 80rpx;
		height: 80rpx;
	}
}
</style>