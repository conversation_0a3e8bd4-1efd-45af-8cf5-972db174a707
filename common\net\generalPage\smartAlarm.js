import {
    request
} from '../request.js'

//查询告警类型字典
export function smartAlarm() {
    return request({
      url: "/user/dict/data/list/smart_alarm",
      method: "GET"
    });
}
//获取告警列表（分页）
export function findPage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/smartAlarm/findPage',
        method: "POST",
        params,
    });
}
//处理告警
export function update(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/smartAlarm/update',
        method: "POST",
        params,
    });
}
//获取详情
export function findInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/smartAlarm/findInfo',
        method: 'GET',
        params,
    })
}
