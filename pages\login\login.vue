<template>
  <view class="login">
    <image class="bgimg" src="@/jimo/static/images/loginbg.png"></image>
    <!-- <view class="close-icon" :style="{'top':topIconDistance +'px'}" @click="skip2home"><u-icon name="close"
                color="#000" size="60rpx"></u-icon></view> -->
    <view class="container">
      <view class="center">
        <view class="title">{{ loginTitle }}</view>
        <u--form
          labelPosition="left"
          :model="data.form"
          :rules="pwdRules"
          ref="uForm"
        >
          <u-form-item prop="phoneNum">
            <view class="itemBox">
              <u--input
                v-model="data.form.phoneNum"
                border="bottom"
                placeholder="请输入手机号"
                placeholderStyle="font-size:14px;"
                maxlength="11"
                :customStyle="inputCustomStyle"
                prefixIconStyle="font-size: 22px;color: rgb(84, 115, 232)"
              ></u--input>
            </view>
          </u-form-item>
          <u-form-item prop="password">
            <view class="itemBox">
              <u-input
                v-model="data.form.password"
                border="bottom"
                :password="isPassword"
                placeholder="请输入密码"
                placeholderStyle="font-size:14px;"
                :customStyle="inputCustomStyle"
                prefixIconStyle="font-size: 22px;color: rgb(84, 115, 232)"
              >
                <template #suffix>
                  <view @click="showPwd">
                    <u-icon v-if="isPassword" name="eye-off" size="21"></u-icon>
                    <u-icon v-else name="eye-fill" size="20"></u-icon>
                  </view>
                </template>
              </u-input>
            </view>
          </u-form-item>
          <u-form-item prop="imgCaptcha" class="formItem">
            <view class="itemBox">
              <u-input
                v-model="data.form.imgCaptcha"
                border="bottom"
                placeholder="请输入图形验证码"
                placeholderStyle="font-size:14px;"
                :customStyle="inputCustomStyle"
              ></u-input>
              <u--image
                class="verificationImgs"
                width="70px"
                height="35px"
                mode="scaleToFill"
                :src="data.captchaUrl"
                @click="refreshCaptcha"
              ></u--image>
            </view>
          </u-form-item>
          <view class="find-pwd">
            <text @click.stop.prevent="skipForgetPwd">找回密码</text>
          </view>
        </u--form>
      </view>
      <view class="btnBox">
        <view class="loginBtn">
          <!-- 微信小程序必须使用customStyle设置样式 -->
          <u-button
            type="primary"
            @click="submit"
            shape="circle"
            text="登录"
            size="large"
            :customStyle="data.customStyle"
          ></u-button>
        </view>
      </view>
      <view class="agreement">
        <view class="agree-box" @click="clickCheckbox">
          <u-checkbox-group @change="checkboxChange">
            <u-checkbox
              shape="circle"
              :checked="isAgreement"
              size="28rpx"
              activeColor="#0CBE88"
            />
          </u-checkbox-group>
        </view>
        <view class="" style="display: flex; align-items: center">
          <view class="privacyAndAgreement">
            登录即同意
            <text class="sys-text" @click.stop.prevent="toAgreement"
              >《用户协议》</text
            >
            和
            <text class="sys-text" @click.stop.prevent="toPrivacy"
              >《隐私政策》</text
            >
          </view>
        </view>
      </view>
      <!-- 快捷登录 -->
      <!-- <view class="bottom">
				<view class="fastLogin">
					<view class="line"></view>
					<view class="text">快捷登录</view>
					<view class="line"></view>
				</view>
				<image src="../../static/images/weixin.png" mode="scaleToFill" />
				<view class="text">微信一键授权登录</view>
			</view> -->
    </view>
    <u-modal
      :show="showModal"
      closeOnClickOverlay
      :showConfirmButton="false"
      :showCancelButton="false"
      @close="showModal = false"
    >
      <view class="privacyAndAgreement modal-content">
        请阅读并同意
        <text class="sys-text" @click.stop.prevent="toAgreement"
          >《用户协议》</text
        >
        和
        <text class="sys-text" @click.stop.prevent="toPrivacy"
          >《隐私政策》</text
        >
      </view>
      <view class="btn-bar">
        <view class="disAgree" @click="cancelSubmit"> 不同意 </view>
        <view class="agree" @click="confirmSubmit"> 同意并继续 </view>
      </view>
    </u-modal>
  </view>
</template>

<script setup>
    import {
        ref,
        reactive,
        computed,
        onMounted
    } from 'vue'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        useMainStore
    } from '@/store/index.js'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        useTabBarStore
    } from '@/store/tabbar.js'
    import {
        imageUrl
    } from '@/common/net/request.js'
    import {
        config
    } from '@/config/config.js'
    import {
        doCrypt
    } from '@/common/secure/sm/sm2Encryptor.js'
    import {
        onReady,
        onLoad
    } from '@dcloudio/uni-app'
    import {
        findInfo
    } from '@/common/net/account/account.js'
    import Platform from '@/common/platform/ePlatform'
    import {
        getUserInfo,
        loginUseSms,
        loginUsePass,
        dingLogin,
        getTrarenInfo,
        commonResources,
        jpushUser,
        dingAuthLogin,
        getVerificationCode,
    } from '@/common/api.js'
    import kvStore from '@/common/store/uniKVStore.js'
    import {
        DEFAULT_AVATOR_IMAGES
    } from '@/common/net/staticUrl.js'
    import {
        randomStr
    } from '@/common/confound.js'
    import { Base64 } from 'js-base64';
import { encode, decode } from 'js-base64';

    const data = reactive({
        form: {
            // userName: '',
            password: '',
            // phoneNum: '***********',
            phoneNum: '',
            captcha: '',
            imgCaptcha: '',
            token: 100000 + Math.floor(Math.random() * 900000),
        },
        customStyle: {
            background: 'linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%)',
            borderColor: 'unset'
        },
        captchaUrl: '',
        captchaApi: imageUrl,
    })
    // 状态store
    const tokenStore = useTokenStore()
    const mainStore = useMainStore()
    const userStore = useUserStore()
    const tabBarStore = useTabBarStore()
    // 标题
    const loginTitle = computed(() => {

            return "密码登录"

    })

    const loginType = ref('')
    onLoad((options) => {
        // findData()

        	loginType.value = "password";

            let username = uni.getStorageSync('phoneNum')
           if(username){
              data.form.phoneNum = username
              data.form.password = decode(uni.getStorageSync('password'))// base64解密
           }
    })
    onMounted(() => {

         uForm.value.setRules(pwdRules)

        getTopPosition()
    })
    // 顶部背景图片
    var login_background_image = ref()
    // getBackgroundImage()
    //动态获取首页标题等内容
    function getBackgroundImage() {
        commonResources({
            configCodes: ['ydmh_loginBackground', 'ydmh_loginTitle'],
            clientType: '2',
            tenantId: config.tenantId_global,
        }).then((res) => {
            console.log('首页动态获取内容', res)
            if (res && res.success) {
                let resources = res.data
                resources.forEach((item, index) => {
                    if (item.configCode == 'ydmh_loginTitle') {
                        loginTitle.value = item.configValue
                    } else if (item.configCode == 'ydmh_loginBackground') {
                        login_background_image.value = item.configValue
                    }
                })
            } else {
                // 默认值
                loginTitle.value = config.LOGIN_TITLE
                login_background_image.value = config.LOGIN_BGIMG
            }
        })
    }
    // 跳过登录，以匿名用户访问首页
    function skip2home() {
        kvStore.set('hasLogin', 0)
		mainStore.isSkipLogin = true
        uni.reLaunch({
            url: '/pages/home/<USER>',
        })
    }
    // 获取关闭图标的位置
    const topIconDistance = ref(0) // 关闭按钮距顶部距离
    function getTopPosition() {
        //获取状态栏高度
        const statusBarHeight = uni.getSystemInfoSync().statusBarHeight
        // #ifdef MP-WEIXIN
        // 获取导航栏的高度（手机状态栏高度 + 胶囊高度 + 胶囊的上下间距）
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2
        // 计算顶部图标距离
        // topIconDistance.value = statusBarHeight + navBarHeight;
        topIconDistance.value = menuButtonInfo.top;
        // #endif
        // #ifdef APP-PLUS
        topIconDistance.value = statusBarHeight + 44;
        // #endif
    }
    // 找回密码
    function skipForgetPwd() {
        uni.navigateTo({
            url: '/pages/generalPage/forgetPwd/forgetPwd?userName=' + data.form.userName,
        })
    }
    // 验证码登录的表单验证规则
    const captchaRules = reactive({
        phoneNum: [{
            type: 'number',
            required: true,
            message: '请填写手机号',
            trigger: ['blur'],
        }, {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
                // 上面有说，返回true表示校验通过，返回false表示不通过
                // uni.$u.test.mobile()就是返回true或者false的
                return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
        }],
        captcha: {
            type: 'string',
            required: true,
            message: '请填写手机验证码',
            // trigger: ['blur', 'change'],
            trigger: ['blur'],
        },
    })
    // 密码登录的表单验证规则
    const pwdRules = reactive({
        phoneNum: [{
            type: 'number',
            required: true,
            message: '请填写手机号',
            trigger: ['blur'],
        }, {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
                // 上面有说，返回true表示校验通过，返回false表示不通过
                // uni.$u.test.mobile()就是返回true或者false的
                return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
        }],
        password: [{
                type: 'string',
                required: true,
                message: '请填写密码',
                trigger: ['blur', 'change'],
            },
            {
                validator: (rule, value, callback) => {
                    // 上面有说，返回true表示校验通过，返回false表示不通过
                    // uni.$u.test.mobile()就是返回true或者false的
                    // return uni.$u.test.mobile(value);
                    if (value.length >= 8 && value.length <= 20) {
                        return true
                    } else {
                        return false
                    }
                },
                message: '密码长度在8~20位之间',
                // 触发器可以同时用blur和change
                trigger: ['change', 'blur'],
            },
        ],
        imgCaptcha: {
            type: 'string',
            required: true,
            message: '请填写图形验证码',
            trigger: ['blur', 'change'],
        },
    })
    // #ifdef APP-PLUS
    if (config.isText_tenantId) {
        const rules = reactive({
            userName: {
                type: 'string',
                required: true,
                message: '请填写用户名',
                trigger: ['blur', 'change'],
            },
            password: [{
                    type: 'string',
                    required: true,
                    message: '请填写密码',
                    trigger: ['blur', 'change'],
                },
                {
                    validator: (rule, value, callback) => {
                        // 上面有说，返回true表示校验通过，返回false表示不通过
                        // uni.$u.test.mobile()就是返回true或者false的
                        // return uni.$u.test.mobile(value);
                        if (value.length >= 8 && value.length <= 20) {
                            return true
                        } else {
                            return false
                        }
                    },
                    message: '密码长度在8~20位之间',
                    // 触发器可以同时用blur和change
                    trigger: ['change', 'blur'],
                },
            ],
            captcha: {
                type: 'string',
                required: true,
                message: '请填写图形验证码',
                trigger: ['blur', 'change'],
            },
        })
    } else {
        const rules = reactive({
            userName: {
                type: 'string',
                required: true,
                message: '请填写用户名',
                trigger: ['blur', 'change'],
            },
            password: [{
                    type: 'string',
                    required: true,
                    message: '请填写密码',
                    trigger: ['blur', 'change'],
                },
                {
                    validator: (rule, value, callback) => {
                        // 上面有说，返回true表示校验通过，返回false表示不通过
                        // uni.$u.test.mobile()就是返回true或者false的
                        // return uni.$u.test.mobile(value);
                        if (value.length >= 8 && value.length <= 20) {
                            return true
                        } else {
                            return false
                        }
                    },
                    message: '密码长度在8~20位之间',
                    // 触发器可以同时用blur和change
                    trigger: ['change', 'blur'],
                },
            ],
            captcha: {
                type: 'string',
                required: true,
                message: '请填写手机验证码',
                // trigger: ['blur', 'change'],
                trigger: ['blur'],
            },
        })
    }
    // #endif

    const uForm = ref()

    function submit() {
        uForm.value.validate().then((res) => {
            if (res) {
                if (!isAgreement.value) {
                    showModal.value = true;
                    return;
                }
                //校验通过
                uni.showLoading({
                    title: '登录中',
                    mask: true,
                })

                    // 密码登录
                    loginUsePwd();

            } else {
                return
            }
        })
    }
    // 使用验证码登录
    function loginUseCaptcha() {
        loginUseSms({
                mobile: data.form.phoneNum,
                smscode: data.form.captcha,
            })
            .then((res) => {
                if (res && res.success) {
                    const result = res.data
                    //APP信息持久化
                    // #ifdef APP-PLUS
                    try {
                        let tokenInfo = kvStore.get('tokenInfo', true, 'OBJ')
                        console.log('tokenInfo', tokenInfo)
                        kvStore.remove('tokenInfo', true)
                        kvStore.set('tokenInfo', result, false, 'OBJ')
                    } catch (e) {
                        //不存在信息 存储新用户的信息
                        console.log('不存在信息', typeof result)
                        kvStore.set('tokenInfo', result, true, 'OBJ')
                    }
                    keepUserInfo(result)
                    // 设置别名
                    // jpushModule.setAlias({
                    // 	alias: result.additionalInformation.customParam.tenantId,
                    // 	sequence: 1,
                    // })
                    // #endif
                    // #ifndef APP-PLUS
                    // uni.clearStorage({
                    // 	success: (r) => {
                    // 		keepUserInfo(result)
                    // 	},
                    // 	fail: (error) => {},
                    // })
                    keepUserInfo(result)
                    // #endif
                    // 保存登录状态，用于拦截器放行 0:未登录 1:已登录
                    kvStore.set('hasLogin', 1)
					// 判断token是否已失效跳转，临时方案 0未失效，1已失效
					kvStore.set('isTokenInvalid', 0)
                    // 登录成功后移除路由拦截器
                    uni.removeInterceptor('navigateTo')
                    uni.removeInterceptor('switchTab')
                    uni.removeInterceptor('reLaunch')
                } else {
                    uni.showToast({
                        title: res.message || "登录失败",
                        icon: 'none',
                        duration: 2000
                    })
                }
            })
            .catch((err) => {
                uni.hideLoading()
                console.log('err123123', err);
                uni.showToast({
                    title: err || "登录失败",
                    icon: 'none',
                    duration: 2000
                })
                // 验证码错误或过期刷新验证码
                refreshCaptcha()
            })
    }

    // 使用密码登录
    function loginUsePwd() {
        let password = encode(data.form.password) // base64加密
             uni.setStorageSync('phoneNum', data.form.phoneNum);
             uni.setStorageSync('password', password);
        const params = {
            username: data.form.phoneNum,
            password: doCrypt(data.form.password),
            // password: doCrypt('f7QA@D_7qxAVGsmWoTLs'),
			captcha: data.form.imgCaptcha,
			token: data.form.token,
            source: "app", // 固定参数 不能改
            pgp: randomStr()
        }
        loginUsePass(params)
            .then(async(res) => {
                if (res && res.success) {
                    const result = res.data
                    //APP信息持久化
                    // #ifdef APP-PLUS
                    try {
                        let tokenInfo = kvStore.get('tokenInfo', true, 'OBJ')
                        console.log('tokenInfo', tokenInfo)
                        kvStore.remove('tokenInfo', true)
                        kvStore.set('tokenInfo', result, false, 'OBJ')
                    } catch (e) {
                        //不存在信息 存储新用户的信息
                        console.log('不存在信息', typeof result)
                        kvStore.set('tokenInfo', result, true, 'OBJ')
                    }
                    keepUserInfo(result)
                    // 设置别名
                    // jpushModule.setAlias({
                    // 	alias: result.additionalInformation.customParam.tenantId,
                    // 	sequence: 1,
                    // })
                    // #endif
                    // #ifndef APP-PLUS
                     // 仅保留 key 为 phoneNum 和 password 的缓存，清除其他所有缓存
        const keys = uni.getStorageInfoSync().keys || [];
        for(let itemKey of keys){
          if (itemKey !== 'phoneNum' && itemKey !== 'password') {
          await   uni.removeStorageSync(itemKey);
          }
        };
        keepUserInfo(result)
                    // uni.clearStorage({
                    //     success: (r) => {
                    //         keepUserInfo(result)
                    //     },
                    //     fail: (error) => {},
                    // })
                    // #endif
                    // 保存登录状态，用于拦截器放行 0:未登录 1:已登录
                    kvStore.set('hasLogin', 1)
					// 判断token是否已失效跳转，临时方案 0未失效，1已失效
					kvStore.set('isTokenInvalid', 0)
                    // 登录成功后移除路由拦截器
                    uni.removeInterceptor('navigateTo')
                    uni.removeInterceptor('switchTab')
                    uni.removeInterceptor('reLaunch')
                } else {
                    uni.showToast({
                        title: res.message || "登录失败",
                        icon: 'none',
                        duration: 2000
                    })
                }
            })
            .catch((err) => {
                uni.hideLoading()
                uni.showToast({
                    title: err || "登录失败",
                    icon: 'none',
                    duration: 2000
                })
                // 验证码错误或过期刷新验证码
                refreshCaptcha()
            })
    }
    // 存储用户信息
    function keepUserInfo(info) {
        tokenStore.$patch((state) => {
            state.refreshToken = info.refreshToken
            state.tokenType = info.tokenType
            state.value = info.value
            state.expiration = info.expiration
            state.clientId = info.clientId
            state.userid = info.additionalInformation.userid
            state.tenantId = info.additionalInformation.customParam.tenantId
        })
        // 持久化存储用户登录信息
        kvStore.set('tokenInfo', info, false, 'OBJ')
        // 修改登录状态
        mainStore.$patch((state) => {
            state.hasLogin = true
            state.loginProvider = Platform
        })
        userStore.userInfo = info.additionalInformation;

        // 如果当前登录人不是游客身份，则根据租户id获取租户信息，当前登录人有多个租户的时候从缓存中取上次登录的那个，没有缓存则取第一个
        if (info.additionalInformation.customParam.userType != 'Visitor') {
            let tenantId = ''
            let lastLoginTenantId = kvStore.get('lastLoginTenantId', true, 'STR')
            console.log('lastLoginTenantId----', lastLoginTenantId);
            // 缓存中没取到就默认第一个
            if (!lastLoginTenantId || lastLoginTenantId == 'fail') {
                tenantId = info.additionalInformation.userJobDetailVOList[0].tenantId;
            } else {
                // 缓存中取到了，再判断上次存的在不在这次登录的用户的租户里面，因为可能两次登录的不是同一个账号
                let tmp = info.additionalInformation.userJobDetailVOList.find((item) => {
                    return item.tenantId == lastLoginTenantId
                })
                if (!tmp) {
                    tenantId = info.additionalInformation.userJobDetailVOList[0].tenantId;
                } else {
                    tenantId = lastLoginTenantId
                }
            }
            getTrarenInfo({
                    tenantId: tenantId,
                })
                .then((res) => {
                    if (res.success) {
                        // 持久化存储用户登录信息
                        kvStore.set('tokenInfo', res.data, false, 'OBJ')
                        // 更新token
                        tokenStore.value = res.data.value;
                        // 保存个人信息
                        userStore.userInfo = Object.assign(userStore.userInfo, res.data.additionalInformation)
                        tokenStore.tenantId = res.data.additionalInformation.customParam.tenantId
                        let peopleId = res.data?.additionalInformation?.customParam?.peopleInfo?.villagePeopleId || ''
                        findData(peopleId)
                        // 如果当前用户在当前租户下有多个角色，则判断是否有干部角色，有的话默认展示干部页
                        for (let i = 0; i < res.data.additionalInformation.authorityList.length; i++) {
                            let item = res.data.additionalInformation.authorityList[i];
                            if (item.roleId == 'village_cadres' || item.roleId == 'town_cadres') {
                                //设置干部角色的tabbar
                                tabBarStore.role = 'cadreTabs';
                                // 保存当前用户角色
                                userStore.currentRole = item.roleId;
                                userStore.isCadre = true;
                                break;
                            } else {
                                // 不是村干部也不是镇干部，展示村民页
                                tabBarStore.role = 'villagerTabs';
                                userStore.currentRole = 'villager';
                                userStore.isCadre = false;
                            }
                        }
                        let roleInfo = {
                            tenantId: res.data.additionalInformation.customParam.tenantId,
                            role: userStore.currentRole,
                            isCadre: userStore.isCadre
                        }
                        kvStore.set('roleInfo', roleInfo, false, 'OBJ')
                        //在这里进行极光推送的绑定
                        // #ifdef APP-PLUS
                        // jpushMobile(); TODO
                        // #endif
                        uni.hideLoading()
                        uni.showToast({
                            title: '登录成功',
                            duration: 1000,
                            success: () => {
                                setTimeout(() => {
                                    jump2Home()
                                }, 1000)
                            },
                        })
                    } else {
                        uni.hideLoading()
                        uni.showToast({
                            title: "登录失败",
                            icon: 'none',
                            duration: 2000
                        })
                        return false;
                    }
                })
                .catch((err) => {
                    uni.hideLoading()
                    console.log(err)
                })
        } else {
            uni.hideLoading()
			findData()
            // 游客，展示村民页
            tabBarStore.role = 'villagerTabs';
            userStore.currentRole = 'villager';
            // userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
            // userStore.userInfo.nickName = '段泊岚镇农综平台用户'
            userStore.isCadre = false;
            let roleInfo = {
                tenantId: "",
                role: 'villager',
                isCadre: false
            }
            kvStore.set('roleInfo', roleInfo, false, 'OBJ')
            // 把用户头像和昵称也放在tokenInfo中缓存起来
            // let peopleInfo = {
            //     headPhoto: DEFAULT_AVATOR_IMAGES,
            //     nickName: '段泊岚镇农综平台用户'
            // }
            // kvStore.set('peopleInfo', peopleInfo, false, 'OBJ')
            uni.showToast({
                title: '登录成功',
                duration: 1000,
                success: () => {
                    setTimeout(() => {
                        jump2Home()
                    }, 1000)
                },
            })
        }
    }
    const dataList = ref({})
    //获取用户的昵称和头像
    function findData(peopleId = '') {
        let params = {
            peopleId: peopleId,
        }
        findInfo(params).then(res => {
            if (res && res.success) {
                console.log(res.data, '昵称头像')
                if (!res.data) {
                    userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
                    userStore.userInfo.nickName = '段泊岚镇农综平台用户'
                } else if (res.data.headPhoto && res.data.nickname == "") {
                    userStore.userInfo.headPhoto = res.data.headPhoto
                    userStore.userInfo.nickName = '段泊岚镇农综平台用户'
                } else if (res.data.nickname && res.data.headPhoto == "") {
                    userStore.userInfo.nickName = res.data.nickname
                    userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
                } else if (res.data.nickname && res.data.headPhoto) {
                    userStore.userInfo.headPhoto = res.data.headPhoto
                    userStore.userInfo.nickName = res.data.nickname
                }
            } else {
                userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
                userStore.userInfo.nickName = '段泊岚镇农综平台用户'
            }
            // 把用户头像和昵称也放在tokenInfo中缓存起来
            let peopleInfo = {
                headPhoto: userStore.userInfo.headPhoto,
                nickName: userStore.userInfo.nickName
            }
            kvStore.set('peopleInfo', peopleInfo, false, 'OBJ')
        }).catch(err => {
            console.log(err);
            userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
            userStore.userInfo.nickName = '段泊岚镇农综平台用户'
        })
    }

    // 跳转首页
    function jump2Home() {
        let redirectUrl = mainStore.login_redirect_url;
		console.log('login redirectUrl', redirectUrl);
        if (!!redirectUrl) {
        	uni.reLaunch({
        		url:redirectUrl
        	})
        } else {
        // uni.reLaunch({
        	//     url: '/pages/home/<USER>',
        	// })
            console.log('login userInfo', userStore.userInfo);
            if( userStore.userInfo.authorityList.find(item=>item.roleId=='town_admin')){
            uni.navigateTo({
                url: '/jimo/anquanjianguan/adminstrator/home'

            })
            return
        }
        else if(userStore.userInfo.authorityList.find(item=>item.roleId=='town_supervisor')){
         uni.navigateTo({
                url: '/jimo/anquanjianguan/companyList/companyList'

            })
        }
        else{
          uni.reLaunch({
        	    url: '/pages/home/<USER>',
        	})
        }
        }
    }
    // #ifdef APP-PLUS
    // app端极光推送
    // 极光推送
    const jpushModule = uni.requireNativePlugin('JG-JPush')
    connect()

    function connect() {
        uni.$on('connectStatusChange', (connectStatus) => {
            console.log('进入连接')
            var connectStr = ''
            if (connectStatus == true) {
                connectStr = '已连接'
                getRegistrationID()
            } else {
                connectStr = '未连接'
            }
            console.log('监听到了连接状态变化', connectStr)
        })
    }

    function getRegistrationID() {
        // 获取应用程序的 RegistrationID
        jpushModule.getRegistrationID((result) => {
            let registerID = result.registerID
            console.log('registerID ~~~', result)
        })
    }
    const jPush = async (registerationId) => {
        const res = await jpushUser({
            registerationId: registerationId,
        })
        console.log('jPush', res)
    }

    function jpushMobile() {
        jpushModule.getRegistrationID((result) => {
            console.log('登录后getRegistrationID', result.registerID)
            if (result.registerID) {
                // this.updateRegisterId(result.registerID);
                //调用接口绑定设备对应的用户
                jPush(result.registerID)
            }
        })
    }
    // #endif
    // 表单密码显示或隐藏
    var isPassword = ref(true)

    function showPwd() {
        isPassword.value = !isPassword.value
    }

    //图形验证码
    refreshCaptcha()

    function refreshCaptcha() {
		data.form.token = 100000 + Math.floor(Math.random() * 900000);
        data.captchaUrl =
            data.captchaApi + '?token=' + data.form.token + '&t=' + Math.random()
        data.form.imgCaptcha = ''
    }

    onReady(() => {
        // #ifdef APP-PLUS
        // 获取APP.vue传递的租户名称
        uni.$on('updateTenantName', function(res) {
            if (res.tenantName) {
                data.form.tenantName = res.tenantName
            }
        })
        // #endif
    })
    // 获取手机验证码功能
    const tips = ref('');
    const seconds = ref(60); // 60秒后可以重新获取
    const uCodeRef = ref();
    const codeBtnStyle = reactive({
        background: 'linear-gradient(90deg, #3ADC9B 0%, #0CBE88 100%)',
        borderRadius: '10rpx',
        width: '200rpx',
        fontSize: '26rpx'
        // height: '56rpx'
    })
    const inputCustomStyle = reactive({
        paddingLeft: '0',
        paddingRight: '0'
    })
    const codeChange = (text) => {
        tips.value = text;
    };
    // 获取验证码
    const getCode = () => {
        if (uCodeRef.value.canGetCode) {
            if (!data.form.phoneNum) {
                uni.showToast({
                    title: '请先填写手机号',
					icon: 'none'
                });
                return;
            }
			if(!uni.$u.test.mobile(data.form.phoneNum)) {
				uni.showToast({
				    title: '手机号码不正确',
					icon: 'none'
				});
				return;
			}
            uni.showLoading({
                title: '正在获取验证码',
				mask:true
            });
            // 向后端请求验证码
            sendSms()
        } else {
            uni.$u.toast('请在倒计时结束后再发送');
        }
    };

    function sendSms() {
        getVerificationCode({
            phoneNum: data.form.phoneNum
        }).then(result => {
            console.log(result)
            if (result.success) {
                uni.hideLoading();
                // 这里此提示会被start()方法中的提示覆盖
                uni.$u.toast('验证码已发送');
                // 通知验证码组件内部开始倒计时
                uCodeRef.value.start();

            } else {
                uni.hideLoading();
                // 这里此提示会被start()方法中的提示覆盖
                uni.$u.toast('验证码发送失败');
            }
        })
    }
    // 切换登录方式
    function changeLoginType() {
        if (loginType.value == 'captcha') {
            loginType.value = 'password'
            uForm.value.setRules(pwdRules)
            console.log('uForm.value----------', uForm.value);
        } else {
            loginType.value = 'captcha'
            uForm.value.setRules(captchaRules)
        }
        // uForm.value.resetFields()
        uForm.value.clearValidate()
    }
    // 跳转到一键登录页
    function goFastLogin() {
         // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
    }
    // 切换用户协议复选框选中状态
    const isAgreement = ref(false)
    // const isAgreement = ref(true)

    function clickCheckbox() {
        isAgreement.value = !isAgreement.value
        console.log('isAgreement--------', isAgreement.value);
    }

    function checkboxChange(checked) {
        if (checked.length == 0) {
            //未选中
            isAgreement.value = false;
        } else {
            //同意协议
            isAgreement.value = true;
        }
    }
    // 跳转到用户协议
    function toAgreement() {
        uni.navigateTo({
            url: "/pages/generalPage/userAgreement/userAgreement",
        });
    }
    // 跳转到隐私政策
    function toPrivacy() {
        uni.navigateTo({
            url: "/pages/generalPage/privacyPolicy/privacyPolicy",
        });
    }
    // 确认用户协议弹窗
    const showModal = ref(false)

    function confirmSubmit() {
        isAgreement.value = true;
        showModal.value = false;
        submit();
    }

    function cancelSubmit() {
        isAgreement.value = false;
        showModal.value = false;
    }
</script>

<style lang="scss" scoped>
.login {
  min-height: 1319rpx;
  height: 100vh;
  /* #ifndef MP-WEIXIN  */
  width: 100%;
  /* #endif */
  /* #ifdef MP-WEIXIN */
  width: 750rpx;
  /* #endif */
  position: relative;
}

.close-icon {
  position: absolute;
  // top: 60rpx;
  left: 24rpx;
}

// .login_top {
// 	height: 495rpx;
// 	width: 100%;
// 	// background: url(../../static/images/Mask.png) no-repeat left top;
// 	background-size: 100% 495rpx;
// }

.itemBox {
  display: flex;
  flex-direction: row;
  // width: 90%;
  width: 100%;
}

.container {
  box-sizing: border-box;
  position: absolute;
  top: 380rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx 0 40rpx;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  background-color: #fff;
  // height: 100%;
  height: calc(100% - 380rpx);
  width: 100%;

  .center {
    // position: absolute;
    // left: 42rpx;
    // top: 90rpx;
    width: 100%;

    .title {
      // margin-left: 20rpx;
      margin-bottom: 20rpx;
      font-size: 36rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #000000;
      line-height: 50rpx;
    }
  }

  .btnBox {
    position: absolute;
    bottom: 200rpx;
    // margin-top: 150rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;

    .loginBtn {
      width: 668rpx;
    }

    .forget {
      // width: 126rpx;
      height: 48rpx;
      font-size: 28rpx;
      font-family: SourceHanSansSC-Normal, SourceHanSansSC;
      font-weight: 400;
      color: #787979;
      line-height: 48rpx;
      margin-top: 10rpx;
    }
  }

  .bottom {
    // position: absolute;
    // top: 987rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;

    .fastLogin {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;

      .line {
        width: 28%;
        background: #000;
        opacity: 0.05;
        height: 2rpx;
      }

      .text {
        // width: 128rpx;
        font-size: 32rpx;
        font-family: SourceHanSansSC-Normal, SourceHanSansSC;
        font-weight: 400;
        color: #787979;
        margin: 0 50rpx;
      }
    }

    image {
      width: 85rpx;
      height: 85rpx;
      margin-top: 30rpx;
    }

    .text {
      font-size: 26rpx;
      font-family: SourceHanSansSC-Normal, SourceHanSansSC;
      color: #787979;
      margin-top: 15rpx;
    }
  }
}

.imageIcon-box {
  width: 76rpx;
  height: 86rpx;
  background: #d3e3ff;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 6rpx;
}

.seatBox {
  width: 76rpx;
  height: 86rpx;
  margin-right: 20rpx;
  margin-bottom: 6rpx;
}

.imageIcon {
  width: 32rpx;
  height: 36rpx;
}

.bgimg {
  width: 100%;
  height: 400rpx;
}

.tips {
  display: inline-block;
  margin: 50rpx 0;
  font-size: 24rpx;
  font-family: PingFang-SC-Regular, PingFang-SC;
  font-weight: 400;
  color: #333333;
  line-height: 33rpx;
}

.other-login {
  margin-top: 50rpx;
  font-weight: 400;
  color: #0cbe88;
  font-size: 24rpx;
  line-height: 24rpx;

  .login-text {
    // display: flex;
    // justify-content: center;
    display: inline-block;
    padding-right: 20rpx;
    margin-right: 20rpx;
    border-right: 1px solid;
  }
}

::v-deep .u-form-item .u-form-item__body__right__message {
  margin-left: 0 !important;
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 74rpx;
}

.agree-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 10rpx;
  width: 100rpx;
  height: 100rpx;
}

::v-deep radio-group label,
checkbox-group label {
  padding-right: 0;
}

.layers-item-selector {
  outline: none;
  width: 32rpx;
  height: 38rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
}

.privacyAndAgreement {
  font-size: 24rpx;
  font-weight: 400;
  color: #000000;
  line-height: 37rpx;
}

.sys-text {
  color: #0cbe88;
}

.find-pwd {
  margin-top: 20rpx;
  text-align: right;
  font-weight: 400;
  color: #0cbe88;
  font-size: 24rpx;
  line-height: 24rpx;
}

.modal-content {
  padding: 0 20rpx;
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #111111;
  line-height: 46rpx;
}

.btn-bar {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 60rpx;

  .disAgree,
  .agree {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 276rpx;
    height: 80rpx;
    border-radius: 41rpx;
    border: 2rpx solid #e5e5e5;
    font-size: 28rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
  }

  .disAgree {
    background: #ffffff;
    color: #555555;
  }

  .agree {
    background: #0cbe88;
    color: #ffffff;
  }
}

.login ::v-deep .u-modal__content {
  display: flex;
  flex-direction: column;
}

.login ::v-deep .u-modal__content {
  padding-left: 20rpx;
  padding-right: 20rpx;
}
</style>
