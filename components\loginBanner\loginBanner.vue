<template>
	<!-- 登录提示banner 未登录时展示-->
	<view class="main-container" :style="{bottom: bottom}" @click="goLogin">
		<view class="content" :style='customStyle'>
			<view class="tips">登录后享受更多服务</view>
			<view class="btn">去登录</view>
		</view>
	</view>
</template>

<script setup>
  import {
    computed
  } from "vue";
	import { LOGIN_BANNER_IMAGES } from '@/common/net/staticUrl.js'

	function goLogin() {
		  // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
	}
	const customStyle = {
		backgroundImage: `url(${LOGIN_BANNER_IMAGES})`,
		backgroundSize: '100% 100%',
		backgroundRepeat: 'no-repeat'
	}
  
  // 计算banner距离底部的距离
  const bottom = computed(() => {
    return (uni.$u.sys().safeAreaInsets.bottom + 60) + 'px'
  })
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.main-container {
		position: fixed;
		left: 0;
		// bottom: 140rpx;
		z-index: 999;
		padding: 0 20rpx;
		width: 100%;
		height: 100rpx;

		// background-size: cover;
		// background-repeat: no-repeat;
		.content {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 50rpx 0 40rpx;
			height: 100%;
		}

		.tips {
			width: fit-content;
			height: 45rpx;
			font-size: 32rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #fff;
			line-height: 45rpx;
		}

		.btn {
			width: 147rpx;
			height: 54rpx;
			line-height: 54rpx;
			text-align: center;
			background: linear-gradient(180deg, #FFFFFF 0%, #E1FFEF 100%);
			box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(12, 190, 136, 0.51);
			border-radius: 27rpx;
			color: #0CBE88;
		}
	}
</style>