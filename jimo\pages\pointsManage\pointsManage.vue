<template>
    <view class="content-boxes">
        <view class="top-banner">
            <view class="slogan">
                <view>全村总积分</view>
                <view>{{totalPoints}}</view>
            </view>
            <view class="pBox">
                <view v-for="(item,index) in pointsList" @click="gotolink(item)" :key="index" class="pitem">
                    <view>{{item.title}}</view>
                    <view>{{item.points}}</view>
                </view>

            </view>
        </view>
        <view class="quick-access">
            <view v-for="(item, index) in quickAccessList" :key="index" @click="gotolink(item)"
                class="quick-access-item">
                <image :src="item.src" mode="aspectFit" style="height:100%;width:100%;"></image>
                <view class="access-title">{{ item.name }}</view>
            </view>
        </view>

        <view class="pmTop">
            <view style="width: 460rpx">
                <u-tabs class="" :list="tabList" lineWidth="30" lineColor="#0FBF89" @click="clickTab" :activeStyle="{
							color: '#303133',
							fontWeight: 'bold',
							transform: 'scale(1.05)'
						}" :inactiveStyle="{
							color: '#606266',
							transform: 'scale(1)'
						}" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;">
                </u-tabs>
            </view>
            <view class="right" @click="changeDate">
                {{ queryDate }}
                <u-icon name="arrow-down" color="#6D6D6D" size="32rpx"></u-icon>
            </view>

        </view>
        <u-datetime-picker ref="datetimePicker" :show="show" v-model="pickerDate" mode="year-month"
            @confirm="show = false" @cancel="show = false"></u-datetime-picker>

        <view class="pmBox" v-if="curTab == '家庭积分排名'">
            <view class="search-box">
                <u-search placeholder="输入户主或门牌号搜索" :inputStyle="inputStyle" height="39" width="301" v-model="holderName"
                    bgColor="#FFFFFF" borderColor="#edeff3" :showAction="false" :clearabled="false" @custom="search()"
                    @search="search()"></u-search>
            </view>
            <u-sticky offset-top="0">
                <view class="top lineItem">
                    <view class="sort" style="color:#303133;width:10%;text-align:center;">排名</view>
                    <view class="nameBox" style="padding: 0 20rpx;width:35%;">户主</view>
                    <view class="houseNum">门牌号</view>
                    <view class="points" style="color: #333333;width:20%;">总积分</view>
                </view>
            </u-sticky>
            <view v-if="jtList.length > 0">
                <view v-for="(item,index) in jtList" :key="index" class="lineItem lineItemBg">
                    <view class="sort" style="width:10%;">
                        <view v-if="index in [0,1,2]" class="iconBox" style="padding:8rpx 0;">
                            <u-image v-if="index == 0" src="@/jimo/static/images/points/pm1.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                            <u-image v-else-if="index == 1" src="@/jimo/static/images/points/pm2.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                            <u-image v-else-if="index == 2" src="@/jimo/static/images/points/pm3.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                        </view>
                        <view style="text-align: center;" v-else> {{index + 1}} </view>
                    </view>
                    <view class="nameBox" style="padding: 0 20rpx;width:35%;">
                        <!-- <view style="padding:12rpx 8rpx 0 0;">
                            <safe-image :src="item.headPhoto || DEFAULT_AVATOR_IMAGES" shape="circle" width="48rpx"
                                height="48rpx" />
                        </view> -->
                        <view class="name">{{item.holderName}} </view>
                    </view>
                    <view class="houseNumber">{{item.houseNumber || '暂无'}}</view>
                    <view class="points" style="width:20%">{{item.totalPointsEarned}}</view>
                </view>
            </view>
            <view class="empty-status" v-else>
                <view class="empty-icon">
                    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                        height="150rpx"></u-image>
                    <view class="no-more">
                        <span class="txt">暂无数据</span>
                    </view>
                </view>
            </view>

        </view>
        <view class="pmBox" v-else>
            <u-sticky offset-top="0">
                <view class="top lineItem">
                    <view class="sort" style="color:#303133;text-align:center;">排名</view>
                    <view class="dzname" style="padding: 0;">队组</view>
                    <view class="points" style="color: #333333;">总积分</view>
                </view>
            </u-sticky>
            <view v-if="dzList.length > 0">
                <view v-for="(item,index) in dzList" :key="index" class="lineItem lineItemBg">
                    <view class="sort">
                        <view v-if="index in [0,1,2]" class="iconBox">
                            <u-image v-if="index == 0" src="@/jimo/static/images/points/pm1.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                            <u-image v-else-if="index == 1" src="@/jimo/static/images/points/pm2.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                            <u-image v-else-if="index == 2" src="@/jimo/static/images/points/pm3.png"
                                mode="aspectFit" width="60rpx" height="38rpx"></u-image>
                        </view>
                        <view style="text-align: center;" v-else> {{index + 1}}</view>
                    </view>
                    <view class="dzname">
                        {{item.villageOrgName}}
                    </view>
                    <view class="points">{{item.totalPointsEarned}}</view>
                </view>
            </view>
            <view class="empty-status" v-else>
                <view class="empty-icon">
                    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                        height="150rpx"></u-image>
                    <view class="no-more">
                        <span class="txt">暂无数据</span>
                    </view>
                </view>
            </view>

        </view>

    </view>
</template>

<script setup>
    import {
        onMounted,
        reactive,
        ref,
        computed,
        watch
    } from 'vue'
    import {
        onReady,
        onShow,
        onHide,
        onLoad
    } from '@dcloudio/uni-app'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        NO_MORE_IMG,
        DEFAULT_AVATOR_IMAGES
    } from "@/common/net/staticUrl";
    import {
        findVillagePoints,
        villaeOrgPointsRank,
        familyPointsRank,
    } from '../../api/points/points';
    import {
        getColumnByKey
    } from '@/common/net/generalPage/common.js'
    import {
        setPoints
    } from '../../api/burialPoint.js'
    import cgbg from '../../static/images/points/cgbg.png'
    import jfscbg from '../../static/images/points/jfscbg.png'
    import jftbbg from '../../static/images/points/jftbbg.png'
    const holderName = ref('') //关键字
    //顶部积分详情
    //reactive
    const pointsList = ref([{
            title: "总加分",
            points: "0"
        },
        {
            title: "总减分",
            points: "0"
        },
        {
            title: "总消耗分",
            points: "0"
        }
    ])
    const totalPoints = ref(0)
    const columnInfo = ref("")
    onShow(() => {
        getVillagePoints()
        getColumnByKey({
            columnKeys: 'cunguiminyue'
        }).then((res) => {
            if (res.success && res.data && res.data.length > 0) {
                columnInfo.value = res.data[0].columnId
            }
        })
        // 埋点-进入积分制管理页
        let param = {
            eventId: 'enter_integral_management',
            attributeValue: '/jimo/pages/pointsManage/pointsManage'
        }
        setPoints(param)
    })

    function getVillagePoints() {
        findVillagePoints({}).then((res) => {

            const {
                success,
                data
            } = res
            if (success && !!data) {
                totalPoints.value = parseInt(data.totalPointsEarned)
                pointsList.value = [{
                        title: "总加分",
                        points: parseInt(data.totalPointsAdd)
                    },
                    {
                        title: "总减分",
                        points: parseInt(data.totalPointsSub)
                    },
                    {
                        title: "总消耗分",
                        points: parseInt(data.totalPointsConsump)
                    }
                ]
            } else {
                totalPoints.value = 0
                pointsList.value = [{
                        title: "总加分",
                        points: "0"
                    },
                    {
                        title: "总减分",
                        points: "0"
                    },
                    {
                        title: "总消耗分",
                        points: "0"
                    }
                ]
            }
        })

    }
    // 中间快捷链接区域
    const userStore = useUserStore()
    const isCadre = ref(userStore.isCadre) // 是否有干部角色，有则展示切换角色按钮
    const quickAccessList = computed(() => {
        if (!!isCadre && userStore.currentRole == 'village_cadres') { //干部
            //&& userStore.currentRole == 'village_cadres'
            return [{
                    name: "村规民约",
                    src: cgbg
                },
                {
                    name: "积分申报",
                    src: jftbbg
                },
            ]
        } else { //村民
            return [{
                    name: "村规民约",
                    src: cgbg
                },
                {
                    name: "积分商城",
                    src: jfscbg
                },
                {
                    name: "积分申报",
                    src: jftbbg
                },
            ]
        }
    })



    const tabList = ref([{
        name: "家庭积分排名"
    }, {
        name: "队组积分排名"
    }])
    let curTab = ref("家庭积分排名")

    function clickTab(item) {
        curTab.value = item.name
        // 埋点-积分排名tab切换
        let param = {
            eventId: 'switch_integral_rank_tab',
            attributeValue: item.name
        }
        setPoints(param)
    }
    onLoad(() => {
        console.log("..onload2")
        if (userStore.isCadre) {
            // &&
            // (userStore.currentRole == 'village_cadres' ||
            //   userStore.currentRole == 'town_cadres')
            tabList

        } else {

        }
        //getData()
    })

    function gotolink(item) {
        if (item.name === '积分申报') {
            // 埋点-点击积分制管理-积分申报
            let param = {
                eventId: 'click_integral_reporting',
                attributeValue: 'jcsb'
            }
            setPoints(param)

            uni.navigateTo({
                url: '/jimo/pages/pointsManage/pointsReport'
            })
        }
        if (item.name === '积分商城') {
            // 埋点-点击积分制管理-积分商城
            let param = {
                eventId: 'click_integral_mall',
                attributeValue: 'jfsc'
            }
            setPoints(param)

            uni.navigateTo({
                url: '/jimo/pages/pointsmall/pointsmall'
            })
        }
        if (item.name === '村规民约') {
            // 埋点-点击积分制管理-村规民约
            let param = {
                eventId: 'click_village_rules',
                attributeValue: 'cgmy'
            }
            setPoints(param)

            uni.navigateTo({
                url: '/pages/listPage/appList?columnId=' + columnInfo.value + '&columnKey=cunguiminyue'
            })
        }
    }

    function getData() {
        pointsList.value = [{
                title: "总积分",
                points: "5678"
            },
            {
                title: "总减分",
                points: "5678"
            },
            {
                title: "总消耗分",
                points: "5678"
            }
        ]

    }
    //下边积分排名相关
    //格式化时间格式
    const datetimePicker = ref(null);
    let pickerDate = ref(Number(new Date()))
    const queryDate = computed(() => {
        if (!!pickerDate.value) {
            let dateTime = new Date(pickerDate.value);
            // 分别获取年月日、时分秒，三元判断长度不够的进行补0操作
            let year = dateTime.getFullYear();
            let month = (dateTime.getMonth() + 1 < 10 ? '0' + (dateTime.getMonth() + 1) : dateTime.getMonth() +
                1); // 这里需要注意的是月份是0~11，所以需要 + 1 操作
            let day = dateTime.getDate() < 10 ? '0' + dateTime.getDate() : dateTime.getDate();
            let hour = dateTime.getHours() < 10 ? '0' + dateTime.getHours() : dateTime.getHours();
            let minutes = dateTime.getMinutes() < 10 ? '0' + dateTime.getMinutes() : dateTime.getMinutes();
            let second = dateTime.getSeconds() < 10 ? '0' + dateTime.getSeconds() : dateTime.getSeconds();
            //按指定格式拼接年月日、时分秒
            let time = year + '-' + month
            return time
        } else {
            return '当月'
        }

    })
    watch(queryDate, (value, oldValue) => {
        if (!!value) {
            getJFData();
        }
    })
    onShow(() => {
        isLoading.value = true;
        getJFData();
    })
    //搜索
    function search() {
        jtList.value = []
        getJFData()
    }
    //控制年月选择弹窗
    let show = ref(false)
    const isLoading = ref(false);
    const dzList = ref([])
    const jtList = ref([])

    function getJFData() {
        show.value = false
        //
        familyPointsRank({
            queryTime: queryDate.value,
            holderName: holderName.value,
        }).then(res => {
            isLoading.value = false;
            if (res.success) {
                jtList.value = res.data
            }
        })
        //{queryTime:queryDate.value}
        villaeOrgPointsRank({
            queryTime: queryDate.value
        }).then(res => {
            if (res.success) {
                dzList.value = res.data
            }
        })

    }

    function changeDate() {
        show.value = true;
        // 埋点-点击时间筛选
        let param = {
            eventId: 'click_time_filter',
            attributeValue: 'time_filter'
        }
        setPoints(param)
    }
</script>

<style lang="scss" scoped>
    .content-boxes {
        padding: 0 20rpx;
        height: 100%;
    }

    .top-banner {
        margin-top: 20rpx;
        display: flex;
        flex-flow: column;
        // align-items: center;
        width: 100%;
        height: 260rpx;
        border-radius: 14rpx;
        background: url('../static/images/points/top_bg.png');
        background-size: cover;
        background-repeat: no-repeat;
        color: #fff;

        .slogan {
            // height: 50%;
            //width: 100%;
            // margin: 0 40rpx;
            line-height: 60rpx;
            font-size: 38rpx;
            font-weight: 700;
            font-family: YouSheBiaoTiHei;
            // color: rgba(13, 190, 137, 1);
            text-stroke: 1rpx #ffffff;
            text-align: left;
            padding: 30rpx 40rpx 10rpx 40rpx;
        }

        .pBox {
            //width: 100%;
            // height: 50%;
            display: flex;
            justify-content: space-between;
            padding: 0 40rpx;

            .pitem {}
        }

    }

    .quick-access {
        display: flex;
        justify-content: space-between;
        margin: 20rpx 0;

        .quick-access-item {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0;
            height: 132rpx;
            margin-right: 20rpx;
            background-size: 100% 100%;
            background-repeat: no-repeat;

            &:last-child {
                margin-right: 0;
            }

            .access-title {
                margin-left: 22rpx;
                height: 42rpx;
                font-size: 32rpx;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                color: #333333;
                line-height: 45rpx;
                position: absolute;

            }
        }

        .quickBg {
            width: 100%;
            height: 100%;
        }

        // .quick-access-item:first-of-type {
        // 	background: url('../static/images/points/cgbg.png');
        // 	background-size: 100% 100%;
        // 	background-repeat: no-repeat;

        // }

        // .quick-access-item:last-of-type {
        // 	background: url('../static/images/points/jfscbg.png');
        // 	background-size: 100% 100%;
        // 	background-repeat: no-repeat;

        // }
    }

    .pmTop {
        display: flex;
        justify-content: space-between;

        .right {
            width: 200rpx;
            height: 52rpx;
            border-radius: 8rpx;
            border: 1rpx solid #D9D9D9;
            font-size: 26rpx;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            color: #6D6D6D;
            line-height: 52rpx;
            display: flex;
            justify-content: space-around;
        }
    }

    .search-box {
        box-sizing: border-box;
        margin-bottom: 20rpx;
        width: 100%;
        height: 80rpx;
    }

    .pmBox {
        padding: 20rpx 20rpx;

        .lineItem {
            display: flex;
            justify-content: space-between;
            line-height: 80rpx;
            padding: 0 10rpx;
            border-bottom: 1px #eee dotted;
            font-size: 28rpx;

            .sort {
                font-size: 28rpx;
                font-family: Arial;
                font-weight: normal;
                color: #0BBD88;
                width: 20%;

                .pmIcon {
                    margin: 20rpx auto;
                }

                .iconBox {
                    padding: 14rpx 36rpx;
                    text-align: center;
                    vertical-align: center;
                }
            }

            .nameBox {
                width: 40%;
                display: flex;
                justify-content: flex-start;
                padding: 4rpx 0;
            }

            .houseNum {
                width: 35%;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
            }

            .houseNumber {
                width: 35%;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; //溢出不换行
            }

            .name {
                width: 80%;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
                // .headImg{
                // 	margin:6rpx 2rpx;
                // }
            }

            .dzname {
                width: 35%;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                overflow: hidden;
            }

            .points {
                width: 30%;
                font-family: Source Han Sans CN, Source Han Sans CN;
                font-weight: 500;
                color: #FF8627;
                text-align: center;
            }
        }
		.lineItemBg{
			&:nth-child(1) {
			    background: #FFFBF4;
			}
			
			&:nth-child(2) {
			    background: #F9FBFC;
			}
			
			&:nth-child(3) {
			    background: #FFF7F5;
			}
			
		}

        .top {
            background: #F9F9F9;
            // padding-left: 50rpx;
        }
    }

    .empty-status {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 50rpx 0;

        .empty-icon {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .no-more {
                margin-top: 20rpx;
                display: flex;
                justify-content: center;
                margin-top: 10rpx;

                .txt {
                    text-align: center;
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #d5d5d6;
                    line-height: 37rpx;
                }
            }
        }
    }
</style>