<template>
	<view class="container">
		<!-- <u-navbar title="选择成员" :title-style="{'fontSize': '36rpx'}" placeholder border auto-back>
		</u-navbar> -->
		<view style="padding: 20rpx;box-sizing: border-box;">
			<u-search placeholder="输入姓名搜索" :inputStyle="inputStyle" height="39" width="301" v-model="keyword"
				bgColor="#FFFFFF" :showAction="false" :clearabled="false" @custom="search()"
				@search="search()"></u-search>
		</view>
		<view class="content-container">
			<template v-if="peopleList.length > 0">
				<view class="article-list">
					<view class="article-item" v-for="(item, index) in peopleList" :key="item.villagePeopleId"
						@click="chooseMember(item)">
						<!-- <view class="article-radio" v-if="chooseType == 'multi'">
							<view style="position: relative;">
								<view class="radio-img"></view>
								<view v-if="isChoosed(item)" class="active"></view>
							</view>
						</view> -->
						<view class="article-content">
							<u-avatar :src="item.headPhoto" size="98rpx"
								:default-url="DEFAULT_AVATOR_IMAGES"></u-avatar>
							<view class="article-text">
								<view class="online" style="font-size: 32rpx;margin-bottom: 10rpx;">
									{{ item.peopleName }}
								</view>
								<view class="online" style="font-size: 28rpx;;color: #999999;">{{ item.villageOrgName}}
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 底部加载，三个状态：more、loading、nomore -->
				<!-- <u-loadmore :status="loadStatus"></u-loadmore> -->
			</template>
			<view v-else class="empty-status">
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
		</view>
		<!-- 占位块，使上一个view的marginbottom生效 -->
		<view style="height: 1rpx;"></view>
	</view>
	<view class="submit-container" v-if="chooseType == 'multi'">
		<view class="submit" @click="submit">
			确认
		</view>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref
	} from "vue";
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import {
		NO_MORE_IMG,
		DEFAULT_AVATOR_IMAGES
	} from '@/common/net/staticUrl.js'
	import {
		onPullDownRefresh,
		onReachBottom
	} from '@dcloudio/uni-app';
	import {
		useTokenStore
	} from '@/store/token.js'
	import {
		findPeople
	} from '../../api/taskPoints/points';
	const peopleList = ref([])
	const isLoading = ref(false);
	const chooseType = ref('single') // 单选还是多选
	const eventType = ref("") // 事件类型，办理类的工单需要这个参数来过滤人员
	const operate = ref("") // 操作，用来判断是不是选转交人，如果是的话则不能选择自己
	const inputStyle = {
		width: '500rpx',
	}
	const keyword = ref('') //关键字
	const tokenStore = useTokenStore()
	const tenantId = tokenStore.tenantId
	onLoad((options) => {
		//chooseType.value = options.chooseType;
		if (!!options.choosedMembers) {
			choosedMembers.value = JSON.parse(options.choosedMembers);
		}
		if (!!options.operate) {
			operate.value = options.operate;
		}
		if (!!options.operate) {
			eventType.value = options.eventType
		}
		//f();
		getPeople()
	})

	//搜索
	function search() {
		peopleList.value = []
		getPeople()
	}

	function getPeople() {
		//显示加载中动画
		uni.showNavigationBarLoading();
		let paging = {
			tenantId: tenantId,
			peopleName: keyword.value
		}
		findPeople(paging).then(res => {
			handleView();
			if (res.success) {
				peopleList.value = res.data
			}
		}).catch(() => {
			handleView();
			uni.showToast({
				title: '查询数据失败',
				icon: 'none'
			});
		})
	}


	// 请求成功后更新视图
	function handleView() {
		//成功获取数据后隐藏加载动画
		uni.hideNavigationBarLoading();
		//成功获取数据后结束下拉刷新
		uni.stopPullDownRefresh();
		isLoading.value = false;
	}

	//下拉刷新
	onPullDownRefresh(() => {
		// 触发下拉刷新时执行
		if (isLoading.value == false) {
			isLoading.value = true;
			// queryParams.currentPage = 1;
			// loadStatus.value = "loading";
			getPeople();
		}
	});

	//下拉监听方法
	onReachBottom(() => {
		// if (loadStatus.value == 'nomore') {
		// 	return;
		// }
		// queryParams.currentPage += 1;
		getPeople();
	});

	// 选中的成员列表
	const choosedMembers = ref([]);

	// 选中成员
	function chooseMember(memberInfo) {
		// 单选时选中即返回，多选时选中后只保存选项，需要通过按钮触发返回
		if (chooseType.value == 'single') {
			// 如果是选转交人，则不能选自己
			// if(operate.value == 'transmit') {
			// 	if(peopleId.value == memberInfo.peopleId) {
			// 		uni.showToast({
			// 			title: "不能转交给自己",
			// 			icon: 'none',
			// 			duration: 2000
			// 		});
			// 		return false;
			// 	}
			// }
			choosedMembers.value = [];
			choosedMembers.value.push(memberInfo)
			// 返回上一页
			uni.navigateBack({
				delta: 1
			});
			// 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
			uni.$emit('chooseMemberCallback', choosedMembers.value);
		} else {
			// 先判断当前的点击的是否已被选中，已被选中的点击是取消选中，未被选中的点击是选中
			let tmpIndex = choosedMembers.value.findIndex(member => {
				return member.peopleId == memberInfo.peopleId
			})
			// 不存在则添加
			if (tmpIndex == -1) {
				choosedMembers.value.push(memberInfo)
			} else {
				// 已存在则删除
				choosedMembers.value.splice(tmpIndex, 1)
			}
			console.log("choosedMembers.value------", choosedMembers.value);
		}
	}
	// 判断当前选项是否已选中
	function isChoosed(memberInfo) {
		let tmpObj = choosedMembers.value.find(item => {
			return item.peopleId == memberInfo.peopleId
		})
		if (!!tmpObj) {
			return true
		} else {
			return false;
		}
	}
	// 提交选中人员
	function submit() {
		// 返回上一页
		uni.navigateBack({
			delta: 1
		});
		// 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
		uni.$emit('chooseMemberCallback', choosedMembers.value);
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background: #F0F7F7;

		.content-container {
			display: block;
			margin: 20rpx;
			padding: 25rpx;
			background: #fff;
			border-radius: 10rpx;
			box-shadow: 1rpx 2rpx 8rpx 6rpx rgba(79, 139, 250, 0.05);
			min-height: 100rpx;

			.article-list {
				width: 100%;

				.article-item {
					display: flex;
					align-items: center;
					width: 100%;
					height: 120rpx;
					margin-bottom: 18rpx;
					padding-top: 18rpx;

					&:last-child {
						padding-bottom: 24rpx;
					}

					.article-radio {
						width: 60rpx;
						padding-bottom: 18rpx;

						.radio-img {
							width: 30rpx;
							height: 30rpx;
							margin: auto;
							background: #FFFFFF;
							border: 2rpx solid #C3C3C3;
							border-radius: 50%;
						}

						.active {
							position: absolute;
							top: 50%;
							left: 50%;
							width: 20rpx;
							height: 20rpx;
							background: #0BBD88;
							border-radius: 50%;
							transform: translate(-50%, -50%);
						}
					}

					.article-content {
						display: flex;
						align-items: center;
						height: 100%;
						border-bottom: 1rpx solid #E0E0E0;
						flex: 1;
						padding-bottom: 18rpx;
						margin-left: 25rpx;

						.article-text {
							margin-left: 20rpx;
							display: flex;
							flex-direction: column;
							align-items: flex-start;
							line-height: 1.5;
						}
					}
				}
			}

			.empty-status {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 50rpx 0;

				.empty-icon {
					display: flex;
					flex-direction: column;
					justify-content: center;

					.no-more {
						margin-top: 20rpx;
						display: flex;
						justify-content: center;
						margin-top: 10rpx;

						.txt {
							text-align: center;
							height: 37rpx;
							font-size: 26rpx;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #333333;
							line-height: 37rpx;
						}
					}
				}
			}
		}
	}

	.submit-container {
		position: absolute;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 125rpx;
		background: #FFFFFF;

		.submit {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-size: 36rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}

	.online {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
	}
</style>