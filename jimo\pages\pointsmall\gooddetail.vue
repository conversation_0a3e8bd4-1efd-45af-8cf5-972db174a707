<template>
  <view class="gooddetail">
    <view class="detailinfo" v-if="!isDeleted">
      <u-swiper
        :list="commoditydetail.imgs"
        height="280"
        imgMode="scaleToFill"
        @change="(e) => (currentNum = e.current)"
        @click="previewimg(currentNum)"
        :autoplay="false"
      >
      </u-swiper>
      <view class="indicator-num" v-if="commoditydetail.imgs">
        <text class="indicator-num__text"
          >{{ currentNum + 1 }}/{{ commoditydetail.imgs.length }}</text
        >
      </view>
      <view class="info">
        <view class="title">
          {{ commoditydetail.commodityTitle }}
        </view>
        <view class="priceline">
          <text class="price">{{ sourceType == '2' ? commoditydetail.integralParty : commoditydetail.integral }}</text>
          <text class="label">积分</text>
        </view>
      </view>
    </view>
    <view class="detailinfo" v-if="!isDeleted">
      <view class="titleline"> 商品详情 </view>
      <view class="richtext">
        <u-parse
          :content="commoditydetail.commodityDesc"
          :lazyLoad="true"
          :tagStyle="style"
        ></u-parse>
      </view>

      <!-- <image
        class="img"
        src="https://img1.baidu.com/it/u=4026683540,2581393474&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333"
      >
      </image>

      <view class="titleline"> 商品信息 </view>
      <view class="infocontainer">
        <view class="infoline">
          <view class="label">商品名称</view>
          <view class="value">双宫桑蚕长丝</view>
        </view>
        <view class="infoline">
          <view class="label">品牌</view>
          <view class="value">山水故事蚕丝被</view>
        </view>
        <view class="infoline">
          <view class="label">单位</view>
          <view class="value">米</view>
        </view>
        <view class="infoline">
          <view class="label">产地</view>
          <view class="value"><view class="p">中国大陆</view></view>
        </view>
        <view class="infoline">
          <view class="label">重量</view>
          <view class="value"><view class="p">山水故事蚕丝被</view></view>
        </view>
        <view class="infoline">
          <view class="label">产品描述</view>
          <view class="value"
            ><view class="p"
              >不可水洗/不可漂白/阴凉处晾晒。图片均为实物拍摄因为拍摄时光线以及显示器不同，会存在一定的色差，介意勿拍。</view
            ></view
          >
        </view>
      </view> -->
    </view>
    <view class="deleted-tip" v-else>
      <text>商品或许已经被删除,将为您返回上一级</text>
    </view>
    <view class="blank"></view>
    <view class="bottom u-border-top">
      <view class="btn pjBtn" @click="goSub">立即购买</view>
      <view class="paddingbottom"></view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { findOne } from '../../api/pointsmall/pointsmall'
import { addOrderNew } from '../../api/pointsmall/hypermarketOrder'
import { loadImage } from '@/common/getImage.js'
const currentNum = ref(0)
const commodityId = ref('')
let commoditydetail = ref({})
let isdisabled = ref(false)
let isDeleted = ref(false)
let style = ref({
  // 字符串的形式
  h1: 'font-size:32rpx;padding:10rpx 0;margin-bottom:20rpx;border-bottom:1rpx solid #e5e5e5',
  h2: 'font-size:32rpx;padding:10rpx 0;margin-bottom:20rpx;border-bottom:1rpx solid #e5e5e5',
  h3: 'font-size:32rpx;padding:10rpx 0;margin-bottom:20rpx;border-bottom:1rpx solid #e5e5e5',
  h4: 'font-size:32rpx;padding:10rpx 0;margin-bottom:20rpx;border-bottom:1rpx solid #e5e5e5',
  h5: 'font-size:32rpx;padding:10rpx 0;margin-bottom:20rpx;border-bottom:1rpx solid #e5e5e5',
  img: 'display:block',
})
let sourceType = ref('1')  //1为家庭 2为党员
onLoad((option) => {
  commodityId.value = option.id
  sourceType.value = option?.sourceType || '1'
  getDetail()
})
async function getDetail() {
  try {
    const res = await findOne({ id: commodityId.value })
    if (res.success) {
      let detail = res.data
      detail.imgs = detail.commodityPicture.split(',')
	  await getTempImg(detail.imgs)
      detail.commodityDesc = detail.commodityDesc
        .replace(/&quot;/g, '')
        .replace(/\<img/gi, '<img style="width:100%";height:auto')

      commoditydetail.value = detail
    } else {
      isDeleted.value = true
      setTimeout(() => {
        uni.$emit('refreshList')
        uni.navigateBack({
          delta: 1,
        })
      }, 1500)
    }
  } catch (e) {
    isDeleted.value = true
    setTimeout(() => {
      uni.$emit('refreshList')
      uni.navigateBack({
        delta: 1,
      })
    }, 1500)
  }
}
async function getTempImg(imgs) {
	for(let i=0; i<imgs.length;i++) {
		let tempimg = await loadImage(imgs[i])
		imgs[i] = tempimg
	}
}
async function goSub() {
  // if (isdisabled.value) return
  // isdisabled.value = true
  return uni.navigateTo({
    url: `/jimo/pages/pointsmall/submitorder?commodityId=${commoditydetail.value.commodityId}&sourceType=${sourceType.value}`,
    complete: () => {
      isdisabled.value = false
    },
  })
  try {
    let res = await addOrderNew({ commodityId: commoditydetail.value.commodityId, role: sourceType.value })
    if (res.success) {
      uni.navigateTo({
        url: `/jimo/pages/pointsmall/submitorder?id=${res.data}&sourceType=${sourceType.value}`,
        complete: () => {
          isdisabled.value = false
        },
      })
    } else {
      isdisabled.value = false
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    isdisabled.value = false
    uni.showToast({
      title: '兑换失败！',
      icon: 'none',
    })
  }
}
function previewimg(index) {
  uni.previewImage({
    current: index,
    urls: commoditydetail.value.imgs,
    referrerPolicy: 'origin', // 必填，否则会受到图床的防盗链影响
    success() {
      console.log('预览成功')
    },
  })
}
</script>
<style>
page {
  background-color: white;
}
</style>
<style lang="scss" scoped>
.indicator-num {
  background-color: rgba(0, 0, 0, 0.35);
  border-radius: 100px;
  width: 35px;
  position: absolute;
  top: 250px;
  right: 40rpx;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: center;
  .indicator-num__text {
    color: #ffffff;
    font-size: 12px;
  }
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  //display: flex;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 180rpx;
}

.gooddetail {
  width: 100%;
  .detailinfo {
    background: white;
    margin-bottom: 20rpx;

    .img {
      width: 100%;
    }
    .info {
      padding: 22rpx 32rpx;
      .title {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 52rpx;
        margin-bottom: 10rpx;
      }
      .priceline {
        display: flex;
        align-items: center;
        .price {
          font-size: 43rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 600;
          color: #666666;
          line-height: 61rpx;
          background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-right: 10rpx;
        }
        .label {
          font-size: 30rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #999999;
          line-height: 42rpx;
        }
      }
    }
    .titleline {
      line-height: 96rpx;
      padding-left: 27rpx;
      font-size: 32rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #333333;
      border-bottom: 1rpx solid #e5e5e5;
    }
    .richtext {
      padding: 28rpx 32rpx;
      padding-top: 10rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      line-height: 1.8;
      ::v-deep img {
        width: 100%;
        display: block;
      }
      ::v-deep h1,
      ::v-deep h2,
      ::v-deep h3,
      ::v-deep h4,
      ::v-deep h5,
      ::v-deep h6 {
        font-size: 32rpx;
        padding: 10rpx 0;
        margin-bottom: 20rpx;
        border-bottom: 1rpx solid #e5e5e5;
      }

      ::v-deep span,
      ::v-deep p,
      ::v-deep td,
      ::v-deep th {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 48rpx;
      }
    }
    .infocontainer {
      display: flex;
      flex-direction: column;
      padding: 30rpx 32rpx 0 32rpx;
      .infoline {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30rpx;
        .label {
          font-size: 28rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #999999;
          line-height: 48rpx;
          min-width: 160rpx;
        }
        .value {
          width: 100%;
          font-size: 28rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #333333;
          line-height: 48rpx;
          text-align: right;
          .p {
            text-align: left;
            display: inline-block;
          }
        }
      }
    }
  }
}

.deleted-tip {
  padding: 40rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
</style>
