import {
    request
} from '@/common/net/request.js';

export function findOrderById(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/findById",
      method: "GET",
      params
    });
  }
  export function findRedeemablePoints(){
    let params = {}
    params['uniContentType'] = 'json'
    return request({
      url: "/user/tbFamilyPoints/points/findRedeemablePoints",
      method: "POST",
      params
    });
  }
  export function findOrderPage(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/findOfPageApp",
      method: "POST",
      params
    });
  }
  export function addOrder(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/add",
      method: "POST",
      params
    });
  }
  // 创建订单（家庭积分/党员积分）
  export function addOrderNew(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/addParty",
      method: "POST",
      params
    });
  }
  export function payUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/payUpdate",
      method: "POST",
      params
    });
  }
  // 支付接口（家庭积分/党员积分）
  export function payUpdateNew(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/payUpdateParty",
      method: "POST",
      params
    });
  }
  export function refundUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/refundUpdate",
      method: "POST",
      params
    });
  }

  export function exchangeUpdate(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/hypermarketOrder/exchangeUpdate",
      method: "POST",
      params
    });
  }