<template>
    <view class="main-conitaner">
        <view class="top-part">
            <u-navbar title="地图导览" bgColor="transparent" :placeholder="true" leftIconColor='#000' :autoBack="true" />
            <u-tabs :list="tabList" @click="changeTab" lineWidth="30"
                lineColor="linear-gradient( 270deg, #0FC568 0%, #4BE092 100%)" :activeStyle="activeTabStyle"
                :inactiveStyle="inactiveTabStyle" :itemStyle="itemStyle"></u-tabs>
        </view>
        <view :style="scrollViewHeight">
            <map :class="['map']" :id="'map'" theme="satellite" :scale="18" :longitude="longitude" :latitude="latitude"
                :show-location="true" :markers="markers" @markertap="handleMarkerTap"
                @callouttap="handleMarkerTap"></map>
        </view>
        <view class="fixed-part" @click="handleCurrentLocation">
            <image :src="VILLAGECODETOUR_MAP_LOCATION" class="loc-img"></image>
        </view>
        <up-popup :show="dialogVisible" :round="10" @close="dialogVisible=false">
            <view class="detail-dialog">
                <view class="title">{{current?.title}}</view>
                <view class="desc" v-if="current && current.content">{{current?.content}}</view>
                <view class="btn-list">
                    <view class="btn-item" @click="handleClick('1')">
                        <image :src="VILLAGECODETOUR_MAP_NAVIGATE" class="img1"></image>
                        <view class="text">导航</view>
                    </view>
                    <view class="btn-item" @click="handleClick('2')">
                        <image :src="VILLAGECODETOUR_MAP_DETAIL" class="img1"></image>
                        <view class="text">详情</view>
                    </view>
                </view>
            </view>

        </up-popup>
    </view>
</template>

<script setup>
    import {
        ref,
        onMounted,
        computed
    } from 'vue';
    import {
        onLoad,
        onUnload,
        onShow,
    } from '@dcloudio/uni-app'
    import {
        VILLAGECODETOUR_MAP_NAVIGATE,
        VILLAGECODETOUR_MAP_DETAIL,
        VILLAGECODETOUR_MAP_LOCATION
    } from '@/common/net/staticUrl.js'
    import {
        getMapMarkers
    } from '../../api/villageCodeTour/villageCodeTour.js'
    const longitude = ref(null); // 经度
    const latitude = ref(null); // 纬度
    const cur_longitude = ref(0); //当前人位置经度
    const cur_latitude = ref(0); //当前人位置纬度
    const markers = ref([]); // 标记点
    const currTabIndex = ref('')
    const dialogVisible = ref(false)
    const tabList = ref([{
            key: '',
            name: '全部',
        }, {
            key: 'ticketsfun',
            name: '景区'
        },
        {
            key: 'hotelshomestays',
            name: '民宿'
        }
    ])
    // tab选中菜单的样式
    const activeTabStyle = {
        color: '#0BBD88',
        fontSize: '32rpx',
    }
    // tab未选中菜单的样式
    const inactiveTabStyle = {
        color: '#333333',
    }
    // tab菜单item样式
    const itemStyle = {
        paddingLeft: '60rpx',
        paddingRight: '60rpx',
        paddingBottom: '28rpx',
        height: '34rpx',
        fontSize: '28rpx',
        fontFamily: 'Source Han Sans CN, Source Han Sans CN',
        fontWeight: 500,
    }
    const regionCode = ref('')
    const current = ref(null)
    onLoad((options) => {
        // 如果参数中有经纬度，则是在更新设备位置，需要回显之前的位置
        if (options.regionCode) {
            regionCode.value = options.regionCode
            getList()
        }
        getUserLocation()
    })

    // 设置scrollview高度
    const scrollViewHeight = computed(() => {
        let height = 0;
        uni.getSystemInfo({
            success: (res) => {
                console.log('getSystemInfo', res);
                // scrollview高度=屏幕可用高度-底部安全高度 
                height = (res.windowHeight - res.safeAreaInsets.bottom)
            }
        })
        console.log('----------------height', height)
        return `height: ${height - uni.getSystemInfoSync().statusBarHeight - 44}px`
    })

    function getList() {
        getMapMarkers({
            regionCode: regionCode.value,
            parentKey: currTabIndex.value,
        }).then((res) => {
            if (res.success) {
                if (res.data && res.data.length > 0) {
                    longitude.value = res.data[0].longitude
                    latitude.value = res.data[0].latitude;
                    markers.value = res.data.map(item => {
                        return {
                            id: item.sortId,
                            contentId: item.contentId,
                            parentKey: item.parentKey,
                            content: item.content,
                            title: item.title,
                            latitude: item.latitude,
                            longitude: item.longitude,
                            iconPath: item.parentKey == 'ticketsfun' ?
                                '/static/images/scenic-spot.png' : '/static/images/homestay.png',
                            width: 20,
                            height: 20,
                            callout: { //自定义标记点上方的气泡窗口 点击有效
                                content: item.title, //文本
                                color: '#000616', //文字颜色
                                fontSize: 12, //文本大小
                                borderRadius: 5, //边框圆角
                                padding: 5,
                                bgColor: item.parentKey == 'ticketsfun' ? '#DAFDD5' : '#FFE9CF', //背景颜色
                                display: 'ALWAYS', //常显
                            }
                        }
                    });
                }
                console.log('------------markers.value', markers.value)
            }
        })
    }
    //回到当前人得位置
    function handleCurrentLocation() {
        longitude.value = cur_longitude.value;
        latitude.value = cur_latitude.value;
    }

    function getUserLocation() {
        uni.getLocation({
            type: "gcj02",
            success: function(res) {
                // 暂时
                cur_longitude.value = res.longitude; //118.787575;
                cur_latitude.value = res.latitude; //32.05024;
                console.log("获取当前的用户经度", cur_longitude.value);
                console.log("获取当前的用户纬度", cur_latitude.value);
            }
        })
    }

    function handleClick(num) {
        dialogVisible.value = false;
        console.log('------------------current.value')
        console.log(current.value)
        if (num == '1') {
            openMap();
           
        } else {
            uni.navigateTo({
                url: `/jimo/pages/villageCodeTour/detail?parentKey=${current.value.parentKey}&contentId=${current.value.contentId}`
            })
        }
    }

    function changeTab(e) {
        console.log(e)
        currTabIndex.value = e.key;
		markers.value = []
        getList()
    }
    // 用户点击地图
    const handleMarkerTap = (ev) => {
        console.log('-------------ev')
        console.log(markers.value)
        const {
            markerId
        } = ev.detail;
        console.log('----------markerId', markers.value.find(item => item.id == markerId))
        current.value = markers.value.find(item => item.id == markerId);
        current.value.content = filterTag(current.value.content)
        dialogVisible.value = true;
    }
    //过滤掉html标签
    function filterTag(content) {
        if (!content) return ''
        if (typeof content != 'string') {
            //不是HTML字符串格式的暂不处理
            contentArr[0] = content;
            return false;
        }
        const str = content.replace(/<.*?>/g, "");
        console.log('----------filterTag', str)
        return str;
    }

    // 打开地图
    function openMap() {
        let lon = current.value.longitude;
        let lat = current.value.latitude;
        const name = current.value.title;
        const address = current.value.position;
        console.log("获取经纬度ssssfff", lon, lat);
        //打开地图
        // #ifdef MP-WEIXIN
        console.log('------------map-weixin')
        uni.openLocation({
            latitude: parseFloat(lat),
            longitude: parseFloat(lon),
            name,
            address,
            scale: 18,
            success: function(res) {
                console.log('打开系统位置地图成功')
            },
            fail: function(error) {
                console.log(error)
            }
        })
        // #endif
        // #ifdef APP-PLUS
        console.log('------------APP-PLUS')
        // openMapApp(lat, lon, currentInfo.value.title);
        handleNavigation()
        // #endif
    }

    function handleNavigation() {
        // #ifdef APP-PLUS
        // 判断系统安装的地图应用有哪些, 并生成菜单按钮
        let mapList = [{
                title: '高德地图',
                name: 'amap',
                androidName: 'com.autonavi.minimap',
                iosName: 'iosamap://'
            },
            {
                title: '百度地图',
                name: 'baidumap',
                androidName: 'com.baidu.BaiduMap',
                iosName: 'baidumap://'
            },
            {
                title: '腾讯地图',
                name: 'qqmap',
                androidName: 'com.tencent.map',
                iosName: 'qqmap://'
            }
        ];
        // 根据真机有的地图软件 生成的 操作菜单
        let buttons = [];
        let platform = uni.getSystemInfoSync().platform;
        platform === 'android' &&
            mapList.forEach(item => {
                if (plus.runtime.isApplicationExist({
                        pname: item.androidName
                    })) {
                    buttons.push(item);
                }
            });
        platform === 'ios' &&
            mapList.forEach(item => {
                console.log(item.iosName);
                if (plus.runtime.isApplicationExist({
                        action: item.iosName
                    })) {
                    buttons.push(item);
                }
            });
        if (buttons.length) {
            plus.nativeUI.actionSheet({
                    //选择菜单
                    title: '选择地图应用',
                    cancel: '取消',
                    buttons: buttons
                },
                e => {
                    let _map = buttons[e.index - 1];
                    openURL(_map, platform);
                }
            );
        } else {
            uni.showToast({
                title: '请安装地图软件',
                icon: 'none'
            });
            return;
        }
        // #endif
    }

    // 打开第三方程序实际应用
    function openURL(map, platform) {
        console.log('----------------current', current.value)
        const name = current.value.title;
        const latitude = current.value.latitude;
        const longitude = current.value.longitude;
        let urls = {
            android: {
                amap: `amapuri://route/plan/?sid=&did=&dlat=${latitude}&dlon=${longitude}&dname=${name}&dev=0&t=0`,
                qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${latitude},${longitude}&referer=ixinyi_client`,
                baidumap: `baidumap://map/direction?origin=${cur_latitude.value},${cur_longitude.value}&destination=name:${name}|latlng:${latitude.value},${longitude.value}&coord_type=wgs84&mode=driving&src=andr.baidu.openAPIdemo"`
            },
            ios: {
                amap: `iosamap://path?sourceApplication=ixinyi_client&dlat=${latitude}&dlon=${longitude}&dname=${name}&dev=0&t=0`,
                qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${latitude},${longitude}&referer=ixinyi_client`,
                baidumap: `baidumap://map/direction?origin=${cur_latitude.value},${cur_longitude.value}&destination=name:${name}|latlng:${latitude},${longitude}&mode=driving&src=ios.baidu.openAPIdemo`
            }
        };
        let newurl = encodeURI(urls[platform][map.name]);
        console.log(newurl);
        plus.runtime.openURL(
            newurl,
            function(res) {
                uni.showModal({
                    content: res.message
                });
            },
            map.androidName ? map.androidName : ''
        );
    }
</script>

<style lang="scss" scoped>
    .main-conitaner {
        view {
            box-sizing: border-box;
            font-family: Source Han Sans CN, Source Han Sans CN;
        }
    }

    .top-part {
        height: 230rpx;
        background: linear-gradient(180deg, rgba(102, 208, 130, 0.15) 0%, rgba(244, 248, 247, 0.15) 100%);
    }

    .map_container {
        position: relative;
        top: 0;
        bottom: 80px;
        /* 底部留出的空间 */
        left: 0;
        right: 0;
    }

    .fixed-part {
        position: fixed;
        right: 0;
        bottom: 300rpx;

        .loc-img {
            width: 81rpx;
            height: 81rpx;
        }
    }

    .map {
        width: 100%;
        /* 计算总的高度，减去底部空间，然后取45% */
        // height: calc((100vh - 80px) * 0.45);
        height: 100%;
    }

    .map_text {
        background: #fff;
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    text {
        margin: 5px 0;
        display: block;
        font-size: 12px;
    }

    .h1 {
        margin: 15px 0;
        font-size: 15px;
    }

    .detail-dialog {
        padding: 20rpx 40rpx;
        background: linear-gradient(180deg, #F0FFF9 0%, #FFFFFF 80%, #FFFFFF 100%);
        box-shadow: 0rpx 10rpx 11rpx 0rpx rgba(172, 172, 172, 0.22);
        border-radius: 20rpx;

        .title {
            height: 60rpx;
            font-family: SourceHanSansSC-Regular, SourceHanSansSC;
            font-weight: 600;
            font-size: 30rpx;
            color: #000616;
            line-height: 30rpx;
            display: -webkit-box;
            /* 设置为WebKit内核的弹性盒子模型 */
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 5;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .desc {
            margin-bottom: 70rpx;
            width: 100%;
            height: 96rpx;
            font-family: SourceHanSansSC-Regular, SourceHanSansSC;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            line-height: 32rpx;
            display: -webkit-box;
            /* 设置为WebKit内核的弹性盒子模型 */
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 5;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-list {
            display: flex;
            height: 50rpx;

            .btn-item {
                display: flex;

                &:nth-child(2) {
                    margin-left: 30rpx;
                }

                .img1 {
                    width: 28rpx;
                    height: 28rpx;
                }

                .text {
                    margin-left: 10rpx;
                    height: 24rpx;
                    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #333333;
                    line-height: 36rpx;
                }
            }


        }

    }


    .paddingbottom {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }
</style>