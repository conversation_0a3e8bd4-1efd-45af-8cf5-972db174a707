import { burialPoint, burialPointAnonymous } from './api.js'

// 登录状态埋点
export function setPoints(param) {
	burialPoint(param).then(res => {
		if (res.success) {
			console.log('埋点成功',param);
		} else {
			console.log('埋点失败', param);
		}
	}).catch(err => {
		console.log(err);
	})
}
// 未登录状态埋点
export function setPointsAnonymous(param) {
	burialPointAnonymous(param).then(res => {
		if (res.success) {
			console.log('埋点成功',param);
		} else {
			console.log('埋点失败', param);
		}
	}).catch(err => {
		console.log(err);
	})
}