import {
    request
} from '../request.js'

// 根据key获取栏目信息
export function getColumnByKey(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/column/findListByColumnKeys',
        method: 'GET',
        params,
    })
}

// 根据dictCode获取列表
export function getDictListByKey(params) {
    params['uniContentType'] = 'json'
    const code = params.code
    delete params.code
    return request({
        url: '/user/dict/data/list/' + code,
        method: 'GET',
        params,
    })
}

// 根据流程key获取id
export function getFlowIdByKey(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/flow/tbFlow/findFlowInfoByFlowFlag',
        method: 'POST',
        params,
    })
}