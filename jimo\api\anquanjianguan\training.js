import { request } from '@/common/net/request.js';

export class TrainingService {

	// 获取培训活动列表
	static getTrainingList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/training/findPage',
		    method: 'GET',
			params
		})
	}

	// 根据ID获取培训活动详情
	static findTrainingById (params) {
			params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/training/findInfo',
		    method: 'GET',
			params
		})
	}

	// 新增培训活动
	static addTraining (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/training/add',
		    method: 'POST',
			params
		})
	}

	// 更新培训活动
	static updateTraining (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/training/update',
		    method: 'POST',
			params
		})
	}

	// 删除培训活动
	static deleteTraining (params) {
		params['uniContentType'] = 'json'

		return request({
		    url: '/farm/enterprise/training/delete',
		    method: 'GET',
			params
		})
	}

}
