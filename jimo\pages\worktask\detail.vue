<template>
	<view class="detailcon">
		<view class="topinfo">
			<view class="title">{{workform.missionName}} </view>
			<view class="tag" :class="getTagClass()">{{getStatusText()}}</view>
			<view class="cell">
				<view class="label">工作要求</view>
				<view class="value">{{workform.missionContent}}</view>
			</view>
			<view class="cell">
				<view class="label">工作类别</view>
				<view class="value">{{selectDictLabel(typelist,workform.missionType)}}</view>
			</view>
			<view class="cell">
				<view class="label">完成时限</view>
				<view class="value">{{workform.completeTime}}</view>
			</view>
			<view class="cell">
				<view class="label">图片</view>
				<view class="value">
					<view class="imgbox" v-for="(item,index) in taskimgs" :key="item">
						<view class="imgcon">
							<image @click="previewHandler(index,taskimgs)" :src="item"></image>
						</view>
					</view>

				</view>
			</view>
			<!--  #ifdef  APP-PLUS -->
			<!-- <view class="cell">
				<view class="label">附件</view>
				<view>
					<view v-for="(item,index) in taskfiles" :key="item" class="fileitem">
						<image :src="FILE_ADD" mode="widthFix" style="width: 48rpx; height: 48rpx; margin-right: 18rpx">
						</image>
						<text>{{ missionAnnexNames[index] }}</text>

					</view>
				</view>
			</view> -->
			<!--  #endif -->
		</view>
		<view class="topinfo">
			<view class="cell">
				<view class="label">完成进度</view>
				<view class="value">
					<view class="progresscon">
						<text></text>
						<u-line-progress v-if="workform && workform.peopleSchedule == 100"
							:percentage="workform.peopleSchedule" height="6" :showText="false"></u-line-progress>
						<u-slider v-else v-model="workform.peopleSchedule" block-color="#3cc16c" activeColor="#3cc16c"
							:min="0" :max="100" height="6rpx"></u-slider>

						<text class="percent">{{workform.peopleSchedule}}%</text>
					</view>
				</view>
			</view>
			<view class="cell">
				<view class="label">完成情况</view>
				<view class="value">
					<u-textarea v-model.trim="workform.peopleContent" placeholder="请输入内容" :height="90"
						:cursorSpacing="140"></u-textarea>
				</view>
			</view>
			<view class="cell">
				<view class="label">图片</view>
				<view class="imgcell">
					<view class="imgitem" v-for="(item, index) in imglist" :key="item">
						<!-- <image
							class="img"
							:src="item"
							@click="previewHandler(index, imglist)"
						></image> -->
						<view class="img">
							<safe-image :src="item" width='219rpx' height='164rpx'></safe-image>
						</view>
						<image class="closebtn" :src="CLOSE_BTN" @click="deleteHanlder(item)"></image>
					</view>
					<image :src="IMG_ADD" mode="widthFix" v-show="showaddimg" 
						style="width: 48rpx; height: 48rpx; margin-top: 18rpx; margin-bottom: 12rpx;"
						@click="uploadimgHandler"></image>
				</view>
			</view>
			<!--  #ifdef  APP-PLUS -->
			<!-- <view class="cell">
				<view class="label">附件</view>
				<view>
					<view v-for="(item,index) in filelist" :key="item" class="fileitem">
						<image :src="FILE_ADD" mode="widthFix" style="width: 48rpx; height: 48rpx; margin-right: 18rpx">
						</image>
						<text>{{ peopleAnnexNames[index] }}</text>
						<image class="closebtn" :src="CLOSE_BTN" @click="deleteFileHanlder(item)"></image>
					</view>

					<image :src="FILE_ADD" mode="widthFix" v-show="showaddfile" 
						style=" width: 48rpx; height: 48rpx; margin-top: 18rpx; margin-bottom: 12rpx;"
						@click="uploadfileHandler"></image>
				</view>
			</view> -->
			<!--  #endif -->
		</view>
		<view class="blank"></view>
		<view class="bottom u-border-top">
			<view class="btn pjBtn" @click="goSub">确定</view>
			<view class="paddingbottom"></view>
		</view>

	</view>
</template>
<script setup>
	import {
		reactive,
		ref,
		computed
	} from 'vue'
	import {
		onLoad,
		onUnload
	} from '@dcloudio/uni-app'
	import {
		IMG_ADD,
		FILE_ADD,
		RIGHT_ARROW,
		CLOSE_BTN
	} from '@/common/net/staticUrl'
	import {
		uploadFile
	} from '@/common/net/request.js'
	import {
		API_FILEUPLOAD_URL
	} from '@/common/net/netUrl.js'
	import {
		findWorkById,
		updateWork
	} from '../../api/worktask/worktask'
	import {
		getDicts,
	} from '@/common/net/contacts/contacts.js'
	import {
		useTokenStore
	} from '@/store/token.js'
	const tokenStore = useTokenStore()
	const taskimgs = ref([])
	const taskfiles = ref([])
	const imglist = ref([])
	const filelist = ref([])
	const typelist = ref([])
	const isdisabled = ref(false)
	const workform = ref({
		peopleSchedule: ''
	})
	let missionAnnexNames = reactive([])
	let peopleAnnexNames = reactive([])
	const showaddimg = computed(() => {
		return imglist.value.length >= 9 ? false : true
	})
	onLoad((option) => {
		const id = option.id;
		getDicts("task_type").then((response) => {
			typelist.value = response.data;
		});
		findDetail(id)
		uni.$on('clickImg', (index) => {
			previewPics(index)
		})
	})
	onUnload(() => {
		uni.$off('clickImg')
	})

	function getTagClass() {
		if (workform.value.peopleStatus == 0) {
			return ''
		}
		if (workform.value.peopleStatus == 1 || workform.value.peopleStatus == 3) {
			return 'wctag'
		}
		if (workform.value.peopleStatus == 2) {
			return 'yqtag'
		}
	}

	function getStatusText() {
		if (workform.value.peopleStatus == 0) {
			return '待完成'
		}
		if (workform.value.peopleStatus == 1) {
			return '已完成'
		}
		if (workform.value.peopleStatus == 2) {
			return '已逾期'
		}
		if (workform.value.peopleStatus == 3) {
			return '逾期完成'
		}
	}


	function selectDictLabel(datas, value) {
		var actions = [];
		Object.keys(datas).some((key) => {
			if (datas[key].dictValue == ('' + value)) {
				actions.push(datas[key].dictLabel);
				return true;
			}
		})
		return actions.join('');
	}
	async function findDetail(id) {
		try {
			let res = await findWorkById({
				id
			})
			if (res.success) {
				workform.value = res.data
				taskimgs.value = workform.value.missionPicture.length > 0 ? workform.value.missionPicture.split(',') :
					[],
					taskimgs.value = await getTempImg(taskimgs.value)
				taskfiles.value = workform.value.missionAnnex.length > 0 ? workform.value.missionAnnex.split(',') : []
				imglist.value = workform.value.peoplePicture.length > 0 ? workform.value.peoplePicture.split(',') : [],
					imglist.value = await getTempImg(imglist.value)
				filelist.value = workform.value.peopleAnnex.length > 0 ? workform.value.peopleAnnex.split(',') : []
				missionAnnexNames = res.data.missionAnnexNames.length > 0 ? res.data.missionAnnexNames.join(',') : []
				console.log(missionAnnexNames, '99999999')
				peopleAnnexNames = res.data.peopleAnnexNames.length > 0 ? res.data.peopleAnnexNames.join(',') : []
			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} catch (e) {
			uni.showToast({
				title: '查询事务详情失败！',
				icon: 'none',
			})
		}
	}
	async function getTempImg(imgs) {
		let result = []
		for (let i = 0; i < imgs.length; i++) {
			let tempimg = await loadImage(imgs[i])
			result.push(tempimg)
		}
		return result
	}

	function uploadimgHandler() {
		uni.chooseImage({
			count: 9 - imglist.value.length, //默认9
			extension: ['.jpg', '.png', '.gif'],
			sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
			sourceType: ['album'], //从相册选择
			success: async (res) => {
				const tempFilePaths = res.tempFilePaths
				uni.showLoading({
					mask: true
				})
				let list = await uploadFileList(tempFilePaths)
				imglist.value = [...imglist.value, ...list]
				uni.hideLoading()
			},
		})
	}

	async function goSub() {
		if (isdisabled.value) return
		isdisabled.value = true
		try {
			workform.value.peoplePicture = imglist.value ? imglist.value.join(',') : ''
			workform.value.peopleAnnex = filelist.value ? filelist.value.join(',') : ''
			console.log(peopleAnnexNames, 'gggggggggggg')
			workform.value.peopleAnnexNames = peopleAnnexNames ? peopleAnnexNames.join(',') : ''
			let {
				peopleSchedule,
				peopleContent,
				peoplePicture,
				peopleAnnex,
				peopleId,
				peopleAnnexNames
			} = workform.value
			let res = await updateWork({
				peopleSchedule,
				peopleContent,
				peoplePicture,
				peopleAnnex,
				peopleId,
				peopleAnnexNames
			})
			if (res.success) {
				setTimeout(() => {
					let page = prePage(1)
					console.log(page)
					if(page.$scope.route.indexOf('/message') == -1){
						page.refreshData()
					}
					uni.navigateBack({
						delta: 1,
						complete: () => {
							isdisabled.value = false
							uni.showToast({
								title: '修改成功!',
								icon: 'none',
							})
						},
					})
				}, 500)
				//   uni.navigateBack({
				//     delta: 1,
				//     complete: () => {
				//       isdisabled.value = false
				//       uni.showToast({
				//      title:'修改成功!',
				//      icon: 'none',
				// })
				//     },
				//   })

			} else {
				isdisabled.value = false
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} catch (e) {
			isdisabled.value = false
			uni.showToast({
				title: '修改失败！',
				icon: 'none',
			})
		}
	}

	function deleteHanlder(item) {
		let index = imglist.value.indexOf(item)
		imglist.value.splice(index, 1)
	}



	function previewHandler(index, imgs) {
		console.log(index, imgs)
		uni.previewImage({
			current: index,
			urls: imgs,
			referrerPolicy: 'origin', // 必填，否则会受到图床的防盗链影响
			success() {
				console.log('预览成功')
			},
		})
	}
	//查看详情时的预览图片
	const previewPics = async (index) => {
		let pics = []
		for (let i = 0; i < imglist.value.length; i++) {
			let a = await loadImage(imglist.value[i])
			pics.push(a)
		}
		Promise.all(pics).then((result) => {})
		uni.previewImage({
			urls: pics
		})
	}

	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}

	function uploadfileHandler() {

		uni.chooseMessageFile({
			count: 9,
			extension: [
				'.xls',
				'.xlsx',
				'.doc',
				'.docx',
				'.ppt',
				'.pptx',
				'.pdf',
			],
			type: 'file',
			success: async (res) => {
				const tempFilePaths = res.tempFiles.map(item => item.path)
				const tempFileNames = res.tempFiles.map(item => item.name)
				uni.showLoading({
					mask: true
				})
				let list = await uploadFileList(tempFilePaths, tempFileNames)
				filelist.value = [...filelist.value, ...list.files]
				peopleAnnexNames.value = [...peopleAnnexNames.value, ...list.names]
				uni.hideLoading()
			},
		})
	}

	function prePage(step) {
		let num = step ? 1 + step : 2
		let pages = getCurrentPages()
		let prePage = pages[pages.length - num] ?
			pages[pages.length - num] :
			pages[pages.length - num + 1]
		// #ifdef H5
		return prePage
		// #endif
		return prePage.$vm
	}
	async function uploadFileList(files, names) {
		return new Promise(async (resolve, reject) => {
			let filelist = []
			let namelist = []
			for (let item of files) {
				try {
					let res = await uploadFile({
						url: API_FILEUPLOAD_URL,
						method: 'POST',
						params: {
							filePath: item
						},
					})
					if (res.success) {
						let imgdata = res.data.url
						if (names) {
							namelist.push(names[files.indexOf(item)])
						}
						filelist.push(imgdata)
					} else {
						uni.showToast({
							title: res.message,
							icon: 'none',
							duration: 2000,
						})
					}
				} catch (e) {}
			}
			if (names) {
				resolve({
					files: filelist,
					names: namelist
				})
			} else {
				resolve(filelist)
			}
		})
	}
</script>
<style>
	page {
		background-color: #f4f8f7;
	}
</style>
<style lang="scss" scoped>
	::v-deep .tktip {
		padding: 80rpx 60rpx 40rpx 60rpx;
		width: 650rpx;
		box-sizing: border-box;

		.content {
			font-size: 32rpx;

			color: #111111;
			line-height: 55rpx;
			margin-bottom: 10rpx;
		}

		.btngroup {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.cancel {
				width: 240rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background: #ffffff;
				border-radius: 41rpx;
				border: 2rpx solid #e5e5e5;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #555555;
			}

			.okbtn {
				width: 240rpx;
				height: 80rpx;
				background: #0cbe88;
				border-radius: 41rpx;
				line-height: 80rpx;
				text-align: center;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
			}
		}
	}

	.bottom {
		position: fixed;
		width: 100%;
		background: #fff;
		bottom: 0;
		left: 0;
		//display: flex;
		align-items: center;
		padding: 20rpx 38rpx;
		z-index: 100;
		box-sizing: border-box;

		.btn {
			line-height: 80rpx;
			border-radius: 40rpx;
			flex: 1;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36rpx;
		}

		.pjBtn {
			background: #3cc16c;
			color: #fff;
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.blank {
		width: 100%;
		height: 180rpx;
	}

	.label {
		font-size: 28rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #999999;
		line-height: 40rpx;
		margin-bottom: 12rpx;
	}

	.imgcell {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.fileitem {
		display: flex;
		flex-wrap: wrap;
		white-space: nowrap;
		position: relative;

		text {
			font-size: 24rpx;
			color: #ccc;
			white-space: nowrap;
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.closebtn {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.imgitem {
		width: 233rpx;
		height: 179rpx;
		border: 0.5px solid #E2E2E2;
		margin-right: 40rpx;
		margin-bottom: 20rpx;
		position: relative;

		.img {
			margin: 7rpx;
			width: 219rpx;
			height: 164rpx;
		}

		.closebtn {
			width: 40rpx;
			height: 40rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.detailcon {
		width: 100%;

		.topinfo {
			margin: 20rpx;
			background: #ffffff;
			border-radius: 8rpx;
			padding: 23rpx 26rpx;
			position: relative;

			.title {
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
				line-height: 60rpx;
			}

			.tag {
				position: absolute;
				right: 0;
				top: 0;
				width: 128rpx;
				height: 62rpx;
				background: rgba(255, 200, 168, 0.4);
				border-radius: 0rpx 8rpx 0rpx 26rpx;
				line-height: 62rpx;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ff6d1c;
				text-align: center;
			}

			.wctag {
				background: rgba(180, 242, 183, 0.4);
				color: rgba(1, 189, 93, 1);
			}

			.yqtag {
				background-color: rgba(242, 180, 180, 0.38);
				color: #e90000;
			}

			.cell {
				padding-top: 25rpx;
				margin-bottom: 15rpx;


				.value {
					font-size: 32rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					line-height: 45rpx;
					display: flex;
					flex-wrap: wrap;

					.progresscon {
						width: 100%;
						display: flex;
						align-items: center;

						::v-deep .u-slider {
							width: calc(100% - 80rpx);

							.u-slider__button-wrap {
								top: 4rpx;
							}
						}

						.percent {
							font-size: 28rpx;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #0bbd88;
							min-width: 80rpx;
							text-align: right;
						}
					}

					.imgbox {
						width: 233rpx;
						height: 179rpx;
						border: 0.5px solid #e2e2e2;
						overflow: hidden;
						margin-right: 20rpx;
						margin-bottom: 20rpx;

						.imgcon {
							margin: 7rpx;
							width: 219rpx;
							height: 165rpx;
							overflow: hidden;
							position: relative;

							image {
								width: 100%;
								height: 100%;
								// position: absolute;
								// top: 50%;
								// transform: translateY(-50%);
							}
						}
					}
				}
			}
		}
	}
</style>