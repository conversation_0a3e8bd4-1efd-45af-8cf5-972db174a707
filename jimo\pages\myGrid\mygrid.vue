<template>
	<view class="grid-container paddingbottom">
		<u-navbar title="网格管理" bgColor="#0abd88" :autoBack="true" :placeholder="true" leftIconColor='#fff'
			titleStyle="font-size: 36rpx;color: #fff">
		</u-navbar>
		<!-- 顶部渐变色背景 -->
		<topBgBox :boxOffset="true"></topBgBox>
		<!-- 数据总览 -->
		<view class="statistics-container">
			<view class="statistics-base">
				<view class="grid-name u-line-1">
					{{gridName}}数据总览
				</view>
				<view class="date" @click="changeDate">
					<view class="month">{{dateDisp}}</view>
					<u-icon name="arrow-down-fill"></u-icon>
				</view>
			</view>
			<view class="statistics-list">
				<!-- 事务下发 -->
				<view class="statistics-item" @click="goToList('workDistribute')">
					<view class="work-count color0">
						{{workDistribute}}
					</view>
					<view class="work-name">事务下发</view>
				</view>
				<!-- 工作上报 -->
				<view class="statistics-item" @click="goToList('workReport')">
					<view class="work-count color1">
						{{workReport}}
					</view>
					<view class="work-name">工作上报</view>
				</view>
				<!-- 工作任务 -->
				<view class="statistics-item" @click="goToList('workTask')">
					<view class="work-count color2">
						{{workTask}}
					</view>
					<view class="work-name">工作任务</view>
				</view>
				
			</view>
		</view>
		<!-- 任务统计 -->
		<view class="task-statistics" :style="taskStyle" @click="goToMyTask">
			<view class="label">
				<u-image src="@/jimo/static/images/myGrid/statistics.png" width="32rpx"
					height="32rpx"></u-image>
				<text class="title">我的任务统计</text>
			</view>
			<u-image src="@/jimo/static/images/myGrid/arrow-green.png" width="32rpx"
				height="32rpx"></u-image>
		</view>
		<!-- 组织机构、网格排名 -->
		<view class="quick-link">
			<view class="organization" :style="organizationStyle" @click="goToOrganization">
				<view class="label">
					<u-image src="@/jimo/static/images/myGrid/organization.png" width="32rpx"
						height="32rpx"></u-image>
					<text class="title">组织结构</text>
				</view>
				<u-image src="@/jimo/static/images/myGrid/arrow-blue.png" width="32rpx"
					height="32rpx"></u-image>
			</view>
			<view class="grid-rank" :style="rankStyle" @click="goToGridRank">
				<view class="label">
					<u-image src="@/jimo/static/images/myGrid/rank.png" width="32rpx"
						height="32rpx"></u-image>
					<text class="title">网格排名</text>
				</view>
				<u-image src="@/jimo/static/images/myGrid/arrow-orange.png" width="32rpx"
					height="32rpx"></u-image>
			</view>
		</view>
		<!-- 人员构成 -->
		<view class="people-structure">
			<view class="structure-item">
				<view class="tag-count">{{countAll}}</view>
				<view class="tag-name">全部</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countMan}}</view>
				<view class="tag-name">男性</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countWoman}}</view>
				<view class="tag-name">女性</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countKid}}</view>
				<view class="tag-name">少年儿童</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countOldPeople}}</view>
				<view class="tag-name">老年人口</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countWorkAge}}</view>
				<view class="tag-name">劳动年龄</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countFocus}}</view>
				<view class="tag-name">重点关注</view>
			</view>
			<view class="structure-item">
				<view class="tag-count">{{countPartyMember}}</view>
				<view class="tag-name">党员</view>
			</view>
		</view>
		<!-- 搜索栏 -->
		<u-input v-model="keyword" shape='circle' border='none' :clearable='true' placeholder="请输入姓名搜索"
			placeholder-style="color:#909399;font-size: 28rpx" confirmType='search' prefixIcon="search"
			:customStyle="{'background': '#fff','padding': '12rpx 18rpx'}" fontSize='28rpx' @confirm="searchMember"
			@clear='clearName'></u-input>
		<view class="people-list">
			<view class="curr-grid" @click="changeGrid">
				<view class="grid-name">{{currentGridName}}</view>
				<u-icon name="arrow-down-fill" color="#d8d8d8"></u-icon>
			</view>
			<view class="head">
				<view class="head-item" v-for="(item,index) in headList" :key="index">
					{{item}}
				</view>
			</view>
			<scroll-view :scroll-y="true" @scrolltolower="loadMore" class="content">
				<view class="content-row" v-for="(item, index) in peopleList" :key="index" @click="goToDetail(item)">
					<view class="content-item u-line-1">
						{{item.name}}
					</view>
					<view class="content-item">
						{{getDictLabel('realation', item.relation)}}
					</view>
					<view class="content-item">
						{{item.birthday || '--'}}
					</view>
					<view class="content-item">
						{{getDictLabel('sex', item.sex)}}
					</view>
					<view class="content-item u-line-1">
						{{item.gridName || '--'}}
					</view>
				</view>
				<view class="empty-status" v-if="peopleList.length == 0">
					<view class="empty-icon">
						<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
							height="150rpx"></u-image>
						<view class="no-more">
							<span class="txt">暂无数据</span>
						</view>
					</view>
				</view>
				<!-- 底部加载，三个状态：loadmore、loading、nomore -->
				<u-loadmore :status="loadStatus" v-if="peopleList.length > 0"></u-loadmore>
			</scroll-view>
		</view>
		<!-- <u-calendar :show="showCalendar" mode="range" :allowSameDay='true' :minDate="minDate" :maxDate="maxDate"
			:defaultDate="[beginTime, endTime]" monthNum='150' @confirm="confirmDate"
			@close="closeCalendar"></u-calendar> -->
		<uni-calendar
			ref="calendar"
			:clear-date="true"
			:insert="false"
			:startDate="minDate"
			:endDate="maxDate"
			:range="true"
			@confirm="confirmDate"
			@close="closeCalendar"
		/>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import { onLoad, onUnload, onShow } from '@dcloudio/uni-app'
	import topBgBox from '@/components/top-bg-box/top-bg-box.vue'
	import { GRID_TASK_BG, GRID_WGPM_BG, GRID_ZZJG_BG, NO_MORE_IMG } from '@/common/net/staticUrl.js'
	import { useUserStore } from '@/store/user.js'
	import { useTokenStore } from '@/store/token.js'
	import {
		getMissions,
		getWorkReport,
		getWorkTask,
		getVillageStatistical,
		getAllPeople
	} from '../../api/mygrid/mygrid.js'
	import { getDictListByKey } from '@/common/net/generalPage/common.js'
	const userStore = useUserStore()
	const tokenStore = useTokenStore()
	// 当前角色
	const currentRole = computed(() => {
		return userStore.currentRole
	})
	onLoad(() => {
		// 获取默认展示的月份数据
		getDateDisp()
		// 获取日历组件所需数据
		setDate()
		// 获取总览数据
		// getWorkCountData()
		// 获取人员结构数据
		getStructureData()
		uni.$on('selectGrid', (val) => {
			if (val.length > 0) {
				currentGridId.value = val[0].gridId
				currentGridName.value = val[0].gridName
			} else {
				currentGridId.value = ''
				currentGridName.value = '全部'
			}
			
			searchMember()
		})
		// 获取字典数据
		getDictData()
		// 获取顶级网格下的所有成员
		getAllGridMembers()
	})
	onShow(() => {
		// 获取总览数据
		getWorkCountData()
	})
	// 展示的日期字段，不用于调接口。调接口的参数是一个日期范围，默认是本月
	const calendar = ref()
	const dateDisp = ref('')
	const startDay = ref('') // 查询总览数据的开始时间
	const endDay = ref('') // 查询总览数据的结束时间
	// 获取默认展示的时间
	function getDateDisp() {
		let now = new Date();
		let month = now.getMonth() + 1;
		dateDisp.value = month + '月'
	}
	// 租户名
	const gridName = computed(() => {
		if (!userStore.userInfo.customParam.tenantName) {
			return '全国'
		} else {
			return userStore.userInfo.customParam.tenantName
		}
	})

	// 工作数据总览
	const workDistribute = ref(0) // 事务下发
	const workReport = ref(0) // 工作上报
	const flowId = ref('') // 工作上报对应的流程应用id
	const workTask = ref(0) // 工作任务
	// 获取工作数据
	function getWorkCountData() {
		let param = {
			startTime: beginTime.value,
			endTime: endTime.value
		}
		getMissions(param).then(res => {
			console.log('事务下发总览数据', res);
			if (res.success) {
				workDistribute.value = res.data
			}
		})
		getWorkReport(param).then(res => {
			console.log('工作上报总览数据', res);
			if (res.success) {
				workReport.value = res.data.count
				flowId.value = res.data.flowId
			}
		})
		getWorkTask(param).then(res => {
			console.log('工作任务总览数据', res);
			if (res.success) {
				workTask.value = res.data
			}
		})
	
	}
	// 跳转相应的列表页
	function goToList(target) {
		// 事务下发
		if (target == 'workDistribute') {
			// 只有干部角色的网格员才能查看事务下发
			if (currentRole.value != 'villager') {
				uni.navigateTo({
					url: '/jimo/pages/eventPublication/list'
				})
			}
		} else if (target == 'workReport') {
			// 工作上报
			if (currentRole.value != 'villager') {
				// 只有干部角色的网格员才能查看工作上报
				uni.navigateTo({
					url: '/jimo/pages/flowPage/flowList?flowId=' + flowId.value
				})
			}
		} else if (target == 'workTask') {
			// 工作任务
			uni.navigateTo({
				url: '/jimo/pages/worktask/list'
			})
		} 
	}
	// 我的任务统计、组织机构、网格排名的背景图
	const taskStyle = {
		backgroundImage: `url(${GRID_TASK_BG})`,
		backgroundSize: '100% 100%',
		backgroundRepeat: 'no-repeat',
	}
	const organizationStyle = {
		backgroundImage: `url(${GRID_ZZJG_BG})`,
		backgroundSize: 'cover',
		backgroundRepeat: 'no-repeat',
	}
	const rankStyle = {
		backgroundImage: `url(${GRID_WGPM_BG})`,
		backgroundSize: 'cover',
		backgroundRepeat: 'no-repeat',
	}
	// 人员构成数据
	const countAll = ref(0) // 全部
	const countMan = ref(0) // 男性
	const countWoman = ref(0) //女性
	const countKid = ref(0) // 少年儿童
	const countOldPeople = ref(0) // 老年人
	const countWorkAge = ref(0) //劳动年龄
	const countFocus = ref(0) //重点关注
	const countPartyMember = ref(0) //党员
	// 获取人员构成数据
	function getStructureData() {
		getVillageStatistical().then(res => {
			if (res.success) {
				countAll.value = res.data.count
				countMan.value = res.data.countMan
				countWoman.value = res.data.countWoman
				countKid.value = res.data.countErtong
				countOldPeople.value = res.data.countLaonian
				countWorkAge.value = res.data.countLaodong
				countFocus.value = res.data.countZhongDian
				countPartyMember.value = res.data.countDangyuan
			}
		})
	}
	const keyword = ref('') // 搜索关键字
	const currentGridName = ref('全部') // 当前网格名称
	const currentGridId = ref('') // 当前网格id
	// 根据姓名搜索人员
	function searchMember() {
		console.log('clear触发了');
		currentPage.value = 1;
		getAllGridMembers()
	}
	// 清空搜索框
	function clearName() {
		keyword.value = '';
		searchMember();
	}
	const headList = ['姓名', '与户主关系', '出生日期', '性别', '所属网格']
	const peopleList = ref([])
	const total = ref(0);
	const currentPage = ref(1)
	const loadStatus = ref("");

	const relationshipLabel = ref([]) // 与户主关系字典数据
	const sexLabel = ref([]) // 性别字典数据
	// 获取字典数据
	async function getDictData() {
		let dictParam = {
			code: 'family_relation'
		}
		const resRelation = await getDictListByKey(dictParam)
		if (resRelation.success) {
			relationshipLabel.value = resRelation.data
		}

		dictParam.code = 'sys_user_sex'
		const resSex = await getDictListByKey(dictParam)
		if (resSex.success) {
			sexLabel.value = resSex.data
		}
	}
	// 获取网格人员列表数据
	async function getAllGridMembers() {
		let params = {
			gridId: currentGridId.value,
			pageNum: currentPage.value,
			pageSize: 10,
			name: keyword.value
		}
		let res = await getAllPeople(params)
		if (res.success && res.data) {
			if (res.data.records.length > 0) {
				total.value = res.data.total
				if (currentPage.value > 1) {
					peopleList.value = [...peopleList.value, ...res.data.records];
				} else {
					peopleList.value = res.data.records;
				}
				loadStatus.value = total.value == peopleList.value.length ? "nomore" : "loadmore";
			} else {
				total.value = res.data.total
				peopleList.value = []
				loadStatus.value = 'nomore'
			}

		}
	}
	// 获取字典属性
	function getDictLabel(key, code) {
		let target = {};
		if (key == 'realation') {
			target = relationshipLabel.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key == 'sex') {
			target = sexLabel.value.find((item) => {
				return item.dictValue == code
			})
		}
		if (!!target) {
			return target.dictLabel
		} else {
			return '--'
		}
	}
	// 加载更多
	function loadMore() {
		if (loadStatus.value == 'nomore') {
			return;
		}
		currentPage.value = currentPage.value + 1;
		getAllGridMembers()
	}
	
	// 跳转农户信息页
	function goToDetail(item) {
		uni.navigateTo({
			url: '/jimo/pages/myGrid/villagerInfo?id=' + item.villagePeopleId
		})
	}
	const showCalendar = ref(false) // 日历展示
	const minDate = ref('')
	const maxDate = ref('')
	const beginTime = ref('')
	const endTime = ref('')
	// 设置日历组件相关数据
	function setDate() {
		let date = new Date()
		let year = date.getFullYear()
		let month = date.getMonth() + 1
		let day = date.getDate()
		let tom = date.getDate() + 1
		if (month <= 9) {
			month = '0' + month
		}
		if (day <= 9) {
			day = '0' + day
			tom = '0' + tom
		}
		let minyear = year - 1
		minDate.value = minyear + '-' + month + '-' + day
		maxDate.value = year + '-' + month + '-' + day
		beginTime.value = year + '-' + month + '-' + '01'
		endTime.value = year + '-' + month + '-' + day
	}
	// 切换日期
	function changeDate() {
		// showCalendar.value = true
		calendar.value.open();
	}
	// 确认日期范围
	function confirmDate(val) {
		console.log('confirmDate----', val);
		beginTime.value = val.range.before;
		endTime.value = val.range.after;
		dateDisp.value = beginTime.value + '~' + endTime.value
		getWorkCountData()
		// showCalendar.value = false
		// beginTime.value = val[0]
		// endTime.value = val[val.length - 1]
		// dateDisp.value = beginTime.value + '~' + endTime.value
		// getWorkCountData()
	}
	// 关闭日历组件
	function closeCalendar() {
		showCalendar.value = false
	}
	// 跳转我的任务统计
	function goToMyTask() {
		uni.navigateTo({
			url: './myTask',
		})
	}
	// 跳转组织结构
	const gridId = ref("")

	function goToOrganization() {
		uni.navigateTo({
			url: './gridsetting?id=' + gridId.value,
		})
	}
	// 跳转网格排名
	function goToGridRank() {
		uni.navigateTo({
			url: './gridranking',
		})
	}
	// 切换网格
	function changeGrid() {
		uni.navigateTo({
			url: './switchgrid',
		})
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.grid-container {
		position: relative;
		padding: 20rpx;
		width: 100%;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	// 数据总览
	.statistics-container {
		margin-bottom: 20rpx;
		padding: 25rpx 30rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 710rpx;
		height: 232rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 2rpx 7rpx 5rpx rgba(11, 189, 136, 0.03);
		border-radius: 20rpx;

		.statistics-base {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.grid-name {
				font-weight: 400;
				font-size: 30rpx;
				color: #000000;
				max-width: 48%;
			}

			.date {
				display: flex;

				.month {
					margin-right: 10rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
				}
			}
		}

		.statistics-list {
			width: 100%;
			display: flex;
			align-items: center;

			.statistics-item {
				width: 25%;
				height: 118rpx;
				display: flex;
				flex-direction: column;
				// justify-content: space-evenly;
				align-items: center;

				.work-count {
					margin-bottom: 20rpx;
					font-size: 46rpx;
				}

				.work-name {
					font-weight: 400;
					font-size: 26rpx;
					color: #333333;
				}

				.color0 {
					color: #0190FF;
				}

				.color1 {
					color: #4CB34C;
				}

				.color2 {
					color: #FF9229;
				}

				.color3 {
					color: #FF6B27;
				}
			}
		}
	}

	// 我的任务统计
	.task-statistics {
		margin-bottom: 20rpx;
		padding: 40rpx 50rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 710rpx;

		.label {
			display: flex;

			.title {
				margin-left: 36rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #0BBD88;
			}
		}
	}

	// 跳转链接
	.quick-link {
		display: flex;
		margin-bottom: 20rpx;

		.organization,
		.grid-rank {
			padding: 40rpx 50rpx;
			width: 345rpx;
			height: 116rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.label {
				display: flex;

				.title {
					margin-left: 20rpx;
					font-weight: 500;
					font-size: 30rpx;
					color: #4B83FF;
				}
			}
		}

		.grid-rank {
			margin-left: 20rpx;

			.label .title {
				color: #FA6030;
			}
		}
	}

	.people-structure {
		padding: 40rpx 60rpx 0 60rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		width: 710rpx;
		background: #FFFFFF;
		border-radius: 20rpx;

		.structure-item {
			display: flex;
			flex-direction: column;
			margin-bottom: 40rpx;
			width: 40%;
			height: 80rpx;

			.tag-count {
				margin-bottom: 10rpx;
				font-family: Arial;
				font-weight: 900;
				font-size: 36rpx;
				color: #000000;
				line-height: 42rpx;
				text-align: left;
				font-style: normal;
			}

			.tag-name {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}
		}

		.structure-item:nth-child(3n) {
			width: 20%;
		}
	}

	.people-list {
		margin-top: 20rpx;
		padding: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;

		.curr-grid {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			.grid-name {
				margin-right: 10rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
			}
		}

		.head {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 10rpx;
			height: 80rpx;
			background: #F9F9F9;
			border-radius: 8rpx;

			.head-item {
				font-weight: 400;
				font-size: 28rpx;
				color: #030303;
				text-align: center;
			}

			.head-item:nth-child(1),
			.head-item:nth-child(4) {
				width: 14%;
			}

			.head-item:nth-child(2),
			.head-item:nth-child(3),
			.head-item:nth-child(5) {
				width: 24%;
			}
		}

		.content {
			box-sizing: border-box;
			padding: 10rpx;
			max-height: 544rpx;

			.content-row {
				display: flex;
				align-items: center;
				height: 60rpx;
				border-bottom: 1rpx dashed #DBDBDB;

				.content-item {
					font-weight: 400;
					font-size: 26rpx;
					color: #666666;
					text-align: center;
				}

				.content-item:nth-child(1),
				.content-item:nth-child(4) {
					width: 14%;
				}

				.content-item:nth-child(2),
				.content-item:nth-child(3),
				.content-item:nth-child(5) {
					width: 24%;
				}
			}
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F0F7F7;
	}
</style>