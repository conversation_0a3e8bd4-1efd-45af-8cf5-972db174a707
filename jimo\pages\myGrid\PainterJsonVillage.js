export default class PainterJsonVillage {
	palette(infoData) {
		let jsonData = ({
			"width": "375px",
			"height": "630px",
			"background": "#f8f8f8",
			"views": [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "10px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "image",
					"url": infoData.headPhoto,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "22px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.villageName + '>' + infoData.gridName,
					"css": {
						"color": "#000000",
						"background": "rgba(0,0,0,0)",
						"width": "232px",
						"height": "16.95px",
						"top": "35px",
						"left": "110px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "15px",
						"fontWeight": "bold",
						"maxLines": "2",
						"lineHeight": "16.650000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "text",
					"text": infoData.staffName,
					"css": {
						"color": "#000000",
						"background": "rgba(0,0,0,0)",
						"width": "200px",
						"height": "15.819999999999999px",
						"top": "65px",
						"left": "110px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "2",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "120px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": infoData.interviewNum,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "23.729999999999997px",
						"top": "148px",
						"left": "48px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "21px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "23.310000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": "累计下户走访",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "90px",
						"height": "15.819999999999999px",
						"top": "179.00000000000003px",
						"left": "44px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "rect",
					"css": {
						"background": "",
						"width": "1px",
						"height": "40px",
						"top": "152px",
						"left": "188px",
						"rotate": "0",
						"borderRadius": "4.5px",
						"borderWidth": "1px",
						"borderColor": "#EAEAEA",
						"shadow": "",
						"color": ""
					}
				},
				{
					"type": "text",
					"text": infoData.taskNum,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "23.729999999999997px",
						"top": "147px",
						"left": "224px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "21px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "23.310000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": "任务完成",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "15.819999999999999px",
						"top": "181.00000000000003px",
						"left": "225px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		});
		let colleagueInfo = []
		if (infoData.colleagueList.length == 1) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "244.00000000000003px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "275px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.colleagueList.length == 2) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "244.00000000000003px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "275px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[1].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "274px",
						"left": "139px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[1].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "138px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.colleagueList.length >= 3) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "244.00000000000003px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "275px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "20px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[1].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "274px",
						"left": "139px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[1].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "138px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[2].photo,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "275px",
						"left": "260px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[2].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "364.99999999999994px",
						"left": "258px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		}
		if (colleagueInfo.length > 0) {
			jsonData.views = [...jsonData.views, ...colleagueInfo]
		}
		return jsonData;
	}
}