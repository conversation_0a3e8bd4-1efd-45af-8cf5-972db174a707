<template>
	<view class="examine-status" v-if="applyInfo.examineType == '0' || applyInfo.examineType == '1'">
		<view :class="applyInfo.examineType == '0' ? 'examine' : 'pass'">
			{{examineStatus}}
		</view>
	</view>
	<view class="examine-refuse" v-else>
		<view class="label">审核拒绝</view>
		<!-- <expandable-text :longText="applyInfo.reason" :line="1" expandText="展开" foldText="收起" /> -->
		<mote-lines-divide :dt="applyInfo.reason" :line="1"></mote-lines-divide>
	</view>
	<view class="main-conitaner">
		<view class="form-container">
			<view class="label">姓名</view>
			<view class="value">{{applyInfo.name}}</view>
			<view class="label">手机号</view>
			<view class="value">{{applyInfo.phone}}</view>
			<view class="label">服务区域</view>
			<view class="value">{{applyInfo.address}}</view>
			<view class="label">个人照片</view>
			<safe-image :src='applyInfo.photo' width='200rpx' height='200rpx' radius='8rpx'></safe-image>
			<view class="label">服务介绍</view>
			<textarea style="width: 100%;" :value="applyInfo.introduce" disabled auto-height />
			<view style="margin-bottom: 20rpx;" v-for="(item,index) in introducePhotoList" :key="index">
				<safe-image :src='item' width='100%' height='330rpx' radius='8rpx' v-if="!!item"></safe-image>
			</view>
		</view>
	</view>
	<view class="handle-container paddingbottom" @click="handleApply">
		<view class="handle-box">管理</view>
	</view>
	<!-- ActionSheet 操作菜单 -->
	<u-action-sheet :actions="actionList" @select="selectClick" @close="show=false" :show="show"
		round="8rpx"></u-action-sheet>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import { onLoad, onUnload, onShow } from '@dcloudio/uni-app'
	import MoteLinesDivide from './components/mote-lines-divide.vue'
	import {delGuide } from '../../api/villageCodeTour/villageCodeTour.js'

	const actionList = ref([{
			name: '编辑',
		},
		{
			name: '删除',
		},
		{
			name: '取消',
		}
	])
	const examineStatus = computed(() => {
		if (applyInfo.value.examineType == '0') {
			return '审核中'
		} else if (applyInfo.value.examineType == '1') {
			return '审核通过'
		}
	})
	// 审核拒绝原因
	const refuseReason = ref('')
	// 申请信息
	const applyInfo = ref({})
	// 服务介绍图片
	const introducePhotoList = computed(() => {
		return applyInfo.value.introducePhoto?.split(',')
	})
	onLoad((options) => {
		// 获取申请信息
		if (!!options.applyInfo) {
			applyInfo.value = JSON.parse(decodeURIComponent(options.applyInfo))
		}
	})
	const show = ref(false) // 是否展示操作菜单
	// 操作申请
	function handleApply() {
		show.value = true;
	}

	function selectClick(obj) {
		if (obj.name == '删除') {
			uni.showModal({
				content: '确定要删除吗？',
				success: function(res) {
					if (res.confirm) {
						let ids = [applyInfo.value.id]
						delGuide(ids).then((res) => {
							if (res.success) {
								uni.showToast({
									title: '删除成功',
									icon: 'none'
								})
								setTimeout(() => {
									uni.navigateBack()
								}, 1500)
							} else {
								uni.showToast({
									title: '删除失败',
									icon: 'none'
								})
							}
						}).catch((e) => {
							console.log(e)
							uni.showToast({
								title: e,
								icon: 'none'
							});
						})
					} else {}
				}
			});
		} else if (obj.name == '编辑') {
			uni.navigateTo({
				url: '/jimo/pages/villageCodeTour/applyGuide?opreate=edit'
			})
		} else if (obj.name == '取消') {
			show.value = false;
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.examine-status {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 64rpx;
		width: 100%;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
	}

	.examine-refuse {
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		width: 100%;
		min-height: 64rpx;
		background: #FFE1CC;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
		font-size: 24rpx;
		font-weight: 500;

		.label {
			width: 120rpx;
			margin-right: 10rpx;
			color: #F5222D;
			white-space: nowrap;
		}
	}

	.examine {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 64rpx;
		width: 100%;
		background: #FFE1CC;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
		font-size: 24rpx;
		font-weight: 500;
		color: #FF6B00;
	}

	.pass {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 64rpx;
		width: 100%;
		background: #E2FAE3;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
		font-size: 24rpx;
		font-weight: 500;
		color: #01BD5D;
	}

	.main-conitaner {
		padding: 20rpx;
		background: #fff;
		.form-container {
			padding-bottom: 120rpx;
		}
		.label {
			margin-bottom: 20rpx;
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;
		}

		.value {
			margin-bottom: 30rpx;
			font-weight: 400;
			font-size: 32rpx;
			color: #000000;
		}
	}

	.handle-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 2;
		display: flex;
		justify-content: flex-end;
		// align-items: center;
		padding: 20rpx 0;
		width: 100%;
		height: 120rpx;
		background: #FFFFFF;
		border-top: 1rpx solid ddd;
		// box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.5); /* 上边框阴影 */
		box-shadow: 0px -10rpx 10rpx #f8f8f8;

		.handle-box {
			margin-right: 20rpx;
			width: 200rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			border-radius: 48rpx;
			border: 1rpx solid #0BBD88;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #0BBD88;
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F4F8F7;
	}
</style>