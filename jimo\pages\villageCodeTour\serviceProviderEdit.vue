<!-- 服务商认证编辑 -->
<template>
	<view class="service-provider">
		<u-navbar title="服务商认证" bgColor="#FFF"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<u--form labelPosition="top" labelWidth="100%"
					:model="form" :rules="rules" ref="refForm">
					<!-- 服务商名称 -->
					<u-form-item label="服务商名称" prop="serveName" :required="true">
						<u--input v-model="form.serveName" border="none" placeholder="请输入服务商名称" maxlength="100" />
					</u-form-item>
					<!-- 服务类型 -->
					<u-form-item label="服务类型" prop="serveType" :required="true">
						<view class="form-item" @click="serviceTypeShow = true">
							<u--input v-model="form.serveType" border="none" placeholder="请选择服务类型" readonly />
							<u-icon name="arrow-down"></u-icon>
						</view>
						<!-- 服务类型选择 -->
						<multiple-picker 
							title="服务类型" :show="serviceTypeShow" 
							:columns="serviceTypeColumns"
						    :defaultIndex="serviceType" 
							@confirm="handleServiceTypeConfim" 
							@cancel="serviceTypeShow = false" />
					</u-form-item>
					<!-- 姓名 -->
					<u-form-item label="姓名" :required="true">
						<u--input v-model="form.name" border="none" readonly />
					</u-form-item>
					<!-- 手机号 -->
					<u-form-item label="手机号" :required="true">
						<u--input v-model="form.cellphone" border="none" readonly />
					</u-form-item>
					<!-- 服务区域 -->
					<u-form-item label="服务区域" :required="true">
						<u--input v-model="form.address" border="none" readonly />
					</u-form-item>
					<!-- 详细地址 -->
					<u-form-item label="详细地址">
						<u--textarea v-model="form.introduceAddress" border="none" 
							placeholder="请输入详细地址" maxlength="300" />
					</u-form-item>
					<!-- <u-form-item label="法人/经营者身份证照片" :required="true" prop="identityCardFront">
						<view style="display: flex;justify-content: space-between;">
							<view class="img-upload">
								<u-upload :auto-upload="false" :fileList="cardFile1" :maxCount="1"
									:mutiple="true" width="150rpx" height="150rpx"
									@afterRead="(e) => afterRead(e, 1)" 
									@delete="(e) => deletePic(e, 1)" />
								<view class="tip">身份证头像面</view>
							</view>
							<view class="img-upload">
								<u-upload :auto-upload="false" :fileList="cardFile2" :maxCount="1"
									:mutiple="true" width="150rpx" height="150rpx"
									@afterRead="(e) => afterRead(e, 2)" 
									@delete="(e) => deletePic(e, 2)" />
								<view class="tip">身份证国徽面</view>
							</view>
						</view>
					</u-form-item> -->
					<u-form-item label="营业执照">
						<view class="img-upload">
							<u-upload :auto-upload="false" :fileList="fileList" :maxCount="1"
								:mutiple="true" width="150rpx" height="150rpx"
								@afterRead="(e) => afterRead(e, 3)"
								@delete="(e) => deletePic(e, 3)" />
						</view>
					</u-form-item>
				</u--form>
			</view>
		</view>
		
		<view class="submit" @click="handleSubmit()">
			<view class="btn">提交</view>
		</view>
		
		<service-provider-submit ref="refSPS" />
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { fileUpload } from '@/common/api'
import { useUserStore } from '@/store/user.js'
import { SreviceProvider } from '../../api/villageCodeTour/villageCodeTour.js'
import ServiceProviderSubmit from './serviceProviderSubmit.vue'
import MultiplePicker from '../../components/multiple-picker/index.vue'

/** 当前登录人信息 */
const userStore = useUserStore()
/** 顶部导航高度 */
const navbarHeight = ref('')
/** 表单组件 */
const refForm = ref()
/** 表单 */
const form = ref({
	serveName: ''
})
/** 表单验证 */
const rules = reactive({
	serveName: { 
		type: 'string', required: true, max: 100,
		message: '请输入服务商名称', trigger: ['change', 'blur'],
	},
	serveType: { 
		type: 'string', required: true, 
		message: '请选择服务类型', trigger: ['change', 'blur'] ,
	},
	address: { required: true },
	introduceAddress: { required: false },
	identityCardFront: { 
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			console.log('form.value', form.value)
			if (!form.value.identityCardFront) {
				callback(new Error('请上传身份证头像面'))
			}
			if (!form.value.identityCardOpposite) {
				callback(new Error('请上传身份证国徽面'))
			}
			callback()
		},
		trigger: ['change', 'blur'],
	},
	identityCardOpposite: { required: true },
	businessLicense: { required: false }
})
/** 服务类型显示 */
const serviceTypeShow = ref(false)
/** 服务类型列表 */
const serviceTypeColumns = ref([
	{ label: '酒店民宿', value: '酒店民宿' },
	{ label: '本地特产', value: '本地特产' },
	{ label: '美食指南', value: '美食指南'  },
])
/** 服务类型选择 */
const serviceType = ref([])
/** 提交成功弹窗 */
const refSPS = ref()

onLoad(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	getUserInfo()
	getServiceInfo()
})

onMounted(() => {
	refForm.value.setRules(rules)
})

/**
 * @description 获取当前登录人信息
 */
const getUserInfo = () => {
	const { peopleInfo, tenantName } = userStore.userInfo.customParam
	form.value.name = peopleInfo.name
	form.value.cellphone = peopleInfo.cellphone
	form.value.address = tenantName
}
/**
 * @description 获取服务商信息
 */
const getServiceInfo = () => {
	SreviceProvider.findSreviceProviderInfo().then(r => {
		if (r.data) {
			form.value = r.data
			form.value.address = userStore.userInfo.customParam.tenantName
			
			serviceType.value = form.value.serveType.split(',')
			
			cardFile1.value = [{ url: form.value.identityCardFront, type: 'image' }]
			cardFile2.value = [{ url: form.value.identityCardOpposite, type: 'image' }]
			if (form.value.businessLicense) {
				fileList.value = [{ url: form.value.businessLicense, type: 'image' }]
			} else {
				fileList.value = []
			}
		}
	})
}

/**
 * @description 服务类型选择确认
 */
const handleServiceTypeConfim = (e) => {
	serviceType.value = e.value
	form.value.serveType = serviceType.value.toString()
	serviceTypeShow.value = false
	refForm.value.validateField('serveType')
}
/** 身份证图片正面 */
const cardFile1 = ref([])
/** 身份证图片反面 */
const cardFile2 = ref([])
/** 营业执照图片 */
const fileList = ref([])
/**
 * @description 图片上传
 */
const afterRead = async (event, index) => {
	const { url, size } = event.file
	const ex = getFileName(url)
	console.log('ex', ex)
	if (ex !== 'jpg' && ex !== 'jpeg' && ex !== 'png' && ex !== 'gif') {
		uni.showToast({ title: '图片格式错误', icon: 'none' })
		return
	}
	if (size > 5 * 1024 * 1024) {
		uni.showToast({ title: '图片限制上传5M', icon: 'none' })
		return
	}
	loadingUpload(index, url, true)
	const params = {
		filePath: event.file.url,
		formData: { isAnonymous: 1 }	
	}
	fileUpload(params).then(r => {
		if (r.success) {
			loadingUpload(index, r.data.url, false)
		}
	})
}
/**
 * @description 获取文件的后缀名
 */
const getFileName = (item) => {
	let index = item.lastIndexOf('.')
	let name = item.substring(index + 1)
	return name.toLowerCase()
}
/**
 * @description 上传
 */
const loadingUpload = (index, url, loading) => {
	if (index === 1) {
		if (loading) {
			cardFile1.value = [{ url, type: 'image', status: 'uploading', message: '上传中' }]
		} else {
			cardFile1.value = [{ url, type: 'image' }]
		}
		form.value.identityCardFront = url
		refForm.value.validateField('identityCardFront')
	}
	if (index === 2) {
		if (loading) {
			cardFile2.value = [{ url, type: 'image', status: 'uploading', message: '上传中' }]
		} else {
			cardFile2.value = [{ url, type: 'image' }]
		}
		form.value.identityCardOpposite = url
		refForm.value.validateField('identityCardFront')
	}
	if (index === 3) {
		if (loading) {
			fileList.value = [{ url, type: 'image', status: 'uploading', message: '上传中' }]
		} else {
			fileList.value = [{ url, type: 'image' }]
		}
		form.value.businessLicense = url
	}
}
/**
 * @description 图片删除
 */
const deletePic = (event, index) => {
	if (index === 1) {
		cardFile1.value = []
		form.value.identityCardFront = ''
		refForm.value.validateField('identityCardFront')
	}
	if (index === 2) {
		cardFile2.value = []
		form.value.identityCardOpposite = ''
		refForm.value.validateField('identityCardFront')
	}
	if (index === 3) {
		fileList.value = []
		form.value.businessLicense = ''
	}
}
/**
 * @description 提交
 */
const handleSubmit = () => {
	refForm.value.validate().then(res => {
		if (res) {
			uni.showLoading({ title: '提交中...', mask: true })
			const params = {
				id: form.value.id
			}
			Object.keys(rules).forEach(key => {
				params[key] = form.value[key]
			})
			
			const api = params.id ? 'updateSreviceProvider' : 'addSreviceProvider'
			
			SreviceProvider[api](params).then(r => {
				if (r.success) {
					refSPS.value.init()
				} else {
					uni.showToast({ 
						type: 'error', title: r.message, 
						icon: 'none', duration: 3000,
					})
				}
			}).finally(() => {
				uni.hideLoading()
			})
		}
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.service-provider {
	width: 100%;
	height: 100vh;
	background: #F0F7F7;
	
	.container {
		padding: 24rpx 20rpx;
		
		::v-deep.u-form {
			background-color: #FFF;
			padding: 23rpx 26rpx;
			border-radius: 20rpx;
			
			.form-item-view {
				padding: 23rpx 19rpx 23rpx 33rpx;
				background-color: #fff;
				border-radius: 20rpx;
				margin-bottom: 20rpx;
			}
			
			.u-form-item {
				border-bottom: 1rpx solid #E0E0E0;
				
				.u-form-item__body__left__content__label, .label-solt {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 600;
					font-size: 32rpx;
					color: rgba(51, 51, 51, 1);
					line-height: 45rpx;
				}
				
				&:last-child {
					border-bottom: none;
				}
				
				&:nth-child(3), &:nth-child(4), &:nth-child(5) {
					.u-form-item__body__left__content__required {
						color: rgba(245, 34, 45, 0.4) !important;
					}
					.u-form-item__body__left__content__label, input {
						color: rgba(51, 51, 51, 0.4) !important;
					}
				}
			}
			
			.form-item {
				width: 100%;
				display: flex;
				justify-content: space-between;
			}
			
			.label-solt {
				display: flex;
				justify-content: space-between;
				margin-bottom: 23rpx;
			}
			
			.popup-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				height: 84rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				padding: 0 30rpx;
				
				.cancel {
					color: #909193;
				}
				
				.confim {
					color: #3c9cff;
				}
				
				.title {
					color: #303133;
				}
			}
			
			.service-type {
				display: flex;
				justify-content: center;
				
				.u-checkbox__icon-wrap {
					
				}
			}
			
			.img-upload {
				float: left;
				
				.u-upload__button {
					width: 300rpx !important;
					height: 200rpx !important;
				}
				
				.u-upload__deletable {
					width: 40rpx !important;
					height: 40rpx !important;
					
					.u-upload__deletable__icon {
						top: 6rpx !important;
						right: 6rpx !important;
					}
				}
				
				image {
					width: 300rpx !important;
					height: 200rpx !important;
				}
				
				.tip {
					text-align: center;
				}
			}
		
			.u-textarea {
				padding: 0 !important;
			}
		}
	}
}

.submit {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #FFFFFF;
		line-height: 50rpx;
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>