<!-- 我要招工 -->
<template>
	<view class="my-resume-edit">
		<u-navbar title="我要招工" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000" />
		
		<view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<u--form labelPosition="top" labelWidth="300rpx"
					:model="form" :rules="rules" ref="refForm">
					<view class="form-item-view">
						<!-- 职位名称 -->
						<u-form-item label="职位名称" prop="positionName" :required="true">
							<view class="form-item" @click="showWorkTypes = true">
								<u--input v-model="form.positionName" border="none" placeholder="请选择职位名称" readonly />
								<u-icon name="arrow-down"></u-icon>
							</view>
							<!-- 职位名称选择 -->
							<u-picker :show="showWorkTypes" title="职位名称"
								:columns="workTypesColumns" @confirm="selectWorkTypes" 
								@cancel="showWorkTypes = false" />
						</u-form-item>
						<!-- 薪资范围 -->
						<u-form-item label="薪资范围" prop="salaryRange" :required="true">
							<view class="form-item" @click="showSalary = true">
								<u--input v-model="form.salaryRange" border="none" placeholder="请选薪资下限上限范围" readonly />
								<u-icon name="arrow-down"></u-icon>
							</view>
							<!-- 期望薪资选择 -->
							<u-picker :show="showSalary" title="期望薪资"
								:columns="salaryColumns" @confirm="selectSalary"
								@cancel="showSalary = false" />
						</u-form-item>
						<!-- 岗位描述 -->
						<u-form-item label="岗位描述" prop="positionDescription" :required="true">
							<u--textarea v-model="form.positionDescription" border="none" 
								placeholder="请输入岗位描述" maxlength="255" />
						</u-form-item>
					</view>
					
					<view class="form-item-view">
						<!-- 招工人数 -->
						<u-form-item label="招工人数" prop="workerNum" :required="true">
							<u--input v-model="form.workerNum" border="none" placeholder="请输入招工人数" />
						</u-form-item>
						<!-- 工作地点 -->
						<u-form-item label="工作地点" prop="workPlace" :required="true">
							<view class="form-item" @click="goRegionSelect()">
								<u--input v-model="form.workPlace" border="none" placeholder="请选择省市区县" readonly />
								<u-icon name="arrow-down"></u-icon>
							</view>
							<!-- 工作地点选择 -->
							<region-select ref="refRegionSelect" @get="handleRegionSelect" />
						</u-form-item>
						<!-- 详细地址 -->
						<u-form-item label="详细地址" prop="address" :required="true">
							<u--textarea v-model="form.address" border="none" 
								placeholder="请输入详细地址" maxlength="50" />
						</u-form-item>
						<!-- 截止日期 -->
						<u-form-item label="截止日期" prop="deadLine" :required="true">
							<view class="form-item" @click="showDeadLine = true">
								<u--input v-model="form.deadLine" border="none" placeholder="请选择截止日期" readonly />
								<u-icon name="arrow-down"></u-icon>
							</view>
							<!-- 截止日期选择 -->
							 <u-datetime-picker :show="showDeadLine" title="截止日期" 
								mode="date" :min-date="new Date()"
								@confirm="handleDeadLine" @cancel="showDeadLine = false" />
						</u-form-item>
					</view>
					
					<view class="form-item-view">
						<!-- 联系人 -->
						<u-form-item label="联系人" prop="contacts" :required="true">
							<u--input v-model="form.contacts" border="none" 
								placeholder="请输入联系人姓名" maxlength="30" />
						</u-form-item>
						<!-- 联系电话 -->
						<u-form-item label="联系电话" prop="contactsPhone" :required="true">
							<u--input v-model="form.contactsPhone" border="none" 
								placeholder="请输入联系电话" maxlength="11" />
						</u-form-item>
					</view>
				</u--form>
			</view>
		</view>
		
		<view class="submit" @click="handleSubmit()">
			<view class="btn">保存</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { LaborService } from '../../api/laborService/laborService.js'
import RegionSelect from '../../components/regionSelect/index.vue'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 表单 */
const form = ref({
	positionName: '',
	salaryRange: '',
	positionDescription: '',
	workerNum: '',
	workPlace: '',
	deadLine: '',
})
/** 表单验证 */
const rules = reactive({
	positionName: [{ required: true, message: '请选择职位名称', trigger: 'change' }],
	salaryRange: [{ required: true, message: '请选薪资下限上限范围', trigger: 'change' }],
	positionDescription: [{ 
		type: 'string', required: true, max: 255,
		message: '请输入岗位描述且字数不能超过255个', trigger: ['blur', 'change'],
	}],
	workerNum: [{ 
		type: 'number', required: true,
		message: '请输入招工人数且必须为数字', trigger: ['blur', 'change'],
	}],
	workPlace: [{ required: true, message: '请选择工作地点', trigger: 'change' }],
	address: [{ 
		type: 'string', required: true, max: 50,
		message: '请输入详细地址且字数不超过50个', trigger: ['blur', 'change'],
	}],
	deadLine: [{ required: true, message: '请选择截止时间', trigger: 'change' }],
	contacts: [{ 
		type: 'string', required: true, max: 30,
		message: '请输入联系人姓名且字数不超过30个', trigger: ['blur', 'change'],
	}],
	contactsPhone: [{ 
		type: 'string', required: true, max: 11,
		message: '请输入联系人电话且不超过11位', trigger: ['blur', 'change'],
	}, {
		validator: (rule, value, callback) => {
			return uni.$u.test.mobile(value)
		},
		message: '手机号码格式不正确', trigger: ['change', 'blur'],
	}]
})

onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	setSalaryColumns()
	
	if (options && options.positionId) {
		getRecruitWorkInfo(options.positionId)
	}
})


/**
 * @description 获取招工详情
 * @param {String} positionId
 */
const getRecruitWorkInfo = (positionId) => {
	const params = {
		positionId
	}
	LaborService.getRecruitWorkInfo(params).then(r => {
		if (r.success) {
			form.value = { ...r.data }
		}
	})
}

/** 职位名称选择 */
const showWorkTypes = ref(false)
const workTypesColumns = ref([[
	'计算机', '厨师', '老师', '木工技师', '瓦工技师', '管道工技师', '绿化工技师',
	'林木种苗技师', '抚育间伐技师', '特岗护理技师', '闸门运行技师', '果树工技师', '其他'
]])
const selectWorkTypes = (val) => {
	form.value.positionName = val.value[0]
	showWorkTypes.value = false
	refForm.value.validateField('positionName')
}

/** 薪资范围选择 */
const showSalary = ref(false)
const salaryColumns = ref([])
const setSalaryColumns = () => {
	const salary = []
	for (let i = 1; i < 10; i++) {
		salary.push(i * 100)
	}
	for (let i = 1; i < 10; i++) {
		salary.push(i * 1000)
	}
	for (let i = 1; i < 11; i++) {
		salary.push(i * 10000)
	}
	salaryColumns.value = [salary, salary]
}
const selectSalary = ({ value }) => {
	let salaryRangeLow, salaryRangeHigh
	if (value[0] > value[1]) {
		salaryRangeLow = value[1]
		salaryRangeHigh = value[0]
	} else {
		salaryRangeLow = value[0]
		salaryRangeHigh = value[1]
	}
	
	form.value.salaryRange = `${salaryRangeLow}-${salaryRangeHigh}元`
	form.value.salaryRangeLow = salaryRangeLow
	form.value.salaryRangeHigh = salaryRangeHigh
	
	showSalary.value = false
	refForm.value.validateField('salaryRange')
}

/** 工作地点选择 */
const refRegionSelect = ref()
const goRegionSelect = () => {
	refRegionSelect.value.init()
}
const handleRegionSelect = ({ selectRegion }) => {
	form.value.workPlace = selectRegion[0].regionFullName
	refForm.value.validateField('workPlace')
}

/** 截止日期选择 */
const showDeadLine = ref(false)
const handleDeadLine = ({ value }) => {
	console.log(value)
	
	const date = new Date(value)
	
	const year = date.getFullYear()
	let month = date.getMonth() + 1;
	let day = date.getDate();
	
	month = month > 9 ? month : `0${month}`
	day = day > 9 ? day : `0${day}`
	
	form.value.deadLine = `${year}-${month}-${day}`
	
	showDeadLine.value = false
	
	refForm.value.validateField('deadLine')
}

/** 表单提交 */
const refForm = ref(null)
const handleSubmit = () => {
	refForm.value.validate().then(res => {
		if (res) {
			uni.showLoading({ title: '提交中...', mask: true })
			const params = {
				positionId: form.value.positionId,
			}
			Object.keys(rules).forEach(key => {
				if (key === 'salaryRange') {
					params.salaryRangeLow = form.value.salaryRangeLow
					params.salaryRangeHigh = form.value.salaryRangeHigh
				} else {
					params[key] = form.value[key]
				}
			})
			let api = params.positionId ? 'updateMyRecruitWork' : 'addMyRecruitWork'
			
			LaborService[api](params).then(r => {
				if (r.success) {
					uni.showToast({ 
						type: 'success', title: '保存成功', 
						icon: 'none', duration: 1000,
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				} else {
					uni.showToast({ type: 'error', title: r.message, icon: 'none' })
				}
			}).finally(() => {
				uni.hideLoading()
			})
		}
	})
}

onMounted(() => {
	refForm.value.setRules(rules)
})
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 23rpx 20rpx;
	background: #F0F7F7;
	z-index: 11;
	
	::v-deep.u-form {
		.form-item-view {
			padding: 23rpx 19rpx 23rpx 33rpx;
			background-color: #fff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
		}
		
		.u-form-item {
			border-bottom: 1rpx solid #E0E0E0;
			
			.u-form-item__body__left__content__label, .label-solt {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				font-size: 32rpx;
				color: rgba(51, 51, 51, 1);
				line-height: 45rpx;
			}
			
			&:last-child {
				border-bottom: none;
			}
		}
		
		.form-item {
			width: 100%;
			display: flex;
			justify-content: space-between;
		}
		
		.label-solt {
			display: flex;
			justify-content: space-between;
			margin-bottom: 23rpx;
		}
	}
}

.submit {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	padding-bottom: calc(env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	z-index: 10;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #FFFFFF;
		line-height: 50rpx;
	}
}

.w-100 {
	width: 100%;
}
.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>