import {
    request
} from '../request.js'

//** 项目预警管理查询列表 */
export function getProjectWarnList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectWarn/findPage',
        method: 'POST',
        params,
    })
}
//** 项目预警管理详情 */
export function getProjectWarnDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectWarn/findOne',
        method: 'GET',
        params,
    })
}
//** 项目预警处理 */
export function getProjectWarnDeal(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectWarn/update',
        method: 'POST',
        params,
    })
}
//** 项目巡检查询 */
export function getProjectInspectList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectInspect/findPage',
        method: 'POST',
        params,
    })
}
//** 项目巡检详情 */
export function getProjectInspectDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectInspect/findOne',
        method: 'GET',
        params,
    })
}
//** 项目巡检新增 */
export function addProjectInspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectInspect/add',
        method: 'POST',
        params,
    })
}
//** 项目巡检编辑 */
export function uploadProjectInspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectInspect/update',
        method: 'POST',
        params,
    })
}
//** 项目巡检删除 */
export function deleteProjectInspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectInspect/delete',
        method: 'GET',
        params,
    })
}
//** 项目问题查询列表 */
export function getProjectProblemList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectProblem/findPage',
        method: 'POST',
        params,
    })
}
//** 项目问题详情 */
export function getProjectProblemDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectProblem/findOne',
        method: 'GET',
        params,
    })
}
//** 项目问题新增 */
export function addProjectProblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectProblem/add',
        method: 'POST',
        params,
    })
}
//** 项目问题处理 */
export function dealProjectProblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectProblem/update',
        method: 'POST',
        params,
    })
}
//** 项目问题删除 */
export function delProjectProblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectProblem/delete',
        method: 'GET',
        params,
    })
}


//** 施工进度查询 */
export function getProjectConstructionList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectConstruction/findPage',
        method: 'POST',
        params,
    })
}
//** 施工进度详情 */
export function getProjectConstructionDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectConstruction/findOne',
        method: 'GET',
        params,
    })
}
//** 施工进度新增 */
export function addProjectConstruction(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectConstruction/add',
        method: 'POST',
        params,
    })
}
//** 施工进度编辑 */
export function uploadProjectConstruction(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectConstruction/update',
        method: 'POST',
        params,
    })
}
//** 施工进度删除 */
export function deleteProjectConstruction(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectConstruction/delete',
        method: 'GET',
        params,
    })
}
//** 所属标段 */
export function findSectionListConstruction(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/projectSection/findSectionList',
        method: 'GET',
        params,
    })
}


//** 设备预警管理查询列表 */
export function getDeviceWarnList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/findPage',
        method: 'POST',
        params,
    })
}
//** 设备预警管理详情 */
export function getDevicewarnDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/findOne',
        method: 'GET',
        params,
    })
}
//** 设备预警处理 */
export function getDevicewarnDeal(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicewarn/update',
        method: 'POST',
        params,
    })
}
//** 设备巡检查询 */
export function getDevicesinspectList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/findPage',
        method: 'POST',
        params,
    })
}
//** 设备巡检详情 */
export function getDevicesinspectDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/findOne',
        method: 'GET',
        params,
    })
}
//** 设备巡检新增 */
export function addDevicesinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/add',
        method: 'POST',
        params,
    })
}
//设备巡检编辑
export function uploadDevicesinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/update',
        method: 'POST',
        params,
    })
}
//获取设备巡检删除
export function deleteDevicesinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/delete',
        method: 'GET',
        params,
    })
}
//** 设备巡检获取地块 */
export function deviceFindLandList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/findLandList',
        method: 'GET',
        params,
    })
}
//** 设备巡检获取设备列表 */
export function deviceFindDeviceList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/devicesinspect/findDeviceList',
        method: 'GET',
        params,
    })
}
//** 设备问题查询列表 */
export function getDeviceproblemList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/deviceproblem/findPage',
        method: 'POST',
        params,
    })
}
//** 设备问题详情 */
export function getDeviceproblemDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/deviceproblem/findOne',
        method: 'GET',
        params,
    })
}
//** 设备问题新增 */
export function addDeviceproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/deviceproblem/add',
        method: 'POST',
        params,
    })
}
//** 设备问题处理 */
export function dealDeviceproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/deviceproblem/update',
        method: 'POST',
        params,
    })
}
//** 设备问题删除 */
export function delDeviceproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/deviceproblem/delete',
        method: 'GET',
        params,
    })
}
//** 施工内容分类字典列表 */
export function getConstructionContent(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/CONSTRUCTION_CONTENT',
        method: 'GET',
        params,
    })
}
//** 单位分类字典列表 */
export function getFacilityUnit(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_UNIT',
        method: 'GET',
        params,
    })
}

// 获取设施预警列表
export function getFacilitywarnList(params) {//获取设施预警列表
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilitywarn/findPage',
        method: 'POST',
        params,
    })
}

// 获取设施预警类型-
export function getDeviceAlarmType(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/PROJECT_WARN_TYPE',
        method: 'GET',
        params,
    })
}
// 获取设施预警级别-
export function getDeviceAlarmLevel(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/WARN_LEVEL',
        method: 'GET',
        params,
    })
}

// 获取设施告警详情信息-
export function getFacilityDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilitywarn/findOne',
        method: 'GET',
        params,
    })
}


//设施预警处理接口-
export function FacilityWarnDeal(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilitywarn/update',
        method: 'POST',
        params,
    })
}

//行政区划获取接口-
export function findLowerListByTenant(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/region/findLowerListByTenant',
        method: 'POST',
        params,
    })
}

//获取设施巡检列表
export function getFacilityinspectList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/findPage',
        method: 'POST',
        params,
    })
}
//获取设施巡检详情
export function getFacilityinspectDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/findOne',
        method: 'GET',
        params,
    })
}
//设施巡检新增
export function addFacilityinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/add',
        method: 'POST',
        params,
    })
}
//设施巡检编辑
export function uploadFacilityinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/update',
        method: 'POST',
        params,
    })
}
//获取设施巡检删除
export function deleteFacilityinspect(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/delete',
        method: 'GET',
        params,
    })
}
//获取巡检设施项目信息--
export function findProjectList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/findProjectList',
        method: 'GET',
        params,
    })
}
//获取设施巡检分类
export function getInspectClass(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/INSPECT_CLASS',
        method: 'GET',
        params,
    })
}
//获取设施巡检内容
export function getInspectConte(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_INSPECT_CONTENT',
        method: 'GET',
        params,
    })
}
// //获取设施巡检设施
export function getFindFacilityList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityinspect/findFacilityList',
        method: 'GET',
        params,
    })
}



//设施问题
//设施问题列表
export function getProblemList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityproblem/findPage',
        method: 'POST',
        params,
    })
}
//设施问题详情
export function getFacilityproblemDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityproblem/findOne',
        method: 'GET',
        params,
    })
}
//设施问题新增
export function addFacilityproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityproblem/add',
        method: 'POST',
        params,
    })
}

//设施问题处理
export function dealFacilityproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityproblem/update',
        method: 'POST',
        params,
    })
}
//获取问题分类字典项
export function getProblemClass(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_PROBLEM_CLASS',
        method: 'GET',
        params,
    })
}
//设施问题删除
export function delFacilityproblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/farm/facilityproblem/delete',
        method: 'GET',
        params,
    })
}

//设施问题原因分类字典项
export function getReason(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_REASON_TYPE',
        method: 'GET',
        params,
    })
}

//问题处理结果分类
export function getDealResult(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/PROBLEM_DEAL_RESULT',
        method: 'GET',
        params,
    })
}

//项目问题分类
export function getProjectProblem(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/PROJECT_PROBLEM_CLASS',
        method: 'GET',
        params,
    })
}

//项目问题分类
export function getProjectProblemReason(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/PROJECT_PROBLEM_REASON',
        method: 'GET',
        params,
    })
}

//设备问题分类
export function getDeviceProblemClass(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/DEVICE_PROBLEM_CLASS',
        method: 'GET',
        params,
    })
}

//项目预警处理结果
export function getProjectWarnResult(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/PROJECT_WARN_RESULT',
        method: 'GET',
        params,
    })
}

//设施/设备处理结果
export function getFacilityWarnResult(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/dict/data/list/FACILITY_WARN_RESULT',
        method: 'GET',
        params,
    })
}