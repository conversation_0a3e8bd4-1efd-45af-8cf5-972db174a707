<template>
	<view class="bg">
		<u-navbar title="任务积分" :autoBack="true" bgColor="rgb(66,206,147,0)" :placeholder="true" titleStyle="color:#fff"
			leftIconColor="#fff">
		</u-navbar>
		<!-- 背景 -->
		<image class="navBg" mode="widthFix" :src="TASKPOINTS_JF_BG"></image>
		<view class="bkbg" :style="bkheight">
			<image class="bk-fize" mode="widthFix" :src="TASKPOINTS_JF_BK_BG">
			</image>
			<view class="firstBox">
				<view class="flexbetween">
					<view class="item">
						<view class="label">总积分</view>
						<view class="value">{{integrate}}</view>
					</view>
				</view>
			</view>
		</view>
		<scroll-view class="secondBox" scroll-y @scrolltolower="getList">

			<block v-if="accountList.length>0">
				<view class="accountitem u-border-bottom" v-for="(item, index) in accountList" :key="index"
					@click="getData(item)">
					<view class="left">
						<view class="accounttitle u-line-1" v-if="item.taskName">
							{{ item.taskName }}
						</view>
						<view class="accounttitle u-line-1" v-else>
							{{ item.pointsUsageDesc }}
						</view>
						<view class="accounttime">
							{{ item.createDate }}
						</view>
					</view>
					<view>
						<view v-if="item.pointsValue>0" class="accountprice u-line-1">
							+<text>{{ item.pointsValue }}</text>
						</view>
						<view v-else class="minusPoints u-line-1">
							<text>{{ item.pointsValue }}</text>
						</view>
					</view>
				</view>
			</block>
			<view class="empty-status" v-else>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>

			<u-loadmore :status="loadStatus" v-if='accountList.length > 0'></u-loadmore>

		</scroll-view>
		<!-- 占位块，使上一个view的marginbottom生效 -->
		<view style="height: 1rpx"></view>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref,
		onMounted
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		findList,
		taskPoints,
	} from '@/common/net/my/my.js'
	import {
		NO_MORE_IMG,
		TASKPOINTS_JF_BG,
		TASKPOINTS_JF_BK_BG
	} from '@/common/net/staticUrl.js'

	const queryParams = reactive({
		pageNum: 1,
		pageSize: 30,
	})
	const integrate = ref('0')
	const accountList = ref([])
	const total = ref(0)
	const loaded = ref(false)
	const loadStatus = ref('loading')
	onLoad(() => {
		getList()
		integral()

	})
	onMounted(() => {
		getTopPosition()
	})
	const bkheight = ref()

	//积分查询
	function integral() {
		taskPoints().then((res) => {
			if (res.success) {
				integrate.value = res.data
			} else {
				integrate.value = 0
			}
		})
	}

	function getList() {
		if (loadStatus.value == 'nomore') return;
		if (loadStatus.value == 'loadmore') {
			loadStatus.value = 'loading'
		}
		findList(queryParams)
			.then((res) => {
				if (res.success) {
					total.value = res.data.total
					if (queryParams.pageNum > 1) {
						accountList.value = [...accountList.value, ...res.data.records]
					} else {
						accountList.value = res.data.records
					}
					if (total.value === accountList.value.length) {
						loadStatus.value = 'nomore'
					} else {
						loadStatus.value = 'loadmore'
						queryParams.pageNum = queryParams.pageNum + 1
					}
				} else {
					uni.showToast({
						title: res.message || '查询数据失败',
						icon: 'none',
					})
					loadStatus.value = 'loadmore'
				}
				loaded.value = true
			})
			.catch(() => {
				uni.showToast({
					title: '查询数据失败',
					icon: 'none',
				})
				loadStatus.value = 'loadmore'
			})
	}

	// 获取关闭图标的位置
	const bkheig = ref(0)
	const topIconDistance = ref(0) // 关闭按钮距顶部距离
	function getTopPosition() {
		//获取状态栏高度
		const statusBarHeight = uni.getSystemInfoSync().statusBarHeight
		console.log('状态栏高度------', statusBarHeight);
		// #ifdef MP-WEIXIN
		// 获取导航栏的高度（手机状态栏高度 + 胶囊高度 + 胶囊的上下间距）
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		console.log('胶囊信息------', menuButtonInfo.bottom);
		const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2;
		// 计算顶部图标距离
		// topIconDistance.value = statusBarHeight + navBarHeight;
		topIconDistance.value = menuButtonInfo.top;
		bkheight.value = {
			top: menuButtonInfo.bottom + 10 + 'px'
		}
		// #endif
		// #ifdef APP-PLUS
		topIconDistance.value = statusBarHeight + 44;
		// #endif
	}
</script>

<style lang="scss" scoped>
	.bg {
		background-color: white;
		width: 100%;
		height: 100%;
		position: absolute;
		z-index: 1;
	}

	.navBg {
		width: 100%;
		position: absolute;
		/* 使用 z-index 调整上下层级需要脱离文档流 */
		top: 0;
		z-index: 1;
		/* z-index 设低一点，让页面元素可以覆盖它 */
	}

	.bk-fize {
		width: 700rpx;
		height: 212rpx;
		padding-left: 25rpx;
		position: absolute;
		z-index: 2;
	}

	.firstBox {
		top: 50rpx;
		z-index: 2;
		position: absolute;
		line-height: 1;
		box-sizing: border-box;
		width: 290rpx;

		.flexbetween {
			display: flex;
			margin: 0 9.2vw;

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.label {
					font-size: 30rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 600;
					color: #292c39;
					line-height: 42rpx;
				}

				.value {
					letter-spacing: 0;
					font-size: 43rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 600;
					color: #666666;
					line-height: 61rpx;
					background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
		}
	}

	.secondBox {
		z-index: 2;
		position: absolute;
		top: 390rpx;
		height: calc(100% - 390rpx);
		margin: 0 25rpx;
		box-sizing: border-box;

		width: calc(100% - 50rpx);

		.accountitem {
			display: flex;
			align-items: center;
			padding-top: 20rpx;
			padding-bottom: 11rpx;

			.left {
				flex: 1;

				.accounttitle {
					font-size: 28rpx;
					color: #000000;
					line-height: 40rpx;
					margin-bottom: 8rpx;
				}

				.accounttime {
					font-size: 26rpx;

					color: #999999;
					line-height: 37rpx;
				}
			}

			.accountprice {
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				min-width: 100rpx;
				color: #666666;
				line-height: 40rpx;
				background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.minusPoints {
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				min-width: 100rpx;
				color: #666666;
				line-height: 40rpx;
				background: linear-gradient(149deg, #3ADC98 0%, #0CBE88 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.reducepoint {
				background: linear-gradient(149deg, #5cdca0 0%, #1ec58b 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				color: #666666;
			}
		}
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}

	.bkbg {
		z-index: 2;
		position: absolute;
	}

	::v-deep .u-loadmore {
		padding-bottom: 60rpx;
	}
</style>