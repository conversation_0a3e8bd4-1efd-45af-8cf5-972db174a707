<template>
  <view class="container" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})` }">
        <logoutbtn></logoutbtn>

    <!-- 顶部banner -->
    <view class="banner" :style="customStyle">
      <view class="banner-content">
        <view class="time-info">      
             <view class="weekday">{{ currentTime.weekday }}</view>
          <view class="date">{{ currentTime.date }}</view>
        </view>
        <view class="weather-info" v-if="!!weatherInfo.temperature">
          <image :src="weatherIcon" class="weather-icon" mode="aspectFit"></image>
          <view class="weather-desc">
            <view class="temperature">{{ weatherInfo.temperature }}℃</view>
            <view class="weather-name">{{ weatherInfo.weather }}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 功能按钮区域 -->
    <view class="content">
      <view 
        class="system-card backimg1"    @click="handleFunctionSelect(1)"
      >
        <image src="../../static/images/index/icon1.png" class="system-icon" />       <view class="system-info">       <view class="system-name">企业列表</view>
          <view class="system-name-en">Company List</view>
        </view>
      </view>
      <view 
        class="system-card backimg2"    @click="handleFunctionSelect(2)"
      >
        <image src="../../static/images/index/icon2.png" class="system-icon" />       <view class="system-info">       <view class="system-name">处理提醒</view>
          <view class="system-name-en">Process Reminders</view>
        </view>
      </view>
      <view 
        class="system-card backimg3"    @click="handleFunctionSelect(3)"
      >
        <image src="../../static/images/index/icon3.png" class="system-icon" />       <view class="system-info">       <view class="system-name">短信通知</view>
          <view class="system-name-en">SMS Notifications</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import logoutbtn from '@/jimo/components/logoutbtn.vue'
import { 
  LIVE_INVENTORY_BG,
  HOMELAND_WEATHER_01,
  HOMELAND_WEATHER_02,
  HOMELAND_WEATHER_03,
  HOMELAND_WEATHER_04,
  HOMELAND_WEATHER_05,
  HOMELAND_WEATHER_06,
  HOMELAND_WEATHER_07,
  HOMELAND_WEATHER_08,
  HOMELAND_WEATHER_09,
  HOMELAND_WEATHER_10,
  HOMELAND_WEATHER_11,
  HOMELAND_WEATHER_12,
  HOMELAND_WEATHER_13,
  HOMELAND_WEATHER_14,
  HOMELAND_WEATHER_15,
  HOMELAND_WEATHER_16,
  HOMELAND_WEATHER_17,
  HOMELAND_WEATHER_18,
  HOMELAND_WEATHER_19,
  HOMELAND_WEATHER_20,
  HOMELAND_WEATHER_21,
  HOMELAND_WEATHER_22,
  HOMELAND_WEATHER_23,
  HOMELAND_WEATHER_24,
  HOMELAND_WEATHER_25,
  HOMELAND_WEATHER_26,
  HOMELAND_WEATHER_27,
  HOMELAND_WEATHER_28,
  HOMELAND_WEATHER_29,
  HOMELAND_WEATHER_30,
  HOMELAND_WEATHER_31,
  HOMELAND_WEATHER_32,
  HOMELAND_WEATHER_33,
  HOMELAND_WEATHER_34,
  HOMELAND_WEATHER_35,
  HOMELAND_WEATHER_36,
  HOMELAND_WEATHER_37,
  HOMELAND_WEATHER_38,
  HOMELAND_WEATHER_39,
  HOMELAND_WEATHER_40,
  HOMELAND_WEATHER_41,
  HOMELAND_WEATHER_42,
  HOMELAND_WEATHER_43,
  HOMELAND_WEATHER_44,
  HOMELAND_WEATHER_45,
  HOMELAND_WEATHER_46,
  HOMELAND_WEATHER_47,
  HOMELAND_WEATHER_48,
  HOMELAND_WEATHER_49,
  HOMELAND_WEATHER_50,
  HOMELAND_WEATHER_51,
  HOMELAND_WEATHER_52,
  HOMELAND_WEATHER_53,
  HOMELAND_WEATHER_54,
  HOMELAND_WEATHER_55,
  HOMELAND_WEATHER_56,
  HOMELAND_WEATHER_57,
  HOMELAND_WEATHER_58,
  HOMELAND_WEATHER_59,
  HOMELAND_WEATHER_60,
  HOMELAND_WEATHER_61,
  HOMELAND_WEATHER_62,
  HOMELAND_WEATHER_63,
  HOMELAND_WEATHER_64,
  HOMELAND_WEATHER_65,
  HOMELAND_WEATHER_66,
  HOMELAND_WEATHER_67,
  HOMELAND_WEATHER_68,
  JM_ADMIN_BG
} from '@/common/net/staticUrl.js'
import { config } from '@/config/config.js'


// 当前时间
const currentTime = ref({
  weekday: '',
  date: ''
})

// 天气信息
const weatherInfo = ref({})
const weatherIcon = ref('')

// banner样式
const customStyle = {
  backgroundImage: `url(${JM_ADMIN_BG})`,
  backgroundSize: 'cover',
  backgroundRepeat: 'no-repeat',
}

// 天气图标列表
const weatherList =  [
  { id: 1, name: '晴', url: HOMELAND_WEATHER_01},
  { id: 2, name: '少云', url: HOMELAND_WEATHER_02},
  { id: 3, name: '晴间多云', url: HOMELAND_WEATHER_03},
  { id: 4, name: '多云', url: HOMELAND_WEATHER_04},
  { id: 5, name: '阴', url: HOMELAND_WEATHER_05},
  { id: 6, name: '有风', url: HOMELAND_WEATHER_06},
  { id: 7, name: '平静', url: HOMELAND_WEATHER_07},
  { id: 8, name: '微风', url: HOMELAND_WEATHER_08},
  { id: 9, name: '和风', url: HOMELAND_WEATHER_09},
  { id: 10, name: '清风', url: HOMELAND_WEATHER_10},
  { id: 11, name: '强风/劲风', url: HOMELAND_WEATHER_11},
  { id: 12, name: '疾风', url: HOMELAND_WEATHER_12},
  { id: 13, name: '大风', url: HOMELAND_WEATHER_13},
  { id: 14, name: '烈风', url: HOMELAND_WEATHER_14},
  { id: 15, name: '风暴', url: HOMELAND_WEATHER_15},
  { id: 16, name: '狂爆风', url: HOMELAND_WEATHER_16},
  { id: 17, name: '飓风', url: HOMELAND_WEATHER_17},
  { id: 18, name: '热带风暴', url: HOMELAND_WEATHER_18},
  { id: 19, name: '霾', url: HOMELAND_WEATHER_19},
  { id: 20, name: '中度霾', url: HOMELAND_WEATHER_20},
  { id: 21, name: '重度霾', url: HOMELAND_WEATHER_21},
  { id: 22, name: '严重霾', url: HOMELAND_WEATHER_22},
  { id: 23, name: '阵雨', url: HOMELAND_WEATHER_23},
  { id: 24, name: '雷阵雨', url: HOMELAND_WEATHER_24},
  { id: 25, name: '雷阵雨并伴有冰雹', url: HOMELAND_WEATHER_25},
  { id: 26, name: '小雨', url: HOMELAND_WEATHER_26},
  { id: 27, name: '中雨', url: HOMELAND_WEATHER_27},
  { id: 28, name: '大雨', url: HOMELAND_WEATHER_28},
  { id: 29, name: '暴雨', url: HOMELAND_WEATHER_29},
  { id: 30, name: '大暴雨', url: HOMELAND_WEATHER_30},
  { id: 31, name: '特大暴雨', url: HOMELAND_WEATHER_31},
  { id: 32, name: '强阵雨', url: HOMELAND_WEATHER_32},
  { id: 33, name: '强雷阵雨', url: HOMELAND_WEATHER_33},
  { id: 34, name: '极端降雨', url: HOMELAND_WEATHER_34},
  { id: 35, name: '毛毛雨/细雨', url: HOMELAND_WEATHER_35},
  { id: 36, name: '雨', url: HOMELAND_WEATHER_36},
  { id: 37, name: '小雨-中雨', url: HOMELAND_WEATHER_37},
  { id: 38, name: '中雨-大雨', url: HOMELAND_WEATHER_38},
  { id: 39, name: '大雨-暴雨', url: HOMELAND_WEATHER_39},
  { id: 40, name: '暴雨-大暴雨', url: HOMELAND_WEATHER_40},
  { id: 41, name: '大暴雨-特大暴雨', url: HOMELAND_WEATHER_41},
  { id: 42, name: '雨雪天气', url: HOMELAND_WEATHER_42},
  { id: 43, name: '雨夹雪', url: HOMELAND_WEATHER_43},
  { id: 44, name: '阵雨夹雪', url: HOMELAND_WEATHER_44},
  { id: 45, name: '冻雨', url: HOMELAND_WEATHER_45},
  { id: 46, name: '雪', url: HOMELAND_WEATHER_46},
  { id: 47, name: '阵雪', url: HOMELAND_WEATHER_47},
  { id: 48, name: '小雪', url: HOMELAND_WEATHER_48},
  { id: 49, name: '中雪', url: HOMELAND_WEATHER_49},
  { id: 50, name: '大雪', url: HOMELAND_WEATHER_50},
  { id: 51, name: '暴雪', url: HOMELAND_WEATHER_51},
  { id: 52, name: '小雪-中雪', url: HOMELAND_WEATHER_52},
  { id: 53, name: '中雪-大雪', url: HOMELAND_WEATHER_53},
  { id: 54, name: '大雪-暴雪', url: HOMELAND_WEATHER_54},
  { id: 55, name: '浮尘', url: HOMELAND_WEATHER_55},
  { id: 56, name: '扬沙', url: HOMELAND_WEATHER_56},
  { id: 57, name: '沙尘暴', url: HOMELAND_WEATHER_57},
  { id: 58, name: '强沙尘暴', url: HOMELAND_WEATHER_58},
  { id: 59, name: '龙卷风', url: HOMELAND_WEATHER_59},
  { id: 60, name: '雾', url: HOMELAND_WEATHER_60},
  { id: 61, name: '浓雾', url: HOMELAND_WEATHER_61},
  { id: 62, name: '强浓雾', url: HOMELAND_WEATHER_62},
  { id: 63, name: '轻雾', url: HOMELAND_WEATHER_63},
  { id: 64, name: '大雾', url: HOMELAND_WEATHER_64},
  { id: 65, name: '特强浓雾', url: HOMELAND_WEATHER_65},
  { id: 66, name: '热', url: HOMELAND_WEATHER_66},
  { id: 67, name: '冷', url: HOMELAND_WEATHER_67},
  { id: 68, name: '未知', url: HOMELAND_WEATHER_68}
]

let timer = null

onLoad(() => {
  updateTime()
  getWeatherInfo()
  // 每分钟更新一次时间
  timer = setInterval(updateTime, 60000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 更新当前时间
function updateTime() {
  const now = new Date()
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const month = now.getMonth() + 1
  const date = now.getDate()
  
  currentTime.value = {
    weekday: weekdays[now.getDay()],
    date: `${month}月${date}号`
  }
}



// 获取天气信息
function getWeatherInfo() {
  const key = config.AMapKey
  uni.request({
    url: `https://restapi.amap.com/v3/weather/weatherInfo?&key=${key}&city=370215&extensions=base`,
    success: (res) => {
      console.log('weather success', res)
      weatherInfo.value = res.data.lives[0]
     const info =  weatherList.find(item=>item.name==res.data.lives[0].weather)
     weatherIcon.value = info.url
    
    },
  })
}

// 处理功能选择
function handleFunctionSelect(functionType) {
  switch(functionType) {
    case 1:     // 企业列表
      uni.navigateTo({
        url: '/jimo/anquanjianguan/companyList/companyList'
      })
      break
    case 2:     // 处理提醒
      uni.navigateTo({
        url: '/jimo/anquanjianguan/adminstrator/remindList'
      })
      break
    case 3:     // 短信通知
      uni.navigateTo({
        url: '/jimo/anquanjianguan/SMSNotification/notificationList'
      })
      break
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
 height: 100vh;
  background: linear-gradient(180deg, #f0ff 0%, #ffffff 100%);
  background-size: cover;
  background-position: center top;
  background-repeat: no-repeat;
  box-sizing: border-box;
  padding: 40rpx 30rpx;
}

.banner {
  width: 100%;
  height: 300rpx;
  border-radius: 24rpx;
  margin-bottom: 60rpx;
  position: relative;
  overflow: hidden;
  
  
    .time-info {
      color: #ffffff;
      position: absolute;
      bottom: 20rpx;
      display: flex;
      align-items: center;
      right: 20rpx;
      .weekday {
        font-size: 30rpx;
        font-weight: bold;
        margin-right: 20rpx;
      }
      
      .date {
        font-size: 26rpx;
        opacity: 0.9;
      }
    }
    
    .weather-info {
      display: flex;
      
      position: absolute;
      top: 10rpx;
      right: 20rpx;
      .weather-icon {
        width: 60rpx;
        height: 60rpx;
        margin-right: 20rpx;
      }
      
      .weather-desc {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        color: #ffffff;
        
        .temperature {
          font-size: 38rpx;
          font-weight: bold;
          margin-bottom: 8rpx;
        }
        
        .weather-name {
          font-size: 26rpx;
          opacity: 0.9;
        }
      }
    }
  
}

.content {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  padding: 0 10rpx;
}

.system-card {
  display: flex;
  align-items: center;
  padding: 50rpx 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.6));
    z-index: 1;
  }
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.05);
  }
  
  &.backimg1, &.backimg2, &.backimg3 {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}

.system-icon {
  width: 96rpx;
  height: 96rpx;
  margin-right: 40rpx;
  position: relative;
  z-index: 2;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.system-info {
  flex: 1;
  position: relative;
  z-index: 2;
  
  .system-name {
    font-size: 40rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 12rpx;
    letter-spacing: 1rpx;
  }
  
  .system-name-en {
    font-size: 26rpx;
    color: #5c6b7f;
    opacity: 0.85;
    letter-spacing: 0.5rpx;
  }
}

// 为每个卡片添加独特的渐变背景
.backimg1 {
  background-image: linear-gradient(135deg, #6ab3fd 0%, #4481eb 100%);
}

.backimg2 {
  background-image: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.backimg3 {
  background-image: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}
</style> 