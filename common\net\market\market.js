import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDMOBILE_URL,
	API_FINDPAGEMOBILE_URL,
	API_MYRELESASE_URL,
	API_MOBILEDETAIL_URL,
	API_UPDATEMOBILE_URL,
	API_DELETEMOBILE_URL,
} from '@/common/net/netUrl.js'
// 发布买卖
export function mobileAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDMOBILE_URL,
		method: 'POST',
		params,
	})
}

//浏览商品
export function mobileFind(params) {
	return request({
		url: API_FINDPAGEMOBILE_URL,
		method: "GET",
		params,
	});
}

//我的发布
export function myRelesase(params) {
	return request({
		url: API_MYRELESASE_URL,
		method: "GET",
		params,
	});
}

//买卖详情
export function mobileDetail(params) {
	return request({
		url: API_MOBILEDETAIL_URL,
		method: "GET",
		params,
	});
}

//编辑/修改
export function mobileUpdate(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_UPDATEMOBILE_URL,
		method: 'POST',
		params,
	})
}

//删除商品
export function mobileDelete(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_DELETEMOBILE_URL,
		method: 'POST',
		params,
	})
}