<template>
  <view class="company-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="企业详情" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>
    <view class="container">
      <view class="tab-content"   v-if="operationType!='update'">
        <view class="triangle-status-tag"
          v-if="company.auditStatus && company.auditStatus !== 'pending'"
          :class="{
            'triangle-approved': company.auditStatus === 'approved',
            'triangle-rejected': company.auditStatus === 'rejected',
          }"
        >
          <text class="triangle-text" :class="{
            'text-approved': company.auditStatus === 'approved',
            'text-rejected': company.auditStatus === 'rejected',
          }">
            {{ company.auditStatus ? companystatusList.find(item=>item.value==company.auditStatus).text : '' }}
          </text>
        </view>
        <u--form :model="company" ref="formRef" :label-width="'120px'" :labelStyle="{ color: '#333', lineHeight: '50rpx' }" :borderBottom="false">
          <!-- 营业信息 -->
          <div class="collapse-title">营业信息</div>
          <u-form-item label="企业名称"><u--input v-model="company.enterpriseName" readonly border="none" /></u-form-item>
          <u-form-item label="信用代码"><u--input v-model="company.creditCode" readonly border="none" /></u-form-item>
          <u-form-item label="成立日期"><u--input v-model="company.establishDate" readonly border="none" /></u-form-item>
          <u-form-item label="所属社区"><u--input v-model="communityLabel" readonly border="none" /></u-form-item>
          <u-form-item label="经营地址">
            <view class="address-row">
              <u-textarea v-model="company.businessAddress" disabled :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" border="none" />
              <u-icon name="map"></u-icon>
            </view>
          </u-form-item>
          <u-form-item label="生产经营单位类型"><u--input v-model="company.enterpriseNature" readonly border="none" /></u-form-item>
          <u-form-item label="法定代表人"><u--input v-model="company.legalRepresentative" readonly border="none" /></u-form-item>
          <u-form-item label="联系电话"><u--input maxlength="11" type="number" v-model="company.legalRepresentativePhone" readonly border="none" /></u-form-item>
          <u-form-item label="常用联系人"><u--input v-model="company.actualController" readonly border="none" /></u-form-item>
          <u-form-item label="联系电话"><u--input maxlength="11" type="number" v-model="company.actualControllerPhone" readonly border="none" /></u-form-item>
          <u-form-item v-if="company.enterpriseNature" :label="company.enterpriseNature === '个体工商户'?'风险关注点':'涉及重点行业'">
            <u--input v-model="industryCategoryLabel" readonly border="none" />
          </u-form-item>
          <template v-if="company.enterpriseNature === '工贸企业'">
            <u-form-item label="特种设备类型"><u--input v-model="company.specialEquipmentType" readonly border="none" /></u-form-item>
          </template>
          <template v-if="company.enterpriseNature === '个体工商户'">
            <u-form-item label="经营类型"><u--input v-model="company.businessType" readonly border="none" /></u-form-item>
          </template>
          <u-form-item label="年营业额"><u-input v-model="company.annualRevenue" type="number" readonly border="none" >
            <template #suffix>
              <text>万元</text>
            </template>
          </u-input></u-form-item>
          <u-form-item label="企业规模" v-if="company.enterpriseNature === '养殖企业'||company.enterpriseNature === '工贸企业'">
            <u--input v-model="company.enterpriseScale" readonly border="none" />
          </u-form-item>
          <u-form-item v-if="company.enterpriseNature" :label="company.enterpriseNature === '养殖企业'?'固定人员':'从业人员'">
            <u-input type="number" v-model="company.employeeCount" readonly border="none">
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="临时用工" v-if="company.enterpriseNature === '养殖企业'">
            <u-input type="number" v-model="company.employeeCountTemp" readonly border="none">
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="最新巡检时间"><u--input v-model="company.lastInspectTime" readonly border="none" /></u-form-item>
          <u-form-item label="主营业务"><u-textarea v-model="company.mainBusiness" disabled   height="auto" autoHeight  :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" border="none" /></u-form-item>
          <u-form-item label="养殖畜种" v-if="company.enterpriseNature === '养殖企业'">
            <u--input v-model="company.breedingLivestock" readonly border="none" />
          </u-form-item>
          <u-form-item label="安责险名称" v-if="company.safetyInsurance">
            <u--input v-model="company.safetyInsurance" readonly border="none" />
          </u-form-item>  
          <!-- 企业证件 -->
          <div class="collapse-title">企业证件</div>
          <view class="certificates-list" v-if="company.credentialList && company.credentialList.length > 0">
            <view class="certificate-item" v-for="(item, index) in company.credentialList" :key="index">
              <u-form-item label="证件名称"><u--input v-model="item.credentialName" readonly border="none" /></u-form-item>
              <u-form-item label="证件开始日期"><u--input v-model="item.startDate" readonly border="none" /></u-form-item>
              <u-form-item label="证件截止日期"><u--input v-model="item.endDate" readonly border="none" /></u-form-item>
              <view class="certificate-images" v-if="item.credentialImage">
                <view class="section-title">证件图片</view>
                <view class="imgcell">
                  <view class="imgitem" v-for="(img, imgIndex) in item.credentialImage.split(',')" :key="imgIndex">
                    <view class="img">
                      <image :src="img"></image>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="no-data">暂无证件信息</view>
          <!-- 设备与工艺，仅工贸企业显示 -->
          <template v-if="company.enterpriseNature == '工贸企业'">
            <div class="collapse-title">设备与工艺</div>
            <u-form-item label="主要设备设施"><u--input v-model="company.mainEquipment" readonly border="none" /></u-form-item>
            <u-form-item label="涉及工艺"><u--input v-model="company.involvedTechnology" readonly border="none" /></u-form-item>
            <u-form-item label="主要原料"><u--input v-model="company.mainMaterials" readonly border="none" /></u-form-item>
            <u-form-item label="主要/副产品"><u-textarea v-model="company.mainProducts" disabled :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" border="none" /></u-form-item>
          </template>
          <!-- 厂房性质 -->
          <div class="collapse-title">厂房性质</div>
          <template v-if="company.enterpriseNature === '工贸企业'||company.enterpriseNature === '养殖企业'">
            <u-form-item label="土地规划" >
                  <u--input v-model="company.landPlanning" readonly border="none" />
                </u-form-item>
                <u-form-item label="厂房性质" >
                  <u--input v-model="company.factoryNature" readonly border="none"  />
                </u-form-item>
           
            <u-form-item label="厂房所有人"><u--input v-model="company.factoryOwner" readonly border="none" /></u-form-item>
          <u-form-item label="厂房电话"><u--input v-model="company.factoryOwnerPhone" maxlength="11" type="number" readonly border="none" /></u-form-item>
          <u-form-item label="持有形式"><u--input v-model="company.holdingForm" readonly border="none" /></u-form-item>
        </template>
        <template v-if="company.enterpriseNature === '个体工商户'">
          <u-form-item label="租赁/自有" prop="ownershipType" >
            
            <u--input v-model="company.ownershipType" readonly  border="none" />
            </u-form-item>
            <u-form-item label="房东" prop="landlord">
                  <u--input v-model="company.landlord" readonly border="none" />
                </u-form-item>
                <u-form-item label="房东电话" prop="landlordPhone">
                  <u--input v-model="company.landlordPhone" readonly border="none"  maxlength="11" type="number" />
                </u-form-item>
          </template>
          <u-form-item label="建筑面积"><u-input v-model="company.buildingArea" readonly border="none" >
            <template #suffix>
              <text>平方米</text>
            </template>
          </u-input></u-form-item>
          <div class="collapse-title">监管部门</div>
          <u-form-item label="行业类别" >
                  <u--input v-model="company.industryClassification" :readonly="true" border="none"   />
                </u-form-item>
                <u-form-item label="直接监管部门" >
                  <u--input v-model="company.directSupervisionDept" :readonly="true" border="none"  />
                </u-form-item>
        </u--form>
      </view>
      <!-- 其余审核、变更、意见等内容保持不变 -->
      <template v-if="operationType=='update'">
        <view class="risk-section">
          <view class="triangle-status-tag"
            v-if="company.auditStatus && company.auditStatus !== 'pending'"
            :class="{
              'triangle-approved': company.auditStatus === 'approved',
              'triangle-rejected': company.auditStatus === 'rejected',
            }"
          >
            <text class="triangle-text" :class="{
              'text-approved': company.auditStatus === 'approved',
              'text-rejected': company.auditStatus === 'rejected',
            }">
              {{ company.auditStatus ? companystatusList.find(item=>item.value==company.auditStatus).text : '' }}
            </text>
          </view>
          <view class="collapse-title">企业基本信息</view>
          <view class="content" style="height:calc(50vh - 460rpx)">
            <view class="form-item">
              <view class="form-label">企业名称</view>
              <view class="form-content">{{auditDetail.enterpriseName }}</view>
            </view>
            <view class="form-item">
              <view class="form-label">统一社会信用代码</view>
              <view class="form-content">{{auditDetail.creditCode}}</view>
            </view>
            <view class="form-item">
              <view class="form-label">操作类型</view>
              <view class="form-content">{{opTypeTxt}}</view>
            </view>
            <view class="form-item">
              <view class="form-label">提交时间</view>
              <view class="form-content">{{auditDetail.createDate}}</view>
            </view>
          </view>
        </view>
        <view class="risk-section" style="margin-bottom: 0;">
          <view class="collapse-title">变更信息</view>
          <view class="content"  style="height:calc(50vh - 360rpx)">
            <template v-for="item in filteredChangeList" :key="item.fieldDesc">
              <view class="form-label">{{getFieldName(item.fieldDesc)}}：</view>
              <view class="form-content" style="margin:20rpx 0;display: flex;align-items:center"><u-tag :text="item.oldValue" type="error" plain></u-tag>
                <u-icon name="arrow-rightward"  width="28" height="18"></u-icon>
                <u-tag :text="item.newValue" plain></u-tag>
              </view>
            </template>
          </view>
        </view>
      </template>
      <view class="form-section" v-if="company.auditStatus=='pending'">
        <u--form ref="formRef" :model="form"  :label-width="120" :labelStyle="{fontWeight: 'bold', paddingBottom: '10rpx'}" :rules="rules">
           <u-form-item label="巡检周期" prop="inspectInterval" required >
            <u-input v-model="form.inspectInterval" placeholder="请输入巡检周期"   type="number">
              <template #suffix>
               月
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="审核状态" prop="auditActionText" required @click="openAuditStatusPicker = true">
            <u-input v-model="form.auditActionText" placeholder="请选择审核状态"  readonly >
              <template #suffix>
                <u-icon name="arrow-down" style="margin-right: 10rpx;"></u-icon>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="审核意见" prop="comment">
            <u-textarea v-model="form.comment" placeholder="请输入审核意见" maxlength="100" showWordLimit  />
          </u-form-item>
        </u--form>
      </view>
      <view class="content" v-if="company.auditStatus!='pending'&&company.auditComment">
        <template v-if="company.auditComment">
          <view class="form-item" >
              <view class="form-label">巡检周期</view>
              <view class="form-content" >{{ company.inspectInterval }}
              </view>
            </view>
          <view class="form-label">审核意见</view>
          <view class="form-content" style="margin-top: 20rpx;">{{ company.auditComment}}</view>
        </template>
      </view>
      <template v-if="company.auditStatus=='pending'">
        <view class="blank"></view>
        <view class="bottom u-border-top">
          <view class="btn pjBtn" @click="submitForm">提交</view>
        </view>
        <u-picker @close="openAuditStatusPicker = false" :show="openAuditStatusPicker" closeOnClickOverlay :columns="[statusOptions]" keyName="label" @confirm="onStatusConfirm" @cancel="openAuditStatusPicker = false" />
      </template>
    </view>
  </view>
</template>
<script setup>
import { ref, onMounted, reactive,computed } from 'vue'
import { onLoad,onReady} from '@dcloudio/uni-app'
import {LIVE_INVENTORY_BG} from "@/common/net/staticUrl.js"
import { AdminService } from "../../api/anquanjianguan/adminstrator"
import { getDicts } from '@/common/net/contacts/contacts.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'
const company = ref({})
const auditId = ref('')
const openAuditStatusPicker = ref(false)
const statusOptions = [
  { label: '审核通过', value: 'approve' },
  { label: '已驳回', value: 'reject' }
]
const companystatusList = ref([
  { text: '未审核', value: 'pending' },
  { text: '审核通过', value: 'approved' },
  { text: '已驳回', value: 'rejected' }
])
const form = reactive({
  auditActionText:'',
  auditAction: '',
  comment: ''
})
const formRef = ref(null)
const rules = ref({
  inspectInterval: [{type:'number', required: true, message: '请输入巡检周期', trigger: ['blur', 'change'] }],
  auditActionText: [{ required: true, message: '请选择审核状态', trigger: ['blur', 'change'] }]
})
const communities = ref([])
const industryCategories = ref([])
const auditDetail = ref({})
const changeList = ref([])


const operationType = ref('')
const opTypeTxt = computed(() => {
  const opType = operationType.value
  switch(opType) {
    case 'update':
      return '修改'
    case 'add':
      return '添加'
    case 'delete':
      return '删除'
    default:
      return ''
  }
})
const filteredChangeList = computed(() => changeList.value.filter(item => getFieldName(item.fieldDesc)))

onReady(()=>{
  formRef.value.setRules(rules)
})
const communityLabel = ref('')
const industryCategoryLabel = ref('')
async function getDetail(id){
  try{
  const detail = await AdminService.findAuditDetail(id)
  
  if(detail.success){
    company.value = detail.data
      // 确保证件数组存在
      if (!company.value.credentialList) {
        company.value.credentialList = []
      }
    
      // 确保新增字段存在
      if (!company.value.communityValue) {
         communityLabel.value = ''
      }
      else{
        console.log(communities.value,company.value.communityValue,communities.value.find(item=>item.dictValue==company.value.communityValue))
        communityLabel.value = communities.value.find(item=>item.dictValue==company.value.communityValue).dictLabel
      }
      if (!company.value.industryCategory) {
         industryCategoryLabel.value = ''
      }
      else{
        console.log(industryCategories.value,company.value.industryCategory,industryCategories.value.find(item=>item.dictValue==company.value.industryCategory))
        industryCategoryLabel.value = industryCategories.value.find(item=>item.dictValue==company.value.industryCategory).dictLabel
      }
     
      if (!company.value.lastInspectTime) {
        company.value.lastInspectTime = ''
      }
      if (!company.value.longitude) {
        company.value.longitude = ''
      }
      if (!company.value.latitude) {
        company.value.latitude = ''
      }
  }
  else{
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    })
  }
}catch(e){
  uni.showToast({
    title: '获取详情失败',
    icon: 'none'
  })
}
}
function getFieldName(fieldKey) {
  const fieldNameMapArr = fieldNameMap()
      return fieldNameMapArr[fieldKey] || '';
    }
function fieldNameMap() {
      return {
        lastInspectTime:'最新巡检时间',
        credentialList:'企业证件',
        communityName:'所属社区',
        specialEquipmentType:'特种设备类型',
        businessType:'经营类型',
        enterpriseName: '企业名称',
        creditCode: '统一社会信用代码',
        enterpriseNature: '生产经营单位类型',
        enterpriseScale: '企业规模',
        breedingLivestock:'养殖畜种',
        establishDate: '成立日期',
        employeeCount: '固定人员数量',
        employeeCountTemp:'临时人员数量',
        businessAddress: '生产经营地址',
        annualRevenue: '年营业额',
        legalRepresentative: '法定代表人',
        legalRepresentativePhone: '法定代表人联系电话',
        actualController: '常用联系人',
        actualControllerPhone: '常用联系人电话',
        mainBusiness: '主营业务',
        mainEquipment: '主要设备设施',
        involvedTechnology: '涉及工艺',
        mainMaterials: '主要原料',
        mainProducts: '主要产品/副产品',
        landPlanning: '土地规划',
        factoryNature: '厂房性质',
        factoryOwner: '厂房所有人',
        factoryOwnerPhone: '厂房所有人联系电话',
        holdingForm: '持有形式',
        certificateStatus: '证件情况',
        buildingArea: '建筑面积',
        industryCategory: '涉及重点行业',
        safetyInsurance: '安责险名称',
        ownershipType: '租赁/自有',
        landlord: '房东',
        landlordPhone: '房东电话',
        industryClassification: '行业类别',
        directSupervisionDept: '直接监管部门',
        address: '详细地址',
      };
    }
onLoad(async(options) => {
  auditId.value = options.id
    let response = await getDicts('communities')
    let list = response.data
    list.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    communities.value = list.map(item=>({...item,label:item.dictLabel}))



    let industryCategoriesresponse = await getDicts('industryCategories')
    let industryCategorieslist = industryCategoriesresponse.data
    industryCategorieslist.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    industryCategories.value = industryCategorieslist.map(item=>({...item,label:item.dictLabel}))
  let  res = await AdminService.auditDetail(options.id)
  if(res.success){
   const optype = res.data?.audit?.operationType
   operationType.value = optype
   auditDetail.value = res.data?.audit
  const changes =  res.data?.changes
  // 根据changes对象生成变更信息数组
  if (changes) {
    for (const key in changes) {
      if (changes.hasOwnProperty(key)) {
        const item = changes[key]
        changeList.value.push({
          fieldDesc: key,
          oldValue: item.oldValue&&item.oldValue.length?item.oldValue:'',
          newValue: item.newValue&&item.newValue.length?item.newValue:''
        })
      }
    }
  }
  }
  getDetail(options.id)
})
function onStatusConfirm(e) {
  form.auditAction = e.value[0].value
  form.auditActionText = e.value[0].label
  openAuditStatusPicker.value = false
}
const isdisabled = ref(false)
function submitForm() {
  formRef.value.validate().then( async () => {
    if (isdisabled.value) return;
      isdisabled.value = true
      let {auditActionText,...params} = form
      AdminService.checkCompany({...params,auditId:auditId.value}).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title: '提交成功！', icon: 'none' })
          setTimeout(() => {
            uni.navigateBack()
          }, 1000)
        } else {
          uni.showToast({ title: res.message, icon: 'none' })
        }
      }).catch(()=>{
        isdisabled.value = false
        uni.showToast({
				title: '提交失败！',
				icon: 'none',
			})
      })
  })
}
</script>
<style lang="scss" scoped>
.blank {
  width: 100%;
  height: 120rpx;
}
.company-detail {
  width: 100%;
  min-height: 100vh;
  background: #F0F7F7;
  .container {
    overflow: scroll;
    box-sizing: border-box;
    padding: 20rpx;
    position: relative;
    .triangle-status-tag {
      position: absolute;
      top: 0;
      right: 0;
      width: 160rpx;
      height: 160rpx;
      z-index: 10;
      background: transparent;
      .triangle-text {
        position: absolute;
        top: 38rpx;
        right: -40rpx;
        width: 160rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        transform: rotate(50deg);
        text-align: center;
        pointer-events: none;
        white-space: nowrap;
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-top: 160rpx solid #e53935;
        border-left: 140rpx solid transparent;
      }
    }
    .triangle-approved::before {
      border-top-color: #0cbe88 !important;
    }
    .triangle-rejected::before {
      border-top-color: #e53935 !important;
    }
    .triangle-pending::before {
      border-top-color: #e5e5e5 !important;
    }
    .triangle-approved .triangle-text {
      color: #fff;
    }
    .triangle-rejected .triangle-text {
      color: #fff;
    }
    .triangle-pending .triangle-text {
      color: #666;
    }
    .status-tag {
      position: absolute;
      top: 10rpx;
      right: 30rpx;
      background: #f5f5f5;
      color: #0cbe88;
      border: 1rpx solid #0cbe88;
      border-radius: 24rpx;
      font-size: 26rpx;
      padding: 6rpx 28rpx;
      z-index: 10;
      font-weight: 600;
      letter-spacing: 2rpx;
      box-shadow: 0 2rpx 8rpx rgba(12,190,136,0.08);
    }
    .tab-content {
      position: relative;
      background: linear-gradient(180deg, #FFFFFF 0%, #F5F6F6 100%);
      border-radius: 50rpx 50rpx 0 0;
      padding: 20rpx 20rpx 40rpx 20rpx;
      margin-top: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
      height: calc(100vh - 730rpx);
      box-sizing: border-box;
      overflow: scroll;
    }
    ::v-deep .u-tabs {
      padding: 0 25rpx;
      .u-tabs__wrapper__nav__line {
        height: 8rpx !important;
        background: linear-gradient(117deg, #0CBE88 0%, #3ADB97 100%) !important;
        border-radius: 5rpx !important;
      }
    }
    ::v-deep .uni-input-wrapper{
      text-align: right;
    }
    ::v-deep .u-input{
      margin-right: 40rpx;
    }
  }
  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin: 30rpx 0 20rpx 0;
  }
  .risk-section {
    margin-bottom: 20rpx;
    background: #fff;
    position: relative;
    border-radius: 16rpx;
    padding: 18rpx 18rpx 0 18rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  }
  .risk-section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #0CBE88;
    margin-bottom: 18rpx;
    letter-spacing: 2rpx;
  }
  ::v-deep .u-form-item__body__right__content__slot {
    padding: 0 10rpx;
    .form-label {
      min-width: 200rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-size: 26rpx;
      color: rgba(51, 51, 51, 1);
      margin-bottom: 10rpx;
      padding: 0 5rpx;
      text {
        font-weight: 400;
        font-size: 24rpx;
        color: rgba(153, 153, 153, 1);
      }
    }
    .form-body {
      display: flex;
      .u-input {
        height: 80rpx;
        border: none;
        padding: 0 !important;
      }
      .divider {
        width: 37rpx;
        height: 52rpx;
        border-bottom: 1rpx solid #979797;
        margin-right: 44rpx;
      }
    }
  }
  .address-row {
    display: flex;
    width: 100%;
    align-items: center;
    .u-icon {
      margin-left: 10rpx;
    }
  }
  ::v-deep .u-collapse-item__content {
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
    padding: 10rpx 10rpx 0 10rpx;
  }
  ::v-deep .u-collapse-item {
    background: #fff;
  }
  .collapse-title {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 700;
    color: #222;
    letter-spacing: 1rpx;
    padding: 12rpx 0 12rpx 0;
  }
  .collapse-title::before {
    content: '';
    display: inline-block;
    width: 8rpx;
    height: 32rpx;
    background: #0CBE88;
    border-radius: 4rpx;
    margin-right: 16rpx;
  }

  // 证件相关样式
  .certificates-list {
    .certificate-item {
      position: relative;
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16rpx;
      border: 1rpx solid rgba(12, 190, 136, 0.1);

      .certificate-images {
        margin-top: 16rpx;

        .section-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 12rpx;
        }

        .imgcell {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .imgitem {
            position: relative;

            .img {
              width: 120rpx;
              height: 120rpx;
              border-radius: 8rpx;
              overflow: hidden;

              image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
      }
    }
  }

  // 培训相关样式
  .trainings-list {
    .training-item {
      position: relative;
      margin-bottom: 32rpx;
      padding: 24rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16rpx;
      border: 1rpx solid rgba(12, 190, 136, 0.1);

      .training-images {
        margin-top: 16rpx;

        .section-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 12rpx;
        }

        .imgcell {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .imgitem {
            position: relative;

            .img {
              width: 120rpx;
              height: 120rpx;
              border-radius: 8rpx;
              overflow: hidden;

              image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    color: #999;
    font-size: 28rpx;
    padding: 40rpx 0;
  }
  .form-section {
    background: #fff;
    border-radius: 20rpx;
    height: 360rpx;
    box-sizing: border-box;
    overflow: scroll;
    margin-top:20rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
    .form-item {
      margin-bottom: 40rpx;
      display: flex;
      flex-direction: column;
      .form-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 18rpx;
      }
      &.required .form-label::before {
        content: '*';
        color: #FF2D2D;
        margin-right: 6rpx;
      }
    }
    .u-select, .u-textarea {
      width: 100%;
    }
  }
  .content {
    padding: 24rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
    box-sizing: border-box;
    height: 360rpx;
    overflow: scroll;
    .form-label {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 30rpx;
      color: #999999;
      line-height: 40rpx;
    }
    .form-content {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 30rpx;
      color: #000000;
      line-height: 40rpx;
      background: none;
      border-radius: 0;
      padding: 0;
      margin-top: 0;

    }
    .form-item {
      margin-bottom: 53rpx;
      display: flex;
      justify-content: space-between;
      width: 100%;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;
  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }
  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style> 