import { request } from '../request.js'
import { isDef } from '@/common/uniUtils.js'
import { MENU_ALL_URL, MENU_PERSON_URL } from '@/common/net/netUrl.js'
//获取首页菜单接口
//说明：目前仅支持H5和APP，其他暂不支持。
export function findAppAllMenuVoList(params) {
  if (!isDef(params)) {
    params = {}
  }
  params['permissionScope'] = 'mb_app'
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: MENU_ALL_URL,
    method: 'GET',
    params,
  })
}

//保存个人主页配置接口
//configCode、configId、configStatus、personalId、personalValue、staffOrgId;
export function savePersonalConfig(params) {
  if (!isDef(params)) {
    params = {}
  }
  return request({
    url: MENU_PERSON_URL,
    method: 'POST',
    params,
  })
}
