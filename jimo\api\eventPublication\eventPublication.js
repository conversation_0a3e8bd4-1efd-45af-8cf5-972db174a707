import {
    request
} from '@/common/net/request.js';


export function getInfoList(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/mission/findOfPage",
      method: "POST",
      params
    });
  }
  
  export function addEvent(params){
    params['uniContentType'] = 'json'
    return request({
        url: "/village/mission/add",
        method: "POST",
        params
    })
  }
  export  function updateEvent(params){
    
    params['uniContentType'] = 'json'
    return request({
        url: "/village/mission/update",
        method: "POST",
        params
    })
  }

  export function findOneInfo(params){
    return request({
        url: "/village/mission/findOne",
        method: "GET",
        params
    })
  }
  export function getTasksComplete(params){
    params['uniContentType'] = 'json'
    return request({
        url: "/village/missionPeople/findOfPageALL",
        method: "POST",
        params
    })
  }
  export function gridOrgAndMember(){
    return request({
        url: "/village/grid/gridOrgAndMember",
        method: "GET"
    })
  }
  // 获取所有网格
  export function getAllGrid() {
      return request({
          url: "/village/grid_organization_management/management/findAllList",
          method: 'GET',
      })
  }



