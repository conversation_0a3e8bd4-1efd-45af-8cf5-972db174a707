<template>
  <view class="supplycontainer">
    <image class="backimg" mode="widthFix" :src="RANK_BG"></image>
    <view class="table">
      <view class="tableheader">
        <view class="th">排名</view>
        <view class="th" style="text-align: center;">网格</view>
        <view class="th" style="text-align: center;">网格长</view>
        <view class="th" style="text-align: center;">完成任务数</view>
      </view>
      <scroll-view class="secondBox" scroll-y @scrolltolower="getList">
        <!-- <u-empty
          v-if="loaded === true && gridlist.length === 0"
          mode="data"
        ></u-empty> -->
        <view class="empty-status" v-if='loaded === true && gridlist.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
        <block v-if="gridlist.length > 0">
          <view
            class="tb u-border-bottom"
            :class="
              index == 0
                ? 'firstbg'
                : index == 1
                ? 'secondbg'
                : index == 2
                ? 'thirdbg'
                : ''
            "
            v-for="(item, index) in gridlist"
            :key="index"
          >
            <view class="mc" v-if="index < 3">
              <image
                :src="
                  index == 0
                    ? NUM1
                    : index == 1
                    ? NUM2
                    : index == 2
                    ? NUM3
                    : ''
                "
              ></image>
            </view>

            <view v-else class="mctext"><text style="padding-left: 20rpx;">{{ index + 1 }}</text></view>
            <view class="zu u-line-1" style="width: 28.6%;">{{ item.gridName }}</view>
            <view class="zuzhang">
              <block    v-if="item.gridMemberName">
                <view class="avatarcon">
                  <safe-image
                width='58rpx' height='58rpx' shape="circle"
                :src="item.headPhoto || DEFAULT_AVATOR_IMAGES"
              ></safe-image>
                </view>
             
              <text class="username u-line-1"  >{{ item.gridMemberName}}</text>
            </block>
             <view v-else  style="text-align: center;width: 100%;"  >暂无人员</view>
            </view>
            <view class="num" style="width: 28.6%;text-align: center;">{{ item.completeNum }}</view>
          </view>
        </block>
        <!-- <u-loadmore
          :status="loadStatus"
          v-show="!(loaded === true && gridlist.length === 0)"
        ></u-loadmore> -->
      </scroll-view>
    </view>
  </view>
</template>
  
  <script setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { gridCompleteSort } from '../../api/mygrid/mygrid.js'
import {
  RANK_BG,
  NUM1,
  NUM2,
  NUM3,
  DEFAULT_AVATOR_IMAGES,
  NO_MORE_IMG
} from '@/common/net/staticUrl'

const queryParams = reactive({
  pageNum: 1,
  pageSize: 16,
})
const gridlist = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
onLoad((option) => {
  getsortList()
})
function getsortList() {
  gridCompleteSort({})
    .then((res) => {
      if (res.success) {
        gridlist.value = [...res.data]
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
    })
}
function getList() {
  //   if (loadStatus.value == 'nomore') return
  //   if (loadStatus.value == 'more') {
  //     loadStatus.value = 'loading'
  //   }
  //   findOfPage(queryParams)
  //     .then((res) => {
  //       if (res.success) {
  //         total.value = res.data.total
  //         if (queryParams.pageNum > 1) {
  //           gridlist.value = [...gridlist.value, ...res.data.records]
  //         } else {
  //           gridlist.value = res.data.records
  //         }
  //         if (total.value === gridlist.value.length) {
  //           loadStatus.value = 'nomore'
  //         } else {
  //           loadStatus.value = 'more'
  //           queryParams.pageNum = queryParams.pageNum + 1
  //         }
  //       } else {
  //         uni.showToast({
  //           title: res.message || '查询数据失败',
  //           icon: 'none',
  //         })
  //         loadStatus.value = 'more'
  //       }
  //       loaded.value = true
  //     })
  //     .catch(() => {
  //       uni.showToast({
  //         title: '查询数据失败',
  //         icon: 'none',
  //       })
  //       loadStatus.value = 'more'
  //     })
}
</script>
  
  <style lang="scss" scoped>
  .empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.supplycontainer {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
  .backimg {
    width: calc(100% - 40rpx);
    margin: 20rpx;
  }
  .table {
    z-index: 2;
    position: absolute;
    top: 322rpx;
    height: calc(100% - 342rpx);
    margin: 0 30rpx;
    margin-bottom: 20rpx;
    box-sizing: border-box;
   
    width: calc(100% - 60rpx);
    .tableheader {
      height: 77rpx;
      line-height: 77rpx;
      background: #ffffff;
      border-radius: 8rpx;
      padding: 0 24rpx;
      display: flex;
      justify-content: space-between;
      .th {
        width: 28.6%;
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #030303;
        &:nth-child(1) {
          width: 14.3%;
        }
      
      }
    }

    .secondBox {
      z-index: 3;
      position: absolute;
      top: 77rpx;
      height: calc(100% - 77rpx);
      box-sizing: border-box;

      .tb {
        padding: 0 24rpx;
        display: flex;
        // justify-content: space-between;
        background: #ffffff;
        height: 80rpx;
        align-items: center;
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #000000;
        text-align: center;
        .mc {
          image {
            width: 59rpx;
            height: 37rpx;
            display: block;
          //  margin: auto;
          }

          width: 14.3%;
        }
        .mctext {
          width: 14.3%;
          font-size: 28rpx;
          font-family: Arial;
          font-weight: normal;
          color: #ff2a00;
          text-align: left;
        }

        .zuzhang {
          width: 28.6%;
          display: flex;
          align-items: center;
          // justify-content: center;
          .avatarcon{
            width: 58rpx;
            height: 58rpx;
            border-radius: 50%;
            margin-right: 10rpx;
            overflow: hidden;
            .avatar {
            width: 58rpx;
            height: 58rpx;
            border-radius: 50%;
          }
          }
        
          .username{
            max-width: calc(100% - 68rpx);
          }
        }
      }
      .firstbg {
        background: rgba(255, 163, 2, 0.04);
      }
      .secondbg {
        background: rgba(212, 231, 237, 0.14);
      }
      .thirdbg {
        background: rgba(254, 116, 81, 0.06);
      }
    }
  }
}
</style>
  