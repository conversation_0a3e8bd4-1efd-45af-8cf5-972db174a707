{
    "name" : "即墨段泊岚镇农业综合管理平台",
    "appid" : "__UNI__3C507FC",
    "description" : "即墨段泊岚镇农业综合管理平台",
    "versionName" : "0.0.1",
    "versionCode" : 1,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Barcode" : {},
            "Bluetooth" : {},
            "Camera" : {},
            "FaceID" : {},
            "Fingerprint" : {},
            "Messaging" : {},
            "Record" : {},
            "Geolocation" : {},
            "Webview-x5" : {},
            "Statistic" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>"
                ],
                "abiFilters" : [ "x86" ],
                "minSdkVersion" : 28,
                "targetSdkVersion" : 30
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "",
                        "UniversalLinks" : ""
                    }
                },
                "statics" : {
                    "umeng" : {
                        "appkey_ios" : "6564373658a9eb5b0a11a6cc",
                        "channelid_ios" : "szxc_ios",
                        "appkey_android" : "654cc74d58a9eb5b0a058e5c",
                        "channelid_android" : "szxc_android"
                    }
                },
                "maps" : {}
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true,
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/app-plus/welcome480.9.png",
                    "xhdpi" : "static/app-plus/welcome720.9.png",
                    "xxhdpi" : "static/app-plus/welcome1080.9.png"
                },
                "iosStyle" : "storyboard",
                "ios" : {
                    "storyboard" : "static/app-plus/CustomStoryboard.zip"
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/app-plus/Android72.png",
                    "xhdpi" : "static/app-plus/Android96.png",
                    "xxhdpi" : "static/app-plus/Android144.png",
                    "xxxhdpi" : "static/app-plus/Android192.png"
                },
                "ios" : {
                    "appstore" : "static/app-plus/iOS1024.png",
                    "iphone" : {
                        "app@2x" : "static/app-plus/iOS120.png",
                        "app@3x" : "static/app-plus/iOS180.png",
                        "spotlight@2x" : "static/app-plus/iOS80.png",
                        "spotlight@3x" : "static/app-plus/iOS120.png",
                        "settings@2x" : "static/app-plus/iOS58.png",
                        "settings@3x" : "static/app-plus/iOS87.png",
                        "notification@2x" : "static/app-plus/iOS40.png",
                        "notification@3x" : "static/app-plus/iOS60.png"
                    },
                    "ipad" : {
                        "notification@2x" : "static/app-plus/iOS40.png",
                        "notification" : "static/app-plus/iOS20.png",
                        "settings@2x" : "static/app-plus/iOS58.png",
                        "settings" : "static/app-plus/iOS29.png",
                        "spotlight@2x" : "static/app-plus/iOS80.png",
                        "spotlight" : "static/app-plus/iOS40.png",
                        "app" : "static/app-plus/IOS76.png",
                        "app@2x" : "static/app-plus/iOS152.png",
                        "proapp@2x" : "static/app-plus/iOS167.png"
                    }
                }
            }
        },
        "nativePlugins" : {
            "JG-JCore" : {
                "JPUSH_APPKEY_IOS" : "",
                "JPUSH_CHANNEL_IOS" : "",
                "JPUSH_APPKEY_ANDROID" : "3eb47c88b4b33ae07caccb27",
                "JPUSH_CHANNEL_ANDROID" : "",
                "__plugin_info__" : {
                    "name" : "JG-JCore",
                    "description" : "极光推送JCore插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "com.cudt.szxc",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {
                        "JPUSH_APPKEY_IOS" : {
                            "des" : "[iOS]极光portal配置应用信息时分配的AppKey",
                            "key" : "JCore:APP_KEY",
                            "value" : ""
                        },
                        "JPUSH_CHANNEL_IOS" : {
                            "des" : "[iOS]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JCore:CHANNEL",
                            "value" : ""
                        },
                        "JPUSH_APPKEY_ANDROID" : {
                            "des" : "[Android]极光portal配置应用信息时分配的AppKey",
                            "key" : "JPUSH_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_CHANNEL_ANDROID" : {
                            "des" : "[Android]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JPUSH_CHANNEL",
                            "value" : ""
                        }
                    }
                }
            },
            "JG-JPush" : {
                "JPUSH_ISPRODUCTION_IOS" : "",
                "JPUSH_ADVERTISINGID_IOS" : "",
                "JPUSH_DEFAULTINITJPUSH_IOS" : "",
                "JPUSH_OPPO_APPKEY" : "",
                "JPUSH_OPPO_APPID" : "",
                "JPUSH_OPPO_APPSECRET" : "",
                "JPUSH_VIVO_APPKEY" : "",
                "JPUSH_VIVO_APPID" : "",
                "JPUSH_MEIZU_APPKEY" : "",
                "JPUSH_MEIZU_APPID" : "",
                "JPUSH_XIAOMI_APPKEY" : "",
                "JPUSH_XIAOMI_APPID" : "",
                "JPUSH_HUAWEI_APPID" : "",
                "__plugin_info__" : {
                    "name" : "JG-JPush",
                    "description" : "极光推送Hbuilder插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "com.cudt.szxc",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {
                        "JPUSH_ISPRODUCTION_IOS" : {
                            "des" : "[iOS]是否是生产环境，是填true，不是填false或者不填",
                            "key" : "JPush:ISPRODUCTION",
                            "value" : ""
                        },
                        "JPUSH_ADVERTISINGID_IOS" : {
                            "des" : "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填",
                            "key" : "JPush:ADVERTISINGID",
                            "value" : ""
                        },
                        "JPUSH_DEFAULTINITJPUSH_IOS" : {
                            "des" : "[iOS]是否默认初始化，是填true，不是填false或者不填",
                            "key" : "JPush:DEFAULTINITJPUSH",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPKEY" : {
                            "des" : "厂商OPPO-appkey,示例：OP-12345678",
                            "key" : "OPPO_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPID" : {
                            "des" : "厂商OPPO-appId,示例：OP-12345678",
                            "key" : "OPPO_APPID",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPSECRET" : {
                            "des" : "厂商OPPO-appSecret,示例：OP-12345678",
                            "key" : "OPPO_APPSECRET",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPKEY" : {
                            "des" : "厂商VIVO-appkey,示例：12345678",
                            "key" : "com.vivo.push.api_key",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPID" : {
                            "des" : "厂商VIVO-appId,示例：12345678",
                            "key" : "com.vivo.push.app_id",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPKEY" : {
                            "des" : "厂商MEIZU-appKey,示例：MZ-12345678",
                            "key" : "MEIZU_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPID" : {
                            "des" : "厂商MEIZU-appId,示例：MZ-12345678",
                            "key" : "MEIZU_APPID",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPKEY" : {
                            "des" : "厂商XIAOMI-appKey,示例：MI-12345678",
                            "key" : "XIAOMI_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPID" : {
                            "des" : "厂商XIAOMI-appId,示例：MI-12345678",
                            "key" : "XIAOMI_APPID",
                            "value" : ""
                        },
                        "JPUSH_HUAWEI_APPID" : {
                            "des" : "厂商HUAWEI-appId,示例：appid=12346578",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        }
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : true
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxcd08b783f1d5c204",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "bigPackageSizeSupport" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "演示定位能力"
            },
            "scope.makePhoneCall" : {
                "desc" : "用于拨打电话"
            }
        },
        // "plugins" : {
        //     "routePlan" : {
        //         "version" : "1.0.19",
        //         "provider" : "wx50b5593e81dd937a"
        //     }
        // },
        "requiredPrivateInfos" : [ "getLocation" ],
        "uniStatistics" : {
            "enable" : true
        },
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "1"
    },
    "vueVersion" : "3",
    "h5" : {
        "router" : {
            "mode" : "history",
            "base" : "/"
        },
        "devServer" : {
            "https" : false
        },
        "uniStatistics" : {
            "enable" : false
        },
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "877ce7d817978b1e77299dbd8b141cff",
                    "securityJsCode" : "0432e3b39d848e6295b74a5d426c37f7",
                    "serviceHost" : ""
                }
            }
        }
    },
    /* 自定义渠道包 */
    "channel_list" : [
        {
            "id" : "website",
            "name" : "官方网站"
        },
        {
            "id" : "honor",
            "name" : "荣耀手机"
        },
        {
            "id" : "wechat",
            "name" : "微信平台分发"
        }
    ],
    "quickapp-webview" : {
        "icon" : "static/app-plus/Android192.png",
        "package" : "com.szxc.vallage",
        "versionName" : "0.0.1",
        "versionCode" : 1
    },
    "quickapp-webview-huawei" : {
        // 快应用华为
        "minPlatformVersion" : 1070 //最小平台支持
    }
}
