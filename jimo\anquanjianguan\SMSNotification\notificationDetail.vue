<template>
    <view class="company-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
      <u-navbar title="短信详情" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
         <logoutbtn></logoutbtn>

      <view class="container">
        <view class="tab-content">
          <u--form :model="form" errorType="toast" :rules="rules" ref="formRef" :label-width="'100px'" :labelStyle="labelStyle" :borderBottom="false">
                  <u-form-item label="短信标题" prop="title" required>
                    <u--input v-model="form.title" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入短信标题'" border="none" clearable/>
                  </u-form-item>
                  <u-form-item label="短信内容" prop="content" required>
                    <u-textarea autoHeight v-model="form.content" :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请输入短信内容'" border="none"></u-textarea>
                  </u-form-item>
                  <u-form-item label="发送人" prop="sendPerson" v-if="isReadOnly">
                    <u--input v-model="form.sendPerson" readonly border="none" />
                   </u-form-item>
                  <u-form-item label="发送方式" prop="sendType" @click.stop="isReadOnly?showSendType = false:showSendType = true" required>
                    <u--input v-model="form.sendType" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择发送方式'" border="none" clearable/>
                    <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
                  </u-form-item>
                  <u-form-item label="发送时间" v-if="form.sendType==='定时发送'" prop="sendTime" required @click.stop="isReadOnly?showTimePicker = false:showTimePicker=true">
                    <u--input v-model="form.sendTime" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择发送时间'" border="none" clearable/>
                    <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
                  </u-form-item>
               
                  <u-form-item label="接收企业" prop="company" required @click.stop="isReadOnly ? null : gotoCompanyList()">
                    <u-textarea autoHeight v-model="form.company" :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请选择接收企业'" border="none"></u-textarea>
                    <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
                  </u-form-item>

                  <!-- 接收人和接受状态 -->
                  <template v-if="isReadOnly && form.receiveDatas && form.receiveDatas.length > 0">
                    <view class="receive-section">
                      <view class="section-title">接收详情</view>
                      <view class="receive-list">
                        <view class="receive-item" v-for="(item, index) in form.receiveDatas" :key="index">
                          <view class="receive-info">
                            <view class="receiver-name">{{ item.receiverName }}</view>
                            <view class="receive-status" :class="getStatusClass(item.receiveStatus)">
                              {{ item.receiveStatus }}
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </template>

                  <u-form-item label="创建时间" prop="createTime"  v-if="isReadOnly">
                    <u--input v-model="form.createTime" readonly border="none" />
                   </u-form-item>
                   <u-form-item label="更新时间" prop="updateTime"  v-if="isReadOnly">
                    <u--input v-model="form.updateTime" readonly border="none" />
                   </u-form-item>
                  <u-form-item label="备注" prop="remark" >
                    <u-textarea autoHeight v-model="form.remark" :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请输入备注'" border="none"></u-textarea>
                  </u-form-item>
                 
          </u--form>
        </view>
      </view>
     
        <template v-if="!isReadOnly">
          <view class="blank"></view>
      <view  class="bottom">
        <view class="btn" @click="reset" style="margin-right: 40rpx;">取消</view>
        <view class="btn pjBtn" @click="handleSubmit">保存</view>
      </view>
    </template>
    
      <u-tabbar :value="barActive" @change="handlerAdminBarChange" fixed safeAreaInsetBottom active-color="#0CBE88" inactive-color="#999999" v-if="isReadOnly">
     
        <u-tabbar-item text="编辑" icon="info-circle" />
        <u-tabbar-item text="删除" icon="trash" />
      </u-tabbar>
     
      <u-picker closeOnClickOverlay @close="showSendType = false" :show="showSendType" :columns="[showSendTypes]" keyName="label" @confirm="onRefrigerationTypeConfirm" @cancel="showSendType = false" />
      <u-popup :show="showPop" mode="center" @close="cancelHandler" closeable :safeAreaInsetBottom="false" round="5">
        <view class="tktip">
          <view class="content">确定要删除该短信吗？</view>
          <view class="btngroup">
            <view class="cancel" @click="cancelHandler">取消</view>
            <view class="okbtn" @click="deleteHandler">确定</view>
          </view>
        </view>
      </u-popup>
      <u-datetime-picker closeOnClickOverlay @close="showTimePicker = false" :show="showTimePicker" @confirm="onTimeConfirm" mode="datetime" @cancel="showTimePicker = false"  />

    </view>
  </template>
  
  <script setup>
  import { ref, computed, reactive, onUnmounted } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import {LIVE_INVENTORY_BG} from "@/common/net/staticUrl.js"
  import logoutbtn from '@/jimo/components/logoutbtn.vue'

  const showPop = ref(false)
  const barActive = ref(-1)
const showSendType = ref(false)
  const formRef = ref(null)


  const showSendTypes = [
    { label: '定时发送', value: '定时发送' },
    { label: '立即发送', value: '立即发送' }
  ]

  const labelStyle = ref({ color: '#333', lineHeight: '50rpx' })
  const isReadOnly = computed(() => type.value === 'info')
  const type = ref('info')
  const isdisabled = ref(false)
  const validateCompany = (rule, value, callback) => {
		if(!value){
			callback(new Error('请选择接受企业'))
		}else{
			callback()
		}
	}
const form = reactive({
  id: '', // 短信ID
  title: '', // 短信标题
  content: '', // 短信内容
  sendType: '', // 发送方式：定时发送/立即发送
  sendTime: '', // 发送时间，当sendType为定时发送时必填
  company: '', // 接收企业列表, 格式为：企业1,企业2,企业3...
  remark: '', // 备注信息
  createTime: '', // 创建时间
  updateTime: '', // 更新时间
  receiveDatas:[]
})

// 保存初始数据的副本
const originalFormData = ref({})
const rules = ref({
  title: [{ required: true, message: '短信标题不能为空', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '短信内容不能为空', trigger: ['blur', 'change'] }],
  sendType: [{ required: true, message: '发送方式不能为空', trigger: ['blur', 'change'] }],
  sendTime: [{ required: true, message: '发送时间不能为空', trigger: ['blur', 'change'] }],
  company:{
			validator:validateCompany
		}
})
  onLoad((options) => {
    const id = options.id || ''

    // 模拟数据加载
    loadFormData()
    console.log('------------')
    // 监听企业选择事件
    uni.$on('selectcompanys', (res) => {
      console.log('选中的企业:', res)
      form.company = res.map(item => item.enterpriseName).join(',')
    })
  })

  // 加载表单数据的函数
  function loadFormData() {
    // 模拟数据（实际项目中这里应该是API调用）
    form.id = 'SMS_001'
    form.title = '安全生产月度通知'
    form.content = '各企业请注意：根据安全生产要求，本月需完成以下工作：1.安全隐患自查；2.消防设施检查；3.应急预案演练。请各单位按时完成并提交相关资料。'
    form.sendType = '定时发送'
    form.sendPerson = '张三'
    form.receiveDatas = [
      {
        receiverName:'李四',
        receiveStatus:'已发送'
      },
       {
        receiverName:'刘能',
        receiveStatus:'已读'
      },
      {
        receiverName:'王五',
        receiveStatus:'未发送'
      }
    ]
    form.sendTime = '2024-01-20 10:00'
    form.company = '青岛某某食品有限公司,即墨某某农产品加工厂'
    form.createTime = '2024-01-15 14:30'
    form.updateTime = '2024-01-15 14:30'

    // 数据加载完成后，保存初始数据的深拷贝
    saveOriginalData()
  }

  // 保存初始数据
  function saveOriginalData() {
    originalFormData.value = JSON.parse(JSON.stringify(form))
    console.log('初始数据已保存:', originalFormData.value)
  }

  // 页面销毁时移除事件监听
  onUnmounted(() => {
    uni.$off('selectcompanys')
  })
  const showTimePicker = ref(false)

  function onTimeConfirm(e) {
  form.sendTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
  showTimePicker.value = false
}
  function reset(){
    type.value = 'info';
    barActive.value = -1

    // 恢复初始数据
    if (originalFormData.value && Object.keys(originalFormData.value).length > 0) {
      console.log('正在恢复初始数据:', originalFormData.value)
      try {
        Object.keys(originalFormData.value).forEach(key => {
          if (key in form) {
            form[key] = JSON.parse(JSON.stringify(originalFormData.value[key]))
          }
        })
        console.log('数据恢复成功')
      } catch (error) {
        console.error('数据恢复失败:', error)
        // 如果恢复失败，重新加载数据
        loadFormData()
      }
    } else {
      console.warn('没有找到初始数据，重新加载')
      // 如果没有初始数据，重新加载
      loadFormData()
    }
  }


  function onRefrigerationTypeConfirm(e) {
    form.sendType = e.value[0].label
    showSendType.value = false
  }

  // 跳转到企业选择页面
  function gotoCompanyList() {
    uni.navigateTo({
      url: './columncontent'
    })
  }

  // 根据接受状态返回对应的CSS类名
  function getStatusClass(status) {
    switch(status) {
      case '已发送':
        return 'status-sent'
      case '已读':
        return 'status-read'
      case '未发送':
        return 'status-unsent'
      default:
        return 'status-default'
    }
  }
  function handleSubmit() {
    formRef.value.validate().then(valid => {
    
        if (isdisabled.value) return;
        isdisabled.value = true
    
            uni.showToast({ title: '已提交修改,待审核！', icon: 'none' })
        
          reset()
        }).catch(e=>{
          isdisabled.value = false
          uni.showToast({
            title: '修改失败！',
            icon: 'none',
          })
        })
    
  }
  
  function cancelHandler() {
          showPop.value = false
      reset()
      }
  
  
  
      async function deleteHandler() {
          if (isdisabled.value) return;
          isdisabled.value = true
          try {
              isdisabled.value = false
              
          showPop.value = false
              setTimeout(() => { 
          uni.showToast({
                      title: '已提交删除，待审核！',
                      icon: 'none',
                  })
          uni.navigateBack()
              }, 1000)
          
          } catch (e) {
              isdisabled.value = false
        uni.showToast({
                      title: '删除失败！',
                      icon: 'none',
                  })
          }
      }
    const handlerAdminBarChange = (index) => {
    barActive.value = index
  
    if(index == 0) {
      type.value = 'edit'
      formRef.value.setRules(rules)
  
       }else{
      showPop.value = true
  
    } 
  }

  </script>
  
  <style lang="scss" scoped>
  .company-detail {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    ::v-deep .tktip {
          padding: 80rpx 60rpx 60rpx 60rpx;
          width: 650rpx;
          box-sizing: border-box;
  
          .content {
              font-size: 32rpx;
  
              color: #111111;
              line-height: 55rpx;
              margin-bottom: 20rpx;
          }
  
          .btngroup {
              display: flex;
              align-items: center;
              justify-content: space-between;
  
              .cancel {
                  width: 240rpx;
                  height: 80rpx;
                  line-height: 80rpx;
                  text-align: center;
                  background: #ffffff;
                  border-radius: 41rpx;
                  border: 2rpx solid #e5e5e5;
                  font-size: 28rpx;
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 400;
                  color: #555555;
              }
  
              .okbtn {
                  width: 240rpx;
                  height: 80rpx;
                  background: #0cbe88;
                  border-radius: 41rpx;
                  line-height: 80rpx;
                  text-align: center;
                  font-size: 28rpx;
                  font-family: Source Han Sans CN, Source Han Sans CN;
                  font-weight: 500;
                  color: #ffffff;
              }
          }
      }
    .container {
      overflow: hidden;
      height: calc(100vh - 200rpx);
      box-sizing: border-box;
      padding: 24rpx;

    .tab-content {
      background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 24rpx;
      padding: 32rpx;
      margin-top: 16rpx;
      box-shadow:
        0 8rpx 32rpx rgba(12, 190, 136, 0.08),
        0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      height: calc(100vh - 280rpx);
      box-sizing: border-box;
      overflow-y: auto;
      position: relative;

    }

    // 表单项样式优化
    ::v-deep .u-form-item {
      margin-bottom: 24rpx;
      padding: 10rpx;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 16rpx;
      border: 1rpx solid rgba(12, 190, 136, 0.1);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(12, 190, 136, 0.2);
        box-shadow: 0 4rpx 12rpx rgba(12, 190, 136, 0.05);
      }

      .u-form-item__body__left__content__label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 28rpx;
      }
    }

    ::v-deep .uni-input-wrapper{
      text-align: right;
    }

    ::v-deep .u-input{
      margin-right: 40rpx;
      font-size: 28rpx;
      color: #34495e;
    }

    ::v-deep .u-form-item__body__left__content__required {
      position: inherit;
      left: auto;
      color: #e74c3c;
    }

    ::v-deep .u-textarea__field{
      height: auto!important;
      font-size: 28rpx;
      color: #34495e;
      line-height: 1.6;
    }
  }

  // 接收详情样式优化
  .receive-section {
    margin: 32rpx 0;
    padding: 24rpx;
    background: linear-gradient(135deg, rgba(12, 190, 136, 0.02) 0%, rgba(58, 219, 151, 0.02) 100%);
    border-radius: 20rpx;
    border: 1rpx solid rgba(12, 190, 136, 0.1);
    position: relative;
    overflow: hidden;

    .section-title {
      font-size: 32rpx;
      font-weight: 700;
      margin-bottom: 20rpx;
      padding-left: 16rpx;
      position: relative;

    }

    .receive-list {
      background: transparent;
      border-radius: 0;
      padding: 0;

      .receive-item {
        margin-bottom: 16rpx;
        transform: translateY(0);
        transition: all 0.3s ease;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateY(-2rpx);
        }

        .receive-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20rpx 24rpx;
          background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
          border-radius: 16rpx;
          box-shadow:
            0 4rpx 20rpx rgba(12, 190, 136, 0.08),
            0 1rpx 3rpx rgba(0, 0, 0, 0.05);
          border: 1rpx solid rgba(12, 190, 136, 0.08);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4rpx;
            background: linear-gradient(180deg, #0CBE88 0%, #3ADB97 100%);
          }

          .receiver-name {
            font-size: 30rpx;
            color: #2c3e50;
            font-weight: 600;
            display: flex;
            align-items: center;

            &::before {
              content: '👤';
              margin-right: 8rpx;
              font-size: 24rpx;
            }
          }

          .receive-status {
            font-size: 24rpx;
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-weight: 600;
            letter-spacing: 0.5rpx;
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
              transition: left 0.5s ease;
            }

            &:hover::before {
              left: 100%;
            }

            &.status-sent {
              background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
              color: #1565c0;
              border: 1rpx solid rgba(21, 101, 192, 0.2);
            }

            &.status-read {
              background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
              color: #2e7d32;
              border: 1rpx solid rgba(46, 125, 50, 0.2);
            }

            &.status-unsent {
              background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
              color: #ef6c00;
              border: 1rpx solid rgba(239, 108, 0, 0.2);
            }

            &.status-default {
              background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%);
              color: #616161;
              border: 1rpx solid rgba(97, 97, 97, 0.2);
            }
          }
        }
      }
    }
  }

  // 地址行样式
  .address-row {
    display: flex;
    width: 100%;
    align-items: center;

    .u-icon {
      margin-left: 12rpx;
      color: #0CBE88;
      font-size: 32rpx;
      transition: all 0.3s ease;

      &:hover {
        color: #3ADB97;
        transform: scale(1.1);
      }
    }
  }
  // 底部安全区域
  .paddingbottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .blank {
    width: 100%;
    height: 140rpx;
  }
  // 底部按钮样式优化
  .bottom {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    z-index: 100;
    box-sizing: border-box;
    background: linear-gradient(180deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%);
    backdrop-filter: blur(10rpx);
    border-top: 1rpx solid rgba(12, 190, 136, 0.1);

    .btn {
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 44rpx;
      flex: 1;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      font-weight: 600;
      background-color: #bdc3c7;
      color: #fff;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
      }
    }

    .pjBtn {
      background: linear-gradient(135deg, #3ADB97 0%, #0CBE88 100%);
      box-shadow: 0 6rpx 20rpx rgba(12, 190, 136, 0.3);

      &:hover {
        box-shadow: 0 8rpx 25rpx rgba(12, 190, 136, 0.4);
        transform: translateY(-2rpx);
      }

      &:active {
        transform: translateY(1rpx);
        box-shadow: 0 4rpx 15rpx rgba(12, 190, 136, 0.3);
      }
    }
  }
  }

  </style>