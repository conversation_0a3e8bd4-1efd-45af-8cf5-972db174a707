// @use "sass:math";
@import '../tools/functions.scss';
// 间距基础倍数
$uni-space-root: 2 !default;
// 边框半径默认值
$uni-radius-root: 5px !default;
$uni-radius: () !default;
// 边框半径断点
$uni-radius: map-deep-merge(
  (
    0: 0,
    // TODO 当前版本暂时不支持 sm 属性
    // 'sm': math.div($uni-radius-root, 2),
    null: $uni-radius-root,
    'lg': $uni-radius-root * 2,
    'xl': $uni-radius-root * 6,
    'pill': 9999px,
    'circle': 50%,
  ),
  $uni-radius
);
// 字体家族
$body-font-family: 'Roboto', sans-serif !default;
// 文本
$heading-font-family: $body-font-family !default;
$uni-headings: () !default;
$letterSpacing: -0.01562em;
$uni-headings: map-deep-merge(
  (
    'h1': (
      size: 32px,
      weight: 300,
      line-height: 50px,
      // letter-spacing:-0.01562em
    ),
    'h2': (
      size: 28px,
      weight: 300,
      line-height: 40px,
      // letter-spacing: -0.00833em
    ),
    'h3': (
      size: 24px,
      weight: 400,
      line-height: 32px,
      // letter-spacing: normal
    ),
    'h4': (
      size: 20px,
      weight: 400,
      line-height: 30px,
      // letter-spacing: 0.00735em
    ),
    'h5': (
      size: 16px,
      weight: 400,
      line-height: 24px,
      // letter-spacing: normal
    ),
    'h6': (
      size: 14px,
      weight: 500,
      line-height: 18px,
      // letter-spacing: 0.0125em
    ),
    'subtitle': (
      size: 12px,
      weight: 400,
      line-height: 20px,
      // letter-spacing: 0.00937em
    ),
    'body': (
      font-size: 14px,
      font-weight: 400,
      line-height: 22px,
      // letter-spacing: 0.03125em
    ),
    'caption': (
      'size': 12px,
      'weight': 400,
      'line-height': 20px,
      // 'letter-spacing': 0.03333em,
      // 'text-transform': false,,,,
    ),
  ),
  $uni-headings
);

// 主色
$uni-primary: #2979ff !default;
$uni-primary-disable: lighten($uni-primary, 20%) !default;
$uni-primary-light: lighten($uni-primary, 25%) !default;

// 辅助色
// 除了主色外的场景色，需要在不同的场景中使用（例如危险色表示危险的操作）。
$uni-success: #18bc37 !default;
$uni-success-disable: lighten($uni-success, 20%) !default;
$uni-success-light: lighten($uni-success, 25%) !default;

$uni-warning: #f3a73f !default;
$uni-warning-disable: lighten($uni-warning, 20%) !default;
$uni-warning-light: lighten($uni-warning, 25%) !default;

$uni-error: #e43d33 !default;
$uni-error-disable: lighten($uni-error, 20%) !default;
$uni-error-light: lighten($uni-error, 25%) !default;

$uni-info: #8f939c !default;
$uni-info-disable: lighten($uni-info, 20%) !default;
$uni-info-light: lighten($uni-info, 25%) !default;

// 中性色
// 中性色用于文本、背景和边框颜色。通过运用不同的中性色，来表现层次结构。
$uni-main-color: #3a3a3a !default; // 主要文字
$uni-base-color: #6a6a6a !default; // 常规文字
$uni-secondary-color: #909399 !default; // 次要文字
$uni-extra-color: #c7c7c7 !default; // 辅助说明

// 边框颜色
$uni-border-1: #f0f0f0 !default;
$uni-border-2: #ededed !default;
$uni-border-3: #dcdcdc !default;
$uni-border-4: #b9b9b9 !default;

// 常规色
$uni-black: #000000 !default;
$uni-white: #ffffff !default;
$uni-transparent: rgba(
  $color: #000000,
  $alpha: 0,
) !default;

// 背景色
$uni-bg-color: #f7f7f7 !default;

/* 水平间距 */
$uni-spacing-sm: 8px !default;
$uni-spacing-base: 15px !default;
$uni-spacing-lg: 30px !default;

// 阴影
$uni-shadow-sm: 0 0 5px
  rgba(
    $color: #d8d8d8,
    $alpha: 0.5,
  ) !default;
$uni-shadow-base: 0 1px 8px 1px
  rgba(
    $color: #a5a5a5,
    $alpha: 0.2,
  ) !default;
$uni-shadow-lg: 0px 1px 10px 2px
  rgba(
    $color: #a5a4a4,
    $alpha: 0.5,
  ) !default;

// 蒙版
$uni-mask: rgba(
  $color: #000000,
  $alpha: 0.4,
) !default;
