<template>
  <view class="company-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="企业详情" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>
    <view class="container">
      <scroll-view  @scroll="listScroll" :scroll-top="scrollTop" scroll-y="true" class="tab-content" ref="tabContentRef">
        <u--form :model="company" errorType="toast" :rules="isReadOnly?[]:rules" ref="formRef" :label-width="'130px'" :labelStyle="labelStyle" :borderBottom="false">
          <div class="collapse-title">营业信息</div>
          <image class="yyzz" @click="uploadyyzzhandler" v-if="!isReadOnly" src="@/jimo/static/images/uploadyyzz.png"></image>
          <!-- 营业信息表单项 -->
          <u-form-item label="生产经营单位类型" prop="enterpriseNature" @click="isReadOnly?showTypePicker = false:showTypePicker = true" required>
            <u--input v-model="company.enterpriseNature" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择生产经营单位类型'" border="none" clearable />
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <u-form-item label="企业名称" prop="enterpriseName" required>
            <u--input v-model="company.enterpriseName" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入企业名称'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="信用代码" prop="creditCode" required>
            <u--input v-model="company.creditCode" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入信用代码'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="成立日期" prop="establishDate" required>
            <u--input v-model="company.establishDate" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入日期，格式：yyyy-mm-dd'" border="none" clearable @blur="validateDate(company.establishDate, (value)=>{company.establishDate = value} )" />
          </u-form-item>
          <u-form-item label="所属社区" prop="communityValue" required @click="isReadOnly ? null : (showCommunityPicker = true)">
            <u--input v-model="communityLabel" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择所属社区'" border="none" clearable/>
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <u-form-item label="经营地址" prop="businessAddress" required @click="isReadOnly ? null : getLocation()">
            <view class="address-row">
              <u-textarea v-model="company.businessAddress" :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请输入经营地址'" border="none" ></u-textarea>
              <u-icon name="map" ></u-icon>
            </view>
          </u-form-item>
        
          <u-form-item label="法定代表人" prop="legalRepresentative" required>
            <u--input v-model="company.legalRepresentative" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入法定代表人'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="联系电话" prop="legalRepresentativePhone" required>
            <u--input maxlength="11" type="number" v-model="company.legalRepresentativePhone" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入联系电话'" border="none" clearable />
          </u-form-item>
          <u-form-item label="常用联系人" prop="actualController" required>
            <u--input v-model="company.actualController" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入常用联系人'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="联系电话" prop="actualControllerPhone" required>
            <u--input maxlength="11" type="number" v-model="company.actualControllerPhone" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入联系电话'" border="none" clearable />
          </u-form-item>
          <u-form-item v-if="company.enterpriseNature" :label="company.enterpriseNature === '个体工商户'?'风险关注点':'涉及重点行业'" prop="industryCategory" @click="isReadOnly ? null : openIndustryCategoryPicker()" required>
            <u--input v-model="industryCategoryLabel" :readonly="true" :placeholder="isReadOnly ? (company.enterpriseNature === '个体工商户' ? '' : '') : (company.enterpriseNature === '个体工商户' ? '请选择风险关注点' : '请选择涉及重点行业')" border="none" clearable @click="isReadOnly ? null : openIndustryCategoryPicker()" />
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <template v-if="company.enterpriseNature === '工贸企业'">
            <u-form-item label="特种设备类型" prop="specialEquipmentType">
              <u--input v-model="company.specialEquipmentType" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入特种设备类型'" border="none" clearable />
            </u-form-item>
          </template>
          <template v-if="company.enterpriseNature === '个体工商户'">
            <u-form-item label="经营类型" prop="businessType" required @click="isReadOnly?showBusinessTypePicker = false:showBusinessTypePicker = true" >
              <u--input v-model="company.businessType" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择经营类型'" border="none" clearable />
              <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
            </u-form-item>
          </template>
          <u-form-item label="年营业额" prop="annualRevenue" required>
            <u-input v-model="company.annualRevenue" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入年营业额'" border="none" clearable type="number">
              <template #suffix>
                <text>万元</text>
              </template> 
            </u-input>
          </u-form-item>
          <u-form-item label="企业规模" v-if="company.enterpriseNature === '养殖企业'||company.enterpriseNature === '工贸企业'" prop="enterpriseScale" @click="isReadOnly?showScalePicker = false:showScalePicker = true" required>
            <u--input v-model="company.enterpriseScale" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择企业规模'" border="none" clearable />
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon>
          </u-form-item>
          <u-form-item v-if="company.enterpriseNature" :label="company.enterpriseNature === '养殖企业'?'固定人员':'从业人员'" prop="employeeCount" required>
            <u-input type="number" v-model="company.employeeCount" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : (company.enterpriseNature === '养殖企业' ? '请输入固定人员数量' : '请输入从业人员数量')" border="none" clearable >
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="临时用工" prop="employeeCountTemp" v-if="company.enterpriseNature === '养殖企业'" required>
            <u-input type="number" v-model="company.employeeCountTemp" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入临时用工数量'" border="none" clearable >
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item v-if="isadmin" label="巡检周期" prop="inspectInterval" required> 
            <u-input type="number" v-model="company.inspectInterval"
             :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入巡检周期'" border="none" clearable>
              <template #suffix>
                <text>月</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="最新巡检时间" prop="lastInspectTime" required>
            <u--input v-model="company.lastInspectTime" :readonly="isReadOnly||company.lastInspectTime!=''" :placeholder="isReadOnly ? '' : '请输入时间，格式：yyyy-mm-dd HH:mm:ss'" border="none" clearable @blur="validateDateTime(company.lastInspectTime, (value)=>{company.lastInspectTime = value} )" />

            <!-- <u--input v-model="company.lastInspectTime" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请选择最新巡检时间'" border="none" clearable/>
            <u-icon name="arrow-down" v-if="!isReadOnly"></u-icon> -->
          </u-form-item>
          <u-form-item label="主营业务" prop="mainBusiness" required>
            <u-textarea v-model="company.mainBusiness" maxlength="500" count :disabled="isReadOnly" :custom-style="{ backgroundColor: 'rgba(0,0,0,0)' }" :placeholder="isReadOnly ? '' : '请输入主营业务'" border="none" ></u-textarea>
          </u-form-item>
          <u-form-item label="养殖畜种" prop="breedingLivestock" v-if="company.enterpriseNature === '养殖企业'">
            <u--input v-model="company.breedingLivestock" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入养殖畜种'" border="none" clearable />
          </u-form-item>
          <u-form-item label="是否投保安责险"  required v-if="!isReadOnly">
                  <u-radio-group
              v-model="hasSafetyInsurance"
              size="20"
              activeColor="#1BC78D"
            >
            <u-radio :name="'NO'" label="否"></u-radio>
            <u-radio :name="'YES'" label="是"></u-radio>
             
            </u-radio-group>
                </u-form-item>  
                <u-form-item label="安责险名称" v-if="hasSafetyInsurance === 'YES'"  prop="safetyInsurance" required>
                  <u--input v-model="company.safetyInsurance"
                  :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入安责险名称'" border="none" clearable
               />
            </u-form-item>
          <div class="collapse-title">企业证件</div>
          <u-form-item label="" prop="credentialList">
            <view class="certificates-list" style="width:100%">
              <view class="certificate-item" v-for="(item, index) in company.credentialList" :key="index">
                <u-form-item label="证件名称" :prop="'credentialList.' + index + '.credentialName'" required>
                  <u--input v-model="item.credentialName" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入证件名称'" border="none" clearable/>
                </u-form-item>
                <u-form-item label="证件开始日期" :prop="'credentialList.' + index + '.startDate'"  required>
                  <u--input v-model="item.startDate" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入日期，格式：yyyy-mm-dd'" border="none" clearable @blur="validateDate(item.startDate, (value)=>{company.credentialList[index].startDate = value} )" />
                </u-form-item>
                <u-form-item label="证件截止日期" :prop="'credentialList.' + index + '.endDate'"  required>
                  <u--input v-model="item.endDate" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入日期，格式：yyyy-mm-dd'" border="none" clearable @blur="validateDate(item.endDate, (value)=>{company.credentialList[index].endDate = value} )" />
                </u-form-item>
                <view class="certificate-images">
                  <view class="section-title">证件图片</view>
                  <view class="imgcell">
                    <template v-if="item.credentialImage">
                      <view class="imgitem" v-for="(img, imgIndex) in item.credentialImage.split(',')" :key="imgIndex">
                        <view class="img">
                          <image :src="img"></image>
                        </view>
                        <image class="closebtn" :src="CLOSE_BTN" @click="isReadOnly ? null : deleteCertificateImage(index, imgIndex)" v-if="!isReadOnly"></image>
                      </view>
                    </template>
                    <image :src="IMG_ADD" style="width: 80rpx; height: 80rpx" @click="isReadOnly ? null : uploadCertificateImage(index)" v-if="!isReadOnly"/>
                  </view>
                </view>
                <u-icon name="trash" class="trash" @click="isReadOnly ? null : removeCertificate(index)" size="24" v-if="!isReadOnly"></u-icon>
              </view>
              <u-button color="#0CBE88" style="width: 50%;" text="添加证件" @click="addCertificate" :customStyle="{marginTop: '20rpx'}" v-if="!isReadOnly"></u-button>
            </view>
          </u-form-item>
          <template v-if="company.enterpriseNature == '工贸企业'">
            <div class="collapse-title">设备与工艺</div>
            <u-form-item label="主要设备设施" prop="mainEquipment">
              <u--input v-model="company.mainEquipment" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入主要设备设施'" border="none" clearable />
            </u-form-item>
            <u-form-item label="涉及工艺" prop="involvedTechnology">
              <u--input v-model="company.involvedTechnology" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入设计工艺'" border="none" clearable />
            </u-form-item>
            <u-form-item label="主要原料" prop="mainMaterials">
              <u--input v-model="company.mainMaterials" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入主要原料'" border="none" clearable />
            </u-form-item>
            <u-form-item label="主要/副产品" prop="mainProducts">
              <u-textarea v-model="company.mainProducts" :disabled="isReadOnly" :placeholder="isReadOnly ? '' : '请输入主要/副产品'" border="none" ></u-textarea>
            </u-form-item>
          </template>
          <div class="collapse-title">厂房性质</div>
          <template v-if="company.enterpriseNature === '工贸企业'||company.enterpriseNature === '养殖企业'">
                <u-form-item label="土地规划" prop="landPlanning">
                  <u--input v-model="company.landPlanning" :readonly="isReadOnly"   :placeholder="isReadOnly ? '' : '请输入土地规划'" clearable border="none" />
                </u-form-item>
                <u-form-item label="厂房性质" prop="factoryNature" :required="company.enterpriseNature === '工贸企业'?true:false">
                  <u--input v-model="company.factoryNature" :readonly="isReadOnly"   :placeholder="isReadOnly ? '' : '请输入厂房性质'" border="none" clearable/>
                </u-form-item>
          <u-form-item label="厂房所有人" prop="factoryOwner">
            <u--input v-model="company.factoryOwner" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入厂房所有人'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="厂房电话" prop="factoryOwnerPhone">
            <u--input v-model="company.factoryOwnerPhone" maxlength="11" type="number" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入厂房电话'" border="none" clearable/>
          </u-form-item>
          <u-form-item label="持有形式" prop="holdingForm">
            <u--input v-model="company.holdingForm" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入持有形式'" border="none" clearable/>
          </u-form-item>
          </template>
          <template v-if="company.enterpriseNature === '个体工商户'">
                <u-form-item label="租赁/自有" prop="ownershipType" required>
                <u-radio-group v-if="!isReadOnly"
              v-model="company.ownershipType"
              size="20"
              activeColor="#1BC78D"
            >
            <u-radio :name="'租赁'" label="租赁"></u-radio>
            <u-radio :name="'自有'" label="自有"></u-radio>
             
            </u-radio-group>
            <u--input v-else v-model="company.ownershipType" :readonly="true"  border="none" />
            </u-form-item>
            <u-form-item label="房东" prop="landlord">
                  <u--input v-model="company.landlord" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入房东'" border="none"  clearable />
                </u-form-item>
                <u-form-item label="房东电话" prop="landlordPhone">
                  <u--input v-model="company.landlordPhone" :readonly="isReadOnly" maxlength="11" type="number" :placeholder="isReadOnly ? '' : '请输入房东电话'" border="none" clearable />
                </u-form-item>
              </template>
          <u-form-item label="建筑面积" prop="buildingArea">
            <u-input v-model="company.buildingArea" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入建筑面积'" border="none" clearable type="number">
              <template #suffix>
                <text>平方米</text>
              </template> 
            </u-input>
          </u-form-item>
          <div class="collapse-title">监管部门</div>
                  <u-form-item label="行业类别" prop="industryClassification" >
                  <u--input v-model="company.industryClassification" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入行业类别'" border="none"  clearable />
                </u-form-item>
                <u-form-item label="直接监管部门" prop="directSupervisionDept">
                  <u--input v-model="company.directSupervisionDept" :readonly="isReadOnly" :placeholder="isReadOnly ? '' : '请输入直接监管部门'" border="none" clearable />
                </u-form-item>
        </u--form>
      </scroll-view>
    </view>
    <template v-if="!isReadOnly">
      <view class="blank"></view>
      <view  class="bottom">
        <view class="btn" @click="reset" style="margin-right: 40rpx;">取消</view>
        <view class="btn pjBtn" @click="handleSubmit">保存</view>
      </view>
    </template>
    <u-tabbar :value="barActive" @change="handlerBarChange"  fixed safeAreaInsetBottom active-color="#0CBE88" inactive-color="#999999" v-if="isReadOnly&&!isadmin">
      <u-tabbar-item text="巡检打卡" icon="clock" />
      <!-- <u-tabbar-item text="整改复核" icon="checkmark-circle" /> -->
      <u-tabbar-item text="纠错" icon="info-circle" />
      <u-tabbar-item text="删除" icon="trash" />
    </u-tabbar>
    <u-tabbar :value="barActive" @change="handlerAdminBarChange" fixed safeAreaInsetBottom active-color="#0CBE88" inactive-color="#999999" v-if="isReadOnly&&isadmin">
      <u-tabbar-item text="打卡记录" icon="clock" />
      <u-tabbar-item text="整改记录" icon="checkmark-circle" />
      <u-tabbar-item text="编辑" icon="info-circle" />
      <u-tabbar-item text="删除" icon="trash" />
    </u-tabbar>
    <u-picker closeOnClickOverlay @close="showTypePicker = false" :show="showTypePicker" :columns="[typeList]" keyName="label" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
    <u-picker closeOnClickOverlay @close="showScalePicker = false" :show="showScalePicker" :columns="[company.enterpriseNature === '养殖企业'?scaleyzList:company.enterpriseNature === '工贸企业'?scalegmList:[]]" keyName="label" @confirm="onScaleConfirm" @cancel="showScalePicker = false" />
    <u-datetime-picker closeOnClickOverlay @close="showDatePicker = false" :minDate="mindate" :maxDate="maxdate" :show="showDatePicker" @confirm="onDateConfirm" format="YYYY-MM-DD" mode="date" @cancel="showDatePicker = false" />
    <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate" @close="showCertificateStartDatePicker = false" :show="showCertificateStartDatePicker" @confirm="onCertificateStartDateConfirm" format="YYYY-MM-DD" mode="date" @cancel="showCertificateStartDatePicker = false" />
    <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate" @close="showCertificateEndDatePicker = false" :show="showCertificateEndDatePicker" @confirm="onCertificateEndDateConfirm" format="YYYY-MM-DD" mode="date" @cancel="showCertificateEndDatePicker = false" />
    <u-picker closeOnClickOverlay @close="showCommunityPicker = false" :show="showCommunityPicker" :columns="[communities]" keyName="label" @confirm="onCommunityConfirm" @cancel="showCommunityPicker = false" />
    <u-popup :show="showIndustryCategoryPicker" mode="center" @close="cancelIndustryCategoryHandler" closeable :safeAreaInsetBottom="false" round="5">
      <view class="industry-category-popup">
        <view class="popup-header">
          <text class="popup-title">{{ company.enterpriseNature === '个体工商户' ? '选择风险关注点' : '选择涉及重点行业' }}</text>
        </view>
        <view class="popup-content">
          <scroll-view scroll-y="true" class="options-list">
            <view
              v-for="(item, index) in (company.enterpriseNature === '个体工商户' ? riskFocusPoints : industryCategories)"
              :key="index"
              class="option-item"
              @click="toggleIndustryCategorySelection(item)"
            >
              <view class="option-content">
                <text class="option-label">{{ item.label }}</text>
                <u-icon
                  :name="selectedIndustryCategories.some(selected => selected.value === item.value) ? 'checkbox-mark' : 'checkbox'"
                  :color="selectedIndustryCategories.some(selected => selected.value === item.value) ? '#0CBE88' : '#c8c9cc'"
                  size="20"
                ></u-icon>
              </view>
            </view>
          </scroll-view>
        </view>
        <view class="popup-footer">
          <view class="footer-buttons">
            <view class="btn-cancel" @click="cancelIndustryCategoryHandler">取消</view>
            <view class="btn-confirm" @click="confirmIndustryCategoryHandler">确定</view>
          </view>
        </view>
      </view>
    </u-popup>
    <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate" @close="showLastInspectTimePicker = false" :show="showLastInspectTimePicker" @confirm="onLastInspectTimeConfirm" format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showLastInspectTimePicker = false" />
    <u-picker closeOnClickOverlay @close="showBusinessTypePicker = false" :show="showBusinessTypePicker" :columns="[businessTypeList]" keyName="label" @confirm="onBusinessTypeConfirm" @cancel="showBusinessTypePicker = false" />
    <u-popup :show="showPop" mode="center" @close="cancelHandler" closeable :safeAreaInsetBottom="false" round="5">
      <view class="tktip">
        <view class="content">确定要删除该企业吗？</view>
        <view class="btngroup">
          <view class="cancel" @click="cancelHandler">取消</view>
          <view class="okbtn" @click="deleteHandler">确定</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
import { ref, computed,nextTick } from 'vue'
import { onLoad,onShow} from '@dcloudio/uni-app'
import { AdminService } from '../../api/anquanjianguan/adminstrator'
import { AJYService } from '../../api/anquanjianguan/companyList'
import {LIVE_INVENTORY_BG, IMG_ADD, CLOSE_BTN} from "@/common/net/staticUrl.js"
import {
        useUserStore
    } from '@/store/user.js'
    const userStore = useUserStore()
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import { getDicts } from '@/common/net/contacts/contacts.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const showPop = ref(false)
const showDatePicker = ref(false)
const showScalePicker = ref(false)
const showTypePicker = ref(false)
const showTrainDatePicker = ref(false)
const showCertificateStartDatePicker = ref(false)
const showCertificateEndDatePicker = ref(false)
const showCommunityPicker = ref(false)
const showIndustryCategoryPicker = ref(false)
const showLastInspectTimePicker = ref(false)
const showBusinessTypePicker = ref(false)
const company = ref({})
const communities = ref([])
const industryCategories = ref([])
const selectedIndustryCategories = ref([]) // 存储选中的行业分类
const riskFocusPoints = ref([
  { label: '“三合一”场所', value: '“三合一”场所' },
  { label: '人员密集', value: '人员密集' },
  { label: '电气焊', value: '电气焊' },
  { label: '电动车', value: '电动车' },
  { label: '燃气', value: '燃气' },
  { label: '消防', value: '消防' },
  { label: '用电', value: '用电' },
  { label: '其他', value: '其他' },
])
const businessTypeList = [
  { label: '小学或幼儿园', value: '小学或幼儿园' },
  { label: '小医院', value: '小医院' },
  { label: '小商店', value: '小商店' },
  { label: '小生产加工场所', value: '小生产加工场所' },
  { label: '小餐饮', value: '小餐饮' },
  { label: '小旅馆', value: '小旅馆' },
  { label: '小网吧', value: '小网吧' },
  { label: '小歌舞娱乐场所', value: '小歌舞娱乐场所' },
  { label: '小美容洗浴场所', value: '小美容洗浴场所' },
  { label: '其他', value: '其他' }
]
// 保存初始数据的副本
const originalCompanyData = ref({})
const tabActive = ref(0)
const scrollTop = ref(0)
const tabContentRef = ref(null)
const barActive = ref(-1)
const hasSafetyInsurance = ref('NO')
const mindate = ref(-2208988800000)
const maxdate = ref(new Date().getTime())
const formRef = ref(null)
const communityLabel = ref('')
const industryCategoryLabel = ref('')
const typeList = [
  { label: '个体工商户', value: '个体工商户' },
  { label: '工贸企业', value: '工贸企业' },
  { label: '养殖企业', value: '养殖企业' }
]

const scaleyzList = [
  { label: '养殖规模户', value: '养殖规模户' },
  { label: '养殖专业户', value: '养殖专业户' },

]
const scalegmList = [
  { label: '规上', value: '规上' },
  { label: '小微', value: '小微' }
]

const labelStyle = ref({ color: '#333', lineHeight: '50rpx' })
const isReadOnly = computed(() => type.value === 'info')
const type = ref('info')
const isdisabled = ref(false)
const oldScrollTop = ref(0)
        // scroll-view的滚动事件
       function listScroll(e) {
            oldScrollTop.value = e.detail.scrollTop
        }
        // 滚动到页面顶部
       function  scrollToTop() {
          scrollTop.value = oldScrollTop.value
            // 当调用此方法时，会先保存当前的滚动位置，并在下一个Vue的DOM更新周期开始时滚动到页面顶部
            nextTick(() => {
              scrollTop.value = 0
            })
        }

// 企业性质验证函数
const enterpriseNatureValidate = (rule, value, callback) => {
  if(!value || !value.length){
    callback(new Error('企业性质不能为空'))
  } else {
    callback()
  }
}
// 新增lastInspectTime的输入校验方法
const validateDateTime = (value, callback) => {
  if(isReadOnly.value||company.value.lastInspectTime){
    return
  }
  if (!value) return;
  // 先去除首尾空格，并将多个空格替换为一个空格
  value = value.trim().replace(/\s+/g, ' ');
  // 检查格式：YYYY-MM-DD HH:mm:ss
  const dateTimePattern = /^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
  const match = value.match(dateTimePattern);
  if (!match) {
    uni.showToast({
      title: '请输入正确的时间格式：yyyy-MM-dd HH:mm:ss',
      icon: 'none'
    });
    callback('');
    return;
  }
  let [_, year, month, day, hour, minute, second] = match;
  year = Number(year);
  month = Number(month);
  day = Number(day);
  hour = Number(hour);
  minute = Number(minute);
  second = Number(second);

  // 校验年月日
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
    callback('');
    return;
  }
  // 校验时分秒
  if (
    hour < 0 || hour > 23 ||
    minute < 0 || minute > 59 ||
    second < 0 || second > 59
  ) {
    uni.showToast({
      title: '请输入有效的时间（00-23:00-59:00-59）',
      icon: 'none'
    });
    callback('');
    return;
  }
  // 格式化
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  const formattedHour = hour.toString().padStart(2, '0');
  const formattedMinute = minute.toString().padStart(2, '0');
  const formattedSecond = second.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay} ${formattedHour}:${formattedMinute}:${formattedSecond}`);
}
const validateDate = (value, callback) => {
  if(isReadOnly.value){
    return
  }
  if (!value) return;
  // 检查日期格式是否正确
  const datePattern = /^\d{4}-\d{1,2}-\d{1,2}$/;
  if (!datePattern.test(value)) {
    uni.showToast({
      title: '请输入正确的日期格式：yyyy-mm-dd',
      icon: 'none'
    });
    callback('')
    
    return;
  }
  
  // 分割日期
  const [year, month, day] = value.split('-').map(Number);
  
  // 验证日期是否有效
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
   callback('')
    return;
  }
  
  // 格式化日期，确保月份和日期是两位数
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay}`)
}
// 证件验证函数
const certificatesValidate = (rule, value, callback) => {
  if (!value || value.length === 0) {
    callback()
    return
  }

  for (let i = 0; i < value.length; i++) {
    const cert = value[i]
    if (!cert.credentialName || cert.credentialName.trim() === '') {
      callback(new Error(`第${i + 1}个证件名称不能为空`))
      return
    }
    if (!cert.startDate || cert.startDate.trim() === '') {
      callback(new Error(`第${i + 1}个证件开始日期不能为空`))
      return
    }
    if (!cert.endDate || cert.endDate.trim() === '') {
      callback(new Error(`第${i + 1}个证件截止日期不能为空`))
      return
    }
    if (cert.endDate < cert.startDate) {
      callback(new Error(`第${i + 1}个证件的截止日期不能小于开始日期`))
      return
    }
  }
  callback()
}

// 培训记录验证函数
const trainingListValidate = (rule, value, callback) => {
  if (!value || value.length === 0) {
    callback()
    return
  }

  for (let i = 0; i < value.length; i++) {
    const training = value[i]
    if (!training.trainingTitle || training.trainingTitle.trim() === '') {
      callback(new Error(`第${i + 1}个培训标题不能为空`))
      return
    }
    if (!training.trainingTrainingTime || training.trainingTrainingTime.trim() === '') {
      callback(new Error(`第${i + 1}个培训时间不能为空`))
      return
    }
  }
  callback()
}
const industryCategoryValidate = (rule,value,callback)=>{
  if(!company.value.enterpriseNature){
    callback()
    return;
  }
  if(company.value.enterpriseNature=='个体工商户'){
    if(company.value.industryCategory.trim()==''){
      callback('请选择风险关注点')
      return;
    }
   callback()
  }
  else{
    if(company.value.industryCategory.trim()==''){
      callback('请选择涉及重点行业')
      return;
    }
    callback()
  }
}
const factoryNatureValidate = (rule, value, callback) => {
  console.log(company.value.enterpriseNature,company.value.factoryNature,'------------------------------')
  if(company.value.enterpriseNature === '工贸企业'){
    if(company.value.factoryNature.trim()==''){
      callback('请输入厂房性质')
      return;
    }
  }
  callback()
}
const employeeCountTempValidate = (rule, value, callback) => {
  if(company.value.enterpriseNature === '养殖企业'){
    if(company.value.employeeCountTemp.trim()==''||!company.value.employeeCountTemp){
      callback('请输入临时用工人数')
      return;
    }
  }
  callback()
}
const enterpriseScaleValidate = (rule, value, callback) => {
  if(company.value.enterpriseNature === '养殖企业'||company.value.enterpriseNature === '工贸企业'){
    if(company.value.enterpriseScale.trim()==''){
      callback('请输入企业规模')
      return;
    }
  }
  callback()
}
const validateinspectInterval = (rule, value, callback) => { 
  if(isReadOnly.value){
    return
  }
  console.log(company.value.inspectInterval,isadmin.value)
  if(!isadmin.value){
    callback()
    return
  }
  if (company.value.inspectInterval.trim() == '') {
    callback('请输入巡检周期')
    return;
  }
  callback()
}
const rules = ref({
  enterpriseName: [{ required: true, message: '企业名称不能为空', trigger: ['blur', 'change'] }],
  creditCode: [{ required: true, message: '信用代码不能为空', trigger: ['blur', 'change'] }],
  enterpriseNature: [{ required: true, message: '生产经营单位类型不能为空', trigger: ['blur', 'change'] }],
  industryCategory: [{  validator: industryCategoryValidate, trigger: ['blur', 'change'] }],
  factoryNature: [{validator: factoryNatureValidate, trigger: ['blur', 'change'] }],
  ownershipType: [{ required: true, message: '租赁/自有不能为空', trigger: ['blur', 'change'] }],
  businessType: [{ required: true, message: '经营类型不能为空', trigger: ['blur', 'change'] }],
 annualRevenue: [{ type:'number',required: true, message: '年营业额不能为空', trigger: ['blur', 'change'] }],
  enterpriseScale: [{validator:enterpriseScaleValidate, trigger: ['blur', 'change'] }],
  businessAddress:[{ required: true, message: '经营地址不能为空', trigger: ['blur', 'change'] }],
inspectInterval:[{type:'number',validator: validateinspectInterval, trigger: ['blur', 'change'] }],
  establishDate: [{ required: true, message: '成立日期不能为空', trigger: ['blur', 'change'] }],
  lastInspectTime:[{ required: true, message: '最新巡检时间不能为空', trigger: ['blur', 'change'] }],
  mainBusiness: [{ required: true, message: '主营业务不能为空', trigger: ['blur', 'change'] }],
  employeeCount: [{ type:'number',required: true, message: '从业人员不能为空', trigger: ['blur', 'change'] }],
   employeeCountTemp: [{type:'number', validator: employeeCountTempValidate, trigger: ['blur', 'change'] }],
   legalRepresentative: [{ required: true, message: '法定代表人不能为空', trigger: ['blur', 'change'] }],
  legalRepresentativePhone: [{
				type: 'number',
				required: true, 
				message: '手机号码不能为空',
				trigger: ['blur', 'change'],
			},
			{
				validator: (rule, value, callback) => {
				    return uni.$u.test.mobile(value);
				},
				message: '手机号码格式不正确',
				// 触发器可以同时用blur和change
				trigger: ['change', 'blur'],
			}],
  actualController: [{ required: true, message: '常用联系人不能为空', trigger: ['blur', 'change'] }],
  actualControllerPhone: [{
				type: 'number',
				required: true,
				message: '手机号码不能为空',
				trigger: ['blur', 'change'],
			},
			{
				validator: (rule, value, callback) => {
				    return uni.$u.test.mobile(value);
				},
				message: '手机号码格式不正确',
				// 触发器可以同时用blur和change
				trigger: ['change', 'blur'],
			}],
      communityValue: [{ required: true, message: '所属社区不能为空', trigger: ['blur', 'change'] }],
      safetyInsurance: [{ required: true, message: '安责险名称不能为空', trigger: ['blur', 'change'] }],
      credentialList: [{ validator: certificatesValidate, trigger: ['blur', 'change'] }]
})
const isadmin = ref(false)
onLoad(async (options) => {
  const id = options.id || ''
  isadmin.value = userStore.userInfo.authorityList.find(item=>item.roleId=='town_admin')

  // 加载社区数据
   let response = await getDicts('communities')
    let list = response.data
    list.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    communities.value = list.map(item=>({...item,label:item.dictLabel}))

let industryCategoriesresponse = await getDicts('industryCategories')
    let industryCategorieslist = industryCategoriesresponse.data
    industryCategorieslist.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    industryCategories.value = industryCategorieslist.map(item=>({...item,label:item.dictLabel}))
  
// let riskFocusPointsresponse = await getDicts('riskFocusPoints')
 //   let riskFocusPointslist = riskFocusPointsresponse.data
//    riskFocusPointslist.forEach((item) => {
//      item.label = item.dictLabel
//      item.value = item.dictValue
 //   })
 //   riskFocusPoints.value = riskFocusPointslist.map(item=>({...item,label:item.dictLabel})) -->

  // let businessTyperesponse = await getDicts('businessType')
  //   let businessTypelist = businessTyperesponse.data
  //   businessTypelist.forEach((item) => {
  //     item.label = item.dictLabel
  //     item.value = item.dictValue
  //   })
  //   businessTypeList.value = businessTypelist.map(item=>({...item,label:item.dictLabel}))

  AdminService.findCompanyById(id).then(res => {
    if (res.success) {
      company.value = res.data
      // 确保证件数组存在
      if (!company.value.credentialList) {
        company.value.credentialList = []
      }
      // 确保培训记录数组存在
      // if (!company.value.trainingList) {
      //   company.value.trainingList = []
      // }
      // 确保新增字段存在
      if (!company.value.communityValue) {
        company.value.communityValue = ''
        communityLabel.value = ''
      }
      else{
        communityLabel.value = communities.value.find(item=>item.dictValue==company.value.communityValue).dictLabel
      }
      if (!company.value.safetyInsurance) {
        hasSafetyInsurance.value = 'NO'
      }else{
        hasSafetyInsurance.value = 'YES'
      }
      if (!company.value.industryCategory) {
        company.value.industryCategory = ''
        industryCategoryLabel.value = ''
      }
      else{
        // 支持多选数据的解析
        const selectedValues = company.value.industryCategory.split(',')
        const availableOptions = company.value.enterpriseNature === '个体工商户' ? riskFocusPoints : industryCategories.value

        // 初始化选中的项目
        selectedIndustryCategories.value = availableOptions.filter(item =>
          selectedValues.includes(item.value)
        )

        // 设置显示标签
        if (selectedIndustryCategories.value.length > 0) {
          industryCategoryLabel.value = selectedIndustryCategories.value.map(item => item.label).join(',')
        } else {
          // 如果没有找到匹配的项目，清空数据
          company.value.industryCategory = ''
          industryCategoryLabel.value = ''
        }
      }
      if (!company.value.lastInspectTime||company.value.lastInspectTime==null) {
        company.value.lastInspectTime = ''
      }
      if (!company.value.longitude) {
        company.value.longitude = ''
      }
      if (!company.value.latitude) {
        company.value.latitude = ''
      }
      // 保存初始数据的深拷贝
      originalCompanyData.value = JSON.parse(JSON.stringify(res.data))
      console.log('初始企业数据已保存:', originalCompanyData.value)
    }
  })
})
function uploadyyzzhandler(){
  uni.chooseImage({
    count: 1,
    extension: ['.jpg', '.png'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera','album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      const imageBase64 = await onChange(tempFilePaths[0])
      try{
      const response = await AJYService.uploadYYZZ({
        imageBase64
      })
      if(response.success){
        const data = response.data;
      // 遍历data对象，将属性值不为null或""的属性赋值给form对应的属性
      Object.keys(data).forEach(key => {
        if (data[key] !== null && data[key] !== ""&&key!='businessAddress'&&typeof company.value[key] !== 'undefined') {
          company.value[key] = data[key]
        }
      })
      }
      else{
        uni.showToast({
          title: response.message,
          icon: 'none'
        })
      }
    }catch(e){
      uni.showToast({
        title: 'ocr识别失败',
        icon: 'none'
      })
    }
      uni.hideLoading()
    }
  })

}
function onChange(newValue){
     return new Promise((resolve,reject)=>{
                var xhr = new XMLHttpRequest()
                xhr.open('GET', newValue, true)
                xhr.responseType = 'blob'
                xhr.onload = function() {
                    if (this.status === 200) {
                        let fileReader = new FileReader()
                        fileReader.onload  = function(e) {
　　　　　　　　　　　　　　　　const base64= e.target.result
                             resolve(base64)
　　　　　　　　　　　　　　}
                        fileReader.onerror = function(err){
                            console.log(err);
                        }
                        fileReader.readAsDataURL(this.response)
                    }
                }
                xhr.onerror = function(e){
                    console.log(e);
                }
                xhr.send()
     })
}
function handleTabClick(item) {
  tabActive.value = item.index
  
  // 滚动到顶部
  nextTick(() => {
    if (tabContentRef.value) {
scrollToTop()
    }


  })
}
function reset(refresh=false){
  type.value = 'info';
  barActive.value = -1
 if(refresh){
     AdminService.findCompanyById(company.value.enterpriseId).then(res => {
          if (res.success) {
            company.value = res.data
            originalCompanyData.value = JSON.parse(JSON.stringify(res.data))
          }
        })
        return
 }
  // 恢复初始数据
  if (originalCompanyData.value && Object.keys(originalCompanyData.value).length > 0) {
    console.log('正在恢复初始企业数据:', originalCompanyData.value)
    try {
      company.value = JSON.parse(JSON.stringify(originalCompanyData.value))
      console.log('企业数据恢复成功')
    } catch (error) {
      console.error('企业数据恢复失败:', error)
      // 如果恢复失败，重新从服务器获取数据
      const id = company.value.enterpriseId
      if (id) {
        AdminService.findCompanyById(id).then(res => {
          if (res.success) {
            company.value = res.data
            originalCompanyData.value = JSON.parse(JSON.stringify(res.data))
          }
        })
      }
    }
  } else {
    console.warn('没有找到初始企业数据')
  }
}
function onDateConfirm(e) {
  company.value.establishDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showDatePicker.value = false
}
function onTypeConfirm(e) {
  company.value.enterpriseNature = e.value[0].label
  showTypePicker.value = false
  formRef.value.clearValidate()

}
function onScaleConfirm(e) {
  company.value.enterpriseScale = e.value[0].label
  showScalePicker.value = false
}
function onBusinessTypeConfirm(e) {
  company.value.businessType = e.value[0].label
  showBusinessTypePicker.value = false
}
function handleSubmit() {
  formRef.value.validate().then(valid => {
    if (valid) {
      if (isdisabled.value) return;
      isdisabled.value = true
      const {
        tenantId,
        createBy,
        createDate,
        updateBy,
        updateDate,
        versions,
        communityName,
        deleteFlag,
        ...formParams
      } = company.value;
      AdminService.updateCompanyDetail(formParams).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title: isadmin.value?'已修改':'已提交修改,待审核！', icon: 'none' })
        //  if(isadmin.value){
        //   reset(true)
        //  }
        //  else{
          setTimeout(()=>{
            uni.navigateBack()
          },1000)
          
        // }
   
        } else {
          uni.showToast({ title: res.message, icon: 'none' })
           reset()
        }
        
      }).catch(e=>{
        isdisabled.value = false
        uni.showToast({
          title: '修改失败！',
          icon: 'none',
        })
      })
    }
  })
}

function cancelHandler() {
		showPop.value = false
    reset()
	}



	async function deleteHandler() {
		if (isdisabled.value) return;
		isdisabled.value = true
		try {
			let res = await AdminService.deleteCompany(company.value.enterpriseId)
			isdisabled.value = false
			if (res.success) {
        showPop.value = false
			setTimeout(() => { 
        uni.showToast({

					title: isadmin.value?'已删除':'已提交删除，待审核！',
					icon: 'none',
				})
        uni.navigateBack()
			}, 1000)
			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} catch (e) {
			isdisabled.value = false
      uni.showToast({
					title: '删除失败！',
					icon: 'none',
				})
		}
	}
  const handlerAdminBarChange = (index) => {
  barActive.value = index
  if(index === 0) {
    reset()
    uni.navigateTo({ url: '/jimo/anquanjianguan/adminstrator/workList?tab=0&id='+ company.value.enterpriseId })
  }
  if(index === 1) {
    reset()
    uni.navigateTo({ url: '/jimo/anquanjianguan/adminstrator/workList?tab=1&id='+ company.value.enterpriseId })
  }
  if(index === 2) {
    type.value = 'edit'
    formRef.value.setRules(rules)

     }
  if(index === 3) {
    showPop.value = true

  } 
}
const handlerBarChange = (index) => {
  barActive.value = index
  if(index === 0) {
    reset()
    uni.navigateTo({ url: './companyCheck?id='+ company.value.enterpriseId})
  }
  // if(index === 1) {
  //   reset()
  //   uni.navigateTo({ url: './companyChange?id='+ company.value.enterpriseId })
  // }
  if(index === 1) {
    type.value = 'edit'
    formRef.value.setRules(rules)

     }
  if(index === 2) {
    showPop.value = true

  } 
}

// 证件相关函数
function addCertificate() {
  if (!company.value.credentialList) {
    company.value.credentialList = []
  }
  company.value.credentialList.push({
    credentialName: '',
    startDate: '',
    endDate: '',
    credentialImage: ''
  })
}

function removeCertificate(index) {
  company.value.credentialList.splice(index, 1)
}

// 当前选中的证件索引，用于日期选择
const currentCertificateIndex = ref(0)

function showCertificateStartDatePickerHandler(index) {
  currentCertificateIndex.value = index
  showCertificateStartDatePicker.value = true
}

function showCertificateEndDatePickerHandler(index) {
  currentCertificateIndex.value = index
  showCertificateEndDatePicker.value = true
}

function onCertificateStartDateConfirm(e) {
  company.value.credentialList[currentCertificateIndex.value].startDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showCertificateStartDatePicker.value = false
}

function onCertificateEndDateConfirm(e) {
  company.value.credentialList[currentCertificateIndex.value].endDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showCertificateEndDatePicker.value = false
}

function uploadCertificateImage(certificateIndex) {
  uni.chooseImage({
    count: 9 - company.value.credentialList[certificateIndex].credentialImage?company.value.credentialList[certificateIndex].credentialImage.split(',').length:0,
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera','album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = company.value.credentialList[certificateIndex].credentialImage?company.value.credentialList[certificateIndex].credentialImage.split(','):[]
      company.value.credentialList[certificateIndex].credentialImage = [...arr, ...list].join(',')
      console.log(company.value.credentialList[certificateIndex].credentialImage,'0000000000000000000000000000')
      uni.hideLoading()
    }
  })
}

function deleteCertificateImage(certificateIndex, imageIndex) {
let arr = company.value.credentialList[certificateIndex].credentialImage.split(',')
   arr.splice(imageIndex, 1).join(',')
   company.value.credentialList[certificateIndex].credentialImage = arr.length?arr.join(','):''
 }
// 培训相关函数
function addTraining() {
  if (!company.value.trainingList) {
    company.value.trainingList = []
  }
  company.value.trainingList.push({
    trainingTitle: '',
    trainingContent: '',
    trainingTrainingTime: '',
    imageUrl: ''
  })
}

function removeTraining(index) {
 company.value.trainingList.splice(index, 1)
}

function uploadTrainingImage(trainingIndex) {
  uni.chooseImage({
    count: 9 - company.value.trainingList[trainingIndex].imageUrl?company.value.trainingList[trainingIndex].imageUrl.split(',').length:0,
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera','album'],
    success: async (res) => {
        const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = company.value.trainingList[trainingIndex].imageUrl?company.value.trainingList[trainingIndex].imageUrl.split(','):[]
      company.value.trainingList[trainingIndex].imageUrl = [...arr, ...list].join(',')
      console.log(company.value.trainingList[trainingIndex].imageUrl,'培训图片上传结果')
      uni.hideLoading()
    }
  })
}
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url

          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) {}
    }
    resolve(filelist)
  })
}
function deleteTrainingImage(trainingIndex, imageIndex) {
  let arr = company.value.trainingList[trainingIndex].imageUrl.split(',')
  arr.splice(imageIndex, 1).join(',')
  company.value.trainingList[trainingIndex].imageUrl = arr.length?arr.join(','):''
}

// 当前选中的培训记录索引，用于日期选择
const currentTrainingIndex = ref(0)

function showTrainingDatePicker(index) {
  currentTrainingIndex.value = index
  showTrainDatePicker.value = true
}

function onTrainingDateConfirm(e) {
  company.value.trainingList[currentTrainingIndex.value].trainingTrainingTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showTrainDatePicker.value = false
}
// 社区选择相关函数
function onCommunityConfirm(e) {
  communityLabel.value = e.value[0].label
  company.value.communityValue = e.value[0].value
  showCommunityPicker.value = false
}
function onIndustryCategoryConfirm(e) {
  industryCategoryLabel.value = e.value[0].label
  company.value.industryCategory = e.value[0].value
  showIndustryCategoryPicker.value = false
}

// 新增多选相关方法
function openIndustryCategoryPicker() {
  // 初始化已选择的项目
  if (company.value.industryCategory) {
    const selectedValues = company.value.industryCategory.split(',')
    const availableOptions = company.value.enterpriseNature === '个体工商户' ? riskFocusPoints : industryCategories.value
    selectedIndustryCategories.value = availableOptions.filter(item =>
      selectedValues.includes(item.value)
    )
  } else {
    selectedIndustryCategories.value = []
  }
  showIndustryCategoryPicker.value = true
}

function toggleIndustryCategorySelection(item) {
  const index = selectedIndustryCategories.value.findIndex(selected => selected.value === item.value)
  if (index > -1) {
    // 如果已选中，则取消选择
    selectedIndustryCategories.value.splice(index, 1)
  } else {
    // 如果未选中，则添加到选择列表
    selectedIndustryCategories.value.push(item)
  }
}

function confirmIndustryCategoryHandler() {
  if (selectedIndustryCategories.value.length === 0) {
    uni.showToast({
      title: company.value.enterpriseNature === '个体工商户' ? '请选择风险关注点' : '请选择涉及重点行业',
      icon: 'none'
    })
    return
  }

  // 将选中的值用逗号分隔
  company.value.industryCategory = selectedIndustryCategories.value.map(item => item.value).join(',')
  industryCategoryLabel.value = selectedIndustryCategories.value.map(item => item.label).join(',')

  showIndustryCategoryPicker.value = false
}

function cancelIndustryCategoryHandler() {
  showIndustryCategoryPicker.value = false
}

// 最新巡检时间选择函数
function onLastInspectTimeConfirm(e) {
  company.value.lastInspectTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showLastInspectTimePicker.value = false
}
function getAddress(longs,lat){
uni.request({
			method: 'GET',
			url: 'https://restapi.amap.com/v3/geocode/regeo?parameters',
			data: {
				key: '2273413e2dc369ea8ad9b72b58ffdfcc',
				location: `${longs},${lat}`,
				output: 'JSON'
			},
			success: async (res) => {
				console.log('用户所在的地理位置信息',res);
				//用户所在的地理位置信息
        company.value.businessAddress = res.data.regeocode.formatted_address
			},
			fail: r => {
				console.log(r);
			}
		});
  }
// 地图选择地址函数
function getLocation() {
  uni.chooseLocation({
    latitude: company.value.latitude || 36.0671, // 默认纬度
    longitude: company.value.longitude || 120.3826, // 默认经度
    success(res) {
      console.log(res)
      
       // company.value.businessAddress = res.address + res.name
        company.value.latitude = res.latitude
        company.value.longitude = res.longitude
        getAddress(res.longitude,res.latitude)
    },
    fail(err) {
      console.log('选择地址失败:', err)
    }
  })
}

// 获取用户当前位置
function getUserLocation() {
  uni.getLocation({
    type: "gcj02",
    success(res) {
      console.log('获取位置成功:', res)
      if (!company.value.longitude) {
        company.value.longitude = res.longitude
      }
      if (!company.value.latitude) {
        company.value.latitude = res.latitude
      }
    },
    fail(err) {
      console.log('获取位置失败:', err)
    }
  })
}
</script>

<style lang="scss" scoped>
.address-row {
  width: 100%;
  display: flex;
  align-items: center;
 
}
.company-detail {
  width: 100%;
  min-height: 100vh;
  background: #F0F7F7;
  ::v-deep .tktip {
		padding: 80rpx 60rpx 60rpx 60rpx;
		width: 650rpx;
		box-sizing: border-box;

		.content {
			font-size: 32rpx;

			color: #111111;
			line-height: 55rpx;
			margin-bottom: 20rpx;
		}

		.btngroup {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.cancel {
				width: 240rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background: #ffffff;
				border-radius: 41rpx;
				border: 2rpx solid #e5e5e5;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #555555;
			}

			.okbtn {
				width: 240rpx;
				height: 80rpx;
				background: #0cbe88;
				border-radius: 41rpx;
				line-height: 80rpx;
				text-align: center;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
			}
		}
	}
  .container {
      overflow: scroll;
    height: calc(100vh - 200rpx);
    box-sizing: border-box;
      padding: 20rpx;
  .tab-content {
    background: linear-gradient(180deg, #FFFFFF 0%, #F5F6F6 100%);
    border-radius: 50rpx 50rpx 0 0;
    padding: 20rpx 40rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
    height: calc(100vh - 204rpx);
    box-sizing: border-box;
    overflow: auto;
      ::v-deep .u-form-item__body__left__content__required {
        position: inherit;
        left: auto;
      }
  }
  ::v-deep .u-tabs {
    padding: 0 25rpx;
    .u-tabs__wrapper__nav__line {
      height: 8rpx !important;
      background: linear-gradient(117deg, #0CBE88 0%, #3ADB97 100%) !important;
      border-radius: 5rpx !important;
    }
  }
  ::v-deep .u-radio {
        margin-right: 20rpx;
      }
}
.imgcell {
  padding: 20rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .imgitem {
    width: 233rpx;
    height: 179rpx;
    border: 0.5px solid #e2e2e2;
    margin-right: 40rpx;
    margin-bottom: 20rpx;
    position: relative;

    image {
      margin: 7rpx;
      width: 219rpx;
      height: 164rpx;
    }

    .closebtn {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: -16rpx;
      right: -16rpx;
    }
  }
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin: 30rpx 0 20rpx 0;
}
.risk-section {
  margin-bottom: 36rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 18rpx 18rpx 0 18rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.risk-section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #0CBE88;
  margin-bottom: 18rpx;
  letter-spacing: 2rpx;
}
::v-deep .u-form-item__body__right__content__slot {
  padding: 0 10rpx;
  .form-label {
    min-width: 200rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 26rpx;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 10rpx;
    padding: 0 5rpx;
    text {
      font-weight: 400;
      font-size: 24rpx;
      color: rgba(153, 153, 153, 1);
    }
  }
  .form-body {
    display: flex;
    .u-input {
      height: 80rpx;
      border: none;
      padding: 0 !important;
    }
    .divider {
      width: 37rpx;
      height: 52rpx;
      border-bottom: 1rpx solid #979797;
      margin-right: 44rpx;
    }
  }
}
.address-row {
  display: flex;
  width: 100%;
  align-items: center;
  .u-icon {
    margin-left: 10rpx;
  }
}
  // .container {
  //   padding:25rpx 22rpx;
  //   box-sizing: border-box;
  //   .content {
  //     overflow: scroll;
  //   height: calc(100vh - 260rpx);
  //   box-sizing: border-box;
  //     padding: 34rpx 24rpx 53rpx 28rpx;
  //     background: #FFFFFF;
  //     border-radius: 20rpx;
  //     box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
  //     ::v-deep .uni-input-wrapper{
  //       text-align: right;
  //     }
  //     ::v-deep .u-input{
  //      margin-right: 40rpx;
  //     }
  //   }
  // }
  .paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.blank {
  width: 100%;
  height: 120rpx;
}
  .bottom {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    padding: 20rpx 32rpx;
    z-index: 100;
    box-sizing: border-box;
    .btn {
      line-height: 80rpx;
      border-radius: 48rpx;
      flex: 1;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 36rpx;
      background-color: #999;
      color: #fff;
    }
    .pjBtn {
      background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
      
    }
  }
}
.yyzz{
  margin: 10rpx 0;
  width: 100%;
  border-radius: 16rpx;
  height: 300rpx;
}
.collapse-title {
  position: relative;
  font-size: 34rpx;
  font-weight: 700;
  color: #0CBE88;
  letter-spacing: 1rpx;
  padding: 20rpx;
  text-align: left;
  margin: 20rpx 0;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 36rpx;
    background: #0CBE88;
    border-radius: 3rpx;
  }
  
  &::after {
    content: '';
    position: absolute;
    left: 16rpx;
    right: 0;
    bottom: 0;
    height: 1px;
    background: linear-gradient(90deg, #0CBE88 0%, transparent 100%);
  }
}

// 证件相关样式
.certificates-list {
  .certificate-item {
    position: relative;
    margin-bottom: 32rpx;
    padding: 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
    border: 1rpx solid rgba(12, 190, 136, 0.1);

    .trash {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
      color: #e74c3c;
    }

    .certificate-images {
      margin-top: 16rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 12rpx;
      }

      .imgcell {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .imgitem {
          position: relative;

          .img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            overflow: hidden;

            image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .closebtn {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }
  }
}

// 培训相关样式
.trainings-list {
  .training-item {
    position: relative;
    margin-bottom: 32rpx;
    padding: 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16rpx;
    border: 1rpx solid rgba(12, 190, 136, 0.1);

    .trash {
      position: absolute;
      top: 16rpx;
      right: 16rpx;
      color: #e74c3c;
    }

    .training-images {
      margin-top: 16rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 12rpx;
      }

      .imgcell {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .imgitem {
          position: relative;

          .img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            overflow: hidden;

            image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .closebtn {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }
  }
}

.industry-category-popup {
  width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  .popup-header {
    padding: 30rpx 40rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
    text-align: center;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .popup-content {
    max-height: 60vh;

    .options-list {
      max-height: 60vh;
      padding: 20rpx 0;

      .option-item {
        padding: 0 40rpx;

        .option-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx 0;
          border-bottom: 1rpx solid #f5f5f5;

          .option-label {
            font-size: 28rpx;
            color: #333;
            flex: 1;
          }
        }

        &:last-child .option-content {
          border-bottom: none;
        }
      }
    }
  }

  .popup-footer {
    padding: 20rpx 40rpx 30rpx;
    border-top: 1rpx solid #f0f0f0;

    .footer-buttons {
      display: flex;
      gap: 20rpx;

      .btn-cancel,
      .btn-confirm {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        border-radius: 40rpx;
        font-size: 28rpx;
      }

      .btn-cancel {
        background: #f5f5f5;
        color: #666;
      }

      .btn-confirm {
        background: #0CBE88;
        color: #fff;
      }
    }
  }
}
</style>