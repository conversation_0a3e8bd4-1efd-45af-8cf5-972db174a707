<template>
	<view class="main-container">
		<!-- 搜索栏 -->
		<u-navbar title="选择乡村" :autoBack="true" titleStyle="font-size: 36rpx;" :placeholder="true"></u-navbar>
		<u-search placeholder="请输入乡村名称搜索" v-model="keyword" bg-color="#ffffff" borderColor="#DDDFE0"
			:show-action="false" height="78rpx" margin="24rpx 0" :clearabled="false" @search="searchVillage"
			@change="changeKeyword"></u-search>
		<!-- 行政区划级别 -->
		<view class="orgs" v-if="!isSearched">
			<view v-for="(item,index) in areaList" :key="index" class="org-item" @click="changeOrgLev(index)"
				:class="index == currentLevel ? 'active' : ''">{{item}}</view>
			<view class="current-city" @click="selectCity">
				<up-icon name="map" color="#0cbe88"></up-icon>
				<view class="label">
					定位本市
				</view>
			</view>
		</view>
		<!-- 行政区划列表 -->
		<scroll-view scroll-y="true" class="list-box" :scroll-top="scrollTop" @scroll="scroll"
			v-if="currentList.length > 0">
			<view v-for="(item,index) in currentList" :key="index" class="content">
				<view class="list-item" @click="selectVillage(item)"
					v-html="brightenKeyword(item.displayName,editKeyword) || item.regionName || item.tenantName">
				</view>
				<view class="item-icon" @click="viewNextOrg(item)">
					<up-icon v-if="currentLevel != '2'" name="arrow-right" color="#000" size="20"></up-icon>
				</view>
			</view>
		</scroll-view>
		<scroll-view scroll-y="true" class="list-box" v-else>
			<u-empty mode="search" text="暂无数据"></u-empty>
		</scroll-view>
	</view>
</template>

<script setup>
	import { getRegionInfo, getVillageInfo } from '../../api/villageCodeTour/chooseVillage.js'
	import { ref, reactive, nextTick, computed } from 'vue'
	import { onShow, onLoad, onReachBottom } from '@dcloudio/uni-app'

	import { findRegionInfo } from  '../../api/villageCodeTour/villageCodeTour.js'
	
	onLoad((options) => {
		// 页面加载时获取当前市的数据
		cityRegion.value = options.regionCode
		const params = ref({
			regionCode: options.regionCode,
			queryType: 'down'
		})
		getRegionList(params.value, "county", true);
	})
	const cityRegion = ref('')
	const scrollTop = ref(0); // 滚动条距离顶部的距离，重置为0可使滚动区域返回顶部
	const oldScrollTop = ref(0); // 记录滚动位置
	const keyword = ref(''); // 搜索关键字
	const editKeyword = ref('');
	const areaList = ref(["区县"]); // tags数组，依次是省-市-区-街道-村镇
	const defaultOrgName = ref(["区县", "乡/镇", "村"]);
	const currentLevel = ref(0); // 当前选中的哪个级别
	const currentLevelKey = ref(["county", "street", "village"]);
	const allData = reactive({ // 存储各个级别的列表数据
		county: [],
		street: [],
		village: []
	});
	const currentList = ref([]); // 当前展示的列表
	const currentOrg = ref([]); // 当前已选择的行政区划,，依次是区-街道-村镇
	const isSearched = ref(false);



	function scroll(e) {
		//记录scroll  位置
		oldScrollTop.value = e.detail.scrollTop
	}

	// 查看子级行政区划
	async function viewNextOrg(village) {
		// 如果点击的是已经选过的级别，则把子集清空
		// 例如已经选完山西省-太原市-小店区，这时再点击回选择省的列表，切换省，则后面市县区都要重置
		let childLevel = currentLevel.value + 1;
		if (!!areaList.value[childLevel]) {
			areaList.value = areaList.value.slice(0, childLevel);
			currentOrg.value = currentOrg.value.slice(0, childLevel);
		}
		// 如果不是最后一级，则更改行政区划级别tabs的名称
		// 例：页面初始化后，行政区划级别默认只展示“省份”，选择一个省份之后，“省份”会被替换为当前选中的省：
		// this.$set(this.areaList, this.currentLevel, e.regionName);
		areaList.value[currentLevel.value] = village.regionName
		// 保存当前选中的行政区划数据
		// this.$set(this.currentOrg, this.currentLevel, e);
		currentOrg.value[currentLevel.value] = village;
		// currentLevel代表当前要选择的行政区划级别，当前选中了之后就该选下一个级别了，所以级别要+1
		// 因为最多只有5级，最后一级没有子集了所以最大就到3
		if (currentLevel.value < 2) {
			currentLevel.value++;
		}
		// 当前选中后，要展示的下一级的数据
		// 例：页面初始化后，默认展示省份列表currentList为allData.province，currentLevel为0，
		// 选中一个省后该选择市的数据了，currentLevel++后为1，currentList应该为allData.city
		// 这里的key就是为了知道接口返回的数据是哪个级别的
		let key = currentLevelKey.value[currentLevel.value];
		uni.showLoading({
			title: "加载中..."
		})
		if (village.regionLevel == "5") {
			areaList.value.push(defaultOrgName.value[currentLevel.value])
			getVillageList(village.regionCode, key)
		} else {
			areaList.value.push(defaultOrgName.value[currentLevel.value])
			let params = {
				regionCode: village.regionCode,
				queryType: 'down'
			}
			getRegionList(params, key, true)
		}
		//视图会发生重新渲染
		scrollTop.value = oldScrollTop.value;
		//当视图渲染结束 重新设置为0,返回滚动区域顶部
		nextTick(() => {
			scrollTop.value = 0;
		});
	}
	// 获取区域数据 params:接口参数 key:行政区划级别 show:是否展示，自动定位时获取列表数据只保存不展示，手动点击获取时要展示
	function getRegionList(params, key, show) {
		uni.showLoading({
			title: "加载中...",
			mask: true
		})
		getRegionInfo(params).then(res => {
			if (res.success) {
				allData[key] = res.data;
				if (show) {
					currentList.value = res.data;
				}
				uni.hideLoading();
			} else {
				uni.showToast({
					title: "请求失败",
					duration: 2000,
					icon: "none"
				})
				uni.hideLoading();
			}
		}).catch(err => {
			uni.hideLoading();
		})
	}
	// 获取村数据 这里的村都是已经开通了租户的
	function getVillageList(regionCode, key) {
		getVillageInfo({
			regionCode: regionCode
		}).then(res => {
			allData[key] = res.data;
			currentList.value = res.data;
			uni.hideLoading();
		})
	}
	// 切换行政区划
	function changeOrgLev(index) {
		currentLevel.value = index;
		let key = currentLevelKey.value[index];
		currentList.value = allData[key];
	}
	// 根据村名搜索村 后台固定返回最多50条数据 不需要分页了
	function searchVillage() {
		if (!keyword.value.trim()) {
			uni.showToast({
				title: '关键字不能为空',
				icon: "none"
			})
			return false;
		}
		editKeyword.value = keyword.value;
		uni.showLoading({
			title: '查询中...',
			mask: true
		});
		getVillageInfo({
			tenantName: keyword.value
		}).then(res => {
			if (res.success && res.data.length > 0) {
				isSearched.value = true; //展示搜索结果时不展示行政区划级别
				currentList.value = res.data;
			}

			uni.hideLoading();
		})
	}
	// 改变关键字
	function changeKeyword(value) {
		if (value == "") {
			isSearched.value = false;
			let key = currentLevelKey.value[currentLevel.value];
			currentList.value = allData[key];
		}
	}

	function brightenKeyword(val, editKeyword) {
		const Reg = new RegExp(editKeyword);
		if (val) {
			const res = val.replace(Reg, `<span style="color: #0D91E5;">${editKeyword }</span>`);
			return res;
		}
	}
	// 选中目标行政区划,返回上一页，切换到目标行政区划
	function selectVillage(item) {
		let target = {
			villageName: item.tenantName || item.regionName,
			regionCode: item.regionCode,
			regionLevel: item.regionLevel || '6',
			tenantId: item.tenantId
		}
		uni.$emit('changeVillage', target)
		// 返回上一页
		uni.navigateBack({
			delta: 1
		});
	}
	// 查看本市-获取行政区划信息
	function selectCity() {
		let param = {
			rootCode: cityRegion.value,
			regionLevel: '3'
		}
		findRegionInfo(param).then(res => {
			if (res.success) {
				console.log('findRegionInfo--------', res);
				let target = {
					villageName: res.data[0].cityName,
					regionCode: res.data[0].cityNumber,
					regionLevel: res.data[0].regionLevel,
					tenantId: res.data[0].tenantId
				}
				uni.$emit('changeVillage', target)
				// 返回上一页
				uni.navigateBack({
					delta: 1
				});
			}
		})
	}
</script>

<style lang="scss" scoped>
	.main-container {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		padding: 0 24rpx;
		// height: 100%;
		height: 100vh;
		// background: #F4F5F7;
		overflow: hidden;
		background: #F4F8F7;
	}

	.title {
		margin-bottom: 20rpx;
		padding: 0 13rpx;
		font-size: 32rpx;
		color: #000000;
		font-weight: 500;
		position: relative;


		&::after {
			content: "";
			position: absolute;
			left: 0rpx;
			top: 50%;
			transform: translate(0, -50%);
			width: 6rpx;
			height: 28rpx;
			border-radius: 3rpx;
			background-image: linear-gradient(180deg, #0CBE88 0%, #1CD073 100%);
		}
	}

	.mb-0 {
		margin-bottom: 0;
	}

	.visit-history {
		.history-list {
			display: flex;
			flex-wrap: wrap;

			.history-item {
				margin: 0 15rpx 20rpx 0;
				padding: 0 20rpx;
				width: fit-content;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				background: #FFFFFF;
				border-radius: 32rpx;
				border: 1rpx solid #E0E8E5;
			}
		}
	}

	.list-box {
		margin-bottom: 20rpx;
		flex: 1;
		overflow-y: auto;
		background: #F4F8F7;
		border-radius: 8rpx;
	}

	.content {
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid rgba(151, 151, 151, 0.3);

		.list-item {
			flex: 1;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			min-height: 84rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			// line-height: 84rpx;
			color: #333333;
		}
		.item-icon {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			width: 80rpx;
		}
	}

	.orgs {
		position: relative;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin: 20rpx 0;
		height: 40rpx;
		width: 100%;
		white-space: nowrap;
		color: #999999;
		background: #F4F8F7;

		.org-item {
			display: inline-block;
			padding-right: 10rpx;
			width: 20%;
			font-size: 28rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-family: Source Han Sans CN, Source Han Sans CN;
		}

		.active {
			color: #0CBE88;
		}
		.current-city {
			position: absolute;
			right: 0;
			bottom: 0;
			display: flex;
			align-items: center;
			.label {
				margin-left: 4rpx;
				font-size: 28rpx;
				color: #0cbe88;
			}
		}
	}

	.main-container ::v-deep .u-search {
		flex: unset;
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F4F8F7;
	}
</style>