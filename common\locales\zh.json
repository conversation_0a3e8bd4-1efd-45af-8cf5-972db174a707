{
	// 快捷登录页
	"login.fastLoginPhone": "本机号码一键登录",
	"login.fastLoginMp": "微信一键授权登录",
	"login.captchaLogin": "验证码登录",
	"login.pwdLogin": "密码登录",
	"login.agreedLogin": "登录即同意",
	"login.agreementLogin": "《用户协议》",
	"login.gentleLogin": "和",
	"login.PolicyLogin": "《隐私政策》",
	"login.consentAgreement": "请确认同意《用户协议》和《隐私政策》",
	// 密码验证码登录页
	// 登录方式标题
	"login.captchaLoginTitle": "验证码登录",
	"login.pwdLoginTitle": "密码登录",
	// 按钮文案
	"login.findPwdTitle": "找回密码",
	"login.loginBtn": "登录",
	"login.fastLogin": "一键登录",
	"login.getCaptcha": "获取验证码",
	"login.confirmAuthorization": "确认授权",
	"login.authorizationFailures": "授权失败",
	"login.tips": "未注册的手机号验证成功后自动注册",
	//提示文字信息
	"login.phoneNumber": "请输入手机号",
	"login.enterPassword": "请输入密码",
	"login.verificationCode": "请输入验证码",
	"login.writePhone": "请填写手机号",
	"login.incorrectPhone": "手机号码不正确",
	"login.writePassword": "请填写密码",
	"login.passwordIength": "密码长度在8~20位之间",
	"login.phoneVerification": "请填写手机验证码",
	"login.username": "请填写用户名",
	"login.tenantName": "请填写租户名",
	"login.selectTenant": "请选择租户",
	"login.graphicVerification": "请填写图形验证码",
	"login.register": "登录中",
	"login.succeeded": "登录成功",
	"login.gainCode": "正在获取验证码",
	"login.bindPhone": "请绑定手机号",
	"login.alreadyBound": "您已经绑定过该手机号，请勿重复绑定",
	"login.authorizing": "授权中"

}