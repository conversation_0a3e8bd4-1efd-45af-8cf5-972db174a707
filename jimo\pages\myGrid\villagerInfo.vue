<template>
	<view class="main-container paddingbottom">
		<!-- 基础信息栏 -->
		<view class="base-info">
			<view class="name-info">
				<!-- <u-avatar size="132rpx" :src="sculpture"></u-avatar> -->
				<safe-image :src="villagerInfo.photo" width='132rpx' height='132rpx' shape="circle"></safe-image>
				<view class="info">
					<view class="villager-name">
						<view class="name">
							{{villagerInfo.villagerName}}
						</view>
						<u-image src="@/jimo/static/images/myGrid/man.png" width="32rpx" height='32rpx'
							v-if="villagerInfo.sex == 'man'"></u-image>
						<u-image src="@/jimo/static/images/myGrid/woman.png" v-else width="32rpx"
							height='32rpx'></u-image>
					</view>
					<view class="native-place">{{villagerInfo.nativePlace || '--' }}</view>
				</view>
			</view>
			<view class="other-info">
				<u-row customStyle="margin-bottom: 10rpx">
					<u-col span="3">
						<view class="label">手机号</view>
					</u-col>
					<u-col span="3.5">
						<view class="value">{{villagerInfo.phoneNum || '--'}}</view>
					</u-col>
					<u-col span="2.5">
						<view class="label">政治面貌</view>
					</u-col>
					<u-col span="3">
						<view class="value">{{villagerInfo.politicalStatus || '--'}}</view>
					</u-col>
				</u-row>
				<u-row customStyle="margin-bottom: 10rpx">
					<u-col span="3">
						<view class="label">婚姻状态</view>
					</u-col>
					<u-col span="3.5">
						<view class="value">{{villagerInfo.maritalStatus || '--'}}</view>
					</u-col>
					<u-col span="2.5">
						<view class="label">出生日期</view>
					</u-col>
					<u-col span="3">
						<view class="value">{{villagerInfo.birthDay || '--'}}</view>
					</u-col>
				</u-row>
				<u-row>
					<u-col span="3">
						<view class="label">与户主关系</view>
					</u-col>
					<u-col span="3.5">
						<view class="value">{{villagerInfo.relationship || '--'}}</view>
					</u-col>
					<u-col span="2.5">
						<view class="label">民族</view>
					</u-col>
					<u-col span="3">
						<view class="value">{{villagerInfo.nation || '--'}}</view>
					</u-col>
				</u-row>
			</view>
		</view>
		<!-- 标签栏 -->
		<view class="tag-info">
			<view class="title">标签</view>
			<view class="tag-list" v-if="villagerInfo.peopleTags.length > 0 || villagerInfo.skillTags.length > 0">
				<view class="tag-item" v-for="(item,index) in villagerInfo.peopleTags" :key="index">
					{{item}}
				</view>
				<view class="tag-item" v-for="(item,index) in villagerInfo.skillTags" :key="index">
					{{item}}
				</view>
			</view>
			<view class="empty-status" v-else>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
		</view>
		<!-- 相关成员 -->
		<view class="related-member">
			<view class="title">相关成员</view>
			<view class="member" v-for="(item,index) in villagerInfo.relatedMembers" :key="index">
				<view class="member-item">
					<view class="label">姓名</view>
					<view class="value">{{item.name}}</view>
				</view>
				<view class="member-item">
					<view class="label">户主姓名</view>
					<view class="value">{{item.name2}}</view>
				</view>
				<view class="member-item">
					<view class="label">与户主关系</view>
					<view class="value">{{getDictLabel('relation', item.relation)}}</view>
				</view>
				<view class="member-item">
					<view class="label">联系方式</view>
					<view class="value">{{item.cellphone}}</view>
				</view>
			</view>
			<view class="empty-status" v-if="villagerInfo.relatedMembers.length == 0">
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	import { onShow, onLoad, onUnload } from '@dcloudio/uni-app'
	import { getVillagerDetail } from '../../api/mygrid/mygrid.js'
	import { getDictListByKey } from '@/common/net/generalPage/common.js'
	import { NO_MORE_IMG, DEFAULT_AVATOR_IMAGES } from '@/common/net/staticUrl';
	import { findInfo } from '@/common/net/account/account.js'
	onLoad(async(options) => {
		// 获取字典数据
		await getDictData()
		// 根据id获取农户信息详情
		if (!!options.id) {
			await getDetailInfo(options.id)
		}
	})
	const villagerInfo = ref({
		photo: "",
		villagerName: '',
		sex: '--',
		nativePlace: '--',
		phoneNum: '--',
		politicalStatus: '--',
		maritalStatus: '--',
		birthDay: '--',
		relationship: '--',
		nation: '--',
		peopleTags: [],
		skillTags: [],
		relatedMembers: []
	})
	const relationshipLabels = ref([]) // 与户主关系字典数据
	const politicalStatusLabels = ref([]) // 政治面貌字典数据
	const marriageLabels = ref([]) // 婚姻状态字典数据
	const nationLabels = ref([]) // 民族字典数据
	const skillTagsLabels = ref([]) // 技能标签字典数据
	const peopleTagsLabels = ref([]) // 人员标签字典数据
	// 获取字典数据
	async function getDictData() {
		// 与户主关系
		let dictParam = {
			code: 'family_relation'
		}
		const resRelation = await getDictListByKey(dictParam)
		if (resRelation.success) {
			relationshipLabels.value = resRelation.data
		}
		// 政治面貌
		dictParam.code = 'political_status'
		const resPolitical = await getDictListByKey(dictParam)
		if (resPolitical.success) {
			politicalStatusLabels.value = resPolitical.data
		}
		// 婚姻状态
		dictParam.code = 'sys_base_marriage'
		const resMarriage = await getDictListByKey(dictParam)
		if (resMarriage.success) {
			marriageLabels.value = resMarriage.data
		}
		// 民族
		dictParam.code = 'nation'
		const resNation = await getDictListByKey(dictParam)
		if (resNation.success) {
			nationLabels.value = resNation.data
		}
		// 人员标签
		dictParam.code = 'people_tag'
		const resPeople = await getDictListByKey(dictParam)
		if (resPeople.success) {
			peopleTagsLabels.value = resPeople.data
		}
		// 技能标签
		dictParam.code = 'skill_tag'
		const resSkill = await getDictListByKey(dictParam)
		if (resSkill.success) {
			skillTagsLabels.value = resSkill.data
		}
	}
	async function getDetailInfo(id) {
		console.log('relationshipLabels--',relationshipLabels.value);
		let params = {
			peopleId: id,
		}
		const headInfo = await findInfo(params)
		if (headInfo.success) {
			console.log('headInfo-------', headInfo);
			villagerInfo.value.photo = headInfo.data?.headPhoto || DEFAULT_AVATOR_IMAGES;
		}
		const res = await getVillagerDetail({ id })
		if (res.success && res.data) {
			console.log('农户信息---', res);
			// villagerInfo.value.photo = res.data.info.photo || DEFAULT_AVATOR_IMAGES; // 头像
			villagerInfo.value.villagerName = res.data.info.name; // 姓名
			villagerInfo.value.sex = res.data.info.sex; // 性别
			// 获取户籍地址,extend是成员拓展信息，
			if (res.data.extend) {
				// 如果有拓展信息，则需要找到户籍字段对应的fieldId
				let fieldId = ''
				if (Array.isArray(res.data.fields) && res.data.fields.length > 0) {
					let target = res.data.fields.find((field) => {
						return field.name == '户籍'
					})
					if (!!target) {
						fieldId = target.fieldId;
					}
				}
				// 找到fieldId后再去拓展信息中查找对应的值
				if (!!fieldId) {
					let extendObj = JSON.parse(res.data.extend.data)
					if (!!extendObj[fieldId]) {
						villagerInfo.value.nativePlace = extendObj[fieldId];
					}
				}
			}
			villagerInfo.value.phoneNum = res.data.info.cellphone || '--'; // 手机号
			villagerInfo.value.politicalStatus = getDictLabel('political', res.data.info.political) // 政治面貌
			villagerInfo.value.maritalStatus = getDictLabel('marriage', res.data.info.marriage) // 婚姻状况
			villagerInfo.value.birthDay = res.data.info.birthday || '--' // 出生日期
			villagerInfo.value.relationship = getDictLabel('relation', res.data.info.relation) // 与户主关系
			villagerInfo.value.nation = getDictLabel('nation', res.data.info.nation) // 民族
			// 获取人员标签
			if(!!res.data.info.peopleTag) {
				let tagList = res.data.info.peopleTag.split(',')
				if(tagList.length > 0) {
					tagList.forEach((tag) => {
						let result = getDictLabel('peopleTag', tag)
						console.log('peopleTag---', result);
						if(!!result) {
							villagerInfo.value.peopleTags.push(result)
						}
					})
				}
			}
			// 获取技能标签
			if(!!res.data.info.skillTag) {
				let tagList = res.data.info.skillTag.split(',')
				if(tagList.length > 0) {
					tagList.forEach((tag) => {
						let result = getDictLabel('skillTag', tag)
						if(!!result) {
							villagerInfo.value.skillTags.push(result)
						}
					})
				}
			}
			// 相关成员
			villagerInfo.value.relatedMembers = res.data.family;
		}
	}
	// 获取字典属性值
	function getDictLabel(key, code) {
		console.log('getDictLabel', key, code);
		let target = {};
		if (key == 'political') {
			// 政治面貌
			target = politicalStatusLabels.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key == 'marriage') {
			// 婚姻状况
			target = marriageLabels.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key == 'relation') {
			target = relationshipLabels.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key == 'nation') {
			target = nationLabels.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key== 'peopleTag') {
			target = peopleTagsLabels.value.find((item) => {
				return item.dictValue == code
			})
		} else if (key == 'skillTag') {
			target = skillTagsLabels.value.find((item) => {
				return item.dictValue == code
			})
		}
		console.log('getDictLabel', key, code);
		console.log('target', target);
		if (!!target) {
			return target.dictLabel
		} else {
			return '--'
		}
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.main-container {
		position: relative;
		padding: 20rpx;
		width: 100%;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.base-info {
		padding: 25rpx 40rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.name-info {
			display: flex;
			align-items: center;
			padding-bottom: 20rpx;
			margin-bottom: 20rpx;
			border-bottom: 1px solid #F0F0F0;

			.info {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				margin-left: 20rpx;

				.villager-name {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.name {
						margin-right: 16rpx;
						font-weight: 500;
						font-size: 32rpx;
						color: #363636;
					}
				}

				.native-place {
					font-weight: 400;
					font-size: 26rpx;
					color: #999999;
				}
			}
		}

		.other-info {
			.label {
				font-weight: 400;
				font-size: 26rpx;
				color: #333333;
			}

			.value {
				font-weight: 400;
				font-size: 26rpx;
				color: #999999;
			}
		}
	}

	.title {
		margin-bottom: 20rpx;
		font-weight: 500;
		font-size: 32rpx;
		color: #000000;
	}

	.tag-info {
		padding: 20rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.tag-list {
			display: flex;
			// justify-content: space-evenly;
			align-items: center;
			flex-wrap: wrap;

			.tag-item {
				margin: 0 20rpx 20rpx 0;
				width: 148rpx;
				height: 52rpx;
				line-height: 52rpx;
				text-align: center;
				border: 1px solid #0BBD88;
				background-color: #f1fbf8;
				border-radius: 10rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #0BBD88;
			}
		}
	}

	.related-member {
		padding: 30rpx 20rpx;
		background-color: #fff;
		border-radius: 10rpx;

		.member {
			margin-bottom: 20rpx;
			padding: 30rpx 20rpx;
			background: linear-gradient(273deg, #F9FCFB 0%, #F3FFFB 100%);
			border-radius: 10rpx;

			.member-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;

				.label {
					font-weight: 400;
					font-size: 26rpx;
					color: #333333;
				}

				.value {
					font-weight: 400;
					font-size: 26rpx;
					color: #999999;
				}
			}
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F4F8F7;
	}
</style>