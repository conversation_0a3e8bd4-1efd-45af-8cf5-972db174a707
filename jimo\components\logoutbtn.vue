<template>
        <float-button
		@tuichu="logoutOfLogin"
		>
        <view class="logout-btn" @tap.stop="logoutOfLogin">
            <view class="logout-content">
                <image class="logout-icon" src="@/jimo/static/images/logout.png">
                </image>
                <view class="logout-text">退出登录</view>
            </view>
        </view>
</float-button>
</template>
<script>
import { useUserStore } from '@/store/user.js'
import { useTokenStore } from '@/store/token.js'
import { useMainStore } from '@/store/index.js'
import { useTabBarStore } from '@/store/tabbar.js'
import { logout } from '@/common/api.js'
import FloatButton from '@/jimo/components/float-button/float-button.vue'
import kvStore from '@/common/store/uniKVStore.js'

export default {
    name: 'LogoutBtn',
    components: { FloatButton },
    data() {
        return {}
    },
    methods: {
        logoutOfLogin() {
            // 初始化 stores
            const userStore = useUserStore()
            const tokenStore = useTokenStore()
            const mainStore = useMainStore()
            const tabBarStore = useTabBarStore()

            uni.showModal({
                title: '退出登录',
                content: '退出后部分功能将不能使用，确定退出吗？',
                success: function (res) {
                    if (res.confirm) {
                        logout({}).then((res) => {
                            if (res.data == 'success') {
                                mainStore.logout()
                                tabBarStore.logout()
                                userStore.logout()
                                tokenStore.clearAuthInfo()
                                kvStore.clear(true)
                                kvStore.set('hasLogin', 0, true)
                                uni.showToast({
                                    title: '退出成功',
                                    icon: 'none',
                                    success: () => {
                                        setTimeout(() => {
                                            // #ifdef H5
                                            uni.reLaunch({
                                                url: '/pages/login/login?loginType=captcha'
                                            })
                                            // #endif
                                            // #ifndef H5
                                            uni.reLaunch({
                                                url: '/pages/index/index'
                                            })
                                            // #endif
                                        }, 1000)
                                    },
                                })
                            } else {
                                mainStore.logout()
                                tabBarStore.logout()
                                userStore.logout()
                                tokenStore.clearAuthInfo()
                                kvStore.clear(true)
                                kvStore.set('hasLogin', 0, true)
                                uni.showToast({
                                    title: '登录信息已失效，请重新登录',
                                    icon: 'none',
                                    success: () => {
                                        setTimeout(() => {
                                            // #ifdef H5
                                            uni.reLaunch({
                                                url: '/pages/login/login?loginType=captcha'
                                            })
                                            // #endif
                                            // #ifndef H5
                                            uni.reLaunch({
                                                url: '/pages/index/index'
                                            })
                                            // #endif
                                        }, 1000)
                                    },
                                })
                            }
                        })
                    }
                }
            });
        }
    }
};
</script>
<style scoped>
.logout-btn {
  
    width: 120px;
    padding: 20rpx 0;
    border-radius: 0 45rpx 45rpx 0;
    z-index: 997;
    background: linear-gradient(135deg, #4a6604 0%, #496613 100%);
    box-shadow: 0 6rpx 20rpx rgba(44, 62, 80, 0.4);
    backdrop-filter: blur(10rpx);
}



.logout-content {
    display: flex;
    align-items: center;
    justify-content: center;

    padding: 0 16rpx;

}

.logout-icon {
    margin-right: 10rpx;
    width: 50rpx;
    height: 50rpx;

}

.logout-text {
    color: #fff;
}
</style>