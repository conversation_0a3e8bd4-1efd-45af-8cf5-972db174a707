<template>
	<view class="main-container">
		<view class="form-container">
			<view class="form-content">
				<!-- <from-parser :tableFields="tableFields"></from-parser> -->
				<!-- <from-parser ref="formParser" v-model="tableFields" @selectEventType='selectEventType'></from-parser> -->
				<from-parser ref="formParser" :tableFields="tableFields" @selectEventType='selectEventType'
					@updateModelValue="updateModelValue"></from-parser>
			</view>
		</view>
		<view class="flow-container">
			<view class="main-title">
				流程
			</view>
			<view class="flow-content">
				<!-- 审批类流程如果有配置好的处理人则直接展示，不能修改，办理类和信件类只能手动选择-->
				<template v-if="hasDefaultApprover && flowType=='1'">
					<view class="flow-content-item" v-for="(item,index) in approverList" :key="index">
						<view class="step-box">
							<view class="title-box">
								<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
									height="46rpx" mode="aspectFit"></u-image>
								<view class="title">
									{{handlePeople}}
								</view>
							</view>
						</view>
						<view class="people-box">
							<view class="title-box">
								<view class="left-box">
									<view class="line" v-if="approverList.length >1"></view>
									<view class="noline" v-else></view>
								</view>
								<view class="people">
									{{item.name}}
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- 审批类流程如果没有配置处理人则需要手动选择一个处理人 -->
				<template v-else-if="!hasDefaultApprover && flowType=='1'">
					<view class="flow-content-item">
						<view class="step-box">
							<view class="title-box">
								<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
									height="46rpx" mode="aspectFit"></u-image>
								<view class="title">
									{{handlePeople}}
								</view>
							</view>
						</view>
						<view class="people-box">
							<view class="title-box">
								<view class="left-box">
									<view class="line"></view>
								</view>
								<view class="people" v-if="approverList.length > 0" @click="chooseApprover('single')">
									{{approverList[0].name}}
								</view>
								<view v-else class="people">
									<u-image src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
										height="48rpx" @click="chooseApprover('single')"></u-image>
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- 办理类和信件类流程需要手动选择一个处理人 -->
				<!-- 如果有预设的处理人则从预设的中选一个，如果没有预设的则从所有的可办理的人中选择 -->
				<template v-else-if="flowType=='2' || flowType=='3'">
					<view class="flow-content-item">
						<view class="step-box">
							<view class="title-box">
								<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
									height="46rpx" mode="aspectFit"></u-image>
								<view class="title">
									{{handlePeople}}
								</view>
							</view>
						</view>
						<view class="people-box">
							<view class="title-box">
								<view class="left-box">
									<view class="noline"></view>
								</view>
								<view class="people" v-if="transactorList.length > 0" @click="chooseTransactor">
									{{transactorList[0].name}}
								</view>
								<view v-else class="people">
									<u-image src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
										height="48rpx" @click="chooseTransactor"></u-image>
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- 只有审批类流程才展示抄送人 -->
				<view class="flow-content-item send-item" v-if="flowType == '1'">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								抄送人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="noline"></view>
							</view>
							<view class="people send-people">
								<!-- 如果有配置好的抄送人则直接展示，不能修改 -->
								<template v-if="hasDefaultSender">
									<view class="u-line-1">
										{{sendPeopleArr}}
									</view>
								</template>
								<!-- 如果没有配置好的，则去选择 -->
								<view v-else class="send-people-content">
									<!-- <u-tag :text="item.name" plain size="mini" color='#555' borderColor="transparent"
										v-for="(item,index) in sendPeopleListDisp" :key="index">
									</u-tag> -->
									<view class="send-people-item" v-for="(item,index) in sendPeopleListDisp" :key="index">
										{{item.name}}
									</view>
									<!-- <u-tag text="查看全部" v-if="sendPeopleList.length > 2" plain size="mini"
										color='#555' borderColor="transparent" @click="chooseApprover('multi')">
									</u-tag> -->
									<view class="send-people-item" v-if="sendPeopleList.length > 2" @click="chooseApprover('multi')">查看全部</view>
									<u-image src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
										height="48rpx" @click="chooseApprover('multi')"></u-image>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 工作上报 展示转阅人 -->
				<view class="flow-content-item send-item" v-if="flowType == '3' && flowFlag === 'gongzuoshangbao'">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								转阅人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="noline"></view>
							</view>
							<view class="people send-people">
								<view class="send-people-content refereeCont">
									<view class="send-people-item" v-for="(item,index) in refereePeopleList" :key="index">
										{{item.name}}
									</view>
									<u-image src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
										height="48rpx" @click="chooseReferee"></u-image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="submit-container paddingbottom">
		<view class="submit" @click="submitFlow">
			提交
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	import { onShow, onLoad, onUnload } from '@dcloudio/uni-app'
	import fromParser from "./formParser.vue"
	import {
		getFlowTableFields,
		getFlowNodePeople,
		applyFlow
	} from '../../api/flowPage/flowPage.js'
	import { getDicts } from '@/common/net/contacts/contacts.js'
	import { setPoints} from '../../api/burialPoint.js'
	import {
        goldTask
    } from '../../api/api.js'
	const flowId = ref('') // 流程id
	const flowType = ref('') // 流程类型
	const formParser = ref() // 表单对象
	const flowFlag = ref('') // 流程标识
	const taskId = ref('') //金币任务id
	const appKey = ref('')
	onLoad((options) => {
		// 获取应用流程id
		flowId.value = options.flowId;
		// 获取流程配置的表单
		getFlowTable()
		// 获取流程配置的人员信息
		getFlowPeople()
		// 来自金币任务
		const { taskId: taskIdFromOptions = '' } = options;
		taskId.value = taskIdFromOptions
		const { appKey: appKeyFromOptions = ''} = options;
		appKey.value = appKeyFromOptions
	})
	onUnload(() => {
		if(timer.value) {
			clearTimeout(timer.value);
			timer.value = null
		}
	})
	const tableFields = ref([]) // 表单数据
	const processor = ref('') // 处理人类别
	// 审批类相关数据
	const approverList = ref([]) // 处理人列表 用于接收后台返回的数据
	const hasDefaultApprover = ref(false) // 是否配置了处理人
	const sendPeopleList = ref([]) // 抄送人列表 用于接收后台返回的数据
	const sendPeopleListDisp = ref([]) // 抄送人列表 用于页面展示，抄送人超过两个时展示...
	const sendPeopleArr = ref([]) // 抄送人列表用逗号间隔的字符串
	const hasDefaultSender = ref(false) // 是否配置了抄送人
	// 办理类相关数据
	const transactorList = ref([])
	// 处理人类型 1审批类-审批人	2事件类-办理人	3信件类-接收人	3工作上报类-审阅人
	const handlePeople = computed(() => {
		if (processor.value == 'approver') {
			return '审批人'
		} else if (processor.value == 'transactor') {
			return '办理人'
		} else if (processor.value == 'replier') {
			return '回复人'
		} else if (processor.value == 'reader') {
			return '审阅人'
		}
	})
	const refereePeopleList = ref([]) // 转阅人列表 用于接收返回的数据
	// 获取流程信息
	function getFlowTable() {
		let params = {
			flowId: flowId.value
		}
		getFlowTableFields(params).then(res => {
			if (res && res.success) {
				tableFields.value = JSON.parse(res.data.formJsonStr)
				console.log("tableFields", tableFields.value);
				processor.value = res.data.processor;
				flowType.value = res.data.flowType;
				uni.setNavigationBarTitle({
					title: res.data.formName
				});
				flowFlag.value = res.data.flowFlag
				if (res.data.flowFlag == 'shijianshangbao') {
					// 埋点-进入事件上报填写页
					let param = {
						eventId : 'enter_event_reporting_form',
						attributeValue: '/jimo/pages/flowPage/flowPublish'
					}
					setPoints(param)
				}
			}
		})
	}
	// 获取流程人员信息
	function getFlowPeople() {
		let params = {
			flowId: flowId.value,
			isDetail: '1',
			eventType: '',
			isFlowSetting: '0'
		}
		getFlowNodePeople(params).then(res => {
			if (res && res.success) {
				// 如果配置了处理人则使用配置的
				if (res.data.approver.length > 0) {
					approverList.value = res.data.approver
					hasDefaultApprover.value = true
				}
				// 如果配置了抄送人则将列表以字符串形式展示，且不能更改
				if (!!res.data.sendPeople && res.data.sendPeople.length > 0) {
					sendPeopleList.value = res.data.sendPeople
					let tmpArr = [];
					res.data.sendPeople.forEach(item => {
						tmpArr.push(item.name)
					})
					sendPeopleArr.value = tmpArr.join('，')
					hasDefaultSender.value = true
				}
			}
		})
	}
	// 选择审批人或抄送人
	function chooseApprover(type) {
		uni.$once('chooseMemberCallback', (data) => {
			console.log('chooseMemberCallback', data);
			if (type == 'single') {
				approverList.value = data;
			} else if (type == 'multi') {
				sendPeopleList.value = data;
				if (sendPeopleList.value.length > 2) {
					sendPeopleListDisp.value = sendPeopleList.value.slice(0, 2);
				} else {
					sendPeopleListDisp.value = sendPeopleList.value
				}
			}
		})
		let choosedMembers = "";
		if (type == 'single') {
			choosedMembers = JSON.stringify(approverList.value)
		} else if (type == 'multi') {
			choosedMembers = JSON.stringify(sendPeopleList.value)
		}
		uni.navigateTo({
			url: '/jimo/pages/chooseMember/chooseMember?flowId=' + flowId.value + '&chooseType=' + type +
				'&choosedMembers=' + choosedMembers + '&eventType=' + eventType.value
		})
	}
	// 选择转阅人(多选)
	function chooseReferee(){
		uni.$once('chooseMemberCallback', (data) => {
			console.log('chooseMemberCallback', data);
			refereePeopleList.value = data;
		})
		let choosedMembers = JSON.stringify(refereePeopleList.value)
		// flowFlag工作上报传
		uni.navigateTo({
			url: '/jimo/pages/chooseMember/chooseMember?flowId=' + flowId.value + '&chooseType=multi' +
				'&choosedMembers=' + choosedMembers + '&eventType=' + '&flowFlag=' + flowFlag.value
		})
	}
	// 选择办理人
	function chooseTransactor() {
		uni.$once('chooseMemberCallback', (data) => {
			console.log('chooseMemberCallback', data);
			transactorList.value = data;
		})
		let choosedMembers = JSON.stringify(transactorList.value) // 已选择的
		if (flowType.value == '3') {
			eventType.value = '';
		}
		uni.navigateTo({
			// chooseType： 单选还是多选 choosedMembers：已选择的人员，传过去用于回显 eventType:事件类型，办理类的工单选人时需要这个参数
			url: '/jimo/pages/chooseMember/chooseMember?flowId=' + flowId.value + '&chooseType=single' +
				'&choosedMembers=' + choosedMembers + "&eventType=" + eventType.value
		})
	}
	// 提交申请
	function submitFlow() {
		console.log('tableFields----', tableFields.value);
		if (flowFlag.value == 'shijianshangbao') {
			// 埋点-点击事件上报填写页-提交按钮
			let param = {
				eventId : 'submit_event_reporting_form',
				attributeValue: 'submit_btn'
			}
			setPoints(param)
		}
		formParser.value.$vervify().then((formData) => {
			// 组装处理人和抄送人的数据，分别生成只包含人员id的数组
			let nodePeopleIds = []
			let sendPeopleIds = []
			let referPeopleIds = []
			if (flowType.value == '1') {
				if (approverList.value.length == 0) {
					uni.showToast({
						title: `请选择${handlePeople.value}`,
						icon: 'none',
						duration: 2000,
					});
					return false
				}
				approverList.value.forEach((item) => {
					nodePeopleIds.push(item.peopleId)
				})
				sendPeopleList.value.forEach((item) => {
					sendPeopleIds.push(item.peopleId)
				})
			} else {
				if (transactorList.value.length == 0) {
					uni.showToast({
						title: `请选择${handlePeople.value}`,
						icon: 'none',
						duration: 2000,
					});
					return false
				}
				transactorList.value.forEach((item) => {
					nodePeopleIds.push(item.peopleId)
				})
			}
			console.log(nodePeopleIds, sendPeopleIds);
			if(flowType.value == '3' && flowFlag.value === 'gongzuoshangbao' && refereePeopleList.value.length > 0){
				refereePeopleList.value.forEach(item => {
					referPeopleIds.push(item.peopleId)
				})
			}
			let params = {
				flowId: flowId.value,
				formJsonStr: JSON.stringify(tableFields.value),
				dataJsonStr: JSON.stringify(formData),
				nodePeopleIdList: nodePeopleIds,
				sendPeopleIdList: sendPeopleIds,
				referPeopleIdList: referPeopleIds
			}
			uni.showModal({
				title: '提示',
				content: '确定提交吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '提交中...',
							mask: true,
						})
						applyFlow(params).then(res => {
							if (res.success) {
								uni.hideLoading();
								if (flowFlag.value == 'shijianshangbao') {
									// 埋点-事件上报提交成功
									let param = {
										eventId : 'event_reporting_submitted',
										attributeValue: res.data
									}
									setPoints(param)
								}
								getGoldTask()
							} else {
								uni.hideLoading();
								uni.showToast({
									title: '提交失败，请稍后再试',
									duration: 2000,
									icon: 'none'
								})
								if (flowFlag.value == 'shijianshangbao') {
									// 埋点-事件上报提交失败
									let param = {
										eventId : 'event_reporting_submitted',
										attributeValue: 'fail'
									}
									setPoints(param)
								}
							}
						})
					} else if (res.cancel) {}
				}
			});
		}).catch((err) => {
			console.log("err", err);
		});
	}
	
	// 金币任务-上报信息
	async function getGoldTask() {
		try {
			let res = await goldTask({ 
				taskId: taskId.value,
				taskType: '9',
				appKey: appKey.value
			});
			if (res && res.success) {
				let goldMessage = taskId.value ? `任务完成，获得${res.data.coin}金币` : `提交成功，获得${res.data.coin}金币`;
				handleToast(goldMessage);
			} else {
				console.log('获取金币失败',res)
				let msg = taskId.value ? res.message : "提交成功！";
				handleToast(msg);
			}
		} catch (err) {
			console.error('获取金币异常',err)
			// let msg = taskId.value ? '操作异常，请稍后再试！' : "提交成功！";
			let msg = "提交成功！"
			handleToast(msg);
		}
	}
	const timer = ref(null)
	function handleToast(message) {
	  // 使用配置对象
	  const toastConfig = {
		title: message,
		icon: 'none',
		duration: 2000
	  };
	  uni.showToast(toastConfig);
	  // 如果是完任务，关闭当前页面跳转到列表页。如果不是完任务，返回上个页面
	  if(taskId.value) {
		  timer.value = setTimeout(() => {
			  uni.redirectTo({
			  	url: '/jimo/pages/flowPage/flowList?flowId=' + flowId.value
			  });
		  },1000)
	  } else {
		// 延迟返回上一级页面
		timer.value = setTimeout(() => {
		  uni.navigateBack({
			delta: 1
		  });
		}, 1000);
	  }
	}
	
	// 点击标签删除
	function delSendPeople(index) {
		sendPeopleList.value.splice(index, 1)
	}
	const eventType = ref('')
	// 从字典中获取事件类型的值，取第一个作为事件类型默认值
	getDicts("event_type").then((res) => {
		if(res.success && res.data.length>0) {
			eventType.value = res.data[0].dictValue
		}
	});
	// 办理类工单选择了事件类型
	function selectEventType(type) {
		eventType.value = type;
		getList()
	}
	// 监听表单数据改变
	function updateModelValue(formData) {
		tableFields.value = formData
	}
	// 获取成员列表
	function getList() {
		let queryParams = {
			flowId: flowId.value,
			isDetail: '0',
			eventType: eventType.value,
			isFlowSetting: '0'
		};
		getFlowNodePeople(queryParams).then((res) => {
			console.log('res.data', res.data);
			console.log('transactorList', transactorList.value);
			if (res.success) {
				let result = res.data.approver.find((item) => {
					return item.peopleId == transactorList.value[0].peopleId
				})
				if (!result) {
					transactorList.value = [];
				}
			}
		}).catch(() => {
		})
	}
</script>

<style lang="scss" scoped>
	.main-container {
		box-sizing: border-box;
		padding: 20rpx;
		width: 100%;
		padding-bottom: calc(env(safe-area-inset-bottom) + 135rpx);
		// height: 100%;

		.form-container {
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-sizing: border-box;
			background: #fff;
			border-radius: 20rpx;

			.main-title {
				// margin-left: 20rpx;
				font-size: 30rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #333333;
			}
		}

		.flow-container {
			padding: 20rpx;
			box-sizing: border-box;
			background: #fff;
			border-radius: 20rpx;
			padding-bottom: 130rpx;
			.flow-content-item {
				margin-top: 20rpx;
				width: 100%;
				height: 118rpx;

				.step-box,
				.people-box {
					display: flex;
					justify-content: space-between;
					align-items: center;

				}

				.people-box {
					min-height: 72rpx;

					.left-box {
						display: flex;
						justify-content: center;
						width: 46rpx;

						.line {
							width: 2rpx;
							height: 72rpx;
							background: #DDDDDD;
						}

						.noline {
							width: 2rpx;
							height: 72rpx;
							background: transparent;
						}
					}

					.people {
						margin-left: 20rpx;
						font-size: 26rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #555555;
					}

					.send-people {
						width: calc(100% - 46rpx);

						.send-people-content {
							display: flex;
							justify-content: flex-start;
							// align-items: flex-end;
							align-items: center;
							.send-people-item {
								margin-right: 20rpx;
							}
						}
						.refereeCont{
							flex-flow: wrap;
						}
					}
				}

				.title-box {
					display: flex;
					align-items: center;
					width: 100%;

					.title {
						margin-left: 20rpx;
						font-size: 26rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						color: #333333;
					}
				}

				.time {
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #999999;
				}
			}

			.send-item {
				height: auto;
			}
		}
	}

	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 3;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 125rpx;
		background: #FFFFFF;

		.submit {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-size: 36rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}

	.send-people-content ::v-deep .u-tag--mini {
		padding-left: 0;
	}
	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F0F7F7;
	}
</style>