<!-- 酒店民宿详情 -->
<template>
	<view class="service-provider">
		<u-navbar :title="`${pageTitle}详情`" bgColor="#FFF"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<view class="form-item" v-for="item in keyList" :key="item.key" v-show="item.show">
					<!-- 标题 -->
					<view class="label">{{ item.label }}</view>
					<!-- 图片视频展示 -->
					<view class="content-imgs" v-if="item.key === 'coverPhoto'">
						<view v-for="o in info[item.key]" :key="o">
							<image :src="o" v-if="getEx(o) === 'image'" />
							<video :src="o" controls v-else />
						</view>
					</view>
					<!-- 详细内容富文本带图片视频 -->
					<view class="content" v-else-if="item.key === 'content'">
						<view v-for="(content, index) in contentArr" :key="index">
							<rich-text class="rich-text ql-editor" :nodes="content" @click="showImg(content)" />
							<video v-if="videoArr[index] !== null" :src="videoArr[index]" controls
								:style="{ width: '100%', float: 'left', marginTop: '20rpx' }" />
						</view>
					</view>
					<!-- 开放时间 -->
					<view class="content" v-else-if="item.key === 'openingTime'">
						<view class="opening-time">
							<view>{{ info.openingStartTime }}</view>
							<image class="right-img" :src="LABOR_SWAP_RIGHT" 
								v-if="info.openingStartTime && info.openingEndTime" />
							<view>{{ info.openingEndTime }}</view>
						</view>
					</view>
					<!-- 其他标题内容展示 -->
					<view class="content" v-else>{{ info[item.key] }}</view>
				</view>
			</view>
		</view>
		
		<view class="operate">
			<view class="btn" @click="operateShow = true">
				管理
			</view>
		</view>
		
		<!-- 管理按钮弹窗 -->
		<u-popup :show="operateShow" round="20rpx" mode="bottom" @close="operateShow = false">
			<view class="operate-view">
				<view class="edit-btn" @click="goEdit()">编辑</view>
				<u-line></u-line>
				<view class="del-btn" @click="operateShow = false, delShow = true">删除</view>
				<u-line></u-line>
				<view class="close-btn" @click="operateShow = false">取消</view>
				<u-line></u-line>
			</view>
		</u-popup>
		<!-- 删除确认弹窗 -->
		<u-popup :show="delShow" round="20rpx" mode="center" closeable
			:safeAreaInsetBottom="false" @close="delShow = false">
			<view class="del-body">
				<text class="tip">确认删除"{{ info.title }}"{{ typeName }}吗？</text>
				<view class="btn-view">
					<view class="cancel" @click="delShow = false">取消</view>
					<view class="confirm" @click="handleDel()">确定</view>
				</view>
			</view>
		</u-popup>
		<!-- 禁用弹窗 -->
		<u-popup :show="show" mode="center" :safeAreaInsetBottom="false" closeable @close="show = false">
			<view class="del-body">
				<text class="tip">
					服务商权限{{ disable === '0' ? '被禁用' : examineType !== '1' ? '未通过' : '' }}，无法发布
				</text>
				<view class="btn-view">
					<view class="cancel" @click="show = false">取消</view>
					<view class="confirm" @click="show = false">确定</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { LABOR_RESUME_EDIT, LABOR_SWAP_RIGHT } from '@/common/net/staticUrl.js'
import { SreviceProvider, findList } from '../../api/villageCodeTour/villageCodeTour.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 类型 */
const parentKey = ref('')
/** 服务商是否禁用 */
const disable = ref('')
/** 服务商审核状态 */
const examineType = ref('')
/** 服务商信息 */
const info = ref({})
/** 富文本 */
const contentArr = reactive([])
/** 富文本-视频 */
const videoArr = reactive([])

/** 管理操作 */
const operateShow = ref(false)
/** 删除显示 */
const delShow = ref(false)
/** 禁用显示 */
const show = ref(false)

/** 页面标题 */
const pageTitle = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '酒店民宿'
	if (parentKey.value === 'localspecialties') return '产品'
	if (parentKey.value === 'food') return '美食'
	return ''
})
/** 当前服务类名称 */
const typeName = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '房源'
	if (parentKey.value === 'localspecialties') return '特产'
	if (parentKey.value === 'food') return '美食'
	return ''
})

/** 显示信息 */
const keyList = ref([
	{ label: '名称', key: 'title', show: true },
	{ label: `类型`, key: 'typeName', show: true },
	{ label: `图片/视频`, key: 'coverPhoto', show: true },
	{ label: '简介', key: 'introduction', show: true },
	{ label: '位置', key: 'position', show: false },
	{ label: '价格', key: 'price', show: false },
	{ label: '咨询电话', key: 'hotline', show: false },
	{ label: '开放时间', key: 'openingTime', show: false },
	{ label: '详细内容', key: 'content', show: true },
])

onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	info.value.contentId = options.id
	parentKey.value = options.parentKey
	disable.value = options.disable
	examineType.value = options.examineType
})

onShow(() => {
	getInfoData()
})

/**
 * @description 获取服务类详情
 */
const getInfoData = () => {
	const params = {
		contentId: info.value.contentId
	}
	SreviceProvider.getSreviceClassInfo(params).then(r => {
		if (r.success) {
			info.value = r.data
			info.value.coverPhoto = JSON.parse(info.value.coverPhoto)
			setKeyListShow()
			getTypesData()
			parseVideo()
		}
	})
}
/**
 * @description 设置信息显示
 */
const setKeyListShow = (data) => {
	// 酒店民宿
	if (parentKey.value === 'hotelshomestays') {
		keyList.value[0].label = '名称'
		keyList.value[1].label = '房源类型'
		keyList.value[2].label = '酒店图片/视频'
		keyList.value.find(o => o.key === 'position').show = true // 位置
		keyList.value.find(o => o.key === 'price').show = true // 价格
		keyList.value.find(o => o.key === 'hotline').show = true // 咨询电话
	}
	// 本地特产
	if (parentKey.value === 'localspecialties') {
		keyList.value[0].label = '标题'
		keyList.value[1].label = '特产类型'
		keyList.value[2].label = '特产图片/视频'
		keyList.value.find(o => o.key === 'hotline').show = true // 咨询电话
	}
	// 美食指南
	if (parentKey.value === 'food') {
		keyList.value[0].label = '名称'
		keyList.value[1].label = '美食类型'
		keyList.value[2].label = '美食图片/视频'
		keyList.value.find(o => o.key === 'position').show = true // 位置
		keyList.value.find(o => o.key === 'price').show = true // 价格
		keyList.value.find(o => o.key === 'hotline').show = true // 咨询电话
		keyList.value.find(o => o.key === 'openingTime').show = true // 开放时间
	}
}
/**
 * @description 获取分类列表
 */
const getTypesData = () => {
	const params = {
		parentKey: parentKey.value
	}
	findList(params).then(r => {
		if (r.success) {
			info.value.typeName = r.data.find(o => o.typeId === info.value.typeId).typeName
		}
	})
}
/**
 * @description 富文本点击查看大图
 * @param {Object} content
 */
function showImg(content) {
	// 富文本
	const richContent = content;
	// 判断含有图片
	if (richContent.indexOf("src") >= 0) {
		const imgs = [];
		richContent.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function(match, capture) {
			imgs.push(capture);
		})
		uni.previewImage({
			current: imgs[0], // 当前显示图片的链接
			urls: imgs
		})
	}
}
/**
 * @description 处理富文本的数据包含视频
 */
function parseVideo() {
	const content = info.value.content
	if (typeof content != 'string') {
		//不是HTML字符串格式的暂不处理
		contentArr[0] = content;
		videoArr[0] = null;
		return false;
	}

	//同步解决如果图片太大超出手机显示界面的问题
	let nodes = content.replace(/\<img/g, '<img style="max-width:98%!important;height:auto;"');
	let arr = nodes.split('</video>');
	let reg = /<video([\s\S]*)/g;

	for (let i in arr) {
		var item = arr[i];
		var urlMatch = item.match(/<video[\s\S]*src=\"(.*?)\"/);
		if (urlMatch && urlMatch.length > 1) {
			videoArr[i] = urlMatch[1];
		} else {
			videoArr[i] = null;
		}
		contentArr[i] = item.replace(reg, '')
	}
}
/**
 * @description 修改服务商信息
 */
const goEditServiceProvider = () => {
	uni.navigateTo({
	    url: '/jimo/pages/villageCodeTour/serviceProviderEdit'
	})
}
/**
 * @description 删除
 */
const handleDel = () => {
	const params = {
		contentId: info.value.contentId
	}
	SreviceProvider.delSreviceClass(params).then(r => {
		if (r.success) {
			uni.showToast({
			    title: '删除成功!',
			    icon: 'none'
			})
			operateShow.value = false
			uni.navigateBack()
		}
	})
}
/**
 * @description 编辑
 */
const goEdit = () => {
	if (disable.value === '0' || examineType.value !== '1') {
		show.value = true
		return
	}
	uni.navigateTo({
	    url: `/jimo/pages/villageCodeTour/serviceClassEdit` +
		`?parentKey=${parentKey.value}&id=${info.value.contentId}`
	})
	operateShow.value = false
}
/**
 * @description 获取图片后缀
 */
const getEx = (str) => {
	const ex = str.split('.').pop().toLowerCase()
	if (ex === 'jpg' || ex === 'jpeg' || ex === 'png' || ex === 'gif') {
		return 'image'
	} else {
		return 'video'
	}
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}


.service-provider {
	width: 100%;
	height: 100vh;
	
	.container {
		padding: 24rpx 20rpx;
		background: #F0F7F7;
		
		>view {
			background-color: #FFF;
			border-radius: 20rpx;
			padding: 27rpx 26rpx;
		}
		
		.form-item {
			margin-bottom: 32rpx;
			
			.label {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #999999;
				line-height: 40rpx;
				margin-bottom: 11rpx;
			}
			
			.content {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 32rpx;
				color: #000000;
				line-height: 45rpx;
				
				.opening-time {
					display: flex;
					
					.right-img {
						width: 40rpx !important;
						height: 40rpx !important;
						margin: 0 16rpx;
					}
				}
			}
			
			.content-imgs  {
				display: flow-root;
				
				>view {
					margin-top: 6rpx;
					float: left;
				}
				
				image, video {
					width: 308rpx;
					height: 192rpx;
					margin-right: 24rpx;
				}
			}
		}
	}
	
	.operate {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: calc(141rpx + env(safe-area-inset-bottom));
		padding-bottom: calc(env(safe-area-inset-bottom));
		background-color: #fff;
		box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
		background-size: 100% 100%;
		padding-top: 40rpx;
		
		.btn {
			width: 201rpx;
			height: 80rpx;
			border-radius: 48rpx;
			border: 1rpx solid #0BBD88;
			display: flex;
			align-items: center;
			justify-content: center;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #0BBD88;
			float: right;
			margin-right: 20rpx;
		}
	}
	
	.operate-view {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 28rpx;
		color: #000000;
		line-height: 45rpx;
		text-align: center;
		
		.edit-btn {
			padding: 19rpx 0 12rpx;
		}
		.del-btn {
			padding: 28rpx 0 12rpx;
		}
		.close-btn {
			padding: 28rpx 0 12rpx;
		}
	}
}

.del-body {
	width: 650rpx;
	text-align: center;
	padding: 83rpx 30rpx 52rpx 30rpx;
		
	.tip {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 32rpx;
		color: #111111;
		line-height: 55rpx;
	}
	
	.btn-view {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;
		
		view {
			width: 276rpx;
			height: 80rpx;
			border-radius: 41rpx;
			line-height: 80rpx;
		}
		
		.cancel {
			background: #FFFFFF;
			border: 2rpx solid #E5E5E5;
			margin-right: 23rpx;
			
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #555555;
		}
		
		.confirm {
			background: #0CBE88;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}

.ql-editor {
	padding: 0 !important;
}
</style>