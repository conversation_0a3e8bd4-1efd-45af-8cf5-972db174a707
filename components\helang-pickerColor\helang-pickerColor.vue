<template>
  <view v-show="isShowSelf()">
    <view class="shade" @tap="hide"></view>
    <view class="pop">
      <view class="flex_col" style="margin-bottom: 20upx">
        <view
          class="preview"
          v-if="pickerColor"
          :style="{ backgroundColor: pickerColor }"
        ></view>
        <text class="changeColor" v-else>主图切换</text>
        <view class="value">
          <text v-if="pickerColor">颜色值：{{ pickerColor }}</text>
        </view>
        <image @tap="hide" class="close" :src="close_url" mode=""></image>
      </view>
      <view class="xian"> </view>
      <view
        class="list flex_col"
        v-for="(item, index) in colorArr"
        :key="index"
      >
        <view
          class="list_item"
          v-for="(v, i) in item"
          :key="i"
          :style="{ backgroundColor: v }"
          :data-color="v"
          :data-index="index"
          :data-i="i"
          :class="{ active: index == pickerArr[0] && i == pickerArr[1] }"
          @tap="picker"
        ></view>
      </view>
      <view class="xian"> </view>
      <view :style="{ height: bottom + 'px' }"></view>
      <view class="btn">
        <button class="cancel" @tap="hide">取消</button>
        <button class="sure" @tap="setColor">确定</button>
      </view>
    </view>
  </view>
</template>

<script>
import { CLOSE_URL } from '@/common/net/staticUrl.js'
import { mapState } from 'vuex'
export default {
  name: 'picker-color',
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    bottom: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      close_url: CLOSE_URL,
      pickerColor: '',
      pickerArr: [-1, -1],
      pickerIndex: '',
      colorArr: [],
    }
  },
  created() {
    this.colorArr.push(this.colorList)
  },
  computed: {
    ...mapState({
      colorList: (state) => state.colorList,
    }),
  },
  methods: {
    isShowSelf() {
      return this.isShow
    },
    picker(e) {
      let data = e.currentTarget.dataset
      this.pickerColor = data.color
      this.pickerIndex = data.i
      this.pickerArr = [data.index, data.i]
    },
    hide() {
      this.$emit('callback', '')
    },
    setColor() {
      this.$emit('callback', {
        pickerColor: this.pickerColor,
        pickerIndex: this.pickerIndex,
      })
    },
    close() {},
  },
}
</script>

<style scoped>
.shade {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

.pop {
  position: fixed;
  right: 0;
  bottom: 0rpx !important;
  left: 0;
  height: 550rpx;
  line-height: 550rpx;
  background-color: #fff;
  z-index: 100;
  padding: 20rpx 20rpx 10rpx 10rpx;
  font-size: 32rpx;
  border-radius: 30rpx;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.flex_col {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  align-content: center;
}
.list {
  /* justify-content: space-between; */
  height: 180rpx;
  line-height: 180rpx;
  margin-left: 45rpx;
}
.list > .list_item {
  width: 62rpx;
  height: 62rpx;
  margin-bottom: 10rpx;
  box-sizing: border-box;
  border-radius: 3rpx;
  box-shadow: 0 0 2rpx #ccc;
  margin-right: 40rpx;
}
.list .active {
  box-shadow: 0 0 2rpx #09f;
  transform: scale(1.05, 1.05);
}
.preview {
  width: 180rpx;
  height: 60rpx;
}
.value {
  margin: 0 40rpx;
  flex-grow: 1;
}
.ok {
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background-color: #ff9933;
  color: #fff;
  border-radius: 4rpx;
  letter-spacing: 3rpx;
  font-size: 32rpx;
}
.ok:active {
  background-color: rgb(255, 107, 34);
}
.close {
  width: 29rpx;
  height: 29rpx;
  margin-right: 20rpx;
  background-size: cover;
}
.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 110rpx;
}
.cancel {
  width: 208rpx;
  height: 86rpx;
  line-height: 86rpx;
  font-size: 32rpx;
  margin-right: 30rpx;
  background: #ffffff;
  border-radius: 43rpx;
  border: 2rpx solid #e5e5e5;
}
.sure {
  width: 425rpx;
  height: 86rpx;
  line-height: 86rpx;
  font-size: 32rpx;
  color: #ffffff;
  background: linear-gradient(150deg, #3a78fe 0%, #39b3fd 100%);
  border-radius: 43rpx;
}
.xian {
  width: 700rpx;
  height: 1rpx;
  background: #e5e5e5;
}
.changeColor {
  font-size: 32rpx;
  margin-left: 30rpx;
  font-size: 700;
}
</style>
