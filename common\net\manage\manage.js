import {
    request
} from '../request.js'


//设备状态统计接口
export function countStatus(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/deviceInfo/countStatus',
        method: "POST",
        params,
    });
}

//设备列表接口（cbd(虫情测报)、bzy（孢子仪）qxz（气象站））
export function findDevicePage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/deviceInfo/findDevicePage',
        method: "POST",
        params,
    });
}

//测报灯最新一条数据接口
export function findNewOne(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightData/findNewOne',
        method: "POST",
        params,
    });
}

//测报灯列表接口(历史数据折线图)
export function selectByDay(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightData/selectByDay',
        method: "POST",
        params,
    });
}
//测报灯分页接口
export function findOfPage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightData/findOfPage',
        method: "POST",
        params,
    });
}

//测报灯最新图片获取
export function findPicture(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightImage/findByImei',
        method: "POST",
        params,
    });
}

//设备告警分页查询
export function findAlertPage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/deviceAlert/findAlertPage',
        method: "POST",
        params,
    });
}

//1.1.虫情监控-预警信息

export function findInsectWarn(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightData/findInsectWarn',
        method: "POST",
        params,
    });
}


//测报灯虫情信息接口
export function ImageResult(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/insectLightImageResult/selectByDay',
        method: "POST",
        params,
    });
}

//饱子仪分页查询
export function findSporePage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/sporeCatcherData/findSporePage',
        method: "POST",
        params,
    });
}
//饱子仪列表查询
export function findForChart(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/sporeCatcherData/findForChart',
        method: "POST",
        params,
    });
}
//孢子仪图片取出
export function findSporeNewOne(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/sporeCatcherImage/findNewOne',
        method: "POST",
        params,
    });
}
//气象站最新采集信息
export function findWeatherNewOne(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/weatherStationResult/findNewOne',
        method: "POST",
        params,
    });
}
//列表查询气象站采集数据
export function findWeatherResultList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/weatherStationResult/findWeatherResultList',
        method: "POST",
        params,
    });
}
//分页查询气象站采集数据
export function findWeatherResultPage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/weatherStationResult/findWeatherResultPage',
        method: "POST",
        params,
    });
}
//分页查询土地墒情数据
export function selectPageBySensorId(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/measure/selectPageBySensorId',
        method: "POST",
        params,
    });
}

//最新一条
export function findSoilNewOne(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/measure/findNewOne',
        method: "POST",
        params,
    });
}

//图表
export function findSoilForChart(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/measure/findForChart',
        method: "POST",
        params,
    });
}

//获取当前苗情-平安乡村监控列表
export function findVideoList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/video/getStyleList',
        method: "POST",
        params,
    });
}
//视频管理-获取直播流
export function findVideoPath(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/jovision/getRealtimeUrlByDeviceSn',
        method: 'GET',
        params,
    })
}
//云台控制
export function ptzControl(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/jovision/ptzControl',
        method: "POST",
        params,
    });
}