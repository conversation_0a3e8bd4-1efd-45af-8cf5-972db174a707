import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import esbuild from 'rollup-plugin-esbuild'
export default defineConfig({
  plugins: [uni(),
  // 解决钉钉H5不支持可选链语法问题
  {
    ...esbuild({
      target: 'chrome70',
      include: [/\.vue$/,/\.js$/],
      loaders:{
        '.vue':'js'
      }
    }),
    enforce:'post'
  }],
  // 配置代理
  server: {
	   
	      host: 'localhost', // 监听所有地址
	      port: 3000, // 你可以指定一个端口号
	      // 其他服务器配置...
		  strictPort:true
	
  },
  transpileDependencies: ['uview-plus'],
})
