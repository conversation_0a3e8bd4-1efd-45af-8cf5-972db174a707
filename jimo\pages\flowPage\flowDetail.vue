<template>
	<view class="main-container" v-if="!showEmptyImg">
		<view class="form-box">
			<view class="main-title">{{flowTitle}}</view>
			<view class="form-content">
				<from-parser :isPreview="true" :tableFields="tableFields"></from-parser>
			</view>
			<view class="flow-status">
				{{processingStatusName}}
			</view>
		</view>
		<view class="flow-container">
			<view class="main-title">
				流程
			</view>
			<view class="flow-content">
				<view class="flow-content-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">{{applyInfo.name}}</view>
						</view>
						<view class="time">
							{{applyInfo.applyDate}}
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people">
								{{applyInfo.applyPeopleName}}
							</view>
						</view>
					</view>
				</view>
				<view class="flow-content-item" v-for="(item,index) in approverList" :key="index">
					<view class="step-box">
						<view class="title-box">
							<u-image v-if="!!item.auditDate" src="@/jimo/static/images/flowPage/pass.svg"
								width="46rpx" height="46rpx" mode="aspectFit"></u-image>
							<u-image v-else src="@/jimo/static/images/flowPage/handling.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								{{processor}}
							</view>
						</view>
						<view class="time">
							{{item.auditDate}}
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<template v-if="index != approverList.length - 1 || item.auditStatusKey == 'passed'">
									<view class="line"></view>
								</template>
								<template v-else>
									<view class="noline"></view>
								</template>
							</view>
							<view class="people">
								{{item.auditPeopleName}}
							</view>
							<view class="status" :class="{'pass' : item.auditStatusKey == 'passed'}"
								v-if="!!item.auditStatus">
								{{item.auditStatus}}
							</view>
						</view>
					</view>
					<view class="remark-box" v-if="item.remark">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="remark u-line-5">
								{{item.remark}}
							</view>
						</view>
					</view>
					<view class="file-list" v-if="item.imgPath">
						<view class="left-box">
							<view class="line"></view>
						</view>
						<view style="margin-top: 10rpx;">
							<view class="file-item" v-for="file in JSON.parse(item.imgPath)" :key="file.url"
								@click="downLoad(file)">
								<image :src="FILE_IMG" mode="" v-if="file.ex === 'png' || file.ex === 'jpg'"></image>
								<image :src="FILE_WORD" mode="" v-if="file.ex === 'doc' || file.ex === 'docx'"></image>
								<image :src="FILE_EXCEL" mode="" v-if="file.ex === 'xls' || file.ex === 'xlsx'"></image>
								<image :src="FILE_PDF" mode="" v-if="file.ex === 'pdf' || file.ex === 'pdfx'"></image>
								<view class="text" :class="file.ex">{{ file.name }}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="flow-content-item send-item" v-if="flowType == '1' && sendPeopleList.length > 0">
					<view class="step-box">
						<view class="title-box">
							<u-image v-if="processingStatus == 'passed'" src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<u-image v-else src="@/jimo/static/images/flowPage/handling.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								抄送人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="noline"></view>
							</view>
							<view class="people send-people">
								<view class="u-line-1">
									{{sendPeopleList}}
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 工作上报展示转阅人 -->
				<view class="flow-content-item send-item" v-if="flowType == '3' && flowFlag == 'gongzuoshangbao'">
					<view class="step-box">
						<view class="title-box">
							<u-image v-if="processingStatus == 'passed'" src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<u-image v-else src="@/jimo/static/images/flowPage/handling.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								转阅人
							</view>
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="noline"></view>
							</view>
							<view class="people send-people">
								<view class="send-people-content">
									<view class="send-people-item" v-for="(item,index) in refereePeopleList" :key="index">
										{{item.name}}
									</view>
									<u-image v-if="showAddRefereeBtn" src="@/jimo/static/images/flowPage/add.svg" width="48rpx"
										height="48rpx" @click="chooseReferee"></u-image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="submit-container paddingbottom" :class="{'delete-only': isDeleteOnly}" v-if="showHandleBar">
		<view class="delete" @click="delFlow" v-if="showDelBtn">
			删除
		</view>
		<view class="handle-box" v-if='!isDeleteOnly'>
			<view class="operate-left" v-if="flowType != '3'" @click="leftHandle">
				{{leftOperate}}
			</view>
			<view class="operate-right" :class="{'right-only': flowType == '3'}" @click="rightHandle">
				{{rightOperate}}
			</view>
		</view>
	</view>
	<view class="empty-status" v-if="showEmptyImg">
		<view class="empty-icon">
			<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
				height="150rpx"></u-image>
			<view class="no-more">
				<span class="txt">暂无数据</span>
			</view>
		</view>
	</view>
	<u-popup v-if="showRemark" :show="showRemark" mode="bottom" :customStyle="popupStyle" @close="popclosed"
		:overlay='false' @open="showRemark=true" :safeAreaInsetBottom="true" :zIndex='998'>
		<view class="pop-container">
			<div class="comment-container">
				<view class="comment-title" v-if="flowType != '2' || currentOperate == '办理'">
					确认{{currentOperate}}
				</view>
				<view class="comment-title" v-else>
					{{currentOperate}}
				</view>
				<view class="textarea-box">
					<u--textarea class="textarea-content" v-model="remarkData"
						:placeholder="popupPlaceHolder" placeholder-style="color: #B1B1B1"
						:disable-default-padding="true" :show-confirm-bar="false"
						@input="inputHandle($event, 200)" @confirm="inputHandle($event, 200)"
						border="none" maxlength="200" :cursor-spacing="40" count />
					<!-- <view class="fontnum"><text>{{fontnum}}/200</text></view> -->
					<view class="file-list-box">
						<view class="file-item" v-for="(item, index) in fileList" :key="item.url">
							<image :src="FILE_IMG" mode="" v-if="item.ex === 'png' || item.ex === 'jpg'"></image>
							<image :src="FILE_WORD" mode="" v-if="item.ex === 'doc' || item.ex === 'docx'"></image>
							<image :src="FILE_EXCEL" mode="" v-if="item.ex === 'xls' || item.ex === 'xlsx'"></image>
							<image :src="FILE_PDF" mode="" v-if="item.ex === 'pdf' || item.ex === 'pdfx'"></image>
							<view class="text" :class="item.ex">{{ item.name }}</view>
							<u-icon name="close" @click="handleFileDel(index)" />
						</view>
						<u-upload
							accept="all"
							:auto-upload="false" 
							:fileList="[]"
							:mutiple="true"
							uploadIcon="plus"
							@afterRead="afterRead" />
					</view>
				</view>
				<view class="submit-btn" @click="submit" v-if="flowType != '2'">
					确认{{currentOperate}}
				</view>
				<view class="submit-btn" @click="submit" v-else-if="currentOperate == '办理'">
					确认办理
				</view>
				<view class="submit-btn" @click="submit" v-else>
					确认转交
				</view>
			</div>
		</view>
	</u-popup>
	<u-overlay :show="showOverlay" :zIndex='997' @click="hideOverlay"></u-overlay>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import fromParser from "./formParser.vue"
	import {
		getFlowDetail,
		delFlowDetail,
		agreeFlow,
		refuseFlow,
		handleFlow,
		forwardFlow,
		replyFlow,
		reviewFlow,
		getFlowTableFields,
		getIsApplyOrAudit
	} from '../../api/flowPage/flowPage.js'
	import { fileUpload } from '@/common/api'
	import { useUserStore } from '@/store/user.js'
	import { useTokenStore } from '@/store/token.js'
	import {NO_MORE_IMG} from '@/common/net/staticUrl';
	import { setPoints} from '../../api/burialPoint.js'
	import FILE_IMG from '@/jimo/static/images/meeting/img.png'
	import FILE_WORD from '@/jimo/static/images/meeting/word.png'
	import FILE_EXCEL from '@/jimo/static/images/meeting/excel.png'
	import FILE_PDF from '@/jimo/static/images/meeting/pdf.png'
	
	const userStore = useUserStore()
	const tokenStore = useTokenStore()
	const selfPeopleId = ref(userStore.userInfo.customParam.peopleInfo.villagePeopleId)
	const flowId = ref('') // 流程id
	const flowType = ref('') // 流程类型
	const applyId = ref('') // 审批详情id
	const flowTitle = ref('') // 流程标题
	const tableFields = ref([]) // 表单数据
	const applyInfo = ref({}) // 提出人信息
	const processingStatus = ref('')	// 流程状态
	const processingStatusName = ref('') // 流程状态状态名
	const processor = ref('') // 处理人类别标题
	const nextNodeId = ref('') // 下一处理人
	const approverList = ref([]) // 处理人信息
	const sendPeopleList = ref([]) // 抄送人信息
	const showHandleBar = ref(false) // 是否展示操作栏
	const isDeleteOnly = ref(true) //	是否只展示删除按钮
	const showDelBtn = ref(false) // 是否展示删除按钮
	const showRemark = ref(false) // 展示操作意见弹窗
	const remarkData = ref('') // 审批意见
	const focus = ref(true) // 输入框获取焦点
	const leftOperate = ref('') // 左操作按钮名
	const rightOperate = ref('') // 右操作按钮名
	const currentOperate = ref('') // 当前执行的操作
	const popupTitle = ref('') // 弹窗标题
	const popupPlaceHolder = ref('') // 弹窗输入框占位符
	const fileList = ref([])
	const popupBtn = ref('') // 弹窗提交按钮名称
	const showOverlay = ref(false) // 是否展示遮罩
	const showEmptyImg = ref(false)
	const formName = ref('') // 页面标题

	const popupStyle = ref({
		width: '100%',
		borderTopLeftRadius: '8rpx',
		borderTopRightRadius: '8rpx'
	})
	const refereePeopleList = ref([]) // 接收转阅人数据
	const flowFlag = ref('')
	const showAddRefereeBtn = ref(false) // 提出人和审阅人展示增加转阅人按钮
	onLoad((options) => {
		// 获取流程Id
		if(!!options.flowId) {
			flowId.value = options.flowId
		}
		// 获取审批详情id
		if(!!options.applyId) {
			applyId.value = options.applyId
		}
		// 设置页面标题
		if(!!options.formName) {
			formName.value = options.formName
			uni.setNavigationBarTitle({
				title: options.formName
			});
			if (options.formName == '事件上报') {
				// 埋点-进入事件列表详情
				let param = {
					eventId : 'enter_event_detail',
					attributeValue: '/jimo/pages/flowPage/flowDetail'
				}
				setPoints(param)
			}
		}
		// 获取流程详情
		getFlowInfo()
		getFlowTable()
	})
	// 获取流程详情
	function getFlowInfo() {
		uni.showLoading({
		  title: '加载中...',
		  mask: true,
		})
		let param = {
			applyId: applyId.value
		}
		getFlowDetail(param).then((res) => {
			if (res && res.success) {
				tableFields.value = JSON.parse(res.data.formJsonStr);
				console.log('tableFields.value', tableFields.value)
				flowType.value = res.data.flowType; // flowType 1 审批类 2 办理类 3 新建类
				flowTitle.value = res.data.applyName;
				processor.value = res.data.processor;
				approverList.value = res.data.auditInfo.auditInfoList;
				applyInfo.value = res.data.applyInfo;
				processingStatusName.value = res.data.processingStatusName
				processingStatus.value = res.data.processingStatus
				sendPeopleList.value = res.data.sendPeoples;
				nextNodeId.value = res.data.nextNodeId;
				refereePeopleList.value = res.data.referPeoples
				isShowRefAdd()
				handleOptionBar(res.data)
			} else {
				showEmptyImg.value = true;
				uni.showToast({
					title: "流程不存在，无法查看详情",
					icon:"none",
					duration:2000,
					success() {
						setTimeout(()=> {
							uni.navigateBack()
						}, 2000)
					}
				})
			}
			uni.hideLoading()
		}).catch((err)=> {
			console.log(err);
			showEmptyImg.value = true;
			uni.showToast({
				title: "流程不存在，无法查看详情",
				icon:"none",
				duration:2000,
				success() {
					setTimeout(()=> {
						uni.navigateBack()
					}, 2000)
				}
			})
			uni.hideLoading()
		})
	}
	// 处理操作栏的展示逻辑
	function handleOptionBar(flowInfo) {
		// 审批类的
		if (flowInfo.flowType == '1') {
			leftOperate.value = '拒绝'
			rightOperate.value = '同意'
			popupPlaceHolder.value = '请输入审批意见'
		} else if (flowInfo.flowType == '2') {
			// 办理类的
			leftOperate.value = '转交'
			rightOperate.value = '办理'
		} else if (flowInfo.flowType == '3') {
			// 信件类的
			leftOperate.value = '删除'
			if (flowInfo.auditInfo.name == '审阅人') {
				rightOperate.value = '审阅'
				popupPlaceHolder.value = '请输入审阅内容'
			} else {
				rightOperate.value = '回复'
				popupPlaceHolder.value = '请输入回复内容'
			}
		}
		// 根据流程信息判断操作按钮的展示
		// 判断自己在不在处理人中
		let isHandler = flowInfo.auditInfo.auditInfoList.find((item) => {
			return item.auditPeopleId == selfPeopleId.value
		})
		let currentHandler = flowInfo.auditInfo.auditInfoList.find((item) => {
			return item.auditDate == ''
		})
		// 如果流程已结束，则不展示操作栏 nextNodeId是下一节点的id，已结束的流程没有下一节点了
		if (!flowInfo.nextNodeId) {
			showHandleBar.value = false;
			return;
		}
		// 如果当前流程是自己发起的
		if (selfPeopleId.value == flowInfo.applyInfo.applyPeopleId) {
			// 如果流程未开始执行（提出了还没有人审批过）且当前处理人不是自己，则只展示删除按钮
			if (!flowInfo.auditInfo.auditInfoList[0].auditDate &&
				flowInfo.auditInfo.auditInfoList[0].auditPeopleId != selfPeopleId.value) {
				showHandleBar.value = true;
				showDelBtn.value = true;
				isDeleteOnly.value = true;
			} else if (!flowInfo.auditInfo.auditInfoList[0].auditDate &&
				flowInfo.auditInfo.auditInfoList[0].auditPeopleId == selfPeopleId.value) {
				// 自己发起的且自己是第一个处理人且当前节点是自己，则展示删除同意拒绝按钮
				showHandleBar.value = true;
				showDelBtn.value = true;
				isDeleteOnly.value = false;
			} else if (flowInfo.auditInfo.auditInfoList[0].auditDate &&
				currentHandler.auditPeopleId != selfPeopleId.value) {
				// 自己发起的且流程已开始执行（已经有审批人审批过），且当前处理人不是自己，则不展示操作按钮
				showHandleBar.value = false;
			} else if (!!flowInfo.auditInfo.auditInfoList[0].auditDate &&
				currentHandler.auditPeopleId == selfPeopleId.value) {
				// 自己发起的且流程已开始执行（已经有审批人审批过），且当前处理人是自己，则展示同意拒绝
				showHandleBar.value = true;
				showDelBtn.value = false;
				isDeleteOnly.value = false;
			}
		} else {
			// 发起人不是自己，自己是当前处理人，则展示同意拒绝
			if (currentHandler.auditPeopleId == selfPeopleId.value) {
				showHandleBar.value = true;
				showDelBtn.value = false;
				isDeleteOnly.value = false;
			} else {
				showHandleBar.value = false;
			}
		}
	}
	// 删除申请
	function delFlow() {
		let params = {
			applyId: applyId.value
		}
		uni.showModal({
			title: '提示',
			content: '确定删除吗',
			success: function(res) {
				if (res.confirm) {
					uni.showLoading({
						title: '删除中...',
						mask: true,
					})
					delFlowDetail(params).then(res => {
						if (res.success) {
							uni.hideLoading();
							uni.showToast({
								title: "删除成功",
								icon: 'none',
								duration: 2000
							});
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '删除失败，请稍后再试',
								duration: 2000,
								icon: 'none'
							})
						}
					})
				} else if (res.cancel) {}
			}
		});
	}
	const nodePeopleId = ref('')
	// 左侧按钮操作
	function leftHandle() {
		console.log('formName.value-----', formName.value);
		if (formName.value == '事件上报') {
			// 埋点-点击事件上报-转交按钮
			let param = {
				eventId : 'click_transfer_button',
				attributeValue: "transfer_btn"
			}
			setPoints(param)
		}
		currentOperate.value = leftOperate.value
		if (flowType.value == '2') {
			popupPlaceHolder.value = '请输入转交意见'
			uni.$once('chooseMemberCallback', (data) => {
				console.log('转交人---', data);
				nodePeopleId.value = data[0].peopleId
				currentOperate.value = '转交给' + data[0].name;
				showRemark.value = true
				showOverlay.value = true
			})
			uni.navigateTo({
				url: '/jimo/pages/chooseMember/chooseMember?flowId=' + flowId.value + '&chooseType=single' +
					'&operate=transmit'
			})
		} else {
			showRemark.value = true
			showOverlay.value = true
		}
	}
	// 右侧按钮操作
	function rightHandle() {
		if (formName.value == '事件上报') {
			// 埋点-点击事件上报-办理按钮
			let param = {
				eventId : 'click_handle_button',
				attributeValue: "handle_btn"
			}
			setPoints(param)
		}
		currentOperate.value = rightOperate.value
		if (flowType.value == '2') {
			popupPlaceHolder.value = '请输入办理意见'
		}
		showRemark.value = true
		showOverlay.value = true
	}
	// 提交处理
	function submit() {
		let params = {
			applyId: applyId.value,
			nextNodeId: nextNodeId.value,
			remark: remarkData.value,
			imgPath: JSON.stringify(fileList.value),
			nodePeopleId: ''
		}
		if (currentOperate.value == '同意') {
			uni.showModal({
				title: '提示',
				content: '确定同意吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						agreeFlow(params).then(res => {
							handleRes(res)
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == '拒绝') {
			uni.showModal({
				title: '提示',
				content: '拒绝后流程将结束，确定拒绝吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						refuseFlow(params).then(res => {
							handleRes(res)
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value.includes('转交')) {
			params.nodePeopleId = nodePeopleId.value;
			if (formName.value == '事件上报') {
				// 埋点-点击确认转交按钮
				let param = {
					eventId : 'confirm_transfer',
					attributeValue: "confirm_transfer_btn"
				}
				setPoints(param)
			}
			console.log('转交的params----', params);
			uni.showModal({
				title: '提示',
				content: '确定转交吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						forwardFlow(params).then(res => {
							if (res.success) {
								if (formName.value == '事件上报') {
									// 埋点-事件上报转交成功
									let param = {
										eventId : 'event_reporting_transferred',
										attributeValue: params.applyId
									}
									setPoints(param)
								}
							} else {
								if (formName.value == '事件上报') {
									// 埋点-事件上报转交失败
									let param = {
										eventId : 'event_reporting_transferred',
										attributeValue: params.applyId
									}
									setPoints(param)
								}
							}
							handleRes(res)
						}).catch(() => {
							if (formName.value == '事件上报') {
								// 埋点-事件上报转交失败
								let param = {
									eventId : 'event_report_transfer_fail',
									attributeValue: params.applyId
								}
								setPoints(param)
							}
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == '办理') {
			if (formName.value == '事件上报') {
				// 埋点-点击确认办理按钮
				let param = {
					eventId : 'confirm_handle',
					attributeValue: "confirm_handle_btn"
				}
				setPoints(param)
			}
			uni.showModal({
				title: '提示',
				content: '确定办理吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						handleFlow(params).then(res => {
							if (res.success) {
								if (formName.value == '事件上报') {
									// 埋点-事件上报办理成功
									let param = {
										eventId : 'event_reporting_handled',
										attributeValue: params.applyId
									}
									setPoints(param)
								}
							} else {
								if (formName.value == '事件上报') {
									// 埋点-事件上报办理失败
									let param = {
										eventId : 'event_reporting_handled',
										attributeValue: params.applyId
									}
									setPoints(param)
								}
							}
							handleRes(res)
						}).catch(() => {
							if (formName.value == '事件上报') {
								// 埋点-事件上报办理失败
								let param = {
									eventId : 'event_report_handle_fail',
									attributeValue: params.applyId
								}
								setPoints(param)
							}
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == '回复') {
			uni.showModal({
				title: '提示',
				content: '确定回复吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						replyFlow(params).then(res => {
							handleRes(res)
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == '审阅') {
			uni.showModal({
				title: '提示',
				content: '确定审阅吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						reviewFlow(params).then(res => {
							handleRes(res)
						})
					} else if (res.cancel) {}
				}
			});
		}
	}
	// 处理接口返回结果
	function handleRes(res) {
		if (res.success) {
			uni.hideLoading();
			uni.showToast({
				title: "处理成功",
				icon: 'none',
				duration: 2000
			});
			setTimeout(() => {
				uni.navigateBack()
			}, 1000)
		} else {
			uni.hideLoading();
			uni.showToast({
				title: res.message || '处理失败，请稍后再试',
				duration: 2000,
				icon: 'none'
			})
		}
	}
	// 点击遮罩关闭
	function hideOverlay() {
		remarkData.value = ''
		showOverlay.value = false
		showRemark.value = false
		fontnum.value = 0
	}
	// 关闭pop
	function popclosed() {
		remarkData.value = ''
		focus.value = false
		showRemark.value = false
		fontnum.value = 0
	}
	// 输入审批意见
	const _inputTimer = ref(null)
	const fontnum = ref(0)
	function inputHandle(e, maxLength) {
	    clearTimeout(_inputTimer.value)
	    _inputTimer.value = setTimeout(() => {
	        let {
	            value: inputVal
	        } = e.detail
	        if (inputVal.length > maxLength) {
	            remarkData.value = inputVal.slice(0, maxLength)
	            fontnum.value = maxLength
	        } else {
	            remarkData.value = inputVal
	            fontnum.value = inputVal.length
	        }
	    }, 100)
	}
	
	/**
	 * @description 上传审批文件
	 */
	const afterRead = async (event) => {
		if (fileList.value.length > 5) {
			uni.showToast({ title: '文件限制上传6个', icon: 'none' })
			return
		}
		
		const { url, size, type, name } = event.file
		const ex = url.split('.').pop().toLowerCase()
		
		const fileTypes = ['doc', 'docx', 'pdf', 'pdfx', 'xls', 'xlsx', 'jpg', 'png']
		
		if (fileTypes.findIndex(type => type === ex) > -1) {
			if (size > 5 * 1024 * 1024) {
				uni.showToast({ title: '文件限制上传5M', icon: 'none' })
				return
			}
		} else {
			uni.showToast({ title: '文件格式错误', icon: 'none' })
			return
		}
		const params = {
			filePath: url,
			formData: { isAnonymous: 1 }	
		}
		fileUpload(params).then(r => {
			if (r.success) {
				fileList.value.push({ url: r.data.url, type, ex, name })
			}
		})
	}
	/**
	 * @description 文件删除
	 */
	const handleFileDel = (index) => {
		fileList.value.splice(index, 1)
	}

	const downLoad = (file) => {
		if (file.ex === 'png' || file.ex === 'jpg') {
			uni.previewImage({
				urls: [file.url],
			})
			return
		}
		uni.downloadFile({
			header: {
				'Authorization': tokenStore.tokenType + tokenStore.value
			},
			url: file.url,
			success: (data) => {
				let filePath = data.tempFilePath
				
				// #ifdef MP-WEIXIN
				uni.openDocument({
					filePath: filePath,
					showMenu: true,
					fileType: file.ex,
					success: function(res) {
						console.log(res)
					},
					fail: (err) => {
						if (err.errMsg.indexOf('fail filetype not supported')>-1) {
							uni.showToast({
							  title: '文件类型不支持',
							  icon:'none'
							})
						}
						console.log('打开失败：',err.errMsg)
					}
				})
				// #endif
				// #ifdef APP-PLUS
				plus.runtime.openFile(filePath, (err) => {
					uni.showModal({
						content: '无法打开此类型的文件',
						icon:'none'
					})
				})
				// #endif
			}
		})
	}
	// 获取流程信息
	function getFlowTable(){
		let params = {
			flowId: flowId.value
		}
		getFlowTableFields(params).then(res => {
			if (res && res.success) {
				flowFlag.value = res.data.flowFlag
			}
		})
	}
	// 选择转阅人(多选)
	function chooseReferee(){
		uni.$once('chooseMemberCallback', (data) => {
			console.log('chooseMemberCallback', data);
			refereePeopleList.value = data;
		})
		let choosedMembers = JSON.stringify(refereePeopleList.value)
		// flowFlag工作上报传, isEditReferee为1代表修改转阅人
		uni.navigateTo({
			url: '/jimo/pages/chooseMember/chooseMember?flowId=' + flowId.value + '&chooseType=multi' +
				'&choosedMembers=' + choosedMembers + '&eventType=' + '&flowFlag=' + flowFlag.value + '&isEditReferee=1' + 
				'&applyPeopleId=' + applyInfo.value.applyPeopleId + '&applyId=' + applyId.value
		})
	}
	function isShowRefAdd(){
		getIsApplyOrAudit({
			applyId: applyId.value,
			flowId: flowId.value
		}).then(res => {
			if(res.success){
				showAddRefereeBtn.value = res.data
			}
		})
	}
</script>

<style lang="scss" scoped>
	.main-container {
		box-sizing: border-box;
		padding: 20rpx;
		// margin-bottom: 125rpx;
		width: 100%;
		height: 100%;
		padding-bottom: calc(env(safe-area-inset-bottom) + 135rpx);
		.form-box {
			position: relative;
			padding: 20rpx;
			margin-bottom: 20rpx;
			box-sizing: border-box;
			background: #fff;
			border-radius: 20rpx;

			.main-title {
				// margin-left: 20rpx;
				font-size: 30rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #333333;
			}

			.flow-status {
				position: absolute;
				right: 0;
				top: 30rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 130rpx;
				height: 46rpx;
				border-top-left-radius: 30rpx;
				border-bottom-left-radius: 30rpx;
				background: #0BBD88;
				font-size: 26rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
			}
		}

		.flow-container {
			padding: 20rpx;
			box-sizing: border-box;
			background: #fff;
			border-radius: 20rpx;

			.flow-content-item {
				margin-top: 20rpx;
				width: 100%;
				min-height: 118rpx;

				.step-box,
				.people-box {
					display: flex;
					justify-content: space-between;
					align-items: center;

				}

				.people-box {
					min-height: 72rpx;

					.left-box {
						display: flex;
						justify-content: center;
						width: 46rpx;

						.line {
							width: 2rpx;
							height: 72rpx;
							background: #DDDDDD;
						}

						.noline {
							width: 2rpx;
							height: 72rpx;
							background: transparent;
						}
					}

					.remark {
						flex: 1;
						padding: 8rpx;
						margin-left: 20rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #555555;
						font-size: 24rpx;
						background: #f2f2f2;
    
						.noline {
							height: auto;
						}
					}
					
					.people {
						margin-left: 20rpx;
						font-size: 26rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #555555;
					}

					.send-people {
						// width: 100%;
						.send-people-content {
							display: flex;
							justify-content: flex-start;
							// align-items: flex-end;
							align-items: center;
							flex-flow: wrap;
							.send-people-item {
								margin-right: 20rpx;
							}
						}
					}

					.status {
						display: flex;
						justify-content: center;
						align-items: center;
						margin-left: 20rpx;
						width: 103rpx;
						height: 42rpx;
						border-radius: 4rpx;
						border: 1rpx solid #FFBD8B;
						font-size: 26rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #FFBD8B;
					}

					.pass {
						color: #28C98E;
						border: 1rpx solid #28C98E;
					}
				}

				.remark-box {
					height: auto;
					
					.title-box {
						align-items: unset !important;
					}

					.left-box {
						display: flex;
						justify-content: center;
						width: 46rpx;

						.line {
							width: 2rpx;
							height: auto;
							background: #DDDDDD;
						}

						.noline {
							width: 2rpx;
							height: auto;
							background: transparent;
						}
					}

					.remark {
						margin-left: 20rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #555555;
						font-size: 24rpx;

						.noline {
							height: auto;
						}
					}
				}
				
				.file-list {
					display: flex;
					
					.left-box {
						display: flex;
						justify-content: center;
						width: 46rpx;
					
						.line {
							width: 2rpx;
							height: auto;
							background: #DDDDDD;
						}
					
						.noline {
							width: 2rpx;
							height: auto;
							background: transparent;
						}
					}
					
					.file-item {
						width: 100%;
						display: flex;
						align-items: center;
						margin-bottom: 5rpx;
						
						.text {
							margin: 0 19rpx 0 6rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 28rpx;
							line-height: 56rpx;
							
							&.png, &.jpg {
								color: #ee8c21;
							}
							&.doc, &.docx {
								color: #21ABEE;
							}
							&.xls, &.xlsx {
								color: rgb(22, 213, 132);
							}
							&.pdf, &.pdfx {
								color: rgb(210, 41, 44);
							}
						}
						
						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.title-box {
					display: flex;
					align-items: center;
					// width: 100%;
					padding-bottom: 1rpx;

					.title {
						margin-left: 20rpx;
						font-size: 26rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						color: #333333;
					}
				}

				.time {
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #999999;
				}
			}

			.send-item {
				height: auto;
			}
		}
	}

	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 2;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		width: 100%;
		height: 125rpx;
		background: #FFFFFF;

		.delete {
			width: 56rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #999999;
		}

		.handle-box {
			display: flex;
			justify-content: space-evenly;

			.operate-right,
			.operate-left {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 280rpx;
				height: 80rpx;
				border-radius: 48rpx;
				font-size: 36rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
			}

			.operate-right {
				margin-left: 20rpx;
				background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
				border: 1rpx solid;
				color: #FFFFFF;
			}

			.operate-left {
				background: #fff;
				border: 1rpx solid #0BBD88;
				color: #0BBD88;
			}
			.right-only {
				margin-left: 0;
				width: 560rpx;
			}
		}
	}

	.delete-only {
		justify-content: flex-start;
		padding-left: 40rpx;
	}

	.pop-container {
		padding: 20rpx;

		.comment-container {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.comment-title {
				margin-bottom: 20rpx;
				font-size: 30rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
			}

			.textarea-box {
				position: relative;
				margin-bottom: 20rpx;
				width: 100%;
				border: 1rpx #dadbde solid;
				border-radius: 4px;
				background-color: #fff;
				
				.textarea-content {
				    width: 100%;
				    box-sizing: border-box;
				    padding: 20rpx 23rpx 30rpx;
				}
				
				.fontnum {
				    position: absolute;
				    right: 5px;
				    bottom: 2px;
				    font-size: 12px;
				    color: #909193;
				    background-color: #ffffff;
				    padding: 1px 4px;
				}
				
				.file-list-box {
					padding: 0 23rpx 20rpx;
					
					.file-item {
						width: 100%;
						display: flex;
						align-items: center;
						margin-bottom: 5rpx;
						
						.text {
							margin: 0 19rpx 0 6rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 28rpx;
							line-height: 56rpx;
							
							&.png, &.jpg {
								color: #ee8c21;
							}
							&.doc, &.docx {
								color: #21ABEE;
							}
							&.xls, &.xlsx {
								color: rgb(22, 213, 132);
							}
							&.pdf, &.pdfx {
								color: rgb(210, 41, 44);
							}
						}
						
						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
			}

			.submit-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 80rpx;
				background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
			}
		}
	}
	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	.empty-status {
	  display: flex;
	  justify-content: center;
	  align-items: center;
	  padding: 50rpx 0;
	
	  .empty-icon {
	    display: flex;
	    flex-direction: column;
	    justify-content: center;
	
	    .no-more {
	      margin-top: 20rpx;
	      display: flex;
	      justify-content: center;
	      margin-top: 10rpx;
	
	      .txt {
	        text-align: center;
	        height: 37rpx;
	        font-size: 26rpx;
	        font-family: Source Han Sans CN, Source Han Sans CN;
	        font-weight: 400;
	        color: #333333;
	        line-height: 37rpx;
	      }
	    }
	  }
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F0F7F7;
	}
</style>