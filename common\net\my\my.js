import {
	request
} from '../request.js'


//查询总积分
export function taskPoints(params) {
	return request({
		url: '/village/taskPointsRecords/getTaskPointsTotalValue',
		method: "GET",
		params,
	});
}

//查询积分加减记录
export function findList(params) {
	return request({
		url: '/village/taskPointsRecords/findPage',
		method: "GET",
		params,
	});
}

//村干部使用积分加减
export function addTask(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/taskPointsRecords/addForVillageLeader',
		method: 'POST',
		params,
	})
}

//村干部积分登记记录查询
export function taskVillage(params) {
	return request({
		url: '/village/taskPointsRecords/findPageForVillageLeader',
		method: "GET",
		params,
	});
}

//新增
export function responsibility(params) {
	if (!params.taskType) return
	params['uniContentType'] = 'json'
	return request({
		url: '/village/taskPointsRecordsFamily/add',
		method: 'POST',
		params,
	}).then(r => {
		if (r.success) {
			uni.showToast({
				title: `获得${r.data}家庭积分`,
				icon: 'none',
				duration: 1500
			})
		}
	})
}