<template>
	<view class="content">
		<image class="bgimg" :src="LOGIN_BG_IMAGES"></image>
		<view class="close-icon" :style="{'top':topIconDistance +'px'}" @click="skip2home"><u-icon name="close"
				color="#000" size="60rpx"></u-icon></view>
		<view class="login-btn">
			<view class="uni-btn-item">
				<!-- #ifdef MP-WEIXIN -->
				<!-- <button class="item-btn" @click="mpWeixinLogin">
					微信一键授权登录
				</button> -->
				<button class="item-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneMpNew">
					手机号快捷登录
				</button>
				<!-- #endif -->
				<!-- #ifdef APP-PLUS -->
				<button class="item-btn" @click="appFastLogin" v-if="!mainStore.hasPreLogin">
					本机号码一键登录
				</button>
				<!-- #endif -->
			</view>
			<view class="other-login">
				<text class="login-text" @click.stop.prevent="jump2Login('captcha')">{{$t('login.captchaLogin')}}</text>
				<text @click.stop.prevent="jump2Login('password')">{{$t('login.pwdLogin')}}</text>
			</view>
		</view>
		<view class="agreement">
			<view class="agree-box" @click="clickCheckbox">
				<u-checkbox-group @change="checkboxChange">
					<u-checkbox shape="circle" :checked="isAgreement" size="28rpx" activeColor="#0CBE88" />
				</u-checkbox-group>
			</view>
			<view class="" style="display: flex; align-items: center">
				<view class="privacyAndAgreement">
					登录即同意
					<text class="sys-text" @click.stop.prevent="toAgreement">《用户协议》</text>
					和
					<text class="sys-text" @click.stop.prevent="toPrivacy">《隐私政策》</text>
				</view>
			</view>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<u-popup :show="mpgetPhonePopup" mode="bottom" @close="mpPopupClose" @open="mpPopupOpen" :round="10">
			<view class="mp-popup">
				<button type="primary" open-type="getPhoneNumber" @getphonenumber="getPhoneMp">
					确认授权
				</button>
			</view>
		</u-popup>
		<u-popup :show="isShowPopup" @close="close" @open="open" mode="center" closeable :round="10">
			<view style="width: 260px; height: 300px; padding: 30px">
				<view v-if="!isTenants" class="bindbox">
					<u--form labelPosition="left" labelWidth="50" :model="modelForm" ref="uForm" :rules="bdRule">
						<u-form-item label="手机号" prop="telephone" borderBottom ref="item1">
							<u--input border="bottom" placeholder="请输入手机号" v-model="modelForm.telephone"
								clearable></u--input>
						</u-form-item>
						<u-form-item label="验证码" prop="verification" borderBottom ref="item1">
							<u-input border="bottom" v-model="modelForm.verification" placeholder="请输入验证码" clearable>
								<template #suffix>
									<view>
										<u-button v-if="codeStatus" text="获取验证码" type="primary" size="mini"
											:customStyle="customBtnStyle" shape="circle" @click="getCaptha"></u-button>
										<u-button v-if="!codeStatus" :disabled="true" :text="codeTime + 's'"
											type="primary" size="mini" :customStyle="customBtnStyle"
											shape="circle"></u-button>
									</view>
								</template>
							</u-input>
						</u-form-item>
						<view class="popupBtn">
							<u-button type="primary" shape="circle" text="确认绑定" @click="submitSure"></u-button>
						</view>
					</u--form>
				</view>
				<view class="tenant-box" v-else>
					<view class="top">
						<view class="title">请选择租户</view>
						<view class="option">
							<u-radio-group v-model="select_tenantId" placement="column" @change="groupChange">
								<u-radio :customStyle="{ marginBottom: '8px' }" v-for="(item, index) in tenantIdList"
									:key="index" :name="item.tenantId" :label="item.tenantName">
								</u-radio>
							</u-radio-group>
						</view>
					</view>

					<view class="btn-bottom">
						<view class="btn-item">
							<u-button shape="circle" sizi="mini" text="取消" @click="tenantCancel"></u-button>
						</view>
						<view class="btn-item">
							<u-button type="primary" shape="circle" sizi="mini" text="确定"
								@click="tenantConfirm"></u-button>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
		<!-- #endif -->
		<u-modal :show="showModal" closeOnClickOverlay :showConfirmButton='false' :showCancelButton='false'
			@close="showModal = false">
			<view class="privacyAndAgreement modal-content">
				请阅读并同意
				<text class="sys-text" @click.stop.prevent="toAgreement">《用户协议》</text>
				和
				<text class="sys-text" @click.stop.prevent="toPrivacy">《隐私政策》</text>
			</view>
			<view class="btn-bar">
				<view class="disAgree" @click="cancelSubmit">
					不同意
				</view>
				<view class="agree" @click="confirmSubmit">
					同意并继续
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		watch
	} from 'vue'
	import { isDev } from '@/common/EnvUtils.js'
	import kvStore from '@/common/store/uniKVStore.js'
	import {
		useTokenStore
	} from '@/store/token.js'
	import {
		useMainStore
	} from '@/store/index.js'
	import {
		useUserStore
	} from '@/store/user.js'
	import {
		useTabBarStore
	} from '@/store/tabbar.js'
	import Platform from '@/common/platform/ePlatform'
	// #ifdef MP-WEIXIN
	import {
		callbackMp,
		getPhoneNumber,
		wechatMpBindMp,
	} from '@/common/net/wechat/wechatMpApi.js'
	// #endif
	import {
		getUserInfo,
		commonResources,
		dingLogin,
		dingAuthLogin,
		getTrarenInfo
	} from '@/common/api.js'
	import {
		accredit,
		wechatlogin,
		sendVerification,
		binding,
	} from '@/common/net/accredit/accredit.js'
	import {
		getQueryVariable
	} from '@/common/uniUtils.js'
	import doEncrypt from '@/common/secure/crypto.js'
	import {
		config
	} from '@/config/config.js'
	// #ifdef H5-DINGTALK
	import * as myDIng from 'dingtalk-jsapi'
	// #endif
	import { LOGIN_BG_IMAGES } from '@/common/net/staticUrl.js'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
	    findInfo
	} from '@/common/net/account/account.js'
	import {
	    DEFAULT_AVATOR_IMAGES
	} from '@/common/net/staticUrl.js'
	const mainStore = useMainStore()
	const tokenStore = useTokenStore()
	const userStore = useUserStore()
	const tabBarStore = useTabBarStore()
	onLoad(() => {
		getTopPosition()
	})
	const tempCode = ref('')
	// #ifdef H5-WEIXIN-SUB
	/* 微信公众号授权登录 */
	if (!tokenStore.isloginOut) {
		weChatH5Login()
	}
	// #endif
	// #ifndef H5-WEIXIN-SUB
	if (Platform != 'MP-WEIXIN') {
		loadExecution()
	}
	// #endif

	// #ifdef APP-PLUS
	var guidePages = ref(true)
	// #endif
	function loadExecution() {
		// #ifdef H5-DINGTALK
		dingH5Login()
		return
		// #endif
		// #ifndef APP-PLUS
		// if (isDev()) {
		// 	jump2Login()
		// 	return
		// }
		// #endif

		// #ifdef APP-PLUS
		// jump2Login()
		/**
		 * 获取本地存储中launchFlag的值
		 * 若存在，说明不是首次启动，直接进入首页；
		 * 若不存在，说明是首次启动，进入引导页；
		 */
		// try {
		//   // 获取本地存储中launchFlag标识
		//   const value = kvStore.get('launchFlag', true)
		//   if (value) {
		//     // launchFlag=true直接跳转到首页
		//     guidePages.value = false
		//     //判断是否有持久化的数据信息
		//     try {
		//       //获取到了存储的信息
		//       let tokenInfo = kvStore.get('tokenInfo', true, 'OBJ')
		//       console.log('获取到的存储信息idex', tokenInfo)
		//       keepUserInfo(tokenInfo)
		//     } catch (e) {
		//       console.log('获取存储信息失败', e)
		//       jump2Login()
		//     }
		//   } else {
		//     // launchFlag!=true显示引导页
		//     kvStore.set('launchFlag', true)
		//     guidePages.value = true
		//   }
		// } catch (e) {
		//   console.log(e)
		//   kvStore.set('launchFlag', true)
		//   guidePages.value = true
		// }
		return
		// #endif
	}

	// 跳过登录，以匿名用户访问首页
	function skip2home() {
		kvStore.set('hasLogin', 0)
		mainStore.isSkipLogin = true
		uni.reLaunch({
			url: '/pages/home/<USER>',
		})
	}
	// 获取关闭图标的位置
	const topIconDistance = ref(0) // 关闭按钮距顶部距离
	function getTopPosition() {
		//获取状态栏高度
		const statusBarHeight = uni.getSystemInfoSync().statusBarHeight
		// #ifdef MP-WEIXIN
		// 获取导航栏的高度（手机状态栏高度 + 胶囊高度 + 胶囊的上下间距）
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		const navBarHeight = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2
		// 计算顶部图标距离
		// topIconDistance.value = statusBarHeight + navBarHeight;
		topIconDistance.value = menuButtonInfo.top;
		// #endif
		// #ifdef APP-PLUS
		topIconDistance.value = statusBarHeight + 44;
		// #endif
	}
	// 跳转到登录页
	function jump2Login(loginType) {
		uni.reLaunch({
			url: '/pages/login/login?loginType=' + loginType,
		})
	}

	var accessToken = ref('') //授权过程中的accessToken
	const uForm = ref(null)
	// 获取用户信息
	async function loginSystem() {
		const res = await wechatlogin({
			accessToken: accessToken.value,
		})
		if (res && res.success) {
			console.log('wechatlogin----',res);
			//存储用户信息以及token 跳转主页
			let result = res.data
			keepUserInfo(result)
			// 保存登录状态，用于拦截器放行 0:未登录 1:已登录
			kvStore.set('hasLogin', 1)
			// 判断token是否已失效跳转，临时方案 0未失效，1已失效
			kvStore.set('isTokenInvalid', 0)
			// 登录成功后移除路由拦截器
			uni.removeInterceptor('navigateTo')
			uni.removeInterceptor('switchTab')
			uni.removeInterceptor('reLaunch')
		} else {
			uni.hideLoading()
			uni.showToast({
				title: res.message,
				duration: 2000,
			})
			// #ifdef H5-WEIXIN-SUB
			loadExecution()
			// #endif
		}
	}

	// #ifdef MP-WEIXIN
	// 微信小程序授权登录
	function mpWeixinLogin() {
		if (!isAgreement.value) {
			showModal.value = true;
			return;
		}
		uni.showLoading({
			title: '登录中...',
			mask: true,
		})
		uni.login({
				provider: 'weixin',
			})
			.then((res) => {
				console.log("uni.login-----------",res)
				if (res && res.code) {
					callbackMp({
						code: res.code,
					}).then((result) => {
						console.log("callbackMp-----------",result)
						if (result && result.success) {
							accessToken.value = result.data.accessToken
							if (result.data.isBinding) {
								// 已经绑定过手机号
								loginSystem()
							} else {
								uni.hideLoading()
								// 绑定手机号
								mpPopupOpen()
							}
						} else {
							uni.hideLoading()
							uni.showToast({
								title: result.message,
								icon: 'none',
							})
						}
					})
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '授权失败',
						icon: 'none',
					})
				}
			})
			.catch((err) => {
				uni.hideLoading()
			})
	}
	var mpgetPhonePopup = ref(false)

	function mpPopupClose() {
		mpgetPhonePopup.value = false
	}

	function mpPopupOpen() {
		mpgetPhonePopup.value = true
	}
	async function getPhoneMp(e) {
		if (e.detail.errMsg != 'getPhoneNumber:ok') {
			mpPopupClose()
			isShowPopup.value = true
			return uni.showToast({
				title: '请绑定手机号',
				icon: 'none',
				duration: 1500,
			})
		}
		const res = await getPhoneNumber({
			code: e.detail.code,
			accessToken: accessToken.value
		})
		mpPopupClose()

		//绑定手机号 此处不用传手机号，后端做了手机号存储
		// const response = await wechatMpBindMp({
		// 	accessToken: accessToken.value,
		// })
		// if (response && response.success) {
		// 	loginSystem()
		// } else {
		// 	uni.hideLoading()
		// 	//绑定失败
		// 	uni.showToast({
		// 		title: response.message,
		// 		icon: 'none',
		// 	})
		// }
	}
	async function getPhoneMpNew(e) {
		console.log("e.detail",e.detail);
		if (e.detail.errMsg != 'getPhoneNumber:ok') {
			uni.showToast({
				title: '获取手机号失败',
				icon: 'none',
				duration: 1500,
			})
			return false;
		}
		tempCode.value = e.detail.code
		console.log('tempCode.value',tempCode.value,e.detail.code);
		if (!isAgreement.value) {
			showModal.value = true;
			return;
		} else {
			wechatLogin()
		}
	}
	// #endif
	function wechatLogin() {
		uni.showLoading({
			title: '登录中...',
			mask: true,
		})
		uni.login({
				provider: 'weixin',
			})
			.then((res) => {
				if (res && res.code) {
					callbackMp({
						code: res.code,
					}).then(async (result) => {
						if (result && result.success) {
							accessToken.value = result.data.accessToken
							if (result.data.isBinding) {
								// 已经绑定过手机号直接登录
								loginSystem()
							} else {
								// 没有绑定过先获取手机号
								console.log('tempCode--',tempCode.value);
								const res = await getPhoneNumber({
									code: tempCode.value,
									accessToken: accessToken.value
								})
								if(res.success) {
									// 获取成功后绑定手机号
									// 此处不用传手机号，后端做了手机号存储
									const response = await wechatMpBindMp({
										accessToken: accessToken.value,
									})
									if (response && response.success) {
										loginSystem()
									} else {
										uni.hideLoading()
										//绑定失败
										uni.showToast({
											title: response.message,
											icon: 'none',
										})
									}
								}
							}
						} else {
							uni.hideLoading()
							uni.showToast({
								title: result.message,
								icon: 'none',
							})
						}
					})
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '授权失败',
						icon: 'none',
					})
				}
			})
			.catch((err) => {
				uni.hideLoading()
			})
	}
	// #ifdef H5-WEIXIN-SUB
	// 微信公众号授权登录
	var globalTenantId = ref('')
	globalTenantId.value = config.tenantId_global
	async function weChatH5Login() {
		// 判断code  后期考虑校验code有效性
		var code = getQueryVariable('code', window.location.href)
		console.log('code', code)
		if (!code) {
			//调用公共资源接口获取配置信息
			const res = await commonResources({
				configCodes: ['wechat_redirect_uri', 'wechat_appid'],
				clientType: '2',
				tenantId: globalTenantId,
			})
			if (res && res.success) {
				var configData = res.data
				var appid = ''
				var redirect_uri = ''
				if (configData[0].configCode == 'wechat_appid') {
					appid = configData[0].configValue
					redirect_uri = configData[1].configValue
				} else {
					appid = configData[1].configCode
					redirect_uri = configData[0].configCode
				}
				//存储微信公众号appid和重定向地址（vuex和缓存）
				mainStore.$patch({
					h5_wx_appId: appid,
					h5_wx_redirect_url: redirect_uri,
				})
				kvStore.set('h5_wx_appId', appid)
				kvStore.set('h5_wx_redirect_url', redirect_uri)
				//跳转授权，并在授权成功后重定向redirect_uri
				window.location.href =
					'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
					appid +
					'&redirect_uri=' +
					encodeURIComponent(redirect_uri) +
					'&response_type=code&scope=snsapi_userinfo&state=http%3A%2F%2F47.98.51.156&connect_redirect=1#wechat_redirect'
			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} else {
			uni.showLoading({
				title: '授权中',
				mask: true,
			})
			const res = await accredit({
				code,
				state: '123',
			})
			if (res && res.success) {
				accessToken.value = res.data.accessToken
				//判断用户是否在我们系统中
				if (res.data.isBinding) {
					//登录
					loginSystem()
				} else {
					uni.hideLoading()
					//弹窗绑定手机号码
					isShowPopup.value = true
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
				// 授权失败跳转登录页
				loadExecution()
			}
		}
	}
	// #endif

	/* 绑定手机号相关逻辑 */
	var isShowPopup = ref(false)

	function open() {}

	function close() {
		isShowPopup.value = false
		isTenants.value = false
	}
	const modelForm = reactive({
		telephone: '',
		verification: '',
	})
	const customBtnStyle = reactive({
		height: '60rpx',
		width: '160rpx',
	})
	var codeTime = ref(10)
	var codeStatus = ref(true)
	var timeInterval
	// 获取手机验证码
	function getCaptha() {
		var myreg = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1})|(17[0-9]{1}))+\d{8})$/
		if (modelForm.telephone == '') {
			uni.showToast({
				title: '请输入您的手机号',
				duration: 2000,
				icon: 'none',
			})
			return
		}
		if (!myreg.test(modelForm.telephone)) {
			uni.showToast({
				title: '手机号格式不正确',
				duration: 2000,
				icon: 'none',
			})
			return
		}
		codeStatus.value = false
		timeInterval = setInterval(() => {
			if (codeTime.value <= 0) {
				clearInterval(timeInterval)
			}
			codeTime.value--
		}, 1000)
		getCode()
	}

	function getCode() {
		// 请求验证码
		sendVerification({
				cellphone: doEncrypt(modelForm.telephone),
			})
			.then((res) => {
				if (res && res.success) {
					return
				} else {
					codeStatus.value = true
					clearInterval(timeInterval)
					uni.showToast({
						title: res.message,
						duration: 2500,
						icon: 'none',
					})
				}
			})
			.catch((err) => {
				codeStatus.value = true
				clearInterval(timeInterval)
			})
	}
	// 验证码按钮倒计时
	watch(codeTime, (newValue) => {
		if (newValue <= 0) {
			clearInterval(timeInterval)
			codeTime.value = 10
			codeStatus.value = true
		}
	})
	const bdRule = reactive({
		telephone: {
			type: 'string',
			required: true,
			message: '请填写手机号',
			trigger: ['blur', 'change'],
		},
		verification: {
			type: 'string',
			required: true,
			message: '请填写验证码',
			trigger: ['blur', 'change'],
		},
	})
	// 手机号、验证码表单提交
	function submitSure() {
		uForm.value.validate().then((res) => {
			binding({
				cellphone: doEncrypt(modelForm.telephone),
				smscode: modelForm.verification, //手机验证码
				accessToken: accessToken.value,
			}).then((result) => {
				if (result && result.success) {
					if (result.data.length > 1) {
						tenantIdList.value = result.data
						select_tenantId.value = result.data[0].tenantId
						//弹窗选择租户
						isTenants.value = true
					} else {
						uni.showToast({
							title: '绑定成功',
							icon: 'none',
						})
						isShowPopup.value = false
						loginSystem()
					}
				} else {
					uni.showToast({
						title: result.message,
						icon: 'error',
					})
				}
			})
		})
	}
	/* 多租户 */
	const tenantIdList = ref([])
	var isTenants = ref(false)
	var select_tenantId = ref()
	var empower = ref(false) //判断是否为自动授权获取手机号登录
	function groupChange(e) {
		select_tenantId.value = e
	}

	function tenantCancel() {
		// 用户取消选择租户
		close()
	}
	async function tenantConfirm() {
		if (select_tenantId.value) {
			if (empower.value) {
				// 自动获取手机号绑定
				const result = await wechatMpBindMp({
					accessToken: accessToken.value,
					tenantId: select_tenantId.value,
				})
				if (result && result.success) {
					close()
					uni.showLoading({
						title: '登录中',
						mask: true,
					})
					loginSystem()
				} else {
					uni.showToast({
						title: '系统繁忙，请稍候再试',
						icon: 'none',
					})
				}
			} else {
				// 手动输入手机号码绑定
				const result = await binding({
					cellphone: doEncrypt(modelForm.telephone),
					smscode: modelForm.verification,
					accessToken: accessToken.value,
					tenantId: select_tenantId.value,
				})
				if (result && result.success) {
					close()
					uni.showLoading({
						title: '登录中',
						mask: true,
					})
					loginSystem()
				} else {
					uni.showToast({
						title: result.message,
						icon: 'none',
					})
				}
			}
		} else {
			uni.showToast({
				title: '请选择一个租户登录',
				icon: 'none',
			})
		}
	}
	// #ifdef H5-DINGTALK
	function dingH5Login() {
		// uni.showLoading({
		// 	title: '登录中',
		// 	mask: true
		// });
		//用户授权成功之后，钉钉在回调地址redirect_uri中拼接authCode
		var authCode = getQueryVariable('authCode', window.location.href)
		console.log('钉钉授权authCode', authCode, window.location.href)
		if (authCode) {
			//获取首页背景标题等配置
			let state = getQueryVariable('state', window.location.href)
			if (state) {
				let stringData = state.split('%2C')
				let cId = stringData[0]
				let aLabel = stringData[1]
				// getDingConfig(cId,aLabel);
			}
			//state参数：后台识别哪个应用
			dingAuthLogin({
					authCode: authCode,
					state: state,
					type: 'enterprise_internal', //区分是企业内部应用(enterprise_internal)与第三方企业应用(enterprise_external)
				})
				.then((res) => {
					console.log('dingAuthLogin', res)
					if (res && res.success) {
						//保存用户信息
						let result = res.data
						uni.hideLoading()
						keepUserInfo(result)
					} else {
						uni.hideLoading()
						uni.showToast({
							title: res.message,
							icon: 'none',
						})
					}
				})
				.catch((e) => {
					console.log(e)
					uni.hideLoading()
				})
		} else {
			//获取应用标识
			var appLabel = getQueryVariable('appLabel', window.location.href)
			console.log('钉钉授权appLabel', appLabel, window.location.href)
			if (appLabel) {
				//获取企业corpId
				let pages = getCurrentPages()
				console.log('钉钉授权corpId', pages[0].$page.options.corpId)
				var corpId = ''
				if (pages[0].$page.options.corpId) {
					corpId = pages[0].$page.options.corpId
					//获取客户端配置
					// getDingConfig(corpId,appLabel);
					// 钉钉H5微应用授权登录
					myDIng.ready(function() {
						//H5微应用获取临时授权码
						myDIng.runtime.permission.requestAuthCode({
							corpId: corpId,
							onSuccess: function(result) {
								console.log('钉钉授权result', result)
								dingLogin({
										appLabel: appLabel,
										corpId: corpId,
										code: result.code,
										type: 'enterprise_internal', //区分是企业内部应用(enterprise_internal)与第三方企业应用(enterprise_external)
									})
									.then((res) => {
										console.log('钉钉授权dingLogin', res)
										//如果code=0,跳转授权（用户首次登陆）
										var redirect_url = ''
										if (res.code == 0) {
											//获取跳转授权后的回调地址
											commonResources({
													configCodes: ['dingH5_redirect_uri'],
													clientType: '2',
													corpId: corpId,
													appLabel: appLabel,
												})
												.then((response) => {
													if (response && response.success) {
														//拼接跳转地址
														redirect_url = response.data[0]
															.configValue
														//存储钉钉redirect_url
														kvStore.set('setDdH5BaseConfig', {
															h5_dd_redirect_url: redirect_uri,
														})
														kvStore.set('h5_dd_redirect_url',
															redirect_uri)
														//授权成功后跳转redirect_uri,state为自需的参数，也会加在redirect_uri后。
														window.location.href =
															'https://login.dingtalk.com/oauth2/challenge.htm?' +
															'redirect_uri=' +
															encodeURIComponent(redirect_url) +
															'&response_type=code&client_id=' +
															res.data.clientId +
															'&scope=openid&state=' +
															corpId +
															',' +
															appLabel +
															'&prompt=consent'
													} else {
														uni.showToast({
															title: '系统异常，请稍候重试',
															icon: 'none',
														})
													}
												})
												.catch((err) => {
													console.log(err)
													uni.showToast({
														title: '系统异常，请稍候重试',
														icon: 'none',
													})
												})
										} else {
											uni.hideLoading()
											//判断登录是否成功，存储用户信息
											if (res && res.success) {
												let result = res.data
												keepUserInfo(result)
											} else {
												uni.showToast({
													title: res.data.message,
													icon: 'none',
												})
											}
										}
									})
									.catch((e) => {
										console.log('钉钉授权dingLogin异常', e)
										uni.hideLoading()
										uni.showToast({
											title: '服务器异常，请稍候再试',
											icon: 'none',
										})
									})
							},
							onFail: function(err) {
								uni.hideLoading()
								uni.showToast({
									title: '授权失败，请重试',
									icon: 'none',
								})
							},
						})
					})
				} else {
					uni.hideLoading()
					uni.showToast({
						title: '授权失败，请稍后重试',
						icon: 'none',
					})
				}
			} else {
				uni.hideLoading()
				uni.showToast({
					title: '授权失败，请稍后重试',
					icon: 'none',
				})
			}
		}
	}
	// #endif
	// 跳转到用户协议
	function toAgreement() {
		uni.navigateTo({
			url: "/pages/generalPage/userAgreement/userAgreement",
		});
	}
	// 跳转到隐私政策
	function toPrivacy() {
		uni.navigateTo({
			url: "/pages/generalPage/privacyPolicy/privacyPolicy",
		});
	}
	// 切换用户协议复选框选中状态
	const isAgreement = ref(false)

	function clickCheckbox() {
		isAgreement.value = !isAgreement.value
	}

	function checkboxChange(checked) {
		if (checked.length == 0) {
			//未选中
			isAgreement.value = false;
		} else {
			//同意协议
			isAgreement.value = true;
		}
	}
	// #ifdef APP-PLUS
	if (mainStore.hasPreLogin) {
		appFastLogin();
	} else {
		jump2Login('captcha')
	}
	// APP端手机号一键登录
	function appFastLogin() {
		// 一键登录已在APP onLaunch的时候进行了预登陆，可以显著提高登录速度。登录成功后，预登陆状态会重置
		uni.login({
			provider: 'univerify',
			success: async (res) => {
				console.log('login success:', res);
				uni.showToast({
					title: '登录成功',
					icon: 'none',
				})
				// 这里需要更新保存在 store 中的登录状态
				// tokenStore.$patch((state) => {
				// 	state.refreshToken = res.refreshToken
				// 	state.tokenType = res.tokenType
				// 	state.value = res.value
				// 	state.expiration = res.expiration
				// 	state.clientId = res.clientId
				// 	state.userid = res.additionalInformation.userid
				// 	state.tenantId = res.additionalInformation.customParam.tenantId
				// })
				// 保存登录状态，用于拦截器放行 0:未登录 1:已登录
				kvStore.set('hasLogin', 1)
				// 判断token是否已失效跳转，临时方案 0未失效，1已失效
				kvStore.set('isTokenInvalid', 0)
				// 登录成功后移除路由拦截器
				uni.removeInterceptor('request')
			},
			fail: (err) => {
				console.log('login fail:', err);
				// 一键登录点击其他登录方式
				if (err.code == '30002') {
					uni.closeAuthView();
					return;
				}

				// 未开通
				if (err.code == 1000) {
					uni.showModal({
						title: '登录失败',
						content: `${err.errMsg}\n，错误码：${err.code}`,
						confirmText: '开通指南',
						cancelText: '确定',
						success: (res) => {
							if (res.confirm) {
								setTimeout(() => {
									plus.runtime.openWeb(
										'https://ask.dcloud.net.cn/article/37965')
								}, 500)
							}
						}
					});
					return;
				}

				// 一键登录预登陆失败 ==>不需要这个处理，因为预登录失败时一键登录按钮不可用
				// if (err.code == '30005') {
				// 	uni.showModal({
				// 		showCancel: false,
				// 		title: '预登录失败',
				// 		content: this.univerifyErrorMsg || err.errMsg
				// 	});
				// 	return;
				// }

				// 一键登录用户关闭验证界面
				if (err.code != '30003') {
					uni.showModal({
						showCancel: false,
						title: '登录失败',
						content: JSON.stringify(err)
					});
				}
			},
			complete: () => {}
		});
	}
	// #endif
	// 确认用户协议弹窗
	const showModal = ref(false)

	function confirmSubmit() {
		isAgreement.value = true;
		showModal.value = false;
		// #ifdef MP-WEIXIN
		wechatLogin()
		// #endif
	}

	function cancelSubmit() {
		isAgreement.value = false;
		showModal.value = false;
	}
	// 存储用户信息
	function keepUserInfo(info) {
	    tokenStore.$patch((state) => {
	        state.refreshToken = info.refreshToken
	        state.tokenType = info.tokenType
	        state.value = info.value
	        state.expiration = info.expiration
	        state.clientId = info.clientId
	        state.userid = info.additionalInformation.userid
	        state.tenantId = info.additionalInformation.customParam.tenantId
	    })
	    // 持久化存储用户登录信息
	    kvStore.set('tokenInfo', info, false, 'OBJ')
	    // 修改登录状态
	    mainStore.$patch((state) => {
	        state.hasLogin = true
	        state.loginProvider = Platform
	    })
	    userStore.userInfo = info.additionalInformation;
	  
		// 如果当前登录人不是游客身份，则根据租户id获取租户信息，当前登录人有多个租户的时候从缓存中取上次登录的那个，没有缓存则取第一个
	    if (info.additionalInformation.customParam.userType != 'Visitor') {
	        let tenantId = ''
	        let lastLoginTenantId = kvStore.get('lastLoginTenantId', true, 'STR')
	        // 缓存中没取到就默认第一个
	        if (!lastLoginTenantId || lastLoginTenantId == 'fail') {
	            tenantId = info.additionalInformation.userJobDetailVOList[0].tenantId;
	        } else {
	            // 缓存中取到了，再判断上次存的在不在这次登录的用户的租户里面，因为可能两次登录的不是同一个账号
	            let tmp = info.additionalInformation.userJobDetailVOList.find((item) => {
	                return item.tenantId == lastLoginTenantId
	            })
	            if (!tmp) {
	                tenantId = info.additionalInformation.userJobDetailVOList[0].tenantId;
	            } else {
	                tenantId = lastLoginTenantId
	            }
	        }
	        getTrarenInfo({
	                tenantId: tenantId,
	            })
	            .then((res) => {
	                if (res.success) {
	                    // 持久化存储用户登录信息
	                    kvStore.set('tokenInfo', res.data, false, 'OBJ')
	                    // 更新token
	                    tokenStore.value = res.data.value;
	                    // 保存个人信息
	                    userStore.userInfo = Object.assign(userStore.userInfo, res.data.additionalInformation)
	                    tokenStore.tenantId = res.data.additionalInformation.customParam.tenantId
	                    let peopleId = res.data.additionalInformation.customParam.peopleInfo.villagePeopleId
	                    findData(peopleId)
	                    // 如果当前用户在当前租户下有多个角色，则判断是否有干部角色，有的话默认展示干部页
	                    for (let i = 0; i < res.data.additionalInformation.authorityList.length; i++) {
	                        let item = res.data.additionalInformation.authorityList[i];
	                        if (item.roleId == 'village_cadres' || item.roleId == 'town_cadres') {
	                            //设置干部角色的tabbar
	                            tabBarStore.role = 'cadreTabs';
	                            // 保存当前用户角色
	                            userStore.currentRole = item.roleId;
	                            userStore.isCadre = true;
	                            break;
	                        } else {
	                            // 不是村干部也不是镇干部，展示村民页
	                            tabBarStore.role = 'villagerTabs';
	                            userStore.currentRole = 'villager';
	                            userStore.isCadre = false;
	                        }
	                    }
	                    let roleInfo = {
	                        tenantId: res.data.additionalInformation.customParam.tenantId,
	                        role: userStore.currentRole,
	                        isCadre: userStore.isCadre
	                    }
	                    kvStore.set('roleInfo', roleInfo, false, 'OBJ')
	                    //在这里进行极光推送的绑定
	                    // #ifdef APP-PLUS
	                    // jpushMobile(); TODO
	                    // #endif
	                    uni.hideLoading()
	                    uni.showToast({
	                        title: '登录成功',
	                        duration: 1000,
	                        success: () => {
	                            setTimeout(() => {
	                                jump2Home()
	                            }, 1000)
	                        },
	                    })
	                } else {
	                    uni.hideLoading()
	                    uni.showToast({
	                        title: "登录失败",
	                        icon: 'none',
	                        duration: 2000
	                    })
	                    return false;
	                }
	            })
	            .catch((err) => {
	                uni.hideLoading()
	                console.log(err)
	            })
	    } else {
	        uni.hideLoading()
	        // 游客，展示村民页
	        tabBarStore.role = 'villagerTabs';
	        userStore.currentRole = 'villager';
	        userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
	        userStore.userInfo.nickName = '段泊岚镇农综平台用户'
	        userStore.isCadre = false;
	        let roleInfo = {
	            tenantId: "",
	            role: 'villager',
	            isCadre: false
	        }
	        kvStore.set('roleInfo', roleInfo, false, 'OBJ')
	        // 把用户头像和昵称也放在tokenInfo中缓存起来
	        let peopleInfo = {
	            headPhoto: DEFAULT_AVATOR_IMAGES,
	            nickName: '段泊岚镇农综平台用户'
	        }
	        kvStore.set('peopleInfo', peopleInfo, false, 'OBJ')
	        uni.showToast({
	            title: '登录成功',
	            duration: 1000,
	            success: () => {
	                setTimeout(() => {
	                    jump2Home()
	                }, 1000)
	            },
	        })
	    }
	}
	// 跳转首页
	function jump2Home() {
		let redirectUrl = mainStore.login_redirect_url;
		if (!!redirectUrl) {
			uni.reLaunch({
				url:redirectUrl
			})
		} else {
			// uni.reLaunch({
        	//     url: '/pages/home/<USER>',
        	// })
            if( userStore.userInfo.authorityList.find(item=>item.roleId=='town_admin')){
            uni.reLaunch({
                url: '/jimo/anquanjianguan/adminstrator/home'

            })
            return 
        }
        else if(userStore.userInfo.authorityList.find(item=>item.roleId=='town_supervisor')){
         uni.reLaunch({
                url: '/jimo/anquanjianguan/companyList/companyList'

            })
        }
        else{
          uni.reLaunch({
        	    url: '/pages/home/<USER>',
        	})  
        }
		}
	}
	//获取用户的昵称和头像
	function findData(peopleId) {
	    let params = {
	        peopleId: peopleId,
	    }
	    findInfo(params).then(res => {
	        if (res && res.success) {
	            console.log(res.data, '昵称头像')
	            if (!res.data) {
	                userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
	                userStore.userInfo.nickName = '段泊岚镇农综平台用户'
	            } else if (res.data.headPhoto && res.data.nickname == "") {
	                userStore.userInfo.headPhoto = res.data.headPhoto
	                userStore.userInfo.nickName = '段泊岚镇农综平台用户'
	            } else if (res.data.nickname && res.data.headPhoto == "") {
	                userStore.userInfo.nickName = res.data.nickname
	                userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
	            } else if (res.data.nickname && res.data.headPhoto) {
	                userStore.userInfo.headPhoto = res.data.headPhoto
	                userStore.userInfo.nickName = res.data.nickname
	            }
	        } else {
	            userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
	            userStore.userInfo.nickName = '段泊岚镇农综平台用户'
	        }
	        // 把用户头像和昵称也放在tokenInfo中缓存起来
	        let peopleInfo = {
	            headPhoto: userStore.userInfo.headPhoto,
	            nickName: userStore.userInfo.nickName
	        }
	        kvStore.set('peopleInfo', peopleInfo, false, 'OBJ')
	    }).catch(err => {
	        console.log(err);
	        userStore.userInfo.headPhoto = DEFAULT_AVATOR_IMAGES
	        userStore.userInfo.nickName = '段泊岚镇农综平台用户'
	    })
	}
</script>

<style lang="scss" scoped>
	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.close-icon {
		position: absolute;
		// top: 60rpx;
		left: 20rpx;
	}

	.popup {
		width: 500rpx;
		height: 550rpx;
		border-radius: 30rpx;
		background-color: #ffffff;
	}

	.title {
		position: relative;
		display: flex;
		justify-content: center;
		padding-top: 30rpx;
	}

	.title-left {
		font-size: 32rpx;
	}

	.login-btn {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 200rpx;
		height: 100%;
		width: 100%;
	}

	.uni-btn-item {
		width: 80%;
		margin: 0 60rpx 0 60rpx;
		margin-bottom: 30rpx;

		.item-btn {
			margin-top: 20rpx;
			// width: 100%;
			margin: 0 15rpx 0 15rpx;
			border-radius: 48rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			font-size: 36rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 96rpx;
		}
	}

	.other-login {
		font-weight: 400;
		color: #0CBE88;
		font-size: 24rpx;
		line-height: 24rpx;

		.login-text {
			display: inline-block;
			padding-right: 20rpx;
			margin-right: 20rpx;
			border-right: 1px solid;
		}
	}


	.bindbox {
		margin-top: 50rpx;
	}

	.tenant-box {
		height: 560rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.top {
			.title {
				margin: 0 auto;
				font-weight: 600;
			}

			.option {
				margin-top: 10rpx;
			}
		}

		.btn-bottom {
			display: flex;
			justify-content: space-between;

			.btn-item {
				width: 200rpx;
			}
		}
	}

	.popupBtn {
		width: 400rpx;
		margin: 100rpx auto 0;
	}

	.close1 {
		font-size: 28rpx;
		position: absolute;
		top: 30rpx;
		right: 30rpx;
	}

	.mp-popup {
		height: 400rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;

		button {
			width: 200rpx;
		}
	}

	.hint {
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 26rpx;
		margin-top: 30rpx;
	}

	.iphoneForm {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 40rpx;
	}

	.iphoneX {
		width: 300rpx;
		height: 2rpx;
		margin-left: 60rpx;
		background-color: #000000;
		opacity: 0.05;
	}

	.gain {
		display: flex;
		margin-top: 20rpx;
	}

	.iphoneInput {
		margin-left: 80rpx;
		font-size: 26rpx;
	}

	.gainX {
		width: 300rpx;
		height: 2rpx;
		background-color: #000000;
		opacity: 0.05;
		margin-left: 60rpx;
	}

	.verification {
		display: flex;
		justify-content: center;
		align-items: certer;
		color: #007aff;
		width: 150rpx;
		height: 50rpx;
		border-radius: 50rpx;
		font-size: 26rpx;
		border: 1rpx solid #007aff;
	}

	.verificationText {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.SureBtn {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 200rpx;
		height: 50rpx;
		border-radius: 25rpx;
		margin-top: 30rpx;
		font-size: 26rpx;
	}

	.lia {
		width: 325rpx;
		height: 50rpx;
		margin-left: 80rpx;
		border-bottom: 1rpx solid #f1f1f1;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		justify-content: space-between;
	}

	.shuruqian {
		height: 50rpx;
		width: 60%;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
	}

	.shuruhou {
		height: 50rpx;
		width: 60%;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #333333;
	}

	.huoqu {
		width: 160rpx;
		margin-left: 30rpx;
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #2d8bff;
	}

	.huoquzhong {
		font-size: 26rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.radioGroup-box {
		background-color: #fff;
		width: 500rpx;
		height: 600rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.radio-button {
		margin-top: 40rpx;
		display: flex;
		flex-direction: row;
	}

	.close2 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 45rpx;
		height: 45rpx;
		font-size: 32rpx;
		color: #000000;
	}

	.bgimg {
		// position: absolute;
		// top: 0;
		// left: 0;
		width: 100%;
		height: 400rpx;
	}

	.agreement {
		display: flex;
		position: absolute;
		bottom: 74rpx;
	}

	.agree-box {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-right: 10rpx;
		width: 100rpx;
		height: 100rpx;
	}

	::v-deep radio-group label,
	checkbox-group label {
		padding-right: 0;
	}

	.layers-item-selector {
		outline: none;
		width: 32rpx;
		height: 38rpx;
		border-radius: 16rpx;
		margin-right: 12rpx;
	}

	.privacyAndAgreement {
		font-size: 24rpx;
		font-weight: 400;
		color: #000000;
		line-height: 37rpx;
	}

	.sys-text {
		color: #0CBE88;
	}

	.modal-content {
	    padding: 0 20rpx;
	    font-size: 32rpx;
	    font-family: PingFangSC, PingFang SC;
	    font-weight: 500;
	    color: #111111;
	    line-height: 46rpx;
	}
	
	.btn-bar {
	    display: flex;
	    justify-content: space-evenly;
	    align-items: center;
	    margin-top: 60rpx;
	
	    .disAgree,
	    .agree {
	        display: flex;
	        justify-content: center;
	        align-items: center;
	        width: 276rpx;
	        height: 80rpx;
	        border-radius: 41rpx;
	        border: 2rpx solid #E5E5E5;
	        font-size: 28rpx;
	        font-family: PingFangSC, PingFang SC;
	        font-weight: 400;
	    }
	
	    .disAgree {
	        background: #FFFFFF;
	        color: #555555;
	    }
	
	    .agree {
	        background: #0CBE88;
	        color: #FFFFFF;
	    }
	}
	.content ::v-deep .u-modal__content {
	    display: flex;
	    flex-direction: column;
	}
	
	.content ::v-deep .u-modal__content {
	    padding-left: 20rpx;
	    padding-right: 20rpx;
	}
</style>