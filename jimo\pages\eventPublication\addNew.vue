<template>
	<view class="formcontainer">
		<view class="form">
			<view class="label">工作标题</view>
			<u--input v-model="workform.missionName" border="none" maxlength="200"
				:placeholderClass="showtitletiptip ? 'redplaceholder' : ''" class="u-border-bottom"
				placeholder="请输入工作标题"></u--input>
			<view class="label">工作类别</view>
			<view class="cell u-border-bottom" @click="showworkType = true">
				<u--input v-model="missionTypeName" disabled disabledColor="#ffffff"
					:placeholderClass="showtypetip ? 'redplaceholder' : ''" placeholder="请选择工作类别"
					border="none"></u--input>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view class="label">工作要求</view>
			<u-textarea :maxlength="200" :count="true" v-model.trim="workform.missionContent" placeholder="请输入内容"
				:placeholderClass="showcontenttip ? 'redplaceholder' : ''" :height="90"
				:disableDefaultPadding='true'></u-textarea>
			<view class="label">完成时限</view>
			<view class="cell u-border-bottom" @click="showdate = true">
				<u--input v-model="workform.completeTime" disabled
					:placeholderClass="showtimettip ? 'redplaceholder' : ''" disabledColor="#ffffff"
					placeholder="请选择完成时限" border="none"></u--input>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view class="label">图片</view>
			<view class="imgcell">
				<view class="imgitem" v-for="(item, index) in imglist" :key="item">
					<!-- <image class="img" :src="item" @click="previewHandler(index, imglist)"></image> -->
					<view class="img">
						<safe-image :src="item" width='219rpx' height='164rpx'></safe-image>
					</view>
					<image class="closebtn" :src="CLOSE_BTN" @click="deleteHanlder(item)"></image>

				</view>
				<image :src="IMG_ADD" mode="widthFix" v-show="showaddimg" style="
            width: 48rpx;
            height: 48rpx;
            padding: 20rpx;
          " @click="uploadimgHandler"></image>
			</view>
			<!--  #ifdef  APP-PLUS -->


			<!-- <view class="label">附件</view>
      <view>
        <view v-for="(item,index) in filelist" :key="item" class="fileitem">
          <image
            :src="FILE_ADD"
            mode="widthFix"
            style="width: 48rpx; height: 48rpx; margin-right: 18rpx"
          ></image>
          <text>{{ missionAnnexNames[index] }}</text>
          <image class="closebtn" :src="CLOSE_BTN"
            @click="deleteFileHanlder(item)"
          ></image>
        </view>

        <image
          :src="FILE_ADD"
          mode="widthFix"
          v-show="showaddfile"
          style="
            width: 48rpx;
            height: 48rpx;
            padding: 20rpx;
          "
          @click="uploadfileHandler"
        ></image>
      </view> -->
			<!--  #endif -->
			<view class="cell" @click="getGridMember">
				<view class="label">下发网格</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view :class="showgridtip ? 'redplaceholder' : ''">{{ showgridtip ? '请选择下发网格员' : '' }}</view>
			<view class="gridcontent" v-for="(item,index) in gridmemberlist" :key="index">
				<view>网格名称：{{item.gridname}}</view>
				<view>网格级别：{{item.gridlevel}}</view>
				<view>处理人：{{getUsers(item.users)}}</view>
			</view>
		</view>
		<!-- <view
      class="cellcomplete"
      @click="gotocompleteHandler"
      v-if="flag == 'edit'"
    >
      <view class="label">完成情况</view>
      <u-icon name="arrow-right" class="rightarrow"></u-icon>
    </view>
    <view class="workgetcon" v-if="flag == 'edit'">
      <view class="label">工作小结</view>
      <view class="addWorkget" v-if="!showinput" @click="showinput=true"
        ><text class="plus">+</text><text>添加工作小结</text></view
      >
      <view class="workgetarea" v-else>
        <view class="btngroup">
          <view class="btn" v-show="showsave" @click="canclehandler">取消</view>
          <view class="btn greenbtn" v-show="showedit" @click="editHadnler">编辑</view>
          <view class="btn greenbtn" v-show="showsave" @click="savehandler">保存</view>
        </view>
        <view class="areacon">
          <u-textarea
            v-model.trim="workform.missionSummary"
            placeholder="请输入内容"
            :disabled="showedit"
            :height="90"
          ></u-textarea>
        </view>
      </view>
    </view> -->
		<view class="blank"></view>
		<view class="bottom u-border-top">
			<view class="btn pjBtn" @click="goSub">提交</view>
			<view class="paddingbottom"></view>
		</view>
		<!-- <u-action-sheet
      :show="showworkType"
      :actions="typelist"
      title="请选择工作类别"
      @close="showworkType = false"
      @select="selectWorkType"
    >
    </u-action-sheet> -->
		<u-picker :show="showworkType" title="请选择工作类别" :columns="[typelist]" keyName="dictLabel"
			@confirm="selectWorkType" @cancel="showworkType = false"></u-picker>
		<u-calendar :show="showdate" color="#1BC78D" @confirm="confirm" @close="showdate = false"
			closeOnClickOverlay></u-calendar>
	</view>
</template>
<script setup>
	let initSummary = ''
	import { onLoad, onUnload } from '@dcloudio/uni-app'
	import { reactive, ref, computed } from 'vue'
	import { IMG_ADD, FILE_ADD, RIGHT_ARROW, CLOSE_BTN } from '@/common/net/staticUrl'
	import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
	import { uploadFile } from '@/common/net/request.js'
	import {
		addEvent,
		updateEvent,
		findOneInfo,
	} from '../../api/eventPublication/eventPublication.js'
	import { getDicts } from '@/common/net/contacts/contacts.js'
	import {
		useTokenStore
	} from '@/store/token.js'
	const tokenStore = useTokenStore()
	const workform = ref({})
	const showworkType = ref(false)
	const showdate = ref(false)
	let imglist = ref([])
	let filelist = ref([])
	const showinput = ref(false)
	const showedit = ref(false)
	const showsave = ref(false)
	const flag = ref('')
	const missionId = ref('')
	const typelist = ref([])
	const missionTypeName = ref('')
	const issubmiting = ref(false)
	let missionAnnexNames = ref([])

	const showaddimg = computed(() => {
		return imglist.value.length >= 9 ? false : true
	})
	const showaddfile = computed(() => {
		return filelist.value.length >= 9 ? false : true
	})
	const showtitletiptip = computed(() => {
		return !workform.value.missionName && issubmiting.value ? true : false
	})
	const showtypetip = computed(() => {
		return !workform.value.missionType && issubmiting.value ? true : false
	})
	const showcontenttip = computed(() => {
		return !workform.value.missionContent && issubmiting.value ? true : false
	})
	const showtimettip = computed(() => {
		return !workform.value.completeTime && issubmiting.value ? true : false
	})
	const showgridtip = computed(() => {
		console.log(gridmemberlist.value, issubmiting.value)
		return !gridmemberlist.value.length > 0 && issubmiting.value ? true : false
	})
	const gridmemberlist = ref([])

	function getGridMemberList() {
		if (workform.value.gridInfo) {
			let infojson = JSON.parse(workform.value.gridInfo)
			//获取数组中有多少个type
			let gridtypes = [];

			infojson.map((item, index) => {
				if (gridtypes.indexOf(item.gridId) === -1) {
					gridtypes.push(item.gridId)
				}
			})

			//一个包含多个list的结果对象
			let obj = [];

			// 根据type生成多个数组
			gridtypes.map((typeItem, typeIndex) => {
				infojson.map((arrItem, arrIndex) => {
					if (arrItem.gridId == typeItem) {
						obj[typeIndex] = obj[typeIndex] || [];
						obj[typeIndex].push(arrItem)
					}
				})
			})
			let arr = []
			obj.forEach(item => {
				let newobj = {}
				newobj.gridlevel = item[0].path.length + 1 + '级'
				newobj.gridname = item[0].path[item[0].path.length - 1].listname
				newobj.users = item.map(user => user.name)
				arr.push(newobj)
			})
			gridmemberlist.value = arr
			return;
		}
		gridmemberlist.value = []
	}
	function getUsers(users) {
		if (users) {
			return users.join(',')
		}
	}

	onLoad((option) => {
		initSummary = ''
		flag.value = option.flag
		getDicts('task_type').then((response) => {
			let list = response.data
			// list.forEach((item) => {
			//   item.name = item.dictLabel
			// })
			typelist.value = list
			if (flag.value == 'edit') {
				missionId.value = option.id
				findDetail()
			}
		})

		uni.$on('selectSuccess', (res) => {
			console.log(res.list)
			let arr = []

			res.list.forEach(item => {
				let name = item.listname
				const { memberId, gridId, villagePeopleId } = item
				let obj = { memberId, name, gridId, villagePeopleId }
				arr.push(obj)
			})
			workform.value.gridMemberList = arr
			workform.value.gridInfo = JSON.stringify(res.list)
			getGridMemberList()
		})
		uni.$on('clickImg', (index) => {
				previewPics(index)
			})
	})
	onUnload(() => {
		uni.$off('clickImg')
	})
	function confirm(date) {
		workform.value.completeTime = date[0]
		showdate.value = false
	}
	async function goSub() {
		console.log(showtitletiptip.value, showtypetip.value, showcontenttip.value, showtimettip.value)
		issubmiting.value = true
		if (showtitletiptip.value || showtypetip.value || showcontenttip.value || showtimettip.value || showtimettip
			.value || showgridtip.value) {
			// if(showcontenttip.value){
			//   uni.showToast({
			//     title: '工作要求不能为空！',
			//     icon: 'none',
			//   })
			// }

			return
		}
		try {
			let res = null
			console.log(flag.value, 'ggggg')
			if (flag.value == 'add') {
				workform.value.missionPicture = imglist.value ? imglist.value.join(',') : ''
				workform.value.missionAnnex = filelist.value ? filelist.value.join(',') : ''
				workform.value.missionAnnexNames = missionAnnexNames.value ? missionAnnexNames.value.join(',') : ''
				let param = JSON.parse(JSON.stringify(workform.value));
				param.gridInfo = JSON.stringify(gridmemberlist.value);
				res = await addEvent(param)
			} else {
				workform.value.missionPicture = imglist.value ? imglist.value.join(',') : ''
				workform.value.missionAnnex = filelist.value ? filelist.value.join(',') : ''
				workform.value.missionAnnexNames = missionAnnexNames.value ? missionAnnexNames.value.join(',') : ''
				let param = JSON.parse(JSON.stringify(workform.value));
				param.gridInfo = JSON.stringify(gridmemberlist.value);
				res = await updateEvent(param)
			}
			console.log(res)
			if (res.success) {
				setTimeout(() => {
					uni.navigateBack({
						delta: 1,
						complete: () => {
							uni.showToast({
								title: '发布成功!',
								icon: 'none',
							})
						},
					})
				}, 500)
			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} catch (e) {
			uni.showToast({
				title: '发布失败！',
				icon: 'none',
			})
		}
	}
	async function findDetail() {
		try {
			let res = await findOneInfo({ id: missionId.value })
			if (res.success) {
				workform.value = res.data
				imglist.value = workform.value.missionPicture.length > 0 ? workform.value.missionPicture.split(',') :
				[]
				filelist.value = workform.value.missionAnnex.length > 0 ? workform.value.missionAnnex.split(',') : []
				missionTypeName.value = selectDictLabel(typelist.value, workform.value.missionType)
				showinput.value = workform.value.missionSummary.length > 0
				showedit.value = workform.value.missionSummary.length > 0 ? true : false
				showsave.value = false
				initSummary = workform.value.missionSummary
				gridmemberlist.value = JSON.parse(res.data.gridInfo)
			} else {
				uni.showToast({
					title: res.message,
					icon: 'none',
				})
			}
		} catch (e) {
			uni.showToast({
				title: '查询事务详情失败！',
				icon: 'none',
			})
		}
	}

	function selectDictLabel(datas, value) {
		var actions = [];
		Object.keys(datas).some((key) => {
			if (datas[key].dictValue == ('' + value)) {
				actions.push(datas[key].dictLabel);
				return true;
			}
		})
		return actions.join('');
	}

	function selectWorkType(e) {
		console.log(e)
		workform.value.missionType = e.value ? e.value[0].dictValue : ''
		missionTypeName.value = e.value ? e.value[0].dictLabel : ''
		showworkType.value = false
	}

	function getGridMember() {
		uni.navigateTo({
			url: './selectGridMember',
		})
	}



	function uploadimgHandler() {
		uni.chooseImage({
			count: 9 - imglist.value.length, //默认9
			extension: ['.jpg', '.png', '.gif'],
			sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
			sourceType: ['album', 'camera'], //从相册选择
			success: async (res) => {
				const tempFilePaths = res.tempFilePaths
				uni.showLoading({ mask: true })
				let list = await uploadFileList(tempFilePaths)
				imglist.value = [...imglist.value, ...list]
				uni.hideLoading()
			},
		})
	}

	function deleteHanlder(item) {
		let index = imglist.value.indexOf(item)
		imglist.value.splice(index, 1)
	}

	//查看详情时的预览图片
	const previewPics = async (index) => {
		let pics = []
		for(let i=0; i<imglist.value.length;i++) {
			let a = await loadImage(imglist.value[i])
			pics.push(a)
		}
		Promise.all(pics).then((result) => {
		})
		uni.previewImage({
			urls: pics
		})
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}
	function uploadfileHandler() {
		uni.chooseMessageFile({
			count: 9,
			extension: [
				'.xls',
				'.xlsx',
				'.doc',
				'.docx',
				'.ppt',
				'.pptx',
				'.pdf',
			],
			type: 'file',
			success: async (res) => {
				const tempFilePaths = res.tempFiles.map(item => item.path)
				const tempFileNames = res.tempFiles.map(item => item.name)
				uni.showLoading({ mask: true })
				let list = await uploadFileList(tempFilePaths, tempFileNames)
				filelist.value = [...filelist.value, ...list.files]
				missionAnnexNames.value = [...missionAnnexNames.value, ...list.names]
				uni.hideLoading()
			},
		})
	}

	async function uploadFileList(files, names) {
		return new Promise(async (resolve, reject) => {
			let filelist = []
			let namelist = []
			for (let item of files) {
				try {
					let res = await uploadFile({
						url: API_FILEUPLOAD_URL,
						method: 'POST',
						params: { filePath: item },
					})
					if (res.success) {
						let imgdata = res.data.url
						if (names) {
							namelist.push(names[files.indexOf(item)])
						}
						filelist.push(imgdata)
					} else {
						uni.showToast({
							title: res.message,
							icon: 'none',
							duration: 2000,
						})
					}
				} catch (e) {}
			}
			if (names) {
				resolve({ files: filelist, names: namelist })
			} else {
				resolve(filelist)
			}
		})
	}
</script>
<style>
	page {
		background: #f0f7f7;
	}
</style>
<style scoped lang="scss">
	::v-deep .redplaceholder {
		color: red !important;
		font-size: 28rpx;
	}

	.cellcomplete {
		margin: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 0 26rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		color: #000000;

		.rightarrow {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.workgetcon {
		margin: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 23rpx 26rpx;

		.workgetarea {
			position: relative;

			.btngroup {
				position: absolute;
				right: 0;
				top: -46rpx;
				display: flex;
				align-items: center;

				.btn {
					width: 111rpx;
					height: 48rpx;
					border-radius: 31rpx;
					border: 0.5px solid #cccccc;
					line-height: 48rpx;
					text-align: center;
					font-size: 26rpx;
					color: #999999;
					margin-left: 7rpx;
				}

				.greenbtn {
					border: 0.5px solid #0bbd88;
					color: #0bbd88;
				}
			}

			.areacon {
				padding-top: 30rpx;
			}
		}

		.label {
			font-size: 28rpx;

			color: #999999;
			line-height: 40rpx;
		}

		.addWorkget {
			padding: 36rpx 0;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			color: #0bbd88;
			line-height: 45rpx;

			.plus {
				margin-right: 20rpx;
			}
		}
	}

	.imgcell {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.gridcontent {
		padding: 15rpx 20rpx;
		background: #f8f8f8;
		border-radius: 8rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		font-size: 28rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16rpx;
		line-height: 56rpx;
	}

	.fileitem {
		display: flex;
		flex-wrap: wrap;
		white-space: nowrap;
		position: relative;

		text {
			font-size: 24rpx;
			color: #ccc;
			white-space: nowrap;
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.closebtn {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.imgitem {
		width: 233rpx;
		height: 179rpx;
		border: 0.5px solid #e2e2e2;
		margin-right: 40rpx;
		margin-bottom: 20rpx;
		position: relative;

		image {
			margin: 7rpx;
			width: 219rpx;
			height: 164rpx;
		}

		.closebtn {
			width: 40rpx;
			height: 40rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.uploadcon {
		width: 100%;
		overflow: hidden;
	}

	.bottom {
		position: fixed;
		width: 100%;
		background: #fff;
		bottom: 0;
		left: 0;
		//display: flex;
		align-items: center;
		padding: 20rpx 38rpx;
		z-index: 100;
		box-sizing: border-box;

		.btn {
			line-height: 80rpx;
			border-radius: 40rpx;
			flex: 1;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 36rpx;
		}

		.pjBtn {
			background: #3cc16c;
			color: #fff;
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.blank {
		width: 100%;
		height: 180rpx;
	}

	.arrow {
		top: 50rpx;
		width: 16rpx;
		height: 30rpx;
		position: absolute;
		right: 0;
	}

	.formcontainer {
		.form {
			margin: 20rpx;
			background: #ffffff;
			border-radius: 20rpx;
			padding: 0 26rpx;
			padding-bottom: 26rpx;
		}

		::v-deep .u-form-item {
			position: relative;
		}

		.label {
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			color: #333333;
			line-height: 45rpx;
			padding: 23rpx 0;
		}

		.cell {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		::v-deep .u-input__content__field-wrapper__field {
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #b1b1b1;
			line-height: 40rpx;
		}

		.form ::v-deep .u-textarea {
			padding: 0;
		}
	}
</style>