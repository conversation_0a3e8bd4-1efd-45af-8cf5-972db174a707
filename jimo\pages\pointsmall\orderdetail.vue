<template>
  <view class="orderdetail">
    <view class="paystatus u-border-bottom">
      <view class="status">{{ orderstatus }}</view>
      <view class="tip" v-if="orderstatus == '待兑换'"
        >请尽快到商品兑换点兑换</view
      >
      <view class="tip" v-if="orderstatus == '已兑换'"
        >兑换时间：{{ orderdetail.updateDate }}</view
      >
      <view class="tip" v-if="orderstatus == '已退款'">积分已退回积分账户</view>
    </view>
    <view class="gooddetail" v-if="commoditydetail">
      <view class="img">
        <view class="imgbox">
          <image
            :src="goodDetailImg"
            mode="widthFix"
          ></image>
        </view>
      </view>
      <view class="info">
        <view class="title u-line-2">
          {{ commoditydetail.commodityTitle }}
        </view>
        <view class="priceline" v-if="orderdetail">
          <text class="price">{{ orderdetail.orderAmount }}</text>
          <text class="label">积分</text>
        </view>
      </view>
    </view>
    <view
      class="labelline"
      v-if="
        orderstatus == '待兑换' ||
        orderstatus == '已兑换' ||
        orderstatus == '已退款'
      "
    >
      <text>券码信息</text>
      <view
        class="paybackbtn"
        v-if="orderstatus == '待兑换'"
        @click="openTKHandler"
        >申请退款</view
      >
    </view>
    <view
      class="quanmadetail"
      v-if="
        orderstatus == '待兑换' ||
        orderstatus == '已兑换' ||
        orderstatus == '已退款'
      "
    >
      <text
        :class="
          orderstatus == '已兑换' || orderstatus == '已退款' ? 'deleteline' : ''
        "
        >{{ orderdetail.exchangeVote }}</text
      >
      <image
        v-if="orderstatus == '待兑换'"
        class="arrow"
        :src="RIGHT_ARROW"
        @click="showQMHanlder"
      ></image>
      <text v-if="orderstatus == '已兑换'" style="color: #0bbd88">已兑换</text>
      <text v-if="orderstatus == '已退款'" style="color: #fe5537">已退款</text>
    </view>
    <view class="labelline" v-if="orderstatus && orderstatus != '待支付'">
      <text>商品兑换点</text>
    </view>

    <view
      class="addressdetail"
      v-if="addressdetail && orderstatus && orderstatus != '待支付'"
    >
      <view class="left">
        <view class="title">{{ addressdetail.addressName }}</view>
        <view class="addresssetting">{{ addressdetail.addressDesc }}</view>
      </view>
      <image class="phone" @click="callNumber" :src="PHONE_IMG"></image>
    </view>
    <view class="labelline">
      <text>订单详情</text>
    </view>
    <view class="orderinfo" v-if="orderdetail">
      <view class="infoline">
        <view class="label">订单金额：</view>
        <view class="value">{{ orderdetail.orderAmount }}积分</view>
      </view>
      <view class="infoline">
        <view class="label">订单编号：</view>
        <view class="value">{{ orderdetail.orderId }}</view>
      </view>
      <view class="infoline">
        <view class="label">下单时间：</view>
        <view class="value">{{ orderdetail.createDate }}</view>
      </view>
      <view class="infoline">
        <view class="label">下单人：</view>
        <view class="value">{{ orderdetail.name }}</view>
      </view>
      <view class="infoline">
        <view class="label">下单手机号：</view>
        <view class="value">{{ orderdetail.cellphone }}</view>
      </view>
    </view>
    <block v-if="orderdetail && orderstatus == '待支付'">
      <view class="blank"></view>
      <view class="bottom u-border-top">
        <view class="btn pjBtn" @click="goSub" v-if="canbuy">立即兑换</view>
        <view class="btn graybtn" v-else>积分不足</view>
        <view class="paddingbottom"></view>
      </view>
    </block>
    <u-popup
      :show="showTKPop"
      mode="center"
      @close="cancelHandler"
      closeable
      :safeAreaInsetBottom="false"
      round="10"
    >
      <view class="tktip">
        <view class="content">退款后积分将退回积分账户，确定退款吗？</view>
        <view class="btngroup">
          <view class="cancel" @click="cancelHandler">取消</view>
          <view class="okbtn" @click="tkHankder">确定</view>
        </view>
      </view>
    </u-popup>
    <u-popup
      :show="showQMPop"
      mode="center"
      @close="cancelHandler"
      closeable
      round="10"
    >
      <view class="qmtip">
        <view class="content">券码</view>
        <!-- <u-qrcode
          ref="qr"
          canvas-id="qrorderdetail"
          :value="qrCode"
          :size="qrCodeSize"
        ></u-qrcode> -->
        <view class="qrcode">
          <view
            v-for="(row, rowI) in modules"
            :key="rowI"
            style="display: flex; flex-direction: row"
          >
            <view v-for="(col, colI) in row" :key="colI">
              <view
                v-if="col.isBlack"
                style="width: 8rpx; height: 8rpx; background-color: black"
              >
                <!-- 黑色码点 -->
              </view>
              <view
                v-else
                style="width: 8rpx; height: 8rpx; background-color: white"
              >
                <!-- 白色码点 -->
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup>
import { reactive, ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { findOne, findAddressList } from '../../api/pointsmall/pointsmall'
import {
  findOrderById,
  findRedeemablePoints,
  refundUpdate,
  payUpdateNew,
} from  '../../api/pointsmall/hypermarketOrder'

import { PHONE_IMG, RIGHT_ARROW } from '@/common/net/staticUrl.js'
import { useTokenStore } from '@/store/token.js'
import { loadImage } from '@/common/getImage.js'
const orderId = ref('')
const showTKPop = ref(false)
const showQMPop = ref(false)
let orderdetail = ref(null)
let userpoint = ref(null)
let commoditydetail = ref(null)
let addressdetail = ref(null)
const tokenStore = useTokenStore()
const qrCodeSize = '203'
const qrCode = ref('')
let isdisabled = ref(false)
const modules = ref([])
const goodDetailImg = ref('')
import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'
let sourceType = ref('1')
onLoad((option) => {
  orderId.value = option.id
  // qrCode.value = '/jimo/pages/pointsmall/exchangedetail?id=' + option.id+"&tenantId="+tokenStore.tenantId
  const qr = new UQRCode()
  qr.data =
    '/jimo/pages/pointsmall/exchangedetail?id=' +
    option.id +
    '&tenantId=' +
    tokenStore.tenantId
  qr.make()
  console.log(qr.modules, 'fffffffff')
  modules.value = qr.modules
  getAddressDetail()
})

const canbuy = computed(() => {
  const amount = parseFloat(orderdetail.value.orderAmount)

  if (isNaN(amount)) {
    return false
  }

  if (amount === 0) {
    return true
  }

  if (amount && userpoint.value) {
    return userpoint.value - amount >= 0
  }

  return false
})
const orderstatus = computed(() => {
  if (orderdetail.value) {
    console.log(orderdetail.value)
    switch (orderdetail.value.orderStatus) {
      case '0':
        return '待支付'
      case '1':
        return '待兑换'
      case '2':
        return '已兑换'
      case '3':
        return '已退款'
      default:
        return ''
    }
  }
  return ''
})
onShow(() => {
  getDetail()
  getUserPoint()
})
function cancelHandler() {
  showTKPop.value = false
  showQMPop.value = false
}
function openTKHandler() {
  showTKPop.value = true
}
function showQMHanlder() {
  showQMPop.value = true
}
async function tkHankder() {
  if (isdisabled.value) return
  isdisabled.value = true
  try {
    const res = await refundUpdate({
      orderId: orderId.value,
      commodityId: commoditydetail.value.commodityId,
    })
    isdisabled.value = false
    if (res.success) {
      uni.showToast({
        title: '积分已退还，请注意查收！',
        icon: 'none',
      })
      cancelHandler()
      getDetail()
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    isdisabled.value = false
    uni.showToast({
      title: '积分退还失败！',
      icon: 'none',
    })
  }
}
async function getDetail() {
  try {
    const res = await findOrderById({ id: orderId.value })
    if (res.success) {
      let detail = res.data
      orderdetail.value = detail
	  sourceType.value = orderdetail.value.role
      getGoodDetail()
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getGoodDetail() {
  try {
    const res = await findOne({ id: orderdetail.value.commodityId })
    if (res.success) {
      let detail = res.data
      detail.imgs = detail.commodityPicture.split(',')
      commoditydetail.value = detail
	  goodDetailImg.value = await loadImage(detail.commodityPicture.split(',')[0])
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getUserPoint() {
  try {
    let res = await findRedeemablePoints()
    if (res.success) {
      userpoint.value = ~~res.data
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '获取用户积分失败！',
      icon: 'none',
    })
  }
}
async function getAddressDetail() {
  try {
    let res = await findAddressList()
    if (res.success) {
      if (res.data.length > 0) {
        const addresslist = res.data
        addressdetail.value = addresslist[0]
      } else {
        uni.showToast({
          title: '未设置兑换点！',
          icon: 'none',
        })
      }
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
function makeCall() {
  uni.getSetting({
    success: (res) => {
      if (res.authSetting['scope.makePhoneCall']) {
        callNumber()
      } else {
        uni.authorize({
          scope: 'scope.makePhoneCall',
          success: () => {
            callNumber()
          },
          fail: () => {
            uni.openSetting({
              success: (res) => {
                if (res.authSetting['scope.makePhoneCall']) {
                  callNumber()
                }
              },
            })
          },
        })
      }
    },
  })
}
function callNumber() {
  uni.makePhoneCall({
    phoneNumber: addressdetail.value.linkPhone,
    success: () => {
      console.log('拨打电话成功！')
    },
    fail: () => {
      console.error('拨打电话失败！')
    },
  })
}
async function goSub() {
  if (isdisabled.value) return
  isdisabled.value = true
  let detail = orderdetail.value
  try {
    let res = await payUpdateNew({ orderId: detail.orderId, role: sourceType.value })

    if (res.success) {
      if (res.status == '0') {
        isdisabled.value = false
        uni.showToast({
          title: res.message,
          icon: 'none',
        })
      }
      if (res.status == '1') {
        uni.redirectTo({
          url:
            '/jimo/pages/pointsmall/paydetail?cid=' +
            res.data.commodityId +
            '&oid=' +
            orderdetail.value.orderId,
          complete: () => {
            isdisabled.value = false
          },
        })
      }
    } else {
      isdisabled.value = false
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    isdisabled.value = false
    uni.showToast({
      title: '兑换失败！',
      icon: 'none',
    })
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: white;
}
::v-deep .qmtip {
  padding: 36rpx 0;
  width: 650rpx;
  height: 590rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  .content {
    font-size: 32rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    color: #333333;
    line-height: 48rpx;
    margin-bottom: 27rpx;
  }
  .qrcode {
    width: 406rpx;
    height: 406rpx;
  }
}
::v-deep .tktip {
  padding: 80rpx 60rpx 60rpx 60rpx;
  width: 650rpx;
  box-sizing: border-box;
  .content {
    font-size: 32rpx;

    color: #111111;
    line-height: 55rpx;
    margin-bottom: 10rpx;
  }
  .btngroup {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .cancel {
      width: 240rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background: #ffffff;
      border-radius: 41rpx;
      border: 2rpx solid #e5e5e5;
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #555555;
    }
    .okbtn {
      width: 240rpx;
      height: 80rpx;
      background: #0cbe88;
      border-radius: 41rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #ffffff;
    }
  }
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  //display: flex;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
  .graybtn {
    background: #bdbdbd;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 120rpx;
}
.quanmadetail {
  height: 102rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  margin: 0 20rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #000000;
  .arrow {
    width: 12rpx;
    height: 22rpx;
  }
  .deleteline {
    text-decoration: line-through;
  }
}
.labelline {
  font-size: 32rpx;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 600;
  color: #333333;
  line-height: 82rpx;
  height: 82rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  .paybackbtn {
    font-size: 24rpx;

    color: #ccc;
    line-height: 50rpx;
    height: 50rpx;
    width: 130rpx;
    text-align: center;
    border: 0.5px solid #ccc;
    border-radius: 25rpx;
  }
}
.orderinfo {
  margin: 0 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  width: calc(100% - 40rpx);
  padding: 26rpx 20rpx;
  box-sizing: border-box;
  .infoline {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .label {
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #999999;
      line-height: 48rpx;
      min-width: 200rpx;
    }
    .value {
      width: 100%;
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #333333;
      line-height: 48rpx;
      text-align: right;
      .p {
        text-align: left;
        display: inline-block;
      }
    }
  }
}
.addressdetail {
  margin: 0 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  width: calc(100% - 40rpx);
  padding: 26rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .left {
    flex: 1;
    margin-right: 66rpx;
  }
  .title {
    font-size: 28rpx;
    font-weight: 600;
    color: #000000;
    line-height: 40rpx;
    margin-bottom: 13rpx;
  }
  .addresssetting {
    font-size: 28rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 40rpx;
  }
  .phone {
    width: 32rpx;
    height: 33rpx;
  }
}
.orderdetail {
  margin: 20rpx;
  border-radius: 8rpx;
  background-color: white;
  padding-bottom: 20rpx;
  .paystatus {
    padding: 30rpx 25rpx;
    .status {
      font-size: 32rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
      color: #333333;
      line-height: 48rpx;
    }
    .tip {
      margin-top: 13rpx;
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #333333;
      line-height: 40rpx;
    }
  }

  .gooddetail {
    margin: 20rpx 20rpx 0 20rpx;
    padding: 23rpx 20rpx;
    display: flex;
    align-items: center;
    background: #f8f8f8;
    border-radius: 10rpx;

    .img {
      min-width: 241rpx;
      height: 185rpx;
      background-color: white;
      border: 0.5px solid #ccc;
      .imgbox {
        margin: 8rpx;
        box-sizing: border-box;

        overflow: hidden;
        position: relative;
        width: calc(100% - 16rpx);
        height: calc(100% - 16rpx);

        image {
          box-sizing: border-box;
          width: 100%;
          left: 0;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .info {
      padding: 10rpx 20rpx;
      .title {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 52rpx;
        margin-bottom: 10rpx;
      }
      .priceline {
        display: flex;
        align-items: center;
        .price {
          font-size: 43rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 600;
          color: #666666;
          line-height: 61rpx;
          background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-right: 10rpx;
        }
        .label {
          font-size: 30rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #999999;
          line-height: 42rpx;
        }
      }
    }
  }
  .erweima {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    .ma {
      width: 405rpx;
      height: 405rpx;
      border: 4rpx solid #ffffff;
    }
    .code {
      margin-top: 15rpx;
      margin-bottom: 40rpx;
      font-size: 36rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
      color: #000000;
      line-height: 50rpx;
    }
  }
}
</style>
