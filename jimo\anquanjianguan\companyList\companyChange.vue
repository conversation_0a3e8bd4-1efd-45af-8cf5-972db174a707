<template>
  <view class="formcontainer" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="整改复核"  bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>

    <view class="infocontainer">
      <view class="form">
      <view class="formitem">
      <view class="label">企业名称</view>
      <view class="value">{{detail.enterpriseName}}</view>
      </view>
      <view class="formitem">
      <view class="label">企业性质</view>
      <view class="value">{{detail.enterpriseNature}}</view>
     </view>
     <view class="formitem">
      <view class="label">企业规模</view>
      <view class="value">{{detail.enterpriseScale}}</view>
      </view>
      <view class="formitem">
      <view class="label">巡检时间</view>
      <view class="value">{{detail.inspectTime}}</view>    
     </view>
      <view class="label">隐患图片</view>
      <view class="imgcell">
        <image v-for="(img, idx) in detail.reviewImgs" :key="idx" :src="img" style="width: 80rpx; height: 80rpx; margin-right: 16rpx; background: #eee;" />
      </view>
       <view class="label">隐患描述</view>
      <view class="value">{{detail.problemDesc}}</view>
      <view class="formitem">
      <view class="label">整改期限</view>
      <view class="value">{{detail.rectifyDeadline}}</view>    
     </view>
     <view class="formitem">
      <view class="label">上传人</view>
      <view class="value">{{detail.uploader}}</view>   
      </view>
      </view>
      <view class="form">

        <u--form
          errorType="none"
          ref="formRef"
          :model="form"
          :label-width="120"
          :labelStyle="{ fontWeight: 'bold', fontSize: '32rpx' }"
          :rules="rules"
        >
        <u-form-item
            required
            label="复核状态："
            prop="actionType"
            @click="showStatusPicker = true"
          >
            <u-input
              v-model="actionTypeLabel"
              placeholder="请选择复核状态"
              border="none"
              readonly
            >
              <template #suffix>
                <u-icon name="arrow-down"></u-icon>
              </template>
            </u-input>
          </u-form-item>
      <template v-if="form.actionType=='complete'">
      <u-form-item label="" prop="reviewDesc">
            <view style="width: 100%">
              <view class="label">
                复核描述:
              </view>
              <u--textarea
                v-model="form.reviewDesc"
                placeholder="请输入复核描述"
                border="surround"
              ></u--textarea>
            </view>
          </u-form-item>
      <view class="label">复核图片</view>
      <view class="imgcell">
        <template v-if="form.reviewImages">
        <view
              class="imgitem"
              v-for="item in form.reviewImages.split(',')"
              :key="item"
            >
              <!-- <image class="img" :src="item" @click="previewHandler(index, imglist)"></image> -->
              <view class="img">
                <image
                  :src="item"
                 
              
                ></image>
              </view>
              <image
                class="closebtn"
                :src="CLOSE_BTN"
                @click="deleteHanlder(item)"
              ></image>
            </view>
            </template>
            <image
              :src="IMG_ADD"
              style="width: 80rpx; height: 80rpx"
              @click="uploadimgHandler"
            />
         </view>
        </template>
        <template v-if="form.actionType=='delay'">
          <u-form-item
            label="整改截止时间"
            prop="delayDeadline"
            required
          >
          
            <u--input v-model="form.delayDeadline" placeholder="请输入日期，格式：yyyy-mm-dd" border="none" @blur="validateDate(form.delayDeadline, (value)=>{form.delayDeadline = value} )" />

            </u-form-item>
           <u-form-item label="" prop="delayReason">
            <view style="width: 100%">
              <view class="label">
                延期理由:
              </view>
              <u--textarea
                v-model="form.delayReason"
                placeholder="请输入延期理由"
                border="surround"
              ></u--textarea>
            </view>
          </u-form-item>
         
        </template>
</u--form>
    </view>
    </view>
    <!-- <view class="blank"></view> -->
    <view class="bottom u-border-top">
      <view class="btn pjBtn" @click="submit">提交</view>
    </view>
    <u-picker closeOnClickOverlay @close="showStatusPicker = false" :show="showStatusPicker" keyName="label" :columns="[statusList]" @confirm="onStatusConfirm" @cancel="showStatusPicker = false" />
    <u-datetime-picker closeOnClickOverlay @close="showDeadlinePicker = false" :show="showDeadlinePicker" @confirm="onDeadlineConfirm" mode="date" @cancel="showDeadlinePicker = false" />

  </view>
</template>

<script setup>
import { ref } from 'vue'
import { IMG_ADD, LIVE_INVENTORY_BG, CLOSE_BTN } from '@/common/net/staticUrl'
import { onLoad, onReady } from '@dcloudio/uni-app'
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import { AJYService } from '../../api/anquanjianguan/companyList'
import logoutbtn from '@/jimo/components/logoutbtn.vue'
const form = ref({
  "reviewId": "", //复核记录ID
"actionType": "", //操作类型：complete-完成整改, delay-延期整改, transfer-移交执法
"reviewDesc": "", //复核描述
"reviewImages": "", //复核图片（多张用英文逗号分割）
"delayDeadline": "", //申请延期时间
"delayReason": "" //延期理由
})
const detail = ref({})
const rules = ref({
  actionType: [
    {
      required: true,
      message: '复核状态不能为空',
      trigger: ['blur', 'change'],
    },
  ],
  delayDeadline:[
     {
      required: true,
      message: '整改截止时间不能为空',
      trigger: ['blur', 'change'],
    },
  ]
 
})
const showStatusPicker = ref(false)
const statusList = [
  { label: '已完成', value: 'complete' },
  { label: '延期整改', value: 'delay' },
  { label: '移交执法', value: 'transfer' }
]
const showDeadlinePicker = ref(false)
const formRef = ref(null)
onReady(() => {
  formRef.value.setRules(rules)
})
onLoad((options) => {
  form.value.reviewId = options.id
  AJYService.findInspectReviewById(options.id).then(res => {
    if (res.success) {
      console.log(res)
      detail.value = res.data
      detail.value.reviewImgs = detail.value.riskImages.split(',')
    }
  })
})
const validateDate = (value, callback) => {
  if (!value) return;
  // 检查日期格式是否正确
  const datePattern = /^\d{4}-\d{1,2}-\d{1,2}$/;
  if (!datePattern.test(value)) {
    uni.showToast({
      title: '请输入正确的日期格式：yyyy-mm-dd',
      icon: 'none'
    });
    callback('')
    
    return;
  }
  
  // 分割日期
  const [year, month, day] = value.split('-').map(Number);
  
  // 验证日期是否有效
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
   callback('')
    return;
  }
  
  // 格式化日期，确保月份和日期是两位数
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay}`)
}
const actionTypeLabel = ref('')
function onStatusConfirm(e) {
  form.value.actionType = e.value[0].value
  actionTypeLabel.value = e.value[0].label
  showStatusPicker.value = false
}
function onDeadlineConfirm(e) {
  form.value.delayDeadline = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showDeadlinePicker.value = false
}
function uploadimgHandler() {
  uni.chooseImage({
    count: 9 - form.value.reviewImages?form.value.reviewImages.split(',').length:0, //默认9
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
    sourceType: [ 'camera'], //从相册选择
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = form.value.reviewImages?form.value.reviewImages.split(','):[]
       form.value.reviewImages = [...arr, ...list].join(',')

      uni.hideLoading()
    },
  })
}
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url

          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) {}
    }
    resolve(filelist)
  })
}
function deleteHanlder(item) {
  const imageIndex = form.value.reviewImages.split(',').indexOf(item)
   let arr = form.value.reviewImages.split(',')
  arr.splice(imageIndex, 1).join(',')
  form.value.reviewImages = arr.length?arr.join(','):''
}
const isdisabled = ref(false)
function submit() {
  formRef.value.validate().then( async (valid) => {
     if (valid) {
    if (isdisabled.value) return;
      isdisabled.value = true
      AJYService.submitReview(form.value).then(res => {
        isdisabled.value = false
        if (res.success) {
          uni.showToast({ title: '提交成功', icon: 'none' })
          setTimeout(()=>{
           uni.navigateBack()
          },600)
        } else {
          uni.showToast({ title: res.message, icon: 'none' })
        }
      }).catch(e=>{
        console.log(e)
        isdisabled.value = false
        uni.showToast({
				title: '提交失败',
				icon: 'none',
			})
      })
    }
    else{
     uni.showToast({ title: '请填写完整信息', icon: 'none' })
  
    }
  })
   .catch((e) => {
      uni.showToast({ title: '请填写完整信息', icon: 'none' })
    })
}
</script>
<style scoped lang="scss">
.formitem {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  ::v-deep .u-icon {
    margin-left: 20rpx;
  }
}
.value{
  color: #666;
  word-wrap: break-word;
  }
.blank {
  width: 100%;
  height: 90rpx;
}
::v-deep .redplaceholder {
  color: red !important;
  font-size:28rpx;
}
.cellcomplete {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 0 26rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-family: PingFang-SC, PingFang-SC;
  color: #000000;
  .rightarrow {
    width: 30rpx;
    height: 30rpx;
  }
}
.workgetcon {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 23rpx 26rpx;
  .workgetarea {
    position: relative;
    .btngroup {
      position: absolute;
      right: 0;
      top: -46rpx;
      display: flex;
      align-items: center;
      .btn {
        width: 111rpx;
        height: 48rpx;
        border-radius: 31rpx;
        border: 0.5px solid #cccccc;
        line-height: 48rpx;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        margin-left: 7rpx;
      }
      .greenbtn {
        border: 0.5px solid #0bbd88;
        color: #0bbd88;
      }
    }
    .areacon {
      padding-top: 30rpx;
    }
  }
  .label {
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
  }
  .addWorkget {
    padding: 36rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #0bbd88;
    line-height: 45rpx;
    .plus {
      margin-right: 20rpx;
    }
  }
}
.imgcell {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.gridcontent {
  padding: 15rpx 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 28rpx;
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 400;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 56rpx;
}
.fileitem {
  display: flex;
  flex-wrap: wrap;
  white-space: nowrap;
  position: relative;
  text {
    font-size: 24rpx;
    color: #ccc;
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .closebtn {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.imgitem {
  width: 233rpx;
  height: 179rpx;
  border: 0.5px solid #e2e2e2;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
  position: relative;
  image {
    margin: 7rpx;
    width: 219rpx;
    height: 164rpx;
  }
  .closebtn {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.uploadcon {
  width: 100%;
  overflow: hidden;
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 180rpx;
}
.arrow {
  top: 50rpx;
  width: 16rpx;
  height: 30rpx;
  position: absolute;
  right: 0;
}
.formcontainer {
  height: 100vh;
  .infocontainer{
    margin: 20rpx;
   
    height: calc(100vh - 220rpx);
    box-sizing: border-box;
    overflow: scroll;
    .form {
      padding: 0 26rpx;
      background: #ffffff;
      border-radius: 20rpx;
      padding-bottom: 26rpx;
      ::v-deep .uni-input-wrapper {
        text-align: right;
      }
      ::v-deep .u-form-item__body__left__content__required {
        position: inherit;
        left: auto;
      }
    
      &:nth-child(1) {
        margin-bottom: 26rpx;
      }
    }
 }
  ::v-deep .u-form-item {
    position: relative;
  }
  .label {
    font-size: 32rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 600;
    color: #333333;
    line-height: 45rpx;
    padding: 23rpx 0;
  }
  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  ::v-deep .u-input__content__field-wrapper__field {
    font-size: 28rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 400;
    color: #b1b1b1;
    line-height: 40rpx;
  }
}
</style>