<template>
    <view class="comment-title">
        评论 {{commentList.length||0}}
    </view>
    <view class="comment-list" v-if="commentList.length>0">
        <view class="comment-item" v-for="(item, index) in commentList" :key="index" @click="handleDelComment(item)">
            <view class="comment-info">
                <u-avatar :src="item?.peoplePhoto" size="96rpx" :default-url="COMMENT_HEAD_IMG"></u-avatar>
                <view class="author-info">
                    <view class="comment-author">{{ item?.peopleName }}({{item?.nickname ||'段泊岚镇农综平台用户'}})</view>
                    <view class="comment-time">{{timeChange(item?.createDate)||''}}</view>
                </view>
            </view>
            <view class="comment-desc"> {{item.commentContent}}</view>
        </view>
    </view>
    <view class="empty-comment" v-else>
        <view class="empty-icon">
            <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
            <view class="no-more">
                <span class="txt">暂无评论，</span><span class="lik" @click="handleComment">点击评论</span>
            </view>
        </view>
    </view>
    <!-- 占位符 -->
    <view class="defult-place"></view>

    <!-- 键盘谈起的评论框 -->
    <u-popup v-if="talkShow" :show="talkShow" mode="bottom" :customStyle="{'width':'100%','border-radius':'8rpx'}"
        @close="popclosed" @open="talkShow=true" :safeAreaInsetBottom="true">
        <view class="flex" style="padding: 20rpx;">
            <div class="comment-text">
                <view class="com-textarea-box">
                    <!-- <u--textarea :height="100" :cursorSpacing="100" class="com-textarea" :focus="focus"
                        :showConfirmBar="false" v-model="commentData" placeholder="请输入评论内容" count
                        maxlength="300"></u--textarea> -->
                    <!-- <textarea class="u-text1" adjust-position placeholder-style="color:#999" v-model="commentData"
                        @input="sumfontnum" :maxlength="300" :show-confirm-bar="false" :focus="true"
                        placeholder="请输入评论内容" /> -->
                    <textarea class="textarea-content" placeholder="请输入评论内容" placeholder-style="color:#999"
                        :disable-default-padding="true" :value="commentData" :show-confirm-bar="false"
                        @input="inputHandle($event, 300)" @confirm="inputHandle($event, 300)" maxlength="300"
                        :cursor-spacing="150"></textarea>
                    <view class="fontnum"><text>{{fontnum}}/300</text></view>
                </view>
                <view class="comment-btn" :class="{'disabled-btn':fontnum == 0} " @tap="fontnum>0?submit():''">
                    <view class="publish-btn">
                        发布
                    </view>
                </view>
            </div>
        </view>
    </u-popup>
    <!-- 固定在底部的评论框 -->
    <view class="fixed-comment">
        <view class="sub-comments-input" @tap.stop="handleComment()">
            <view class="txt">评论一下</view>
        </view>
        <view class="option">
            <view class="icon-list">
                <u-icon name="chat" size="34rpx" color="#999999"></u-icon>
                <span class="txt">
                    {{ contentInfo?.commentCount ? contentInfo.commentCount > 999 ? '999+' : contentInfo.commentCount: 0 }}
                </span>
            </view>
            <view class="like-list" @click.stop="handleLike">
                <u-icon name="thumb-up-fill" v-if="contentInfo?.upvoteFlag==1" color="#f00" size="34rpx"></u-icon>
                <u-icon name="thumb-up" size="34rpx" v-else color="#999999"></u-icon>
                <span class="txt">{{contentInfo?.upvoteCount ? contentInfo.upvoteCount:0}}</span>
            </view>
        </view>
    </view>
    <!-- ActionSheet 操作菜单 -->
    <u-action-sheet :actions="actionList" :title="title" :cancelText="'取消'" @select="selectClick" @close="show=false"
        :show="show" round="8rpx"></u-action-sheet>
</template>
<script setup>
    import {
        COMMENT_HEAD_IMG,
        NO_MORE_IMG
    } from '@/common/net/staticUrl.js'
    import {
        defineProps,
        ref,
        onMounted,
        reactive,
        defineExpose,
        watchEffect,
        computed,
        watch
    } from 'vue'
    import {
        getContentDetail,
        getContentDetailAnonymous,
        getCommentList,
        getCommentListAnonymous,
        updateContentLike,
        addComment,
        deleteComment
    } from '@/common/net/listPage/listPage.js'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        useMainStore
    } from '@/store/index.js'
    import {
        timeChange
    } from '@/common/date/dateTimeFormat.js'
    const tokenStore = useTokenStore()
    const tenantId = tokenStore.tenantId
    const mainStore = useMainStore()
    const props = defineProps({
        contentId: {
            type: String,
            required: true,
            default: ''
        }
    })
    let _inputTimer = ref(null)
    let contentInfo = ref(null)
    let commentForm = ref(null)
    let talkShow = ref(false)
    let commentData = ref('')
    let focus = ref(true)
    let commentList = ref([])
    let actionList = ref([])
    let title = ref('')
    let show = ref(false)
    let commentInfo = ref(null)
    let fontnum = ref(0)
    const userStore = useUserStore()
    const villagePeopleId = computed(() => {
        console.log('-----------peopleInfo')
        console.log(userStore.userInfo)
        return userStore?.userInfo?.customParam?.peopleInfo?.villagePeopleId || ''
    })
    watchEffect(() => {
        if (props.contentId) {
            getInfo()
            getList()
        }
    })
    // watch(commentData, (newVal, oldVal) => {
    //     if (newVal.length >= 300) {
    //         commentData.value = newVal.substr(0, 300)
    //     }
    // }, {
    //     immediate: true,
    //     deep: true,
    // })
    function inputHandle(e, maxLength) {
        clearTimeout(_inputTimer.value)
        _inputTimer.value = setTimeout(() => {
            let {
                value: inputVal
            } = e.detail
            if (inputVal.length > maxLength) {
                commentData.value = inputVal.slice(0, maxLength)
                fontnum.value = maxLength
            } else {
                commentData.value = inputVal
                fontnum.value = inputVal.length
            }
        }, 100)
    }

    // 获取单个数据
    function getInfo() {
        if (mainStore.hasLogin) {
            getContentDetail({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    console.log(res.data);
                    contentInfo.value = res.data;
                }
            })
        } else {
            getContentDetailAnonymous({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    console.log(res.data);
                    contentInfo.value = res.data;
                }
            })
        }
    }
    //访问接口
    function getList() {
        //显示加载中动画
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
        if (mainStore.hasLogin) {
            getCommentList({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    commentList.value = res.data;
                }
            }).finally(() => {
                uni.hideLoading()
            })
        } else {
            getCommentListAnonymous({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    commentList.value = res.data;
                }
            }).finally(() => {
                uni.hideLoading()
            })
        }
    }

    // 保存
    const submit = () => {
        console.log(commentData.value)
        if (!!commentData.value) {
            addComment({
                contentId: props.contentId,
                commentContent: commentData.value
            }).then((res) => {
                talkShow.value = false;
                commentData.value = ''
				fontnum.value = 0
                getList()
                getInfo()
                uni.showToast({
                    title: '评论成功',
                    icon: 'none'
                });
            })

        } else {
            uni.showToast({
                title: '评论内容不能为空',
                icon: 'none'
            });
        }
    }

    //评论
    function handleComment() {
        //游客身份不能点赞、评论
        const isVisitorFlag = isVisitor()
        if (isVisitorFlag) {
            return;
        }
        console.log('-----------评论')
        talkShow.value = true;
        console.log(talkShow.value)
    }
    // 关闭pop
    function popclosed() {
        commentData.value = ''
        focus.value = false
        talkShow.value = false
        console.log(focus);
    }
    // 点赞
    function handleLike() {
        const isVisitorFlag = isVisitor()
        if (isVisitorFlag) {
            return;
        }
        let content = JSON.parse(JSON.stringify(contentInfo.value))
        updateContentLike({
            contentId: content.contentId,
            upvoteFlag: content.upvoteFlag == 1 ? 0 : 1
        }).then((res) => {
            uni.showToast({
                title: content.upvoteFlag == 1 ? '取消点赞成功' : '点赞成功',
                icon: 'none'
            });
            contentInfo.value = {
                ...content,
                upvoteFlag: content.upvoteFlag == 1 ? 0 : 1,
                upvoteCount: content.upvoteFlag == 1 ? --content.upvoteCount : ++content.upvoteCount
            }
            console.log('---------------')
            console.log(contentInfo.value)
        }).catch((e) => {
            console.log(e)
            uni.showToast({
                title: e,
                icon: 'none'
            });
        })
    }
    // 删除
    function handleDelComment(item) {
        console.log('---------------item')
        console.log(item.peopleId)
        console.log(villagePeopleId.value)
        console.log(item)
        if (villagePeopleId.value == item.peopleId) {
            commentInfo.value = item
            title.value = '删除我的评论'
            show.value = true;
            actionList.value = [{
                name: '删除',
                color: '#C80000',
                fontSize: '20'
            }]
        }

    }

    function selectClick(obj) {
        if (obj.name == '删除' && commentInfo.value) {
            const {
                commentId
            } = commentInfo.value;
            deleteComment({
                commentId
            }).then((res) => {
                if (res.success) {
                    commentInfo.value = null;
                    getList()
                    getInfo()
                    uni.showToast({
                        title: '删除成功!',
                        icon: 'none'
                    })
                }
            }).catch((e) => {
                console.log(e)
                uni.showToast({
                    title: e,
                    icon: 'none'
                });
            })
        }
    }
    //游客身份不能点赞、评论
    function isVisitor() {
        if (userStore.userInfo.customParam.userType == 'Visitor') {
            uni.showModal({
                title: '提示',
                content: '加入该村庄后才能点赞或评论',
                confirmText: '加入',
                success: function(res) {
                    if (res.confirm) {
                        const customParam = userStore.userInfo.customParam;
                        uni.navigateTo({
                            url: "/pages/generalPage/join/join?tenantId=" + customParam.tenantId +
                                '&optionType=add&tenantName=' + customParam.tenantName +
                                '&fullName=' +
                                customParam.tenantName
                        })
                    }
                }
            });
            return true;
        } else {
            return false
        }
    }

    defineExpose({
        handleComment
    });
</script>
<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .comment-title {
        height: 48rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #000000;
        line-height: 48rpx;
    }

    .comment-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 20rpx;
        height: 190rpx;
        width: 100%;
        background-color: #fff;
        border-radius: 6rpx;

        .comment-info {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 20rpx;

            .author-info {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                margin-left: 20rpx;

                .comment-author {
                    margin-top: 6rpx;
                    font-size: 28rpx;
                    font-weight: 600;
                    color: #000000;
                    line-height: 40rpx;
                }

                .comment-time {
                    margin-top: 14rpx;
                    font-size: 26rpx;
                    font-family: PingFang-SC-Regular, PingFang-SC;
                    font-weight: 400;
                    color: #999999;
                }
            }
        }

        .comment-desc {
            font-size: 28rpx;
            font-family: PingFang-SC-Regular, PingFang-SC;
            font-weight: 400;
            line-height: 40rpx;
            color: #000000;
            display: -webkit-box;
            /* 将容器以弹性盒子形式布局 */
            -webkit-line-clamp: 2;
            /* 限制文本显示为两行 */
            -webkit-box-orient: vertical;
            /* 将弹性盒子的主轴方向设置为垂直方向 */
            overflow: hidden;
            /* 隐藏容器中超出部分的内容 */
            text-overflow: ellipsis;
            /* 超出容器范围的文本显示省略号 */
        }
    }

    .empty-comment {
        display: flex;
        justify-content: center;
        align-items: center;

        .empty-icon {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .no-more {
                margin-top: 20rpx;
                display: flex;
                justify-content: center;
                margin-top: 10rpx;

                .txt {
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: PingFang-SC-Regular, PingFang-SC;
                    font-weight: 400;
                    color: #333333;
                    line-height: 37rpx;
                }

                .lik {
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: PingFang-SC-Regular, PingFang-SC;
                    font-weight: 400;
                    color: #7EA0B0;
                    line-height: 37rpx;
                }
            }

        }
    }

    .defult-place {
        height: 85rpx;
    }

    .fixed-comment {
        margin: 0 -20rpx;
        background-color: #fff;
        border-top: 1rpx solid #F1F1F1;
        position: fixed;
        bottom: 10rpx;
        height: calc(86rpx + env(safe-area-inset-bottom));
        display: flex;
        padding: 20rpx 20rpx 0 20rpx;
        overflow: hidden;

        .sub-comments-input {
            width: 522rpx;
            height: 56rpx;
            background: #F6F6F6;
            border-radius: 28rpx;
            opacity: 1;

            .txt {
                margin: 9rpx 0 10rpx 28rpx;
                width: 104rpx;
                height: 37rpx;
                font-size: 26rpx;
                font-family: PingFang-SC-Regular, PingFang-SC;
                font-weight: 400;
                color: #999999;
                line-height: 37rpx;
            }
        }

        .option {
            width: 175rpx;
            height: 56rpx;
            display: flex;
            justify-content: space-around;
            align-items: center;

            .icon-list {
                padding-left: 10rpx;
                display: flex;
                align-items: baseline;
            }

            .like-list {
                display: flex;
                align-items: baseline;
            }

            .txt {
                height: 33rpx;
                font-size: 24rpx;
                font-family: PingFang-SC-Regular, PingFang-SC;
                font-weight: 400;
                color: #999999;
                line-height: 33rpx;
            }
        }

    }

    .comment-text {
        display: flex;
        justify-content: space-between;
        position: relative;

        .com-textarea-box {
            width: 580rpx;
            position: relative;

            .textarea-content {
                width: 575rpx;
                box-sizing: border-box;
                padding: 10rpx;
                border-radius: 4px;
                background-color: #fff;
                border-width: 1rpx;
                border-color: #dadbde;
                border-style: solid;
            }

            .fontnum {
                position: absolute;
                right: 5px;
                bottom: 2px;
                font-size: 12px;
                color: #909193;
                background-color: #ffffff;
                padding: 1px 4px;
            }
        }

        .comment-btn {
            position: absolute;
            right: 0rpx;
            bottom: 0rpx;
            width: 105rpx;
            height: 62rpx;
            background: #0BBD88;
            border-radius: 31rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            .publish-btn {
                width: 52rpx;
                height: 37rpx;
                font-size: 26rpx;
                font-family: PingFang-SC-Regular, PingFang-SC;
                font-weight: 400;
                color: #FFFFFF;
                line-height: 37rpx;
            }

        }

        .disabled-btn {
            background-color: #999;
        }
    }
</style>