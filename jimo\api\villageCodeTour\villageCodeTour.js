import {
    request
} from '@/common/net/request.js'
//乡村一码游
/**
 * @description 获取一码游头图
 * @return {*}
 */
export function getBanner(params) {
    return request({
        url: '/user/tbVillageOneCodeTopImg/img/findInfo',
        method: 'GET',
        params
    })
}
// 获取景区景点列表
export function getPlacePage(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeType/type/findTourisSort',
        method: 'GET',
        params,
    })
}
//获取分类-不分页
export function findList(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeType/type/findList',
        method: 'GET',
        params,
    })
}
// 根据regionCode获取分类
export function findListByRegionCode(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeType/type/findListNew',
        method: 'GET',
        params,
    })
}

//根据分类获取内容-分页
export function findContentPage(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeContent/content/findPage',
        method: 'GET',
        params
    })
}

// 根据分类获取内容-分页（酒店民宿、本地特产、美食指南）
export function findContentPageNew(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeContent/content/findPageApp',
        method: 'GET',
        params
    })
}

//获取单个内容
export function findOneContent(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeContent/content/findInfo',
        method: 'GET',
        params
    })
}
// 获取单个内容(可以查询其他村)
export function findOneContentNew(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeContent/content/findInfoApp',
        method: 'GET',
        params
    })
}

// 获取乡村向导列表
export function findGuideList(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/findPageByALL',
        method: 'GET',
        params
    })
}

// 获取向导详情
export function findGuideDetail(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/findInfo',
        method: 'GET',
        params
    })
}

// 新增向导
export function addGuide(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/add',
        method: 'POST',
        params
    })
}

// 获取申请信息
export function findApplyInfo() {
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/findByMe',
        method: 'GET',
    })
}
// 修改向导申请
export function updateGuide(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/update',
        method: 'POST',
        params
    })
}
// 删除向导申请
export function delGuide(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/delete',
        method: 'POST',
        params
    })
}
// 根据reigoncode获取行政区划信息
export function findRegionInfo(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/findRegion',
        method: 'POST',
        params
    })
}
// 根据租户id获取行政区划信息
export function findRegionInfoByTenantId(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeGuide/guide/findRegionByTenantId',
        method: 'POST',
        params
    })
}
// 获取乡村名片数据
export function getVillageCardInfo(params) {
    return request({
        url: '/user/business/card/findInfoApp',
        method: 'GET',
        params
    })
}
// 获取乡村名片数据-可以查到为审核的乡村名片
export function getVillageCardInfo2(params) {
    return request({
        url: '/user/business/card/findInfoAppuUnlimited',
        method: 'GET',
        params
    })
}

/**----------------------------------------服务商----------------------------------------*/
export class SreviceProvider {
    /**
     * @description 获取当前用户服务商信息
     */
    static findSreviceProviderInfo() {
        return request({
            url: '/user/tbVillageOneCodeServe/merchant/findApp',
            method: 'GET'
        })
    }

    /**
     * @description 新增当前用户服务商
     * @param params
     */
    static addSreviceProvider(params) {
        params['uniContentType'] = 'json'
        return request({
            url: '/user/tbVillageOneCodeServe/merchant/add',
            method: 'POST',
            params
        })
    }
    /**
     * @description 修改当前用户服务商
     * @param params
     */
    static updateSreviceProvider(params) {
        params['uniContentType'] = 'json'
        return request({
            url: '/user/tbVillageOneCodeServe/merchant/updateApp',
            method: 'POST',
            params
        })
    }
    /**
     * @description 获取当前用户服务类（酒店住宿、本地特产、美食指南等）数据
     * @param params
     */
    static getSreviceClassPageList(params) {
        return request({
            url: '/user/tbVillageOneCodeContent/content/findPageMyApp',
            method: 'GET',
            params
        })
    }
    /**
     * @description 新增当前用户服务类（酒店住宿、本地特产、美食指南等）数据
     * @param params
     */
    static addSreviceClass(params) {
        params['uniContentType'] = 'json'
        return request({
            url: '/user/tbVillageOneCodeContent/content/addApp',
            method: 'POST',
            params
        })
    }
    /**
     * @description 修改当前用户服务类（酒店住宿、本地特产、美食指南等）数据
     * @param params
     */
    static updateSreviceClass(params) {
        params['uniContentType'] = 'json'
        return request({
            url: '/user/tbVillageOneCodeContent/content/updateApp',
            method: 'POST',
            params
        })
    }
    /**
     * @description 修改当前用户服务类（酒店住宿、本地特产、美食指南等）详情
     * @param params
     */
    static getSreviceClassInfo(params) {
        return request({
            url: '/user/tbVillageOneCodeContent/content/findInfoMyAppById',
            method: 'GET',
            params
        })
    }
    /**
     * @description 删除当前用户服务类（酒店住宿、本地特产、美食指南等）
     * @param params
     */
    static delSreviceClass(params) {
        params['uniContentType'] = 'json'
        return request({
            url: '/user/tbVillageOneCodeContent/content/deleteApp',
            method: 'POST',
            params
        })
    }
}
/**------------------------------------------------------------------------------------*/

/**----------------------------------------五星村庄----------------------------------------*/
// 根据regionCode查询租户id
export function getTenantId(params) {
    return request({
        url: '/user/tenants/findTenantByRegionCode',
        method: 'GET',
        params
    })
}

// 查询区划
export function findLowerList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/region/findLowerList',
        method: 'POST',
        params
    })
}



/**------------------------------------------------------------------------------------*/

// 一码游地图导览接口
export function getMapMarkers(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/user/tbVillageOneCodeContent/content/findVillageOneCodeMapList',
        method: 'POST',
        params
    })
}