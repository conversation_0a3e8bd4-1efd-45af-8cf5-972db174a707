<template>
  <view>
    <gridtree
      :checkList="checkList"
      v-if="gridList.length > 0"
      :options="prop"
      @sendValue="confirm"
      :searchIf="false"
      :isCheck="true"
      :orgName="villageName"
      :treeNone="gridList"
    ></gridtree>
    <view class="empty-status" v-else>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
  </view>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { reactive, ref, computed } from 'vue'
import gridtree from '../../components/wgtree/tree.vue'
//import { treeNode } from './data.js'
import { getAllGrid } from '../../api/eventPublication/eventPublication'
import { useUserStore } from '@/store/user.js'
import {
  NO_MORE_IMG
} from '@/common/net/staticUrl'
const tree = ref([])
const checkList = ref([])
const backList = ref([])
// const villageName = ref('')
// 村名
const villageName = computed(() => {
	return userStore.userInfo.customParam?.tenantName || ''
})
const gridList = ref([])
const userStore = useUserStore()
const prop = ref({
  label: 'listname',
  children: 'children',
  multiple: true,
  checkStrictly: true,
})

onLoad((option) => {
  //console.log(treeNode, 'fffff')
  // tree.value = treeNode //树形数据赋值
  getTree()
})

//获取选中的值
function confirm(val, back) {
  if (back) {
    backConfirm(val)
    return
  }
  backList.value = val
}
function getGridArray(arr) {
  arr.forEach((item) => {
    if(!!item.memberId) {
      item.user = true
      item.listname = item.name
      item.id = item.memberId
    } else {
		item.listname = item.gridName
		item.id = item.gridId
		item.user = false
		let memberChild = getGridArray([...item.memberList])
		let gridChild = getGridArray([...item.gridList])
		item.children = [...gridChild, ...memberChild]
	}
  })
  return arr
}
async function getTree() {
  try {
    // let res = await gridOrgAndMember()
    let res = await getAllGrid()

    if (res.success) {
      // villageName.value = res.data.villageName
      gridList.value = getGridArray([res.data])
	  console.log('gridList.value------', gridList.value);
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
	  console.log('err-------', e);
    uni.showToast({
      title: '获取成员失败！',
      icon: 'none',
    })
  }
}

// 返回上一页传参
function backConfirm(val) {
  uni.$emit('selectSuccess', { list: val })
  setTimeout(() => {
    uni.navigateBack({
      delta: 1,
      complete: () => {},
    })
  }, 500)
}
</script>
<style>
page {
  background-color: #f8f8fa;
}
</style>
<style scoped lang="scss">
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>