/*
 startYear 开始年份 参数为空时为当前年份前5年
 endYear 结束年份 参数为空时为当前年份
*/
export function getYearArr(startYear, endYear) {
  let yearArr = [],
    prDate = new Date(),
    presentYear = prDate.getFullYear() //当前年份
  if (!endYear) {
    //为空为当前年份
    endYear = presentYear
  }
  if (!startYear) {
    //为空为当前年前5年
    startYear = presentYear - 5
  }
  for (let i = startYear; i <= endYear; i++) {
    yearArr.push(i)
  }
  return yearArr
}

export function getMonthArr() {
  let monthArr = []
  for (let i = 1; i < 13; i++) {
    monthArr.push(i)
  }
  return monthArr
}

export function getQuarterArr() {
  let quarterArr = []
  for (let i = 1; i < 5; i++) {
    quarterArr.push(i)
  }
  return quarterArr
}
