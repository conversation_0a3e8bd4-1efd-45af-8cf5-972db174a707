import {
    request
} from '../request.js';


export function findOfPage(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/agricultureProduct/findOfPage",
      method: "POST",
      params
    });
  }
  
  
  export function agricultureTypes() {
    return request({
      url: "/user/dict/data/list/agriculture_type",
      method: "GET"
    });
  }
  export function findOne(params){
    params['uniContentType'] = 'json'
    return request({
        url: "/village/agricultureProduct/findOne",
        method: "GET",
        params
    })
  }

  export function findAddressList(){
    let params = {}
    params['uniContentType'] = 'json'
    return request({
      url: "/village/productDelivery/findList",
      method: "GET",
      params
    });
  }

