import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'

import {
	API_ASKFREELY_URL,
	API_ASKFREELYDETAIL_URL
} from '@/common/net/netUrl.js'

// 随心问数据列表
export function getData(params) {
	return request({
		url: API_ASKFREELY_URL,
		method: "GET",
		params,
	});
}

// 随心问数据详情
export function getDetail(params) {
	return request({
		url: API_ASKFREELYDETAIL_URL,
		method: "GET",
		params,
	});
}