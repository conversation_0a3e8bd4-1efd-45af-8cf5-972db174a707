<template>
  <view class="content">
    <swiper class="swiper" :autoplay="autoplay" :duration="duration">
      <swiper-item>
        <view class="swiper-item">
          <view class="swiper-item-img"
            ><image :src="title_01" mode="aspectFit"></image
          ></view>
          <view class="swiper-item-img"
            ><image :src="icon_01" mode="aspectFit"></image
          ></view>
        </view>
        <view class="jump-over" @tap="launchFlag()">{{ jumpover }}</view>
      </swiper-item>
      <swiper-item>
        <view class="swiper-item">
          <view class="swiper-item-img"
            ><image :src="title_02" mode="aspectFit"></image
          ></view>
          <view class="swiper-item-img"
            ><image :src="icon_02" mode="aspectFit"></image
          ></view>
        </view>
        <view class="jump-over" @tap="launchFlag()">{{ jumpover }}</view>
      </swiper-item>
      <swiper-item>
        <view class="swiper-item">
          <view class="swiper-item-img"
            ><image :src="title_03" mode="aspectFit"></image
          ></view>
          <view class="swiper-item-img"
            ><image :src="icon_03" mode="aspectFit"></image
          ></view>
        </view>
        <view class="jump-over" @tap="launchFlag()">{{ jumpover }}</view>
      </swiper-item>
      <swiper-item>
        <view class="swiper-item">
          <view class="swiper-item-img"
            ><image :src="title_04" mode="aspectFit"></image
          ></view>
          <view class="swiper-item-img"
            ><image :src="icon_04" mode="aspectFit"></image
          ></view>
        </view>
        <view class="experience" @tap="launchFlag()">{{ experience }}</view>
      </swiper-item>
    </swiper>
    <view class="uniapp-img"
      ><image :src="uniapp4_2x" mode="aspectFit"></image
    ></view>
  </view>
</template>

<script>
import {
  ICON01_URL,
  ICON02_URL,
  ICON03_URL,
  ICON04_URL,
  TITLE01_URL,
  TITLE02_URL,
  TITLE03_URL,
  TITLE04_URL,
  UNIAPP4_2X_URL,
} from '@/common/net/staticUrl.js'
export default {
  data() {
    return {
      background: ['color1', 'color2', 'color3'],
      autoplay: false,
      duration: 500,
      jumpover: '跳过',
      experience: '立即体验',
      icon_01: ICON01_URL,
      icon_02: ICON02_URL,
      icon_03: ICON03_URL,
      icon_04: ICON04_URL,
      title_01: TITLE01_URL,
      title_02: TITLE02_URL,
      title_03: TITLE03_URL,
      title_04: TITLE04_URL,
      uniapp4_2x: UNIAPP4_2X_URL,
    }
  },
  methods: {
    launchFlag: function () {
      /**
       * 向本地存储中设置launchFlag的值，即启动标识；
       */
      uni.setStorage({
        key: 'launchFlag',
        data: true,
      })
      uni.reLaunch({
        url: '/pages/login/login',
      })
    },
  },
}
</script>
<style>
page,
.content {
  width: 100%;
  height: 100%;
  background-size: 100% auto;
  padding: 0;
}
.swiper {
  width: 100%;
  height: 80%;
  background: #ffffff;
}
.swiper-item {
  width: 100%;
  height: 100%;
  text-align: center;
  position: relative;
  display: flex;
  /* justify-content: center; */
  align-items: flex-end;
  flex-direction: column-reverse;
}
.swiper-item-img {
  width: 100%;
  height: auto;
  margin: 0 auto;
}
.swiper-item-img image {
  width: 80%;
}
.uniapp-img {
  height: 20%;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.uniapp-img image {
  width: 40%;
}

.jump-over,
.experience {
  position: absolute;
  height: 60upx;
  line-height: 60upx;
  padding: 0 40upx;
  border-radius: 30upx;
  font-size: 32upx;
  color: #454343;
  border: 1px solid #454343;
  z-index: 999;
}
.jump-over {
  right: 45upx;
  top: 125upx;
}
.experience {
  right: 50%;
  margin-right: -105upx;
  bottom: 0;
}
</style>
