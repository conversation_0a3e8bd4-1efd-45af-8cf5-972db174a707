<template>
 <view class="contentcontainer" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
  <u-navbar title="企业列表" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
       <logoutbtn></logoutbtn>

  <u-sticky>
      <view class="searchCont">
        <u-input v-model="contentTitle"  @confirm="searchHanlder"
        confirmType="search" placeholder="请输入企业关键字搜索" border="none" height="68rpx" />
        <view class="date-select">
          <u-input v-model="dateText" placeholder="行业类型" readonly border="none" @click="showType = true">
            <template #suffix>
              <u-icon name="arrow-down" @click="showType = true" style="margin-right: 10rpx;"></u-icon>
            </template>
          </u-input>
        </view>
      </view>
    </u-sticky>
    <scroll-view 
      class="secondBox" 
      scroll-y 
      @scrolltolower="getList"
      :style="{height: selectitems.length > 0 ? 'calc(100vh - 300rpx)' : 'calc(100vh - 180rpx)'}"
    >
      <view class="empty-status" v-if='loaded === true && contentlist.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
      <block v-if="contentlist.length > 0">
        <view
          class="columnitem"
          :class="selectitems.map(company=>company.enterpriseId).indexOf(item.enterpriseId)>-1 ? 'selected' : ''"
          v-for="item in contentlist"
          :key="item.enterpriseId"
          @click="getData(item)"
        >
          <view class="contentline">
            <view class="content">
                <text class="tag">{{item.enterpriseName}}</text>
                <view class="desc">{{item.enterpriseNature}}</view>
                <view class="time">成立时间：{{item.establishDate}}</view>
            </view>
            <!-- <view class="seedetail" @click.stop="gotodetail(item)" > <u-icon name="eye-fill"  color="#3cc16c" ></u-icon></view> -->
           
          </view>
        </view>
      </block>
      <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && contentlist.length === 0)"
      ></u-loadmore>
    </scroll-view>
    <view class="bottom u-border-top" v-show="selectitems.length">
      <view class="btn pjBtn" @click="goSub">确定</view>
      <view class="paddingbottom"></view>
    </view>
    <u-picker closeOnClickOverlay @close="showType = false" :show="showType" :columns="[industryCategories]" keyName="label" @confirm="onTypeConfirm" @cancel="showType = false" />
  </view>
</template>
  
  <script setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { AdminService } from "../../api/anquanjianguan/adminstrator"
import { NO_MORE_IMG, LIVE_INVENTORY_BG } from '@/common/net/staticUrl.js'
import { getDicts } from '@/common/net/contacts/contacts.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const showType = ref(false)
const contentTitle = ref('')
const dateText = ref('')
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  enterpriseName: undefined,
  industryCategory: undefined
})

const contentlist = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
const selectitems = ref([])
const industryCategories = ref([])
onLoad(async () => {
  const response = await getDicts('industryCategories')
    let list = response.data
    list.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    // 添加"全部"选项
    industryCategories.value = [{ label: '全部', value: '' }, ...list]

  getList()
})
// picker 确认选择
function onTypeConfirm(e) {
  const selectedItem = e.value[0]
  dateText.value = selectedItem.label
  queryParams.industryCategory = selectedItem.value || undefined
  showType.value = false
  // 自动触发搜索
  searchHanlder()
}
function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }
  queryParams.enterpriseName = contentTitle.value

  AdminService.getCompanyList(queryParams)
    .then((res) => {
      if (res.success) {
        total.value = res.data.total
        if (queryParams.pageNum > 1) {
          contentlist.value = [...contentlist.value, ...res.data.records]
        } else {
          contentlist.value = res.data.records
        }
        if (total.value === contentlist.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.pageNum = queryParams.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
}
async function goSub() {
   if(selectitems.value.length){
  
  
      uni.$emit('selectcompanys', selectitems.value )
      console.log(selectitems.value)
      uni.navigateBack({
        delta: 1,
        complete: () => {
          console.log('返回成功')
        },

      })
  
  
  }
  else{
    uni.showToast({
        title: '请选择企业！',
        icon: 'none',
      })
  }
}
function searchHanlder() {
  contentlist.value = []
  total.value = 0
  queryParams.pageNum = 1
  loadStatus.value = 'more'
  showType.value = false
  getList()
}
function getData(item) {
    item.selectcontent = !item.selectcontent
   
   
    if( item.selectcontent){
      selectitems.value.push(item)
    }
    else{
      // 删除selectitems.value中enterpriseId和item.enterpriseId相同的项
      selectitems.value = selectitems.value.filter(i => i.enterpriseId !== item.enterpriseId)
    }
}
function gotodetail(item){
  uni.navigateTo({
      url:
        '/jimo/anquanjianguan/adminstrator/companyDetail?id=' + item.enterpriseId
    })
}
</script>
  <style>
page {
  background: #f4f8f7;
}
</style>
  
  <style lang="scss" scoped>
   .searchCont {
    padding: 0 20rpx;
    height: 68rpx;
    border-radius: 34rpx;
    display: flex;
    align-items: center;
    margin: 0 22rpx 22rpx 22rpx;
    box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
    background: #fff;
    box-sizing: border-box;
    .u-input {
      flex: 2;
      margin-right: 16rpx!important;
    }
    .date-select{
      flex: 1;
    }
  }
  .empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC-Regular, PingFang-SC;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  //display: flex;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 180rpx;
}
::v-deep .searchtab{
    padding: 20rpx 0;
    background-color: #f4f8f7;
}
.contentcontainer {
  padding:0  20rpx;
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
  ::v-deep .tktip {
		padding: 80rpx 60rpx 60rpx 60rpx;
		width: 650rpx;
		box-sizing: border-box;

		.content {
		  
			margin-bottom: 20rpx;
      .checkGroup{
        margin-top: 20rpx;
        display: flex;
        flex-wrap: wrap;
      .checkbox{
        margin-right: 16rpx;
      }
      }
		}

		.btngroup {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.cancel {
				width: 240rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background: #ffffff;
				border-radius: 41rpx;
				border: 2rpx solid #e5e5e5;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #555555;
			}

			.okbtn {
				width: 240rpx;
				height: 80rpx;
				background: #0cbe88;
				border-radius: 41rpx;
				line-height: 80rpx;
				text-align: center;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ffffff;
			}
		}
	}
  .secondBox {
    z-index: 2;
    top: 120rpx;
    height: calc(100vh - 120rpx);
    box-sizing: border-box;


    .columnitem {
      background: #ffffff;
      border-radius: 10rpx;
      padding: 17rpx;
      margin-bottom: 20rpx;
      .contentline {
        display: flex;
        align-items: center;
        position: relative;
        .seedetail{
         position: absolute;
         bottom: 0;
         right: 0;
         z-index: 67;
        }
        .content {
         flex: 1;
          .tag {
            background: rgba(11, 189, 136, 0.05);
            border-radius: 5rpx;
            border: 0.5px solid #0bbd88;
            padding: 0 9rpx;
            height: 52rpx;
           
            font-size: 28rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #0bbd88;
            line-height: 52rpx;
          }
          .desc {
            margin-top: 5rpx;
            font-size: 28rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            color: #000000;
            line-height: 52rpx;
          }
          .time {
            font-size: 26rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 37rpx;
          }
        }
      }
    }
    .selected {
      box-shadow: 0rpx 2rpx 12rpx 0rpx rgba(11, 189, 136, 0.06);
      border: 3rpx solid #0bbd88;
    }
  }
}
</style>
  