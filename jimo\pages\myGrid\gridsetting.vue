<template>
  <view>
    <u-navbar
      title="组织结构"
      @leftClick="goback"
      bgColor="white"
      :placeholder="true"
      titleStyle="color:#000"
      leftIconColor="#000"
    >
    </u-navbar>
    <gridtree
      :checkList="checkList"
      v-if="gridList.length > 0"
      :options="prop"
      :isCheck="false"
      :orgName="villageName"
      :searchIf="false"
      ref="gridtreeref"
      :treeNone="gridList"
      :showsubmit="false"
    ></gridtree>
    <view class="empty-status" v-if='showEmpty'>
		<view class="empty-icon">
			<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
				height="150rpx"></u-image>
			<view class="no-more">
				<span class="txt">暂无数据</span>
			</view>
		</view>
	</view>
  </view>
</template>

<script setup>
import { onLoad, onBackPress } from '@dcloudio/uni-app'
import { reactive, ref, computed } from 'vue'
import gridtree from '../../components/wgtree/wgtree.vue'
import { getAllGrid } from '../../api/mygrid/mygrid'
import { useUserStore } from '@/store/user.js'
import {
  NO_MORE_IMG
} from '@/common/net/staticUrl'
// import {
// 	treeNode
// } from './data.js'
const tree = ref([])
const checkList = ref([])
const backList = ref([])
// const villageName = ref('')
const userStore = useUserStore()
const villageName = computed(() => {
	return userStore.userInfo.customParam?.tenantName || ''
})
const gridList = ref([])
const parentId = ref('')
const gridtreeref = ref(null)
const prop = ref({
  label: 'listname',
  children: 'children',
})
const showEmpty = ref(false)
onLoad((option) => {
  parentId.value = option.id
  //tree.value = treeNode; //树形数据赋值
  getTree()
})

onBackPress((e) => {
  console.log(e.from)
  if (e.from == 'backbutton') {
    if (gridtreeref.value.getCurrentIndex() == 0) {
     return true
  } else {
    gridList.value = [...gridList.value]
  //
  }
  } else {
    return true
  }
})
function goback() {
 
    uni.navigateBack({
      delta: 1,
    })
 
}
function getGridArray(arr) {
  arr.forEach((item) => {
    if (!!item.memberId) {
		item.user = true
		item.listname = item.name
		item.id = item.memberId
    } else {
      item.listname = item.gridName
      item.id = item.gridId
      item.user = false
      let memberChild = getGridArray([...item.memberList])
      let gridChild = getGridArray([...item.gridList])
      item.children = [...gridChild, ...memberChild]
    }
  })
  return arr
}
async function getTree() {
  try {
    let gridId = parentId.value == -1 ? undefined : parentId.value
    // let res = await gridOrgAndMemberApp({ gridId })
	let res = await getAllGrid({ gridId })

    console.log(res)
    if (res.success) {
      // villageName.value = res.data.villageName || res.data.gridName
      console.log(res.data)
      gridList.value = getGridArray([res.data])
      console.log(gridList.value)
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
	if(gridList.value.length == 0) {
			  showEmpty.value = true;
	}
  } catch (e) {
    uni.showToast({
      title: '获取成员失败！',
      icon: 'none',
    })
  }
}

</script>
<style>
page {
  background-color: #f8f8fa;
}
</style>
<style scoped lang="scss">
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>