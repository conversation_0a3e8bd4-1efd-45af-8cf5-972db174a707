<template>
	<view class="report-container">
		<view class="detail-info">
			<view class="label">
				申报对象
			</view>
			<view class="content content-flex" @click="goPeopleList">
				<view class="">
					{{reportInfo.reportName}}
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view class="label">
				申报积分标准
			</view>
			<view class="content content-flex" @click="goStandardDetail">
				<view class="">
					{{reportInfo.reportStandard}}
				</view>
				<u-icon name="arrow-right"></u-icon>
			</view>
			<view class="label">
				申报积分
			</view>
			<view class="content">
				{{reportInfo.points}}
			</view>
			<view class="label">
				申报说明
			</view>
			<view class="content">
				{{reportInfo.reportDesc}}
			</view>
			<view class="label">
				申报凭证
			</view>
			<view class="content content-img">
				<view class="image-item" v-for="(image,imageIndex) in reportInfo.reportImgs" :key="imageIndex">
					<!-- <u-image :show-loading="true" :src="image" width="80rpx" height="80rpx"
						@click="previewPics(index)"></u-image> -->
					<safe-image :showLoading="true" :src="image" width="80rpx" height="80rpx" :imgIndex='index'></safe-image>
				</view>
				<view class="" v-if="reportInfo.reportImgs.length == 0">
					无
				</view>
			</view>
			<view class="report-status" :class="reportStatusClass">
				{{reportInfo.pointApplyStatusName}}
			</view>
		</view>
		<view class="flow-container">
			<view class="main-title">
				申报流程
			</view>
			<view class="flow-content">
				<view class="flow-content-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/pass.svg" width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								申报人
							</view>
						</view>
						<view class="date-box">
							{{ reportInfo.applyDate }}
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people">
								{{ reportInfo.applyPeople }}
							</view>
						</view>
					</view>
				</view>
				<!-- 需要手动选择一个审批人 -->
				<view class="flow-content-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/handling.svg" v-if="reportInfo.auditStatus == '0'"
								width="46rpx" height="46rpx" mode="aspectFit"></u-image>
							<u-image src="@/jimo/static/images/flowPage/pass.svg" v-else width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								审批人
							</view>
						</view>
						<view class="date-box">
							{{ reportInfo.auditDate }}
						</view>
					</view>
					<view class="people-box">
						<view class="title-box">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people approver">
								<view class="people-name">
									{{reportInfo.approver}}
								</view>
								<view class="people-status" :class="reportStatusClass">
									{{reportInfo.auditStatusName}}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="flow-content-item send-item">
					<view class="step-box">
						<view class="title-box">
							<u-image src="@/jimo/static/images/flowPage/handling.svg" v-if="reportInfo.auditStatus == '0'" 
								width="46rpx" height="46rpx" mode="aspectFit"></u-image>
							<u-image src="@/jimo/static/images/flowPage/pass.svg" v-else width="46rpx"
								height="46rpx" mode="aspectFit"></u-image>
							<view class="title">
								抄送人
							</view>
						</view>
						<view class="date-box" v-if="!!reportInfo.sendPeopleArr">
							{{ reportInfo.auditDate }}
						</view>
					</view>
					<view class="people-box">
						<view class="title-box" style="width: 100%;">
							<view class="left-box">
								<view class="line"></view>
							</view>
							<view class="people send-people u-line-1">
								<!-- 如果有配置好的抄送人则直接展示，不能修改 -->
								{{reportInfo.sendPeopleArr || '无'}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="bottom-box paddingbottom" :class="{'delete-only': showDelBtn && !showHandleBar}" 
		v-if="(sourceType=='1' && reportInfo.auditStatus == '0' && userStore.currentRole === 'village_cadres') || (sourceType == '2' && reportInfo.auditStatus == '0')">
		<view class="delete-btn" v-if="showDelBtn" @click.stop="delApply">删除</view>
		<view class="handle-box" v-if='showHandleBar'>
			<view class="refuse-btn" @click.stop="openPop('rejected')">拒绝</view>
			<view class="agree-btn" @click.stop="openPop('passed')">同意</view>
		</view>
	</view>
	<u-popup v-if="showRemark" :show="showRemark" mode="bottom" :customStyle="popupStyle" @close="popclosed"
		:overlay='false' @open="showRemark=true" :safeAreaInsetBottom="true" :zIndex='998'>
		<view class="pop-container">
			<div class="comment-container">
				<view class="comment-title">
					确认{{currentOperateName}}
				</view>
				<view class="textarea-box">
					<textarea class="textarea-content" placeholder="请输入审批意见" placeholder-style="color:#c0c4cc"
						:disable-default-padding="true" :value="remarkData" :show-confirm-bar="false"
						@input="inputHandle($event, 200)" @confirm="inputHandle($event, 200)" maxlength="200"
						:cursor-spacing="20"></textarea>
					<view class="fontnum"><text>{{fontnum}}/200</text></view>
				</view>
				<view class="submit-btn" @click="submit">
					确认{{currentOperateName}}
				</view>
			</div>
		</view>
	</u-popup>
	<u-overlay :show="showOverlay" :zIndex='997' @click="hideOverlay"></u-overlay>
</template>
<script setup>
	import { ref, reactive, nextTick, computed, defineExpose } from 'vue'
	import { onShow, onLoad, onReachBottom, onUnload } from '@dcloudio/uni-app'
	import { NO_MORE_IMG } from '@/common/net/staticUrl.js'
	import { findInfoMobile,findPartyInfoMobile,deleteInfoMobile,deletePartyInfoMobile,auditInfoMobile,auditPartyInfoMobile,getIsApprover } from '../../api/points/points';
	import { useUserStore } from '@/store/user.js'
	import { setPoints} from '../../api/burialPoint.js'
	import {
		useTokenStore
	} from '@/store/token.js'
	const tokenStore = useTokenStore()
	const pointApplyId = ref('') // 申报记录Id
	const userStore = useUserStore()
	// 申报信息
	const reportInfo = reactive({
		reportName: '', // 申报对象
		reportStandard: '', // 积分标准
		reportStandardId: '', // 积分标准id
		points: '', // 申报积分
		reportDesc: '', // 申报说明
		reportImgs: [], // 申报凭证
		applyPeople: '', // 提交人
		applyPeopleId: '', // 提交人ID
		applyDate: '', // 提交时间
		approver: '', // 审批人
		approverStatus: '', // 审批状态
		approverPeopleId: '', // 审批人ID
		auditDate: '', // 审批时间
		sendPeopleArr: '', // 抄送人
		pointApplyStatus: '' ,// 申报状态值
		pointApplyStatusName: '', // 申报状态名
		auditStatus: '', // 审批状态
		auditStatusName: '', // 审批状态名
	})
	const showHandleBar = ref(false) // 是否展示操作栏
	const showDelBtn = ref(false) // 是否展示删除按钮
	const showRemark = ref(false) // 展示操作意见弹窗
	const showOverlay = ref(false)// 展示遮罩
	const currentOperate = ref('passed') // 当前操作 同意还是拒绝
	const currentOperateName = ref('同意') // 当前操作 同意还是拒绝
	const remarkData = ref('') // 审批意见
	const popupStyle = ref({
		width: '100%',
		borderTopLeftRadius: '8rpx',
		borderTopRightRadius: '8rpx'
	})
	const reportStatusClass = computed(() => {
		if (reportInfo.auditStatus == '0') {
			return 'wait'
		} else if (reportInfo.auditStatus == '1') {
			return 'agree'
		} else if (reportInfo.auditStatus == '2') {
			return 'refuse'
		}
	})
	let sourceType = ref('1')  //1为家庭 2为党员
	onLoad((options) => {
		sourceType.value = options?.sourceType || '1'
		if (!!options.pointApplyId) {
			pointApplyId.value = options.pointApplyId
		}
		// 获取申报信息
		getReportInfo()
		// 埋点-进入申报详情
		let param = {
			eventId : 'enter_reporting_detail',
			attributeValue: pointApplyId.value
		}
		setPoints(param)
		uni.$on('clickImg', (index) => {
			previewPics(index)
		})
		findIsApprover()
	})
	onUnload(() => {
		uni.$off('clickImg')
	})
	// 获取申报信息
	function getReportInfo() {
		uni.showLoading({
			title: '加载中...',
			mask: true,
		})
		let param = {
			pointApplyId: pointApplyId.value
		}
		let api = sourceType.value == '2' ? findPartyInfoMobile : findInfoMobile
		api(param).then(res => {
			if (res.success && res.data) {
				reportInfo.reportName = res.data.peopleNames
				reportInfo.reportStandard = res.data.title
				reportInfo.reportStandardId = res.data.pointStandardId
				reportInfo.points = res.data.applyPoint
				reportInfo.reportDesc = res.data.content
				reportInfo.applyPeople = res.data.applyPeopleName
				reportInfo.approver = res.data.nodePeopleName
				reportInfo.auditStatus = res.data.auditStatus
				reportInfo.auditStatusName = res.data.auditStatusName
				reportInfo.pointApplyStatus = res.data.pointApplyStatus
				reportInfo.pointApplyStatusName = res.data.pointApplyStatusName
				reportInfo.sendPeopleArr = res.data.sendPeopleName
				reportInfo.applyDate = res.data.applyDate
				reportInfo.auditDate = res.data.auditDate
				showDelBtn.value = res.data.deleteFlag
				showHandleBar.value = res.data.auditFlag
				if(!!res.data.document) {
					reportInfo.reportImgs = res.data.document.split(',')
				}
				uni.hideLoading()
			} else {
				console.log(res.message);
				uni.hideLoading()
				uni.showLoading({
					title: res.message || '申报信息获取失败',
					mask: true,
					complete: () => {
						setTimeout(() => {
							uni.navigateBack()
						},2000)
					}
				})
			}
		}).catch(err => {
			console.log(err);
			uni.showLoading({
				title: res.message || '申报信息获取失败',
				mask: true,
				complete: () => {
					setTimeout(() => {
						uni.navigateBack()
					},2000)
				}
			})
		})
		// handleOptionBar(res.data)
	}

	function handleOptionBar(reportInfo) {
		// 如果已处理过，则不展示操作栏 
		// if (reportInfo.reportStatus == 'agreed' || reportInfo.reportStatus == 'refused') {
		// 	showHandleBar.value = false;
		// 	return;
		// }
		// // 如果是自己发起的申报
		// if (selfPeopleId.value == reportInfo.applyPeopleId) {
		// 	// 如果提出了还没有人审批 且审批人不是自己，则只展示删除按钮
		// 	if () {
		// 		showHandleBar.value = true;
		// 		showDelBtn.value = true;
		// 		isDeleteOnly.value = true;
		// 	} else if () {
		// 		// 自己发起的且审批人是自己，则展示删除同意拒绝按钮
		// 		showHandleBar.value = true;
		// 		showDelBtn.value = true;
		// 		isDeleteOnly.value = false;
		// 	} else if () {
		// 		// 自己发起的且已经审批过，则不展示操作按钮
		// 		showHandleBar.value = false;
		// 	}
		// } else {
		// 	// 发起人不是自己，自己是审批人，则展示同意拒绝
		// 	if (currentHandler.auditPeopleId == selfPeopleId.value) {
		// 		showHandleBar.value = true;
		// 		showDelBtn.value = false;
		// 		isDeleteOnly.value = false;
		// 	} else {
		// 		showHandleBar.value = false;
		// 	}
		// }
	}
	//预览图片
	async function previewPics (index) {
		let pics = []
		// reportInfo.reportImgs.forEach((item) => {
		// 	pics.push(item)
		// })
		for(let i=0; i<reportInfo.reportImgs.length;i++) {
			let imgUrl = await loadImage(reportInfo.reportImgs[i])
			pics.push(imgUrl)
		}
		Promise.all(pics).then((result) => {
		})
		uni.previewImage({
			urls: pics,
			current: index
		})
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}
	// 删除申请
	function delApply() {
		// 埋点-点击积分申报删除按钮
		let reportParam = {
			eventId : 'delete_integral_reporting',
			attributeValue: 'delete_btn'
		}
		setPoints(reportParam)
		let params = {
			pointApplyId: pointApplyId.value
		}
		uni.showModal({
			title: '提示',
			content: '确定删除吗',
			success: function(res) {
				if (res.confirm) {
					uni.showLoading({
						title: '删除中...',
						mask: true,
					})
					let api = sourceType.value == '2' ? deletePartyInfoMobile : deleteInfoMobile
					api(params).then(res => {
						if (res.success) {
							uni.hideLoading();
							uni.showToast({
								title: "删除成功",
								icon: 'none',
								duration: 2000
							});
							// 埋点-积分申报删除成功
							let reportParam = {
								eventId : 'integral_reporting_deleted',
								attributeValue: 'success'
							}
							setPoints(reportParam)
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.hideLoading();
							uni.showToast({
								title: res.message || '删除失败，请稍后再试',
								duration: 2000,
								icon: 'none'
							})
							// 埋点-积分申报删除失败
							let reportParam = {
								eventId : 'integral_reporting_deleted',
								attributeValue: 'false'
							}
							setPoints(reportParam)
						}
					}).catch((err) => {
						// 埋点-积分申报删除失败
						let reportParam = {
							eventId : 'integral_reporting_deleted',
							attributeValue: 'false'
						}
						setPoints(reportParam)
					})
				} else if (res.cancel) {}
			}
		});
	}
	
	// 打开审批意见弹窗
	function openPop(operate) {
		if (operate == 'passed') {
			currentOperate.value = 'passed'
			currentOperateName.value = '同意'
			// 埋点-点击积分申报同意按钮
			let param = {
				eventId : 'approve_integral_reporting',
				attributeValue: 'approve_btn'
			}
			setPoints(param)
		} else if (operate == 'rejected') {
			currentOperate.value = 'rejected'
			currentOperateName.value = '拒绝'
			// 埋点-点击积分申报拒绝按钮
			let param = {
				eventId : 'reject_integral_reporting',
				attributeValue: 'reject_btn'
			}
			setPoints(param)
		}
		showOverlay.value = true
		showRemark.value = true
	}
	// 点击遮罩关闭
	function hideOverlay() {
		remarkData.value = ''
		showOverlay.value = false
		showRemark.value = false
		fontnum.value = 0
	}
	// 关闭pop
	function popclosed() {
		remarkData.value = ''
		focus.value = false
		showRemark.value = false
		fontnum.value = 0
	}
	// 输入审批意见
	const _inputTimer = ref(null)
	const fontnum = ref(0)
	function inputHandle(e, maxLength) {
	    clearTimeout(_inputTimer.value)
	    _inputTimer.value = setTimeout(() => {
	        let {
	            value: inputVal
	        } = e.detail
	        if (inputVal.length > maxLength) {
	            remarkData.value = inputVal.slice(0, maxLength)
	            fontnum.value = maxLength
	        } else {
	            remarkData.value = inputVal
	            fontnum.value = inputVal.length
	        }
	    }, 100)
	}
	// 提交审批
	function submit() {
		let params = {
			pointApplyId : pointApplyId.value,
			auditType: currentOperate.value,
			remark: remarkData.value
		}
		let api = sourceType.value == '2' ? auditPartyInfoMobile : auditInfoMobile
		if (currentOperate.value == 'passed') {
			uni.showModal({
				title: '提示',
				content: '确定同意吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						api(params).then(res => {
							if (res.success) {
								// 埋点-积分申报同意-成功
								let param = {
									eventId : 'integral_reporting_approved',
									attributeValue: 'success'
								}
								setPoints(param)
							} else {
								// 埋点-积分申报同意-失败
								let param = {
									eventId : 'integral_reporting_approved',
									attributeValue: 'fail'
								}
								setPoints(param)
							}
							handleRes(res)
						}).catch((err) => {
							// 埋点-积分申报同意-失败
							let param = {
								eventId : 'integral_reporting_approved',
								attributeValue: 'fail'
							}
							setPoints(param)
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == 'rejected') {
			uni.showModal({
				title: '提示',
				content: '拒绝后积分申报将终止，确定拒绝吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						api(params).then(res => {
							if (res.success) {
								// 埋点-积分申报拒绝-成功
								let param = {
									eventId : 'integral_reporting_rejected',
									attributeValue: 'success'
								}
								setPoints(param)
							} else {
								// 埋点-积分申报拒绝-失败
								let param = {
									eventId : 'integral_reporting_rejected',
									attributeValue: 'fail'
								}
								setPoints(param)
							}
							handleRes(res)
						}).catch((err) => {
							// 埋点-积分申报拒绝-失败
							let param = {
								eventId : 'integral_reporting_rejected',
								attributeValue: 'fail'
							}
							setPoints(param)
						})
					} else if (res.cancel) {}
				}
			});
		}
	}
	// 处理接口返回结果
	function handleRes(res) {
		if (res.success) {
			uni.hideLoading();
			uni.showToast({
				title: "处理成功",
				icon: 'none',
				duration: 2000
			});
			setTimeout(() => {
				uni.navigateBack()
			}, 1000)
		} else {
			uni.hideLoading();
			showRemark.value = false;
			uni.showLoading({
				title: res.message || '处理失败，请稍后再试',
				mask: true,
				complete: () => {
					setTimeout(() => {
						uni.navigateBack()
					},2000)
				}
			})
		}
	}
	// 查看申报对象
	function goPeopleList() {
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/choosePeople?chooseType=browser&pointApplyId=${pointApplyId.value}&sourceType=${sourceType.value}`
		})
	}
	// 查看标准详情
	function goStandardDetail(item) {
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/pointsStandardDetail?pointApplyId=${pointApplyId.value}&sourceType=${sourceType.value}`
		})
	}
	const isApprover = ref('') // 是否是审批人
	// 查询是否为审批人(党员)
	async function findIsApprover(){
		try{
			let res = await getIsApprover()
			if(res.success){
				isApprover.value = res.data
			}else{
				uni.showToast({
					title:e,
					icon:'none'
				})
			}
		}catch(e){
			uni.showToast({
				title:e,
				icon:'none'
			})
		}
	}
</script>
<style lang="scss" scoped>
	.report-container {
		box-sizing: border-box;
		padding: 20rpx;
		width: 100%;
		height: 100vh;
		padding-bottom: calc(env(safe-area-inset-bottom) + 146rpx);
		overflow-y: auto;

		.detail-info {
			position: relative;
			padding: 20rpx;
			background-color: #fff;
			border-radius: 20rpx;

			.label,
			.content {
				width: 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				text-align: left;
				font-style: normal;
			}

			.label {
				margin-bottom: 20rpx;
				height: 40rpx;
				line-height: 40rpx;
				font-size: 26rpx;
				color: #999999;
			}

			.content {
				margin-bottom: 30rpx;
				font-size: 28rpx;
				color: #333333;
			}
			.content-img {
				display: flex;
				align-items: center;
			}
			.content-flex {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.report-status {
				position: absolute;
				top: 30rpx;
				right: 0;
				width: 150rpx;
				height: 47rpx;
				line-height: 47rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #FFFFFF;
				text-align: center;
				border-top-left-radius: 26rpx;
				border-bottom-left-radius: 26rpx;
			}

			.wait {
				background: #FF6B00;
			}
			.agree {
				background: #0BBD88;
			}
			.refuse {
				background: #f00;
			}
		}
	}

	.flow-container {
		margin-top: 20rpx;
		padding: 20rpx;
		box-sizing: border-box;
		background: #fff;
		border-radius: 20rpx;

		.main-title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
		}

		.flow-content-item {
			margin-top: 20rpx;
			width: 100%;
			height: 118rpx;

			.step-box,
			.people-box {
				display: flex;
				justify-content: space-between;
				align-items: center;

			}

			.people-box {
				min-height: 72rpx;

				.left-box {
					display: flex;
					justify-content: center;
					width: 46rpx;

					.line {
						width: 2rpx;
						height: 72rpx;
						background: #DDDDDD;
					}

					.noline {
						width: 2rpx;
						height: 72rpx;
						background: transparent;
					}
				}

				.people {
					margin-left: 20rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #555555;
				}

				.approver {
					display: flex;
					align-items: center;

					.people-status {
						margin-left: 20rpx;
						width: 103rpx;
						height: 48rpx;
						border-radius: 4rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 26rpx;
						line-height: 48rpx;
						text-align: center;
					}

					.wait {
						color: #FFBD8B;
						border: 1rpx solid #FFBD8B;
					}
					.agree {
						color: #0BBD88;
						border: 1rpx solid #0BBD88;
					}
					.refuse {
						color: #f00;
						border: 1rpx solid #f00;
					}
				}

				.send-people {
					width: 100%;
				}
			}

			.title-box {
				display: flex;
				align-items: center;
				// width: 100%;

				.title {
					margin-left: 20rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					color: #333333;
				}
			}
			.date-box {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #999999;
			}

			.time {
				font-size: 26rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #999999;
			}
		}

		.send-item {
			height: auto;
		}
	}

	.bottom-box {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 2;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		width: 100vw;
		height: 126rpx;
		background: #ffffff;

		.handle-box {
			flex: 1;
			display: flex;
			justify-content: space-evenly;
			align-items: center;

			.agree-btn,
			.refuse-btn {
				width: 280rpx;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				text-align: center;
			}

			.agree-btn {
				margin-left: 20rpx;
				background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
				color: #FFFFFF;
			}

			.refuse-btn {
				background: #fff;
				border-radius: 40rpx;
				border: 1px solid #0BBD88;
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #0BBD88;
				box-sizing: border-box;
			}
		}

		.delete-btn {
			width: 100rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			text-align: center;
		}
	}

	.delete-only {
		justify-content: flex-start;
		padding-left: 40rpx;
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	.pop-container {
		padding: 20rpx;
	
		.comment-container {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
	
			.comment-title {
				margin-bottom: 20rpx;
				font-size: 30rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
			}
	
			.textarea-box {
				position: relative;
				margin-bottom: 20rpx;
				width: 100%;
				.textarea-content {
				    width: 100%;
				    box-sizing: border-box;
				    padding: 10rpx;
				    border-radius: 4px;
				    background-color: #fff;
				    border-width: 1rpx;
				    border-color: #dadbde;
				    border-style: solid;
				}
				
				.fontnum {
				    position: absolute;
				    right: 5px;
				    bottom: 2px;
				    font-size: 12px;
				    color: #909193;
				    background-color: #ffffff;
				    padding: 1px 4px;
				}
			}
	
			.submit-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 80rpx;
				background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F0F7F7;
	}
</style>