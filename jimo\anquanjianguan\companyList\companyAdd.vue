<template>
  <view class="company-add" :style="{ backgroundImage: `url(${LIVE_INVENTORY_BG})` }">
    <u-navbar title="新增企业" bgColor="rgba(0, 0, 0, 0)" :autoBack="true" :placeholder="true" leftIconColor="#000"
      titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>

    <view class="container">
      <!-- 数据状态指示器 -->
      <view v-if="hasDraftData" class="draft-indicator">
        <u-icon name="info-circle" color="#007bff" size="16"></u-icon>
        <text>您有未完成的草稿数据</text>
        <view class="draft-actions">
          <text @click="restoreLastDraft" class="restore-link">恢复</text>
          <text @click="clearDraftData" class="clear-link">清除</text>
        </view>
      </view>

      <scroll-view @scroll="listScroll" :scroll-top="scrollTop" scroll-y="true" class="tab-content" ref="tabContentRef"
        :style="{ height: hasDraftData ? 'calc(100vh - 300rpx)' : 'calc(100vh - 224rpx)' }">
        <u--form ref="formRef" :model="form" labelPosition="top" :label-width="150"
          :labelStyle="{ fontWeight: 'bold', paddingBottom: '10rpx' }" :rules="rules">
          <div class="collapse-title">营业信息</div>
          <image class="yyzz" @click="uploadyyzzhandler" src="@/jimo/static/images/uploadyyzz.png"></image>
          <!-- 营业信息表单项 -->
          <u-form-item label="生产经营单位类型" prop="enterpriseNature" @click="showTypePicker = true" required>
            <u--input v-model="form.enterpriseNature" placeholder="请选择生产经营单位类型" border="none" readonly />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>
          <u-form-item label="企业名称" prop="enterpriseName" required>
            <u--input v-model="form.enterpriseName" placeholder="请输入企业名称" border="none" />
          </u-form-item>
          <u-form-item label="信用代码" prop="creditCode" required>
            <u--input v-model="form.creditCode" placeholder="请输入信用代码" border="none" />
          </u-form-item>

          <u-form-item label="成立日期" prop="establishDate" required>
            <u--input v-model="form.establishDate" placeholder="请输入日期，格式：yyyy-mm-dd" border="none"
              @blur="validateDate(form.establishDate, (value) => { form.establishDate = value })" />
          </u-form-item>


          <u-form-item label="所属社区" prop="communityValue" required @click="showecommunityValuePicker = true">
            <u--input v-model="communityLabel" placeholder="请选择所属社区" border="none" />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>
          <u-form-item label="经营地址" prop="businessAddress" @click="openMap" required>
            <view class="address-row">

              <u-textarea v-model="form.businessAddress" placeholder="请输入经营地址" border="none"></u-textarea>
              <u-icon name="map"></u-icon>
            </view>
          </u-form-item>

          <u-form-item label="法定代表人" prop="legalRepresentative" required>
            <u--input v-model="form.legalRepresentative" placeholder="请输入法定代表人" border="none" />
          </u-form-item>
          <u-form-item label="联系电话" prop="legalRepresentativePhone" required>
            <u--input type="number" maxlength="11" v-model="form.legalRepresentativePhone" placeholder="请输入联系电话"
              border="none" />
          </u-form-item>
          <u-form-item label="常用联系人" prop="actualController" required>
            <u--input v-model="form.actualController" placeholder="请输入常用联系人" border="none" />
          </u-form-item>
          <u-form-item label="联系电话" prop="actualControllerPhone" required>
            <u--input type="number" maxlength="11" v-model="form.actualControllerPhone" placeholder="请输入联系电话"
              border="none" />
          </u-form-item>
          <u-form-item v-if="form.enterpriseNature" :label="form.enterpriseNature === '个体工商户' ? '风险关注点' : '涉及重点行业'"
            prop="industryCategory" @click="showIndustryCategoryPicker = true" required>
            <u--input v-model="industryCategoryLabel"
              :placeholder="form.enterpriseNature === '个体工商户' ? '请选择风险关注点' : '请选择涉及重点行业'" border="none"
              @click="showIndustryCategoryPicker = true" />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>
          <template v-if="form.enterpriseNature === '工贸企业'">
            <u-form-item label="特种设备类型" prop="specialEquipmentType">
              <u--input v-model="form.specialEquipmentType" placeholder="请输入特种设备类型" border="none" />
            </u-form-item>
          </template>
          <template v-if="form.enterpriseNature === '个体工商户'">
            <u-form-item label="经营类型" prop="businessType" required @click="showBusinessTypePicker = true">
              <u--input v-model="form.businessType" placeholder="请选择经营类型" border="none" />
              <u-icon name="arrow-down"></u-icon>
            </u-form-item>
          </template>
          <u-form-item label="年营业额" prop="annualRevenue" required>
            <u-input v-model="form.annualRevenue" placeholder="请输入年营业额" border="none" type="number">
              <template #suffix>
                <text>万元</text>
              </template>
            </u-input>
          </u-form-item>

          <u-form-item label="企业规模" v-if="form.enterpriseNature === '养殖企业' || form.enterpriseNature === '工贸企业'"
            prop="enterpriseScale" @click="showScalePicker = true" required>
            <u--input v-model="form.enterpriseScale" placeholder="请选择企业规模" border="none" readonly />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>

          <u-form-item v-if="form.enterpriseNature" :label="form.enterpriseNature === '养殖企业' ? '固定人员' : '从业人员'"
            prop="employeeCount" required>
            <u-input type="number" v-model="form.employeeCount"
              :placeholder="form.enterpriseNature === '养殖企业' ? '请输入固定人员数量' : '请输入从业人员数量'" border="none">
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="临时用工" prop="employeeCountTemp" v-if="form.enterpriseNature === '养殖企业'" required>
            <u-input type="number" v-model="form.employeeCountTemp" placeholder="请输入临时用工数量" border="none">
              <template #suffix>
                <text>人</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item v-if="isadmin" label="巡检周期" prop="inspectInterval" required> 
            <u-input type="number" v-model="form.inspectInterval"
              placeholder="请输入巡检周期" border="none">
              <template #suffix>
                <text>月</text>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item label="最新巡检时间" prop="lastInspectTime" required>
            <u--input v-model="form.lastInspectTime" placeholder="请输入时间，格式：yyyy-mm-dd HH:mm:ss" border="none"
              @blur="validateDateTime(form.lastInspectTime, (value) => { form.lastInspectTime = value })" />

            <!-- <u--input v-model="form.lastInspectTime" placeholder="请选择最新巡检时间" border="none" readonly />
                  <u-icon name="arrow-down"></u-icon> -->
          </u-form-item>
          <u-form-item label="主营业务" prop="mainBusiness" required>
            <u-textarea v-model="form.mainBusiness" count maxlength="500" height="auto" autoHeight placeholder="请输入主营业务"
              border="none"></u-textarea>
          </u-form-item>
          <u-form-item label="养殖畜种" prop="breedingLivestock" v-if="form.enterpriseNature === '养殖企业'">
            <u--input v-model="form.breedingLivestock" placeholder="请输入养殖畜种" border="none" />
          </u-form-item>
          <u-form-item label="是否投保安责险" required>
            <u-radio-group v-model="hasSafetyInsurance" size="20" activeColor="#1BC78D">
              <u-radio :name="'NO'" label="否"></u-radio>
              <u-radio :name="'YES'" label="是"></u-radio>

            </u-radio-group>
          </u-form-item>
          <u-form-item label="安责险名称" v-if="hasSafetyInsurance === 'YES'" prop="safetyInsurance" required>
            <u--input v-model="form.safetyInsurance" placeholder="请输入安责险名称" border="none" />

          </u-form-item>
          <div class="collapse-title">企业证件</div>

          <u-form-item label="" prop="credentialList">
            <view class="certificates-list">
              <view class="certificate-item" v-for="(item, index) in form.credentialList" :key="index">
                <u-form-item label="证件名称" :prop="'credentialList.' + index + '.credentialName'" required>
                  <u--input v-model="item.credentialName" placeholder="请输入证件名称" border="none" />
                </u-form-item>
                <u-form-item label="证件开始日期" :prop="'credentialList.' + index + '.startDate'" required>
                  <u--input v-model="item.startDate" placeholder="请输入日期，格式：yyyy-mm-dd" border="none"
                    @blur="validateDate(item.startDate, (value) => { form.credentialList[index].startDate = value })" />
                </u-form-item>
                <u-form-item label="证件截止日期" :prop="'credentialList.' + index + '.endDate'" required>
                  <u--input v-model="item.endDate" placeholder="请输入日期，格式：yyyy-mm-dd" border="none"
                    @blur="validateDate(item.endDate, (value) => { form.credentialList[index].endDate = value })" />
                </u-form-item>
                <view class="certificate-images">
                  <view class="section-title">证件图片</view>
                  <view class="imgcell">
                    <template v-if="item.credentialImage">
                      <view class="imgitem" v-for="(img, imgIndex) in item.credentialImage.split(',')" :key="imgIndex">
                        <view class="img">
                          <image :src="img"></image>
                        </view>
                        <image class="closebtn" :src="CLOSE_BTN" @click="deleteCertificateImage(index, imgIndex)">
                        </image>
                      </view>
                    </template>
                    <image :src="IMG_ADD" style="width: 80rpx; height: 80rpx" @click="uploadCertificateImage(index)" />
                  </view>
                </view>
                <u-icon name="trash" class="trash" @click="removeCertificate(index)" size="24"></u-icon>
              </view>
              <u-button color="#0CBE88" style="width: 50%;" text="添加证件" @click="addCertificate"
                :customStyle="{ marginTop: '20rpx' }"></u-button>
            </view>
          </u-form-item>

          <template v-if="form.enterpriseNature == '工贸企业'">
            <div class="collapse-title">设备与工艺</div>
            <u-form-item label="主要设备设施" prop="mainEquipment">
              <u--input v-model="form.mainEquipment" placeholder="请输入主要设备设施" border="none" />
            </u-form-item>
            <u-form-item label="涉及工艺" prop="involvedTechnology">
              <u--input v-model="form.involvedTechnology" placeholder="请输入设计工艺" border="none" />
            </u-form-item>
            <u-form-item label="主要原料" prop="mainMaterials">
              <u--input v-model="form.mainMaterials" placeholder="请输入主要原料" border="none" />
            </u-form-item>

            <u-form-item label="主要/副产品" prop="mainProducts">
              <u-textarea v-model="form.mainProducts" placeholder="请输入主要/副产品" border="none"></u-textarea>
            </u-form-item>
          </template>
          <div class="collapse-title">厂房性质</div>
          <template v-if="form.enterpriseNature === '工贸企业' || form.enterpriseNature === '养殖企业'">
            <u-form-item label="土地规划" prop="landPlanning">
              <u--input v-model="form.landPlanning" placeholder="请输入土地规划" border="none" />
            </u-form-item>
            <u-form-item label="厂房性质" prop="factoryNature" :required="form.enterpriseNature === '工贸企业' ? true : false">
              <u--input v-model="form.factoryNature" placeholder="请输入厂房性质" border="none" />
            </u-form-item>
            <u-form-item label="厂房所有人" prop="factoryOwner">
              <u--input v-model="form.factoryOwner" placeholder="请输入厂房所有人" border="none" />
            </u-form-item>
            <u-form-item label="厂房电话" prop="factoryOwnerPhone">
              <u--input v-model="form.factoryOwnerPhone" maxlength="11" type="number" placeholder="请输入厂房电话"
                border="none" />
            </u-form-item>
            <u-form-item label="持有形式" prop="holdingForm">
              <u--input v-model="form.holdingForm" placeholder="请输入持有形式" border="none" />
            </u-form-item>
          </template>
          <template v-if="form.enterpriseNature === '个体工商户'">
            <u-form-item label="租赁/自有" prop="ownershipType" required>
              <u-radio-group v-model="form.ownershipType" size="20" activeColor="#1BC78D">
                <u-radio :name="'租赁'" label="租赁"></u-radio>
                <u-radio :name="'自有'" label="自有"></u-radio>

              </u-radio-group>
            </u-form-item>
            <u-form-item label="房东" prop="landlord">
              <u--input v-model="form.landlord" placeholder="请输入房东" border="none" />
            </u-form-item>
            <u-form-item label="房东电话" prop="landlordPhone">
              <u--input v-model="form.landlordPhone" maxlength="11" type="number" placeholder="请输入房东电话" border="none" />
            </u-form-item>
          </template>


          <u-form-item label="建筑面积" prop="buildingArea">
            <u-input v-model="form.buildingArea" placeholder="请输入建筑面积" border="none" type="number">
              <template #suffix>
                <text>平方米</text>
              </template>
            </u-input>
          </u-form-item>

          <div class="collapse-title">监管部门</div>
          <u-form-item label="行业类别" prop="industryClassification">
            <u--input v-model="form.industryClassification" placeholder="请输入行业类别" border="none" />
          </u-form-item>
          <u-form-item label="直接监管部门" prop="directSupervisionDept">
            <u--input v-model="form.directSupervisionDept" placeholder="请输入直接监管部门" border="none" />
          </u-form-item>
        </u--form>
      </scroll-view>


      <view class="bottom u-border-top">
        <view class="button-box">
          <view class="btn bg-gray" @click="goback">取消</view>
          <view class="btn" @click="submitForm">提交</view>
        </view>
      </view>
      <u-picker closeOnClickOverlay @close="showTypePicker = false" :show="showTypePicker" :columns="[typeList]"
        keyName="label" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
      <u-picker closeOnClickOverlay @close="showScalePicker = false" :show="showScalePicker"
        :columns="[form.enterpriseNature === '养殖企业' ? scaleyzList : form.enterpriseNature === '工贸企业' ? scalegmList : []]"
        keyName="label" @confirm="onScaleConfirm" @cancel="showScalePicker = false" />
      <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate"
        @close="showestablishDateicker = false" :show="showestablishDateicker" @confirm="onestablishDateConfirm"
        format="YYYY-MM-DD" mode="date" @cancel="showestablishDateicker = false" />
      <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate"
        @close="showCertificateStartDatePicker = false" :show="showCertificateStartDatePicker"
        @confirm="onCertificateStartDateConfirm" format="YYYY-MM-DD" mode="date"
        @cancel="showCertificateStartDatePicker = false" />
      <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate"
        @close="showCertificateEndDatePicker = false" :show="showCertificateEndDatePicker"
        @confirm="onCertificateEndDateConfirm" format="YYYY-MM-DD" mode="date"
        @cancel="showCertificateEndDatePicker = false" />
      <u-picker closeOnClickOverlay @close="showecommunityValuePicker = false" :show="showecommunityValuePicker"
        :columns="[communities]" keyName="label" @confirm="communityValueConfirmHandler"
        @cancel="showecommunityValuePicker = false" />
      <u-picker closeOnClickOverlay @close="showIndustryCategoryPicker = false" :show="showIndustryCategoryPicker"
        :columns="[form.enterpriseNature === '个体工商户' ? riskFocusPoints : industryCategories]" keyName="label"
        @confirm="industryCategoryConfirmHandler" @cancel="showIndustryCategoryPicker = false" />
      <u-datetime-picker closeOnClickOverlay :minDate="mindate" :maxDate="maxdate"
        @close="showlastInspectTimePicker = false" :show="showlastInspectTimePicker" @confirm="lastInspectTimeConfirm"
        format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showlastInspectTimePicker = false" />
      <u-picker closeOnClickOverlay @close="showBusinessTypePicker = false" :show="showBusinessTypePicker"
        :columns="[businessTypeList]" keyName="label" @confirm="onBusinessTypeConfirm"
        @cancel="showBusinessTypePicker = false" />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, nextTick, onBeforeUnmount, onMounted } from 'vue'
import { onReady, onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import { LIVE_INVENTORY_BG, IMG_ADD, CLOSE_BTN } from '@/common/net/staticUrl.js'
import { AdminService } from '../../api/anquanjianguan/adminstrator'
import { AJYService } from '../../api/anquanjianguan/companyList'
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import { getDicts } from '@/common/net/contacts/contacts.js'
import {
  useUserStore
} from '@/store/user.js'
const userStore = useUserStore()
import logoutbtn from '@/jimo/components/logoutbtn.vue'
const tabActive = ref(0)
const scrollTop = ref(0)
const tabContentRef = ref(null)
const mindate = ref(-2208988800000)
const maxdate = ref(new Date().getTime())
const hasSafetyInsurance = ref('NO')
const typeList = [
  { label: '个体工商户', value: '个体工商户' },
  { label: '工贸企业', value: '工贸企业' },
  { label: '养殖企业', value: '养殖企业' }
]
const riskFocusPoints = [
  { label: '“三合一”场所', value: '“三合一”场所' },
  { label: '人员密集', value: '人员密集' },
  { label: '电气焊', value: '电气焊' },
  { label: '电动车', value: '电动车' },
  { label: '燃气', value: '燃气' },
  { label: '消防', value: '消防' },
  { label: '用电', value: '用电' },
  { label: '其他', value: '其他' },
]
const businessTypeList = [
  { label: '小学或幼儿园', value: '小学或幼儿园' },
  { label: '小医院', value: '小医院' },
  { label: '小商店', value: '小商店' },
  { label: '小生产加工场所', value: '小生产加工场所' },
  { label: '小餐饮', value: '小餐饮' },
  { label: '小旅馆', value: '小旅馆' },
  { label: '小网吧', value: '小网吧' },
  { label: '小歌舞娱乐场所', value: '小歌舞娱乐场所' },
  { label: '小美容洗浴场所', value: '小美容洗浴场所' },
  { label: '其他', value: '其他' }
]
const form = reactive({
  enterpriseName: '',
  enterpriseNature: '',
  creditCode: '',
  enterpriseScale: '',
  breedingLivestock: '',
  establishDate: '',
  employeeCount: '',
  employeeCountTemp: '',
  businessAddress: '',
  businessType: '',
  lastInspectTime: '',

  longitude: '',
  latitude: '',
  annualRevenue: '',
  legalRepresentative: '',
  legalRepresentativePhone: '',
  mainBusiness: '',
  mainProducts: '',
  factoryNature: '',
  actualController: '',
  actualControllerPhone: '',
  factoryOwnerPhone: '',
  factoryOwner: '',
  holdingForm: '',
  buildingArea: '',
  industryCategory: '',
  credentialList: [],
  communityValue: '',
  safetyInsurance: '',
  ownershipType: '',
  landlord: '',
  landlordPhone: '',
  specialEquipmentType: '',
  directSupervisionDept: '',
  industryClassification: '',
  inspectInterval:'',
  landPlanning: ''
  // 设备与工艺、厂房性质等字段后续补充
})
// 新增lastInspectTime的输入校验方法
const validateDateTime = (value, callback) => {
  if (!value) return;
  // 先去除首尾空格，并将多个空格替换为一个空格
  value = value.trim().replace(/\s+/g, ' ');
  // 检查格式：YYYY-MM-DD HH:mm:ss
  const dateTimePattern = /^(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;
  const match = value.match(dateTimePattern);
  if (!match) {
    uni.showToast({
      title: '请输入正确的时间格式：yyyy-MM-dd HH:mm:ss',
      icon: 'none'
    });
    callback('');
    return;
  }
  let [_, year, month, day, hour, minute, second] = match;
  year = Number(year);
  month = Number(month);
  day = Number(day);
  hour = Number(hour);
  minute = Number(minute);
  second = Number(second);

  // 校验年月日
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
    callback('');
    return;
  }
  // 校验时分秒
  if (
    hour < 0 || hour > 23 ||
    minute < 0 || minute > 59 ||
    second < 0 || second > 59
  ) {
    uni.showToast({
      title: '请输入有效的时间（00-23:00-59:00-59）',
      icon: 'none'
    });
    callback('');
    return;
  }
  // 格式化
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  const formattedHour = hour.toString().padStart(2, '0');
  const formattedMinute = minute.toString().padStart(2, '0');
  const formattedSecond = second.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay} ${formattedHour}:${formattedMinute}:${formattedSecond}`);
}

const validateDate = (value, callback) => {
  if (!value) return;
  // 检查日期格式是否正确
  const datePattern = /^\d{4}-\d{1,2}-\d{1,2}$/;
  if (!datePattern.test(value)) {
    uni.showToast({
      title: '请输入正确的日期格式：yyyy-mm-dd',
      icon: 'none'
    });
    callback('')

    return;
  }

  // 分割日期
  const [year, month, day] = value.split('-').map(Number);

  // 验证日期是否有效
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {
    uni.showToast({
      title: '请输入有效的日期',
      icon: 'none'
    });
    callback('')
    return;
  }

  // 格式化日期，确保月份和日期是两位数
  const formattedMonth = month.toString().padStart(2, '0');
  const formattedDay = day.toString().padStart(2, '0');
  callback(`${year}-${formattedMonth}-${formattedDay}`)
}

const isadmin = ref(false)

const formRef = ref(null)
const showScalePicker = ref(false)
const showestablishDateicker = ref(false)
const showCertificateStartDatePicker = ref(false)
const showCertificateEndDatePicker = ref(false)
const showecommunityValuePicker = ref(false)
const showIndustryCategoryPicker = ref(false)
const showlastInspectTimePicker = ref(false)
const showTypePicker = ref(false)
const showBusinessTypePicker = ref(false)
const scaleyzList = [
  { label: '养殖规模户', value: '养殖规模户' },
  { label: '养殖专业户', value: '养殖专业户' },

]
const scalegmList = [
  { label: '规上', value: '规上' },
  { label: '小微', value: '小微' }
]
const isdisabled = ref(false)
const oldScrollTop = ref(0)
// scroll-view的滚动事件
function listScroll(e) {
  oldScrollTop.value = e.detail.scrollTop
}
// 滚动到页面顶部
function scrollToTop() {
  scrollTop.value = oldScrollTop.value
  // 当调用此方法时，会先保存当前的滚动位置，并在下一个Vue的DOM更新周期开始时滚动到页面顶部
  nextTick(() => {
    scrollTop.value = 0
  })
}

function handleTabClick(item) {
  tabActive.value = item.index

  // 滚动到顶部
  nextTick(() => {
    if (tabContentRef.value) {
      scrollToTop()
    }


  })
}
function onBusinessTypeConfirm(e) {
  form.businessType = e.value[0].label
  showBusinessTypePicker.value = false
}
function onTypeConfirm(e) {
  form.enterpriseNature = e.value[0].label
  showTypePicker.value = false
  formRef.value.clearValidate()

}
function uploadyyzzhandler() {
  uni.chooseImage({
    count: 1,
    extension: ['.jpg', '.png'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera', 'album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      const imageBase64 = await onChange(tempFilePaths[0])
      try {
        const response = await AJYService.uploadYYZZ({
          imageBase64
        })
        if (response.success) {
          const data = response.data;

          // 遍历data对象，将属性值不为null或""的属性赋值给form对应的属性
          Object.keys(data).forEach(key => {
            if (data[key] !== null&&key!='businessAddress'&& data[key] !== "" && typeof form[key] !== 'undefined') {
              console.log(key, data[key])
              form[key] = data[key]
            }
          })
          console.log(form, 'form')
        }
        else {
          uni.showToast({
            title: response.message,
            icon: 'none'
          })
        }
      } catch (e) {
        uni.showToast({
          title: 'ocr识别失败',
          icon: 'none'
        })
      }
      uni.hideLoading()
    }
  })

}
function onChange(newValue) {
  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    xhr.open('GET', newValue, true)
    xhr.responseType = 'blob'
    xhr.onload = function () {
      if (this.status === 200) {
        let fileReader = new FileReader()
        fileReader.onload = function (e) {
          const base64 = e.target.result
          resolve(base64)
        }
        fileReader.onerror = function (err) {
          console.log(err);
        }
        fileReader.readAsDataURL(this.response)
      }
    }
    xhr.onerror = function (e) {
      console.log(e);
    }
    xhr.send()
  })
}
// 添加数据保存相关的常量
const STORAGE_KEY = 'companyAdd_draft_data'
const STORAGE_TIMESTAMP_KEY = 'companyAdd_draft_timestamp'
const AUTO_SAVE_INTERVAL = 30000 // 30秒自动保存一次

// 自动保存定时器
let autoSaveTimer = null

// 草稿数据状态
const hasDraftData = ref(false)

// 启动自动保存
function startAutoSave() {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
  }
  autoSaveTimer = setInterval(() => {
    if (hasFormData()) {
      saveDraftData()
      console.log('自动保存完成')
    }
  }, AUTO_SAVE_INTERVAL)
}

// 停止自动保存
function stopAutoSave() {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
    autoSaveTimer = null
  }
}

// 检查是否有草稿数据
function checkDraftData() {
  try {
    const draftData = uni.getStorageSync(STORAGE_KEY)
    const draftTimestamp = uni.getStorageSync(STORAGE_TIMESTAMP_KEY)

    if (draftData && draftTimestamp) {
      // 检查草稿数据是否在24小时内
      const now = Date.now()
      const timeDiff = now - draftTimestamp
      const oneDay = 24 * 60 * 60 * 1000

      if (timeDiff < oneDay) {
        hasDraftData.value = true
        return { hasDraft: true, data: draftData }
      } else {
        // 超过24小时，清除过期数据
        clearDraftData()
        hasDraftData.value = false
        return { hasDraft: false, data: null }
      }
    }
    hasDraftData.value = false
    return { hasDraft: false, data: null }
  } catch (e) {
    console.error('检查草稿数据失败:', e)
    hasDraftData.value = false
    return { hasDraft: false, data: null }
  }
}

// 恢复最后的草稿数据
function restoreLastDraft() {
  try {
    const { hasDraft, data } = checkDraftData()
    if (hasDraft && data) {
      restoreDraftData(data)
      hasDraftData.value = false
    }
  } catch (e) {
    console.error('恢复最后草稿失败:', e)
  }
}

// 保存草稿数据
function saveDraftData() {
  try {
    const draftData = {
      ...form,
      hasSafetyInsurance: hasSafetyInsurance.value,
      communityLabel: communityLabel.value,
      industryCategoryLabel: industryCategoryLabel.value
    }
    uni.setStorageSync(STORAGE_KEY, draftData)
    uni.setStorageSync(STORAGE_TIMESTAMP_KEY, Date.now())
    console.log('草稿数据已保存')
  } catch (e) {
    console.error('保存草稿数据失败:', e)
  }
}

// 恢复草稿数据
function restoreDraftData(draftData) {
  try {
    // 恢复表单数据
    Object.keys(draftData).forEach(key => {
      if (key !== 'hasSafetyInsurance' && key !== 'communityLabel' && key !== 'industryCategoryLabel') {
        if (typeof form[key] !== 'undefined') {
          form[key] = draftData[key]
        }
      }
    })

    // 恢复其他状态
    if (draftData.hasSafetyInsurance) {
      hasSafetyInsurance.value = draftData.hasSafetyInsurance
    }
    if (draftData.communityLabel) {
      communityLabel.value = draftData.communityLabel
    }
    if (draftData.industryCategoryLabel) {
      industryCategoryLabel.value = draftData.industryCategoryLabel
    }

    console.log('草稿数据已恢复')

    // 显示恢复成功的详细信息
    const restoredFields = Object.keys(draftData).filter(key =>
      draftData[key] && draftData[key].toString().trim() !== '' &&
      key !== 'hasSafetyInsurance' && key !== 'communityLabel' && key !== 'industryCategoryLabel'
    )

    if (restoredFields.length > 0) {
      uni.showToast({
        title: `已恢复${restoredFields.length}个字段的数据`,
        icon: 'success',
        duration: 2000
      })
    }
  } catch (e) {
    console.error('恢复草稿数据失败:', e)
    uni.showToast({
      title: '数据恢复失败',
      icon: 'none'
    })
  }
}

// 清除草稿数据
function clearDraftData() {
  try {
    uni.removeStorageSync(STORAGE_KEY)
    uni.removeStorageSync(STORAGE_TIMESTAMP_KEY)
    hasDraftData.value = false
    console.log('草稿数据已清除')
  } catch (e) {
    console.error('清除草稿数据失败:', e)
  }
}

// 检查表单是否有数据
function hasFormData() {
  try {
    return Object.values(form).some(value => {
      if (Array.isArray(value)) {
        return value.length > 0 && value.some(item =>
          Object.values(item).some(v => v && v.toString().trim() !== '')
        )
      }
      return value && value.toString().trim() !== ''
    }) || hasSafetyInsurance.value !== 'NO' || communityLabel.value || industryCategoryLabel.value
  } catch (e) {
    console.error('检查表单数据失败:', e)
    return false
  }
}

// 页面退出前提示保存数据
function onPageExit() {
  try {
    if (hasFormData()) {
      uni.showModal({
        title: '提示',
        content: '检测到您有未完成的数据，是否保存以便下次使用？',
        confirmText: '保存',
        cancelText: '不保存',
        success: (res) => {
          if (res.confirm) {
            saveDraftData()
            uni.showToast({
              title: '数据已保存',
              icon: 'success'
            })
          } else {
            clearDraftData()
          }
          // 继续退出
          setTimeout(() => {
            uni.navigateBack({ delta: 1 })
          }, 100)
        }
      })
      return false // 阻止默认退出行为
    }
    return true
  } catch (e) {
    console.error('页面退出检查失败:', e)
    return true
  }
}

// 页面加载时检查草稿数据
function onPageLoad() {
  try {
    const { hasDraft, data } = checkDraftData()

    if (hasDraft) {
      // 延迟显示，确保页面完全加载
      setTimeout(() => {
        uni.showModal({
          title: '提示',
          content: '检测到您上次有未完成的数据，是否恢复使用？',
          confirmText: '恢复',
          cancelText: '不使用',
          showCancel: true,
          success: (res) => {
            if (res.confirm) {
              restoreDraftData(data)
              uni.showToast({
                title: '数据已恢复',
                icon: 'success'
              })
            } else {
              // 询问是否要清除草稿数据
              uni.showModal({
                title: '确认',
                content: '是否清除已保存的草稿数据？',
                confirmText: '清除',
                cancelText: '保留',
                success: (clearRes) => {
                  if (clearRes.confirm) {
                    clearDraftData()
                    uni.showToast({
                      title: '草稿数据已清除',
                      icon: 'success'
                    })
                  }
                }
              })
            }
          }
        })
      }, 500)
    }
  } catch (e) {
    console.error('页面加载检查失败:', e)
  }
}

// 添加页面返回拦截器
onMounted(() => {
  // 在uni-app中，我们可以通过重写页面的onBackPress方法来实现返回拦截
  // 由于这是Vue3 Composition API，我们需要在页面实例上添加这个方法
  nextTick(() => {
    try {
      // 获取当前页面实例
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        // 添加返回拦截方法
        if (currentPage) {
          currentPage.onBackPress = function () {
            try {
              if (onPageExit()) {
                return false // 允许返回
              }
              return true // 阻止返回
            } catch (e) {
              console.error('返回拦截处理失败:', e)
              return false // 出错时允许返回
            }
          }
          console.log('页面返回拦截器已设置')
        }
      }
    } catch (e) {
      console.error('设置页面返回拦截器失败:', e)
    }
  })
})

// 重写goback函数，添加退出前检查
function goback() {
  try {
    if (onPageExit()) {
      uni.navigateBack({ delta: 1 })
    }
  } catch (e) {
    console.error('页面返回处理失败:', e)
    // 出错时直接返回
    uni.navigateBack({ delta: 1 })
  }
}
const communities = ref([])
const industryCategories = ref([])
onLoad(() => {
  isadmin.value = userStore.userInfo.authorityList.find(item => item.roleId == 'town_admin')

  getDicts('communities').then((response) => {
    let list = response.data
    list.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    communities.value = list
  })
  getDicts('industryCategories').then((response) => {
    let list = response.data
    list.forEach((item) => {
      item.label = item.dictLabel
      item.value = item.dictValue
    })
    industryCategories.value = list
  })

  // 检查草稿数据
  onPageLoad()
})

// 页面显示时处理
onShow(() => {
  // 页面显示时启动自动保存
  startAutoSave()
})

// 页面隐藏时自动保存数据
onHide(() => {
  // 页面隐藏时停止自动保存并保存数据
  stopAutoSave()
  if (hasFormData()) {
    saveDraftData()
  }
})

// 页面卸载时处理
onUnload(() => {
  // 页面卸载时停止自动保存并保存数据
  stopAutoSave()
  if (hasFormData()) {
    saveDraftData()
  }
})

// 页面准备就绪时设置表单规则
onReady(() => {
  getUserLocation()
  formRef.value.setRules(rules)
})

const communityLabel = ref('')
const industryCategoryLabel = ref('')

function communityValueConfirmHandler(e) {
  communityLabel.value = e.value[0].label
  form.communityValue = e.value[0].value
  showecommunityValuePicker.value = false
}

function industryCategoryConfirmHandler(e) {
  industryCategoryLabel.value = e.value[0].label
  form.industryCategory = e.value[0].value
  showIndustryCategoryPicker.value = false
}

function submitForm() {
  formRef.value.validate().then(async (valid) => {
    if (isdisabled.value) return;
    isdisabled.value = true
    AdminService.addCompany(form).then(res => {
      isdisabled.value = false
      if (res.success) {
        // 提交成功后清除草稿数据
        clearDraftData()
        uni.showToast({ title: isadmin.value ? '提交成功！' : '提交成功，待审核！', icon: 'none' })
        goback()
      } else {
        uni.showToast({ title: res.message, icon: 'none' })
      }
    }).catch(e => {
      isdisabled.value = false
      uni.showToast({
        title: '提交失败！',
        icon: 'none',
      })
    })

  })
}



function lastInspectTimeConfirm(e) {
  form.lastInspectTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showlastInspectTimePicker.value = false
}

function onScaleConfirm(e) {
  form.enterpriseScale = e.value[0].label
  showScalePicker.value = false
}

function onestablishDateConfirm(e) {
  form.establishDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showestablishDateicker.value = false
}

function getUserLocation() {
  uni.getLocation({
    type: "gcj02",
    isHighAccuracy: true,
    success(res) {
      console.log(res, '0----------000')
      // 暂时
      form.longitude = res.longitude; //118.787575;
      form.latitude = res.latitude; //32.05024;
      getAddress(res.longitude,res.latitude)
    },
    fail(err) {
      console.log(err, 'dd-----------d')
    }
  })
}
function openMap() {
  try {
   
    uni.getSetting({
      success(res) {
        console.log(res)
        if (res.authSetting['scope.userLocation']) {
          console.log('获取位置已授权')
          getLocation()
        } else {
          uni.showModal({
            title: '请求授权当前位置',
            content: "需要获取您的地理位置，请确认授权！",
            confirmColor: "#01BD5D",
            success(res1) {

              if (res1.confirm) {
                uni.authorize({
                  scope: 'scope.userLocation',
                  success(res) {
                    uni.showToast({
                      title: "授权成功",
                      icon: "success",
                      duration: 3000
                    });
                    getLocation()
                  },
                  fail() {
                    uni.showToast({
                      title: "授权失败",
                      icon: "none",
                      duration: 3000
                    });
                  }
                })
              }
            }
          })
        }
      },
      fail() {
        console.log("获取授权信息授权失败")
      }
    })
  } catch (err) {
    getLocation()
  }
}


function getLocation() {
  uni.chooseLocation({
    latitude: form.latitude || 36.538773, /* 纬度 */
    longitude: form.longitude || 120.367355, /* 经度 */
    success(t) {
      console.log(t)
     // form.businessAddress = t.address + t.name
      form.latitude = t.latitude
      form.longitude = t.longitude
      getAddress(t.longitude,t.latitude)


    }
  });
}
// 添加新的证件
function addCertificate() {
  form.credentialList.push({
    credentialName: '',
    startDate: '',
    endDate: '',
    credentialImage: ''
  })
}

// 删除证件
function removeCertificate(index) {
  form.credentialList.splice(index, 1)
}

// 上传证件图片
function uploadCertificateImage(certificateIndex) {
  uni.chooseImage({
    count: 9 - form.credentialList[certificateIndex].credentialImage ? form.credentialList[certificateIndex].credentialImage.split(',').length : 0,
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera', 'album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = form.credentialList[certificateIndex].credentialImage ? form.credentialList[certificateIndex].credentialImage.split(',') : []
      form.credentialList[certificateIndex].credentialImage = [...arr, ...list].join(',')
      console.log(form.credentialList[certificateIndex].credentialImage, '0000000000000000000000000000')
      uni.hideLoading()
    }
  })
}
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url

          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) { }
    }
    resolve(filelist)
  })
}
// 删除证件图片
function deleteCertificateImage(certificateIndex, imageIndex) {
  let arr = form.credentialList[certificateIndex].credentialImage.split(',')
  arr.splice(imageIndex, 1).join(',')
  form.credentialList[certificateIndex].credentialImage = arr.length ? arr.join(',') : ''
}


// 证件开始日期选择确认
function onCertificateStartDateConfirm(e) {
  form.credentialList[currentCertificateIndex.value].startDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showCertificateStartDatePicker.value = false
}

// 证件截止日期选择确认
function onCertificateEndDateConfirm(e) {
  form.credentialList[currentCertificateIndex.value].endDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
  showCertificateEndDatePicker.value = false
}


// 添加ref
const currentCertificateIndex = ref(null)
const industryCategoryValidate = (rule, value, callback) => {
  if (!form.enterpriseNature) {
    callback()
    return;
  }
  if (form.enterpriseNature == '个体工商户') {
    if (form.industryCategory.trim() == '') {
      callback('请选择风险关注点')
      return;
    }
    callback()
  }
  else {
    if (form.industryCategory.trim() == '') {
      callback('请选择涉及重点行业')
      return;
    }
    callback()
  }
}
const factoryNatureValidate = (rule, value, callback) => {
  console.log(form.enterpriseNature, form.factoryNature)
  if (form.enterpriseNature === '工贸企业') {
    if (form.factoryNature.trim() == '') {
      callback('请输入厂房性质')
      return;
    }
  }
  callback()
}
// 添加验证规则
const certificatesValidate = (rule, value, callback) => {
  if (!value || value.length === 0) {
    callback()
    return
  }

  for (let i = 0; i < value.length; i++) {
    if (!value[i].credentialName || value[i].credentialName.trim() === '') {
      callback(new Error(`第${i + 1}个证件的名称不能为空`))
      return
    }
    if (!value[i].startDate || value[i].startDate.trim() === '') {
      callback(new Error(`第${i + 1}个证件的开始日期不能为空`))
      return
    }
    if (!value[i].endDate || value[i].endDate.trim() === '') {
      callback(new Error(`第${i + 1}个证件的截止日期不能为空`))
      return
    }
    if (value[i].endDate < value[i].startDate) {
      callback(new Error(`第${i + 1}个证件的截止日期不能小于开始日期`))
      return
    }
  }
  callback()
}

const employeeCountTempValidate = (rule, value, callback) => {
  if (form.enterpriseNature === '养殖企业') {
    if (form.employeeCountTemp.trim() == '') {
      callback('请输入临时用工人数')
      return;
    }
  }
  callback()
}
const enterpriseScaleValidate = (rule, value, callback) => {
  if (form.enterpriseNature === '养殖企业' || form.enterpriseNature === '工贸企业') {
    if (form.enterpriseScale.trim() == '') {
      callback('请输入企业规模')
      return;
    }
  }
  callback()
}
const inspectIntervalValidate = (rule, value, callback) => { 
  console.log(form.inspectInterval,isadmin.value)
  if(!isadmin.value){
    callback()
    return
  }
  else{
    if (form.inspectInterval.trim() == '') {
      callback('请输入巡检周期')
      return;
    }
    callback()

  }
}
const rules = ref({
  enterpriseName: [{ required: true, message: '企业名称不能为空', trigger: ['blur', 'change'] }],
  creditCode: [{ required: true, message: '信用代码不能为空', trigger: ['blur', 'change'] }],
  enterpriseNature: [{ required: true, message: '生产经营单位类型不能为空', trigger: ['blur', 'change'] }],
  industryCategory: [{ validator: industryCategoryValidate, trigger: ['blur', 'change'] }],
  factoryNature: [{ validator: factoryNatureValidate, trigger: ['blur', 'change'] }],
  ownershipType: [{ required: true, message: '租赁/自有不能为空', trigger: ['blur', 'change'] }],
  businessType: [{ required: true, message: '经营类型不能为空', trigger: ['blur', 'change'] }],
  annualRevenue: [{ required: true, message: '年营业额不能为空', trigger: ['blur', 'change'] }],
  enterpriseScale: [{ validator: enterpriseScaleValidate, trigger: ['blur', 'change'] }],
  businessAddress:[{ required: true, message: '经营地址不能为空', trigger: ['blur', 'change'] }],
  establishDate: [{ required: true, message: '成立日期不能为空', trigger: ['blur', 'change'] }],
  lastInspectTime: [{ required: true, message: '最新巡检时间不能为空', trigger: ['blur', 'change'] }],
  mainBusiness: [{ required: true, message: '主营业务不能为空', trigger: ['blur', 'change'] }],
  employeeCount: [{type:'number',  required: true, message: '从业人员不能为空', trigger: ['blur', 'change'] }],
  inspectInterval:[{type:'number',validator:inspectIntervalValidate, trigger: ['blur', 'change']}],
  employeeCountTemp: [{type:'number', validator: employeeCountTempValidate, trigger: ['blur', 'change'] }],
  legalRepresentative: [{ required: true, message: '法定代表人不能为空', trigger: ['blur', 'change'] }],
  legalRepresentativePhone: [{
    type: 'number',
    required: true,
    message: '手机号码不能为空',
    trigger: ['blur', 'change'],
  },
  {
    validator: (rule, value, callback) => {
      return uni.$u.test.mobile(value);
    },
    message: '手机号码格式不正确',
    // 触发器可以同时用blur和change
    trigger: ['change', 'blur'],
  }],
  actualController: [{ required: true, message: '常用联系人不能为空', trigger: ['blur', 'change'] }],
  actualControllerPhone: [{
    type: 'number',
    required: true,
    message: '手机号码不能为空',
    trigger: ['blur', 'change'],
  },
  {
    validator: (rule, value, callback) => {
      return uni.$u.test.mobile(value);
    },
    message: '手机号码格式不正确',
    // 触发器可以同时用blur和change
    trigger: ['change', 'blur'],
  }],
  communityValue: [{ required: true, message: '所属社区不能为空', trigger: ['blur', 'change'] }],
  safetyInsurance: [{ required: true, message: '安责险名称不能为空', trigger: ['blur', 'change'] }],
  credentialList: [{ validator: certificatesValidate, trigger: ['blur', 'change'] }]
})
function getAddress(longs,lat){
uni.request({
			method: 'GET',
			url: 'https://restapi.amap.com/v3/geocode/regeo?parameters',
			data: {
				key: '2273413e2dc369ea8ad9b72b58ffdfcc',
				location: `${longs},${lat}`,
				output: 'JSON'
			},
			success: async (res) => {
				console.log('用户所在的地理位置信息',res);
				//用户所在的地理位置信息
        form.businessAddress = res.data.regeocode.formatted_address
			},
			fail: r => {
				console.log(r);
			}
		});
  }
</script>

<style lang="scss" scoped>
.company-add {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 1135rpx;
}

.container {
  box-sizing: border-box;

  .draft-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f0f0f0 0%, #fafafa 100%);
    border: 1px solid #0CBE88;
    border-radius: 12rpx;
    padding: 20rpx 30rpx;
    margin: 20rpx 40rpx;
    box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.1);

    text {
      color: #0CBE88;
      font-size: 28rpx;
      margin-left: 10rpx;
    }

    .draft-actions {
      display: flex;
      gap: 20rpx;

      .restore-link,
      .clear-link {
        color: #0CBE88;
        font-size: 26rpx;
        text-decoration: underline;
        cursor: pointer;

      }

      .clear-link {
        color: #f44336;

      }
    }
  }

  .tab-content {
    background: linear-gradient(180deg, #FFFFFF 0%, #F5F6F6 100%);
    border-radius: 50rpx 50rpx 0 0;
    padding: 20rpx 40rpx 40rpx 40rpx;
    margin-bottom: 120rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    height: calc(100vh - 224rpx);
    box-sizing: border-box;
    overflow: scroll;

    ::v-deep .u-form-item__body__left__content__required {
      position: inherit;
      left: auto;
    }
  }

  ::v-deep .u-tabs {
    padding: 0 25rpx;

    .u-tabs__wrapper__nav__line {
      height: 8rpx !important;
      background: linear-gradient(117deg, #0CBE88 0%, #3ADB97 100%) !important;
      border-radius: 5rpx !important;
    }
  }

  ::v-deep .u-radio {
    margin-right: 20rpx;
  }
}

.imgcell {
  padding: 20rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .imgitem {
    width: 233rpx;
    height: 179rpx;
    border: 0.5px solid #e2e2e2;
    margin-right: 40rpx;
    margin-bottom: 20rpx;
    position: relative;

    image {
      margin: 7rpx;
      width: 219rpx;
      height: 164rpx;
    }

    .closebtn {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: -16rpx;
      right: -16rpx;
    }
  }
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin: 30rpx 0 20rpx 0;
}

.risk-section {
  margin-bottom: 36rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 18rpx 18rpx 0 18rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.risk-section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #0CBE88;
  margin-bottom: 18rpx;
  letter-spacing: 2rpx;
}

::v-deep .u-form-item__body__right__content__slot {
  padding: 0 10rpx;

  .certificates-list {
    margin: 20rpx 0;
    width: 100%;

    .certificate-item {
      position: relative;
      margin-bottom: 30rpx;
      padding: 20rpx;
      background: #f8f8f8;
      border-radius: 12rpx;
      border: solid 1rpx #e5e5e5;

      .trash {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        color: #ff4d4f;
      }

    }
  }

  ::v-deep .certificate-images {
    margin-top: 20rpx;
  }

}

.address-row {
  display: flex;
  width: 100%;
  align-items: center;

  .u-icon {
    margin-left: 10rpx;
  }
}

.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    width: 30%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    background: #3cc16c;
    color: #fff;
  }

  .bg-gray {
    background: #aaa;
  }

  .bg-blue {
    background: #0CBE88;
  }
}

.button-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
  gap: 20rpx;
}

.yyzz {
  margin: 10rpx 0;
  width: 100%;
  border-radius: 16rpx;
  height: 300rpx;
}

.collapse-title {
  position: relative;
  font-size: 34rpx;
  font-weight: 700;
  color: #0CBE88;
  letter-spacing: 1rpx;
  padding: 20rpx;
  text-align: left;
  margin: 20rpx 0;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6rpx;
    height: 36rpx;
    background: #0CBE88;
    border-radius: 3rpx;
  }

  &::after {
    content: '';
    position: absolute;
    left: 16rpx;
    right: 0;
    bottom: 0;
    height: 1px;
    background: linear-gradient(90deg, #0CBE88 0%, transparent 100%);
  }
}

</style>
