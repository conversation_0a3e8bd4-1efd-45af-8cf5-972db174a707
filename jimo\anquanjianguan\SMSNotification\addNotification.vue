<template>
  <view class="add-notification" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}">
    <u-navbar title="新增" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>
    <view class="container">
      <u--form ref="formRef" :model="form" labelPosition="top" :label-width="120" :labelStyle="{fontWeight: 'bold', paddingBottom: '10rpx'}" :rules="rules">
        <u-form-item label="短信标题" prop="title" required>
          <u--input v-model="form.title" placeholder="请输入短信标题"  maxlength="20" showWordLimit />
        </u-form-item>
        <u-form-item label="短信内容" prop="content" required>
          <u-textarea v-model="form.content" placeholder="请输入短信内容"  maxlength="500" showWordLimit />
        </u-form-item>
        <u-form-item label="发送方式" prop="sendType" required @click.stop="showTypePicker = true">
          <u-input v-model="form.sendType" placeholder="请选择发送方式"  readonly >
            <template #suffix>
                <u-icon name="arrow-down" class="iconclass"></u-icon>

            </template>
          </u-input>
        </u-form-item>
        <u-form-item label="发送时间" v-if="form.sendType==='定时发送'" prop="sendTime" required @click.stop="showTimePicker = true">
          <u-input v-model="form.sendTime" placeholder="请选择发送时间"  readonly >
          <template #suffix>
                <u-icon name="arrow-down" class="iconclass"></u-icon>

            </template>
          </u-input>
        </u-form-item>
        <u-form-item label="接收企业" prop="company" required @click.stop="gotoCompanyList">
          <u-input v-model="form.company" placeholder="请选择接收企业"  readonly >
          <template #suffix>
                <u-icon name="arrow-down" class="iconclass"></u-icon>

            </template>
          </u-input>
        </u-form-item>
      </u--form>
      <view class="bottom u-border-top">
        <view class="button-box">
          <view class="btn" :class="{ 'bg-gray': loading }" @click="submitForm">提交</view>
        </view>
      </view>
      <u-picker closeOnClickOverlay @close="showTypePicker = false" :show="showTypePicker" :columns="[typeList]" @confirm="onTypeConfirm" @cancel="showTypePicker = false" keyName="label"/>
      <u-datetime-picker closeOnClickOverlay @close="showTimePicker = false" :show="showTimePicker" @confirm="onTimeConfirm" mode="datetime" @cancel="showTimePicker = false"  />

      </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {  LIVE_INVENTORY_BG } from '@/common/net/staticUrl.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const validateCompany = (rule, value, callback) => {
		if(!value.length){
			callback(new Error('请选择接受企业'))
		}else{
			callback()
		}
	}
const form = reactive({
  title: '',
  content: '',
  sendType: '',
  sendTime: '',
  company: ''
})
const rules = ref({
  title: [{ required: true, message: '短信标题不能为空', trigger: ['blur', 'change'] }],
  content: [{ required: true, message: '短信内容不能为空', trigger: ['blur', 'change'] }],
  sendType: [{ required: true, message: '发送方式不能为空', trigger: ['blur', 'change'] }],
  sendTime: [{ required: true, message: '发送时间不能为空', trigger: ['blur', 'change'] }],
  company:{
			validator:validateCompany
		}
})
const formRef = ref(null)
const showTypePicker = ref(false)
const showTimePicker = ref(false)

const loading = ref(false)
const typeList = [
  { label: '定时发送', value: '定时发送' },
  { label: '立即发送', value: '立即发送' }
]

function onTypeConfirm(e) {
  form.sendType = e.value[0].label
  showTypePicker.value = false
}
function onTimeConfirm(e) {
  form.sendTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM')
  showTimePicker.value = false
}
function onCompanyConfirm(e) {
  form.company = e.value[0].label
}
function gotoCompanyList(){
  uni.navigateTo({
    url: './columncontent'
  })
}
function submitForm() {
  if (loading.value) return
  formRef.value.validate().then(() => {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      uni.showToast({ title: '提交成功', icon: 'success' })
      uni.navigateBack()
    }, 1000)
  })
}
onLoad(()=>{
  uni.$on('selectcompanys', (res) => {
    console.log(res)
		form.company = res.map(item=>item.enterpriseName)
	})
})
</script>

<style lang="scss" scoped>
.iconclass{
    margin-right: 10rpx;
}
.add-notification {
  width: 100%;
  height: 100vh;
}
.logout {
  position: fixed; /* 使用固定定位 */
  top: 70rpx;    /* 距离底部的距离 */
  left: 0;    /* 距离右边的距离 */
  padding: 10rpx 20rpx; /* 内边距 */
  background-color: #f44336; /* 背景颜色 */
  color: white; /* 文字颜色 */
  z-index: 1000; /* 确保按钮位于其他元素之上 */
}
 
.logout:hover {
  background-color: #d32f2f; /* 鼠标悬停时的背景颜色 */
}
.container {
    border-radius: 50rpx 50rpx 0 0;
    margin:  40rpx 20rpx 40rpx 20rpx;
    padding: 20rpx 40rpx;
    margin-bottom: 120rpx;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
    height: calc(100% - 220rpx);
    overflow: scroll;
    background-color: rgba(255,255,255,0.95);
  .bottom {
    position: fixed;
    width: 100%;
    background: #fff;
    bottom: 0;
    left: 0;
    align-items: center;
    padding: 20rpx 38rpx;
    z-index: 100;
    box-sizing: border-box;
    .btn {
      line-height: 80rpx;
      border-radius: 40rpx;
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 36rpx;
      background: #0CBE88;
      color: #fff;
    }
    .bg-gray {
      background: #aaa;
      color: #fff;
    }
  }
  .button-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
::v-deep .u-form-item__body__right__content__slot {
  padding: 0 10rpx;
  .form-label {
    min-width: 200rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 26rpx;
    color: rgba(51, 51, 51, 1);
    margin-bottom: 10rpx;
    padding: 0 5rpx;
    text {
      font-weight: 400;
      font-size: 24rpx;
      color: rgba(153, 153, 153, 1);
    }
  }
}
</style> 