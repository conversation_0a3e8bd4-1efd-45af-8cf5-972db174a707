<template>
	<!-- 应用列表组件 -->
	<view class="app-container">
		<!-- 模块标题，服务页展示标题，首页和家园页不展示 -->
		<view v-if="!!moduleTitle" class="section-title">{{ moduleTitle }}</view>
		<!-- 应用列表 -->
		<template v-if="isSwiper">
			<swiper class="swiper" :style="{height: swiperHeight}" circular :indicator-dots="appListSplit.length > 1"
				indicator-color="#D8D8D8" indicator-active-color="#0CBE88" :autoplay="false" :duration="500">
				<swiper-item v-for="(appList,index) in appListSplit" :key="index">
					<view class="application-list" :class="{acfs : appList.length <= 4}">
						<view class="application-item" v-for="(item, index) in appList" :key="item.id"
							:class="{mb40 : appListSplit.length > 1 && index > 3 }" @click="gotolink(item)">
							<view>
								<u-image :src="item.appIcon" radius="30rpx" mode="aspectFit" width="82rpx"
									height="82rpx"></u-image>
							</view>
							<view class="title">{{ item.appName || item.name }}</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</template>
		<template v-else>
			<view class="application-list app-block" :class="{acfs : appList.length <= 4}">
				<view class="application-item" v-for="(item, index) in appList" :key="item.id"
					:class="{mb40 : appListSplit.length > 1 && index > 3 }" @click="gotolink(item)">
					<view>
						<u-image :src="item.appIcon" radius="30rpx" mode="aspectFit" width="82rpx"
							height="82rpx"></u-image>
					</view>
					<view class="title">{{ item.appName || item.name }}</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import kvStore from '@/common/store/uniKVStore.js'
	import { useUserStore } from '@/store/user.js'
	import { useTokenStore } from '@/store/token.js'
	import { getMmystr,addUseRecord } from '@/common/api.js'
	const userStore = useUserStore()
	const tokenStore = useTokenStore()
	const userType = ref(userStore.userInfo.customParam.userType)
	// 判断用户是否已登录
	let hasLogin = kvStore.get('hasLogin', true) //hasLogin是登录成功后在本地存储的登录标识
	hasLogin = Boolean(Number(hasLogin)) //返回布尔值
	// 接收父组件传入的参数
	const props = defineProps({
		// 模块标题，传入值才展示，没有值时不展示标题
		moduleTitle: {
			type: String,
			default: ''
		},
		// 模块内的应用列表
		appList: {
			type: Array,
			default () { return [] }
		},
		// 是否以swiper形式展示
		isSwiper: {
			type: Boolean,
			default: true
		}
	})

	// 分割数组，将应用列表数组按照每8个一组分割成二维数组
	function subGroup(arr, len) {
		var newArr = [];
		for (var i = 0; i < arr.length; i += len) {
			newArr.push(arr.slice(i, i + len));
		}
		return newArr;
	}
	// 分割后的数组，是一个二维数组
	const appListSplit = computed(() => {
		return subGroup(props.appList, 8)
	})
	const swiperHeight = computed(() => {
		if (appListSplit.value.length > 1) {
			return '386rpx';
		} else {
			return '330rpx';
		}
	})
	// 点击应用跳转
	function gotolink(item) {
		console.log("点击应用了----", item);
		// 如果是未登录则提示登录
		if (!hasLogin) {
			  // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
			return false;
		} else if (userType.value == 'Visitor' && !tokenStore.tenantId) {
			uni.showToast({
				title: '请先选择村再继续浏览',
				icon: 'none',
				duration: 2000
			})
			return false;
		} else if (userType.value == 'Visitor' && tokenStore.tenantId && item.appVisitorAccess == 'forbid') {
			uni.showModal({
				title: '提示',
				content: '加入该村庄后可继续浏览',
				confirmText: '加入',
				success: function(res) {
					if (res.confirm) {
						const customParam = userStore.userInfo.customParam;
						uni.navigateTo({
							url: "/pages/generalPage/join/join?tenantId=" + customParam.tenantId +
								'&optionType=add&tenantName=' + customParam.tenantName + '&fullName=' +
								customParam.tenantName
						})
					}
				}
			});
			return false;
		}
		// 如果是小程序链接则跳转小程序
		if (item.type == 'applet') {
			// 如果是跳转齐鲁数村小程序，则需要传参
			if(item.AppID == 'wx1754427c42d2837e') {
				let param = {
					mmy: ''
				}
				getMmystr().then(res => {
					if (res.success) {
						param.mmy = res.data;
						uni.navigateToMiniProgram({
							appId: item.AppID,
							envVersion: "release",
							path: `/pages/login/loginTransfer?mmy=${param.mmy}`,
							extraData: param,
							success(res) {
								console.log(res, "跳转成功");
								// 打开成功
							}
						});
					}
				})
				return;
			}
			uni.navigateToMiniProgram({
				appId: item.AppID,
				envVersion: "release",
				success(res) {
					console.log(res, "跳转成功");
					// 打开成功
				}
			});
			return;
		} else if (item.type == 'platform') {
			const platform = uni.getSystemInfoSync().platform
			let param = {
				appId: item.id,
				source: platform
			}
			addUseRecord(param).then(res => {
				console.log('访问记录增加成功');
			})
			if (item.appType == 'general') {
				// 普通应用直接跳转
				uni.navigateTo({
					url: item.appAddrress,
					fail() {
						uni.showToast({
							title: "打开失败",
							icon: "none",
							duration: 2000
						})
					}
				})
			} else if (item.appType == 'column') {
				// 列表应用，跳转列表页，传递columnId
				uni.navigateTo({
					url: '/pages/listPage/appList?columnId=' + item.appColumnId,
					fail() {
						uni.showToast({
							title: "打开失败",
							icon: "none",
							duration: 2000
						})
					}
				})
			} else if (item.appType == 'flow') {
				// 流程应用，跳转流程列表页，传递flowId
				uni.navigateTo({
					url: '/pages/generalPage/flowPage/flowList?flowId=' + item.flowId,
					fail() {
						uni.showToast({
							title: "打开失败",
							icon: "none",
							duration: 2000
						})
					}
				})
			} else if (item.appType == 'thirdParty') {
				const platform = uni.getSystemInfoSync().platform
				let param = {
					appId: item.id,
					source: platform
				}
				addUseRecord(param).then(res => {
					console.log('访问记录增加成功');
				})
				// 第三方应用
				let mmy = ""
				getMmystr().then(res => {
					if (res.success) {
						mmy = res.data;
						let tmpUrl = ''
						if (item.appAddrress.includes("?")) {
							tmpUrl = item.appAddrress + '&code=' + mmy
						} else {
							tmpUrl = item.appAddrress + '?code=' + mmy
						}
						let url = encodeURIComponent(tmpUrl);
						uni.navigateTo({
							url: '/pages/UNI/webview/webview?url=' + url,
							fail() {
								uni.showToast({
									title: "打开失败",
									icon: "none",
									duration: 2000
								})
							}
						})
					}
				})
			}

		} else if (item.type == 'external') {
			// 外部链接通过webview跳转
			let url = encodeURIComponent(item.linkAddress)
			uni.navigateTo({
				url: '/pages/UNI/webview/webview?url=' + url,
				fail() {
					uni.showToast({
						title: "打开失败",
						icon: "none",
						duration: 2000
					})
				}
			})
		}
	}
</script>

<style lang="scss" scoped>
	.app-container {
		padding-bottom: 40rpx;
		margin-top: 20rpx;
		width: 710rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		padding-top: 20rpx;
		.section-title {
			position: relative;
			padding: 20rpx;
			margin-bottom: 10rpx;
			width: fit-content;
			// height: 45rpx;
			// line-height: 45rpx;
			font-size: 32rpx;
			color: #000000;
			font-weight: 500;
			font-family: PingFangSC-Medium, PingFang SC;

			&::after {
				content: "";
				position: absolute;
				width: 40rpx;
				height: 6rpx;
				left: 50%;

				bottom: 0rpx;
				transform: translate(-50%, 0);
				border-radius: 4rpx;
				background-image: linear-gradient(180deg,
						#49D895 100%,
						#0CBE88 99%);
			}
		}

		.swiper {
			// min-height: 330rpx;
		}

		.application-list {
			display: flex;
			align-items: center;
			justify-content: space-around;
			flex-wrap: wrap;
			width: 100%;
			height: 100%;
			// min-height: 156rpx;
			// padding-top: 20rpx;

			.application-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				// margin-bottom: 20rpx;
				width: 25%;

				.title {
					margin-top: 14rpx;
					font-size: 24rpx;
					font-weight: 400;
					color: #3F3F3F;
				}

				.image {
					width: 82rpx;
					height: 82rpx;
					border-radius: 30rpx;
				}
			}

			&::after {
				content: "";
				flex: 1;
			}
		}
	}

	.mb40 {
		margin-bottom: 40rpx;
	}

	.acfs {
		align-content: flex-start;
		padding-top: 20rpx;
	}

	.app-block {
		padding-top: 20rpx;

		.application-item {
			margin-bottom: 20rpx;
		}
	}
</style>