<template>
    <view class="container" :style="containerStyle">
        <u-navbar :bgColor="isshow?'#fff':'transparent'" placeholder title="乡村治理" :fixed="true" autoBack
            leftIconSize="24" :titleStyle="titleStyle">
        </u-navbar>

        <view class="inner-container">
            <scroll-view class="scroll-style" scroll-y="true" @scroll="onScroll">
                <view class="overview">
                    <view class="overview-box" v-for="item in headerList" @click="handleClick(item)">
                        <safe-image :src="item.image" mode="aspectFit" width="74rpx" height="74rpx"></safe-image>
                        <view class="title">
                            {{item.title}}
                        </view>
                    </view>
                </view>

                <!-- 四治 -->
                <view class="custom sizhi">
                    <view class="custom-title">
                        <view class="icon"></view>
                        <view class="title">四治</view>
                    </view>
                    <view class="subtitle">形成新环境、实现新发展、过上新生活</view>
                    <view class="sizhi-boxes">
                        <view class="sizhi-box" v-for="item in sizhiList" @click="handleClick(item)" :style="{ backgroundImage: `
                                url(${item.bgImg})`,backgroundSize:'326rpx 144rpx' }">
                            <safe-image :src="item.image" mode="aspectFit" width="70rpx" height="70rpx"></safe-image>
                            <view class="box-part">
                                <view class="title">{{item.title}}</view>
                                <view class="subtitle">{{item.subtitle}}</view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 三清 -->
                <view class="custom sanqing">
                    <view class="custom-title">
                        <view class="icon"></view>
                        <view class="title">三清</view>
                    </view>
                    <view class="subtitle">优美环境、生活舒适</view>
                    <view class="sanqing-boxes">
                        <view class="sanqing-box" v-for="item in sanqingList" @click="handleClick(item)">
                            <safe-image :src="item.image" mode="aspectFit" width="92rpx" height="92rpx"></safe-image>
                            <view class="title1">{{item.title}}</view>
                        </view>
                    </view>
                </view>

                <!-- 一改 -->
                <view class="custom yigai">
                    <view class="custom-title">
                        <view class="icon"></view>
                        <view class="title">一改</view>
                    </view>
                    <view class="subtitle">形成新环境</view>
                    <view class="yigai-content" v-for="item in yigaiList" @click="handleClick(item)"
                        :style="{ backgroundImage: 'url('+item.image+')',backgroundSize:'684rpx 144rpx' }">
                        <view class="title1">{{item.title}}</view>
                        <view class="desc">{{item.subtitle}}</view>
                    </view>
                </view>
                <!-- 五防两保 -->
                <view class="custom fangbao">
                    <view class="custom-title">
                        <view class="icon"></view>
                        <view class="title">五防两保</view>
                    </view>
                    <view class="subtitle">保障全乡经济、社会协调发展</view>
                    <view class="fangbao-boxes">
                        <view class="fangbao-box" v-for="item in fangbaoList" @click="handleClick(item)">
                            <safe-image :src="item.image" mode="aspectFit" width="48rpx" height="48rpx"></safe-image>
                            <view class="title1">{{item.title}}</view>
                        </view>
                    </view>

                    <view class="fangbao-bottom">
                        <view class="fangbao-content" v-for="item in fangContentList" @click="handleClick(item)"
                            :style="{ backgroundImage: 'url('+item.image+')', backgroundSize:'327rpx 144rpx' }">
                            <view class="title1">{{item.title}}</view>
                            <view class="desc">{{item.subtitle}}</view>
                        </view>

                    </view>
                </view>

            </scroll-view>
        </view>
    </view>
</template>

<script setup>
    import {
        onMounted,
        reactive,
        ref
    } from 'vue'
    import {
        onShow
    } from '@dcloudio/uni-app'
    import {
        getColumnByKey,
        getFlowIdByKey
    } from '@/common/net/generalPage/common.js'
    import {
        RURAL_GOVERNANCE_BG_IMG,
        BACK_IMG,
        RURAL_GOVERNANCE_HEADER_IMG01,
        RURAL_GOVERNANCE_HEADER_IMG02,
        RURAL_GOVERNANCE_HEADER_IMG03,
        RURAL_GOVERNANCE_SIZHI_BG01,
        RURAL_GOVERNANCE_SIZHI_BG02,
        RURAL_GOVERNANCE_SIZHI_BG03,
        RURAL_GOVERNANCE_SIZHI_BG04,
        RURAL_GOVERNANCE_SIZHI_ZHILAJI,
        RURAL_GOVERNANCE_SIZHI_ZHIWUSHUI,
        RURAL_GOVERNANCE_SIZHI_ZHICESUO,
        RURAL_GOVERNANCE_SIZHI_ZHICUNMAO,
        RURAL_GOVERNANCE_SANQING_BG01,
        RURAL_GOVERNANCE_SANQING_BG02,
        RURAL_GOVERNANCE_SANQING_BG03,
        RURAL_GOVERNANCE_YIGAI_BG,
        RURAL_GOVERNANCE_FANGBAO_FANGYI,
        RURAL_GOVERNANCE_FANGBAO_FANGXUN,
        RURAL_GOVERNANCE_FANGBAO_FIRE,
        RURAL_GOVERNANCE_FANGBAO_ZAIHAI,
        RURAL_GOVERNANCE_FANGBAO_DIZHEN,
        RURAL_GOVERNANCE_FANGBAO_ANQUAN,
        RURAL_GOVERNANCE_FANGBAO_WENDING,
    } from '@/common/net/staticUrl.js'
    let flowId = ref('')
    const titleStyle = {
        height: '50rpx',
        fontSize: '36rpx',
        fontFamily: 'Source Han Sans CN, Source Han Sans CN',
        color: '#000',
        fontWeight: 600,
        lineHeight: '50rpx',
    }
    let isshow = ref(false)
    let headerList = reactive([{
            image: RURAL_GOVERNANCE_HEADER_IMG01,
            columnKey: 'menqiansanbao',
            columnId: '',
            title: '门前三包'
        },
        // {
        //     image: RURAL_GOVERNANCE_HEADER_IMG02,
        //     columnKey: 'baoguangtai',
        //     columnId: '',
        //     title: '曝光台'
        // },
        {
            image: RURAL_GOVERNANCE_HEADER_IMG03,
            columnKey: 'suishoupai',
            columnId: '',
            title: '随手拍'
        }
    ])
    let sizhiList = reactive([{
            bgImg: RURAL_GOVERNANCE_SIZHI_BG01,
            image: RURAL_GOVERNANCE_SIZHI_ZHILAJI,
            columnKey: 'zhilaji',
            columnId: '',
            title: '治垃圾',
            subtitle: '垃圾无害化',
        },
        {
            bgImg: RURAL_GOVERNANCE_SIZHI_BG02,
            image: RURAL_GOVERNANCE_SIZHI_ZHIWUSHUI,
            columnKey: 'zhiwushui',
            columnId: '',
            title: '治污水',
            subtitle: '污水不乱流',
        },
        {
            bgImg: RURAL_GOVERNANCE_SIZHI_BG03,
            image: RURAL_GOVERNANCE_SIZHI_ZHICESUO,
            columnKey: 'zhicesuo',
            columnId: '',
            title: '治厕所',
            subtitle: '标准厕所化',
        },
        {
            bgImg: RURAL_GOVERNANCE_SIZHI_BG04,
            image: RURAL_GOVERNANCE_SIZHI_ZHICUNMAO,
            columnKey: 'zhicunmao',
            columnId: '',
            title: '治村貌',
            subtitle: '安全有秩序',
        }
    ])
    let sanqingList = reactive([{
            image: RURAL_GOVERNANCE_SANQING_BG01,
            columnKey: 'shenghuolaijiqingli',
            columnId: '',
            title: '生活垃圾清理',
        },
        {
            image: RURAL_GOVERNANCE_SANQING_BG02,
            columnKey: 'cunneigoutangqingli',
            columnId: '',
            title: '村内沟塘清理',
        },
        {
            image: RURAL_GOVERNANCE_SANQING_BG03,
            columnKey: 'shenghuofeiwuqingli',
            columnId: '',
            title: '生活废物清理',
        }
    ])
    let yigaiList = reactive([{
        image: RURAL_GOVERNANCE_YIGAI_BG,
        columnKey: 'yigai',
        columnId: '',
        title: '改变影响农村环境的不良习惯',
        subtitle: '线上咨询、热门问题、电话热线'
    }])
    let fangbaoList = reactive([{
            image: RURAL_GOVERNANCE_FANGBAO_FANGYI,
            columnKey: 'fangyi',
            columnId: '',
            title: '防疫',
        },
        {
            image: RURAL_GOVERNANCE_FANGBAO_FANGXUN,
            columnKey: 'fangxun',
            columnId: '',
            title: '防汛',
        },
        {
            image: RURAL_GOVERNANCE_FANGBAO_FIRE,
            columnKey: 'fanghuo',
            columnId: '',
            title: '防火',
        },
        {
            image: RURAL_GOVERNANCE_FANGBAO_ZAIHAI,
            columnKey: 'fangzaihai',
            columnId: '',
            title: '防灾害',
        }, {
            image: RURAL_GOVERNANCE_FANGBAO_DIZHEN,
            columnKey: 'fangdizhen',
            columnId: '',
            title: '防地震',
        }
    ])
    let fangContentList = reactive([{
        image: RURAL_GOVERNANCE_FANGBAO_ANQUAN,
        columnKey: 'baoanquan',
        columnId: '',
        title: '保安全',
        subtitle: '安全是生活的生命线'
    }, {
        image: RURAL_GOVERNANCE_FANGBAO_WENDING,
        columnKey: 'baowending',
        columnId: '',
        title: '保稳定',
        subtitle: '稳定是生活的幸福线'
    }])
    const containerStyle = {
        background: "url(" + RURAL_GOVERNANCE_BG_IMG + ") no-repeat 100% 100%"
    }
    onShow(() => {
        getFlowIdsByKey()
        getData()
    })
    //根据key获取id
    function getFlowIdsByKey() {
        getFlowIdByKey({
            flowFlag: 'suishoupai'
        }).then((res) => {
            flowId.value = res.data && res.data.flowId
        })
    }
    //监听滑动事件
    function onScroll(e) {
        console.log('---------------------onscroll')
        console.log(e)
        if (e.detail.scrollTop < 100) { //滚动距离为100就显示
            isshow.value = false
        } else {
            isshow.value = true
        }
    }

    function getData() {
        const headerkeys = headerList.map(({
            columnKey
        }) => columnKey)
        const sizhikeys = sizhiList.map(({
            columnKey
        }) => columnKey)
        const sanqingkeys = sanqingList.map(({
            columnKey
        }) => columnKey)
        const yigaikeys = yigaiList.map(({
            columnKey
        }) => columnKey)
        const fangbaokeys = fangbaoList.map(({
            columnKey
        }) => columnKey)
        const fangContentkeys = fangContentList.map(({
            columnKey
        }) => columnKey)
        const keys = [
            ...headerkeys,
            ...sizhikeys,
            ...sanqingkeys,
            ...yigaikeys,
            ...fangbaokeys,
            ...fangContentkeys
        ]
        console.log(keys)

        getColumnByKey({
            columnKeys: keys.join(',')
        }).then((res) => {
            if (res.data && res.data.length > 0) {
                const columnList = res.data.filter(item => item);
                console.log(columnList)
                headerList.forEach(item => {
                    if (item.columnKey == 'menqiansanbao') {
                        const info = columnList.find(ele => ele.columnKey == item.columnKey)
                        if (info) {
                            const {
                                columnId
                            } = info;
                            item.columnId = columnId ? columnId : ''
                        }
                    }

                })
                sizhiList.forEach(item => {
                    const info = columnList.find(ele => ele.columnKey == item.columnKey)
                    if (info) {
                        const {
                            columnId
                        } = info;
                        item.columnId = columnId ? columnId : ''
                    }
                })
                sanqingList.forEach(item => {
                    const info = columnList.find(ele => ele.columnKey == item.columnKey)
                    if (info) {
                        const {
                            columnId
                        } = info;
                        item.columnId = columnId ? columnId : ''
                    }
                })
                yigaiList.forEach(item => {
                    const info = columnList.find(ele => ele.columnKey == item.columnKey)
                    if (info) {
                        const {
                            columnId
                        } = info;
                        item.columnId = columnId ? columnId : ''
                    }
                })
                fangbaoList.forEach(item => {
                    const info = columnList.find(ele => ele.columnKey == item.columnKey)
                    if (info) {
                        const {
                            columnId
                        } = info;
                        item.columnId = columnId ? columnId : ''
                    }
                })
                fangContentList.forEach(item => {
                    const info = columnList.find(ele => ele.columnKey == item.columnKey)
                    if (info) {
                        const {
                            columnId
                        } = info;
                        item.columnId = columnId ? columnId : ''
                    }
                })
            }
        })
    }
    //页面跳转
    function handleClick(item) {
        console.log(item)
        switch (item.columnKey) {
            case 'baoguangtai':
                uni.navigateTo({
                    url: '/jimo/pages/exposure/exposure'
                })
                break;
            case 'suishoupai':
                if (flowId.value) {
                    uni.navigateTo({
                        url: '/jimo/pages/flowPage/flowList?flowId=' + flowId.value
                    })
                } else {
                    uni.showToast({
                        title: '还未配置随手拍应用',
                        duration: 2000,
                        icon: "none",
                    });
                }
                break;
            default:
                if (item.columnId) {
                    uni.navigateTo({
                        //保留当前页面，跳转到应用内的某个页面
                        url: '/pages/listPage/appList?columnId=' + item.columnId
                    })
                }
                break;
        }
    }

    function handleBack() {
        uni.redirectTo({
            url: '/pages/home/<USER>'
        })
    }
</script>

<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .container {
        width: 750rpx;
        height: 100%;

        .scroll-style {
            width: 100%;
            height: calc(100vh - 160rpx);
        }

        .inner-container {
            width: 710rpx;
            margin-top: 23rpx;
            width: 100%;
            padding: 20rpx;
            overflow-y: auto;

            .overview {
                width: 710rpx;
                display: flex;

                .overview-box {
                    width: 222rpx;
                    height: 203rpx;
                    background: #FFFFFF;
                    box-shadow: 0rpx 2rpx 33rpx 2rpx rgba(186, 235, 214, 0.51);
                    border-radius: 20rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    margin-right: 28rpx;
					flex: 1;
                    &:last-child {
                        margin-right: 0;
                    }

                    .title {
                        margin-top: 14rpx;
                        width: 112rpx;
                        height: 40rpx;
                        font-size: 28rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #000000;
                        line-height: 40rpx;
                        text-align: center;
                    }
                }
            }

            .custom {
                width: 710rpx;
                max-width: 710rpx;
                margin-top: 20rpx;
                background: #FFFFFF;
                box-shadow: 0rpx 2rpx 33rpx 2rpx rgba(186, 235, 214, 0.51);
                border-radius: 20rpx;
                padding: 20rpx;

                .custom-title {
                    display: flex;

                    .icon {
                        margin: 8rpx 9rpx 6rpx 0;
                        width: 8rpx;
                        height: 32rpx;
                        background: linear-gradient(180deg, #4AD1AA 0%, #30BD99 100%);
                        border-radius: 4rpx;
                    }

                    .title {
                        height: 45rpx;
                        font-size: 32rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 45rpx;
                    }
                }

                .subtitle {
                    margin-top: 11rpx;
                    width: 442rpx;
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #B0B0B5;
                    line-height: 37rpx;
                }

            }

            .sizhi {
                height: 473rpx;

                .sizhi-boxes {
                    margin-top: 17rpx;
                    display: flex;
                    flex-wrap: wrap;

                    .sizhi-box {
                        margin: 0 20rpx 20rpx 0;
                        // width: 326rpx;
                        flex: 1;
                        display: flex;
                        padding: 38rpx 0 30rpx 24rpx;

                        &:nth-child(2n) {
                            margin-right: 0;
                        }

                        .box-part {
                            margin-left: 17rpx;
                            display: flex;
                            flex-direction: column;

                            .title {
                                width: 84rpx;
                                height: 40rpx;
                                font-size: 28rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #000000;
                                line-height: 40rpx;
                            }

                            .subtitle {
                                width: 130rpx;
                                height: 37rpx;
                                font-size: 26rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                color: #999999;
                                line-height: 37rpx;
                            }

                        }

                    }
                }
            }

            .sanqing {
                height: 289rpx;

                .sanqing-boxes {
                    margin-top: 17rpx;
                    display: flex;
                    justify-content: space-around;

                    .sanqing-box {
                        flex: 1;
                        flex-direction: column;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .title1 {
                            margin-top: 10rpx;
                            width: 157rpx;
                            height: 37rpx;
                            font-size: 26rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #010101;
                            line-height: 37rpx;
                        }
                    }
                }
            }

            .yigai {
                height: 292rpx;

                .yigai-content {
                    margin-top: 15rpx;
                    width: 684rpx;
                    height: 144rpx;
                    padding-top: 15rpx;
                    padding-left: 18rpx;

                    .title1 {
                        margin-top: 41rpx;
                        width: 390rpx;
                        height: 42rpx;
                        font-size: 30rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #000000;
                        line-height: 42rpx;
                    }

                    .desc {
                        width: 364rpx;
                        height: 37rpx;
                        font-size: 26rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #999999;
                        line-height: 37rpx;
                    }
                }
            }


            .fangbao {
                height: 421rpx;

                .fangbao-boxes {
                    margin-top: 25rpx;
                    display: flex;
                    justify-content: space-around;

                    .fangbao-box {
                        flex: 1;
                        flex-direction: column;
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .title1 {
                            margin-top: 10rpx;
                            height: 40rpx;
                            font-size: 28rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #000000;
                            line-height: 40rpx;
                        }
                    }
                }

                .fangbao-bottom {
                    margin-top: 25rpx;
                    display: flex;

                    .fangbao-content {
                        width: 322rpx;
                        height: 144rpx;
                        padding: 28rpx 0 32rpx 23rpx;


                        &:first-child {
                            margin-right: 20rpx;
                        }

                        .title1 {
                            margin-left: 28rpx 0 7rpx 23rpx;
                            height: 40rpx;
                            font-size: 28rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #000000;
                            line-height: 40rpx;
                        }

                        .desc {
                            height: 37rpx;
                            font-size: 26rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 37rpx;
                        }
                    }
                }
            }
        }

    }
</style>