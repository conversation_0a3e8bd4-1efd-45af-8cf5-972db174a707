import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_APPLYJOIN_URL,
	API_MESSAGE_URL,
	API_DETERMINE_URL,
	API_CODE_URL,
} from '@/common/net/netUrl.js'

// 申请加入
export function joinAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_APPLYJOIN_URL,
		method: 'POST',
		params,
	})
}
// 根据字典类型查询字典数据信息
export function getDicts(dictCode) {
	return request({
		url: "/user/dict/data/list/" + dictCode,
		method: "GET"
	});
}
// 信息获取
export function headMessage(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_MESSAGE_URL,
		method: 'POST',
		params,
	})
}

// 判断首次申请加入还是编辑信息
export function deterMine(params) {
	return request({
		url: API_DETERMINE_URL,
		method: "GET",
		params,
	});
}

// 验证码获取
export function codeMessage(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_CODE_URL,
		method: 'POST',
		params,
	})
}