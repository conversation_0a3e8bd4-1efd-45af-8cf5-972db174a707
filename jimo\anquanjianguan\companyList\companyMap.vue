<template>
  <view>
    <view class="head">
      <view class="searchbox flexcon">
        <!-- <input
          type="text"
          class="search"
          v-model.trim="search"
          placeholder="请输入帮扶对象姓名"
          @confirm="inputconfirm"
          confirmType="search"
        />
        <image
          class="icon"
          src="https://ln-hcpk.com/test/2022/lndj/1/branchstyle/search.png"
        ></image> -->
        <u-search
          placeholder="请输入企业名称"
          v-model="search"
          :showAction="false"
          @search="inputconfirm"
        ></u-search>
      </view>
    </view>
    <view class="main">
      <!-- #ifdef MP-WEIXIN -->
      <map
        showLocation
        @markertap="showPeople"
        class="map"
        id="myMap"
        :latitude="latitude"
        :longitude="longitude"
        :markers="markers"
        scale="15"
        :style="{ height: height }"
      ></map>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <view id="MapContainer" :style="{ height: height }"></view>
      <!-- #endif -->
    </view>
    <view class="float-tool">
      <view class="float-item" @click="pointCenter">
        <view class="iconfont icon-ditujujiao"></view>
        <view class="float-txt">居中</view>
      </view>
      <view class="float-item" @click="floorSwitch">
        <view class="iconfont icon-ditu-tuceng"></view>
        <view class="float-txt">图层</view>
      </view>
    </view>
    <view class="floor-body" v-show="floorShow">
      <view class="floor-mask" @click="floorSwitch"></view>
      <view class="floor-box">
        <image
          class="close"
          @click="floorSwitch"
          src="http://************:9000/test/3/images/feibei/villagePeople/closebtn.png"
        ></image>
        <view class="floor-title">图层列表</view>
        <view class="floor-list">
          <view
            class="floor-line"
            @click="floorClick(item)"
            :key="item.name"
            v-for="item in floorList"
          >
            <view
              class="check-box iconfont icon-xuanzhong check"
              v-if="item.val"
            >
            </view>

            <view class="check-box iconfont icon-weixuanzhong" v-else> </view>

            <view class="floor-item">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom" v-show="villagePeople.villageFamilyId">
      <view class="header flexcon">
        <view class="line"></view>
        <view class="content">
          <view class="title">帮扶对象：{{ villagePeople.name }}</view>
          <view class="address">地址：{{ villagePeople.helpAddress || '' }}</view>
        </view>
      </view>
      <view class="descline">
        <view class="cell flexcon">
          <text class="title">联系电话：</text>
          <text class="value">{{ villagePeople.cellphone || '' }}</text>
        </view>
		<view class="cell flexcon">
          <text class="title">帮扶项目：</text>
          <text class="value">{{ villagePeople.projectName || '' }}</text>
        </view>
        <view class="cell flexcon">
          <text class="title">结对干部：</text>
          <text class="value">{{ villagePeople.villageCadreName || '' }}</text>
        </view>
      </view>
      <view class="bottom-btns flexcon u-border-top">
        <view class="leftbtn flexcon" @click="toMap">
          <image
            class="icon"
            src="http://************:9000/test/3/images/feibei/villagePeople/daohang.png"
          ></image>
          <text class="label">导航</text>
        </view>
        <navigator class="rightbtn" :url="orgurl">查看走访日志</navigator>
      </view>
      <image
        class="closebtn"
        @click="closeOrg"
        src="http://************:9000/test/3/images/feibei/villagePeople/closebtn.png"
      ></image>
    </view>
  </view>
</template>

<script>
import { getDicts } from '@/common/net/contacts/contacts.js'
import {findAllHelpObject} from "../../api/helpObject.js"
// #ifdef MP-WEIXIN
let plugin = requirePlugin('routePlan')
// #endif
// #ifdef H5
import AMapLoader from '@amap/amap-jsapi-loader'
window._AMapSecurityConfig = {
  securityJsCode: 'eea4053907931dbd85e42f3f93ac40ed',
}
// #endif
export default {
  data() {
    return {
      key: 'PQ7BZ-6ZN6Z-E6NX5-7Q2EM-V4GPK-VCBZJ',
      referer: '辽宁智慧党建云',
      search: '',
      height: 0,
      latitude: 33.038943,
      longitude: 117.303847,
      markers: [],
      villagePeople: {},
      mapCtx: null,
      floorShow: false,
      // #ifdef H5
      curPosition: null,
      geolocation: null,
      mapcontainer: null,
      // #endif
      floorList: [],
      allmarks: [],
    }
  },
  async onLoad(options) {
    
    await this.getSystemInfo()
    // #ifdef MP-WEIXIN
    this.mapCtx = uni.createMapContext('myMap', this)
    this.mapCtx.moveToLocation()
    // #endif
  },
  // #ifdef H5
  async mounted() {
    AMapLoader.load({
    key: 'a5f2c3111a9c0d9dad01ca6e791669ff', // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
  }).then( async(AMap) => {
    this.mapcontainer = new AMap.Map('MapContainer', {
      zoom: 12,
      center: [this.longitude, this.latitude],
      mapStyle: 'amap://styles/40035571fa9fdd05a26fe1b05f48fdc9',
    })
    this.registerPlugin()
    const response = await getDicts("helptype")
      const list = response.data;
      this.floorList = list.map((item) => {
        return {
          name: item.dictLabel,
          val: false,
          type: item.dictValue,
        }
      });

    await this.getDataInfo()
  })
  },
  // #endif
  onShareAppMessage() {},
  methods: {
    // #ifdef H5
    registerPlugin() {
      // 获取当前位置
      this.mapcontainer.plugin('AMap.Geolocation', () => {
        this.geolocation = new AMap.Geolocation({
          showButton: false,
          enableHighAccuracy: true, // 是否使用高精度定位，默认：true
          convert: true,
          showMarker: true, //是否显示定位点
          markerOptions: {
            //自定义定位点样式，同Marker的Options
            offset: new AMap.Pixel(-18, -36),
            content:
              '<img src="https://a.amap.com/jsapi_demos/static/resource/img/user.png" style="width:36px;height:36px"/>',
          },
          timeout: 10000,
          panToLocation: false,
        })

        this.geolocation.getCurrentPosition((status, data) => {
          console.log(data)
          this.curPosition = data.position
        })

        this.mapcontainer.addControl(this.geolocation)
      })

      // 导航
      this.mapcontainer.plugin('AMap.Driving', () => {
        this.driving = new AMap.Driving({
          mapcontainer: this.mapcontainer,
        })
      })
      this.mapcontainer.setCenter([this.longitude, this.latitude]) //设置地图中心点
    },
    floorSwitch() {
      this.floorShow = !this.floorShow
    },
    addAllPoints() {
      this.allmarks = this.getAllMarkers()
      console.log(this.allmarks, '555555555555555555555555555')
      this.mapcontainer.add(this.allmarks)
    },

    startNavigate() {
      this.geolocation.getCurrentPosition((status, data) => {
        console.log(data, 'ooooooooooooooooo')
        this.driving.clear()
        this.driving = new AMap.Driving({
          map: this.mapcontainer,
        })
        this.driving.search(
          [data.position.lng, data.position.lat],
          [this.villagePeople.longitude, this.villagePeople.latitude],
          (status, data) => {
            console.log('status', status)
            console.log('get data', data)
          }
        )
      })
    },
    floorClick(item) {
      item.val = !item.val
      const type = item.type
      if (item.val) {
        this.allmarks.forEach((item2) => {
          if (item2._opts.extData.helpType == type) {
            item2.show()
          }
        })
      } else {
        this.allmarks.forEach((item2) => {
          if (item2._opts.extData.helpType == type) {
            item2.hide()
          }
        })
      }
    },

    getAllMarkers() {
      return this.markers.map((data) => {
        const position = [data.longitude, data.latitude]
        const marker = new AMap.Marker({
          position,
          offset: new AMap.Pixel(-16, -16),
          extData: { ...data },
          icon: this.generateMarkerIcon(data.iconPath),
        })
        marker.on('click', (e) => {
          console.log(e, 'pppppppppppppppppppppppppp', data)
          this.villagePeople = data
        })
		if(this.floorList.filter(item=>item.type==data.helpType)[0].val){
		  marker.show()
		}else{
          marker.hide()
		}
        return marker
      })
    },
    generateMarkerIcon(iconImage) {
      return new AMap.Icon({
        size: new AMap.Size(32, 32), // 图标尺寸
        imageOffset: new AMap.Pixel(0, 0),
        image: iconImage, // Icon的图像
        imageSize: new AMap.Size(32, 32), // 根据所设置的大小拉伸或压缩图片
      })
    },

    // #endif
    async getDataInfo() {
      let res = await findAllHelpObject({})
      if(res.success){
      // let list = [
      //   {
      //     name: '老人1',
      //     partyLocation: '117.303847, 33.028943',
      //     helpAddress: '地址1',
      //     villageFamilyId: 1,
      //     cellphone: '12345678901',
      //     type: 'old',
      //   },
      //   {
      //     name: '残疾人',
      //     partyLocation: '117.303847, 33.038943',
      //     helpAddress: '地址2',
      //     villageFamilyId: 2,
      //     cellphone: '12345678902',
      //     type: 'disabled',
      //   },
      // ]
      let list = res.data

      let that = this
      this.markers = []
      for (let j = 0; j < list.length; j++) {
        if (list[j].longitude && list[j].longitude.length > 0) {
          let obj = Object.assign(
            {},
            { ...list[j] },
            {
              iconPath:
                'http://************:9000/test/3/images/feibei/villagePeople/' + list[j].helpType + '.png',
              latitude: Number(list[j].latitude),
              longitude: Number(list[j].longitude),
              width: 40,
              height: 40,
              zIndedx: 99,
              id: list[j].villageFamilyId,
            }
          )
          that.markers.push(obj)
        }
      }

      // #ifdef H5
      this.addAllPoints()
      // #endif
    }
    else{
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
        duration: 2000,
      })
    }
    },
    async getSystemInfo() {
      uni.getSystemInfo({
        success: (e) => {
          const width = e.windowWidth
          let r = e.windowHeight

          this.height = r + 'px'
        },
      })
    },

    searchSubmit() {
      this.searchHandler()
    },
    inputconfirm() {
      this.searchHandler()
    },
    async searchHandler() {
      if (this.search) {
        if (this.markers.length) {
          let res = this.markers.filter(
            (item) => item.name.indexOf(this.search) > -1
          )
          console.log(res, '66666666666666666')
          ;(this.latitude = res[0].latitude),
            (this.longitude = res[0].longitude),
            (this.villagePeople = res[0])
        } else {
          uni.showToast({
            title: '无数据~',
            icon: 'none',
            duration: 2000,
          })
        }
      }
    },
    showOrg(e) {
      const markerId = e.detail.markerId
      this.villagePeople = this.markers.filter(
        (item) => item.villageFamilyId == markerId
      )[0]
    },
    closeOrg() {
      this.villagePeople = {}
    },
    toMap() {
      // #ifdef MP-WEIXIN
      if (this.villagePeople.latitude && this.villagePeople.latitude) {
        const routestr = JSON.stringify({
          name: this.villagePeople.helpAddress,
          latitude: this.villagePeople.latitude,
          longitude: this.villagePeople.longitude,
        })
        uni.navigateTo({
          url:
            'plugin://routePlan/index?key=' +
            this.key +
            '&referer=' +
            this.referer +
            '&endPoint=' +
            routestr,
        })
      } else {
        uni.showToast({
          title: '无定位信息~',
          icon: 'none',
          duration: 2000,
        })
      }
      // #endif
      // #ifdef H5
      this.startNavigate()
      // #endif
    },
  },
  computed: {
    orgurl() {
      return '/feibei/pages/mapgrid/helplist?id=' + this.villagePeople.villageFamilyId
    },
  },
}
</script>

<style scoped lang="scss">
@import '../../static/css/iconfont.css';
.flexcon {
  display: flex;
  align-items: center;
}
.head {
  background: rgba(255, 255, 255, 0);
  position: fixed;
  top: var(--window-top);
  width: 750rpx;
  z-index: 99;

  .searchbox {
    margin-left: 40rpx;
    margin-top: 29rpx;
    width: 670rpx;
    height: 80rpx;
    background: #ffffff;
    box-shadow: 0px 3rpx 8rpx 0px rgba(0, 0, 0, 0.1);
    border-radius: 40rpx;
    padding: 0 32rpx;
    box-sizing: border-box;

    .search {
      flex: 1;
      font-size: 26rpx;
      color: #333333;
    }

    .icon {
      margin-left: 20rpx;
      width: 38rpx;
      height: 38rpx;
    }
  }
}

.main {
  position: relative;

  .map {
    width: 100%;
  }
}
::v-deep .amap-markers {
  width: 100%;
  height: 100%;
  z-index: 99999;
}
.bottom {
  z-index: 100;
  bottom: calc(var(--window-bottom) + 40rpx);
  position: fixed;
  width: 710rpx;
  box-sizing: border-box;
  left: 20rpx;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0px 6rpx 30rpx 0px rgba(0, 0, 0, 0.2);
  border-radius: 20rpx;
  padding: 33rpx 30rpx 20rpx 30rpx;

  .header {
    margin-bottom: 34rpx;

    .line {
      min-width: 8rpx;
      height: 70rpx;
      background: #188833;
      margin-right: 10rpx;
    }

    .content {
      .title {
        font-size: 36rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 8rpx;
      }

      .address {
        font-size: 26rpx;
        font-weight: 400;
      }
    }
  }

  .descline {
    margin-bottom: 30rpx;

    .cell {
      font-size: 26rpx;
      color: #333333;
      margin-bottom: 20rpx;

      .title {
        font-weight: bold;
      }
    }
  }

  .bottom-btns {
    padding-top: 20rpx;
    justify-content: flex-end;

    .leftbtn {
      width: 190rpx;
      height: 70rpx;
      background: #ffffff;
      border: 1rpx solid #cccccc;
      border-radius: 35rpx;
      margin-right: 20rpx;
      justify-content: center;

      .icon {
        width: 30rpx;
        height: 34rpx;
        margin-right: 15rpx;
      }

      .label {
        font-size: 28rpx;
        color: #333333;
      }
    }

    .rightbtn {
      width: 230rpx;
      height: 70rpx;
      line-height: 70rpx;
      text-align: center;
      background: #188833;
      border-radius: 35rpx;
      font-size: 28rpx;
      color: #ffffff;
    }
  }

  .closebtn {
    position: absolute;
    right: 22rpx;
    top: 28rpx;
    width: 44rpx;
    height: 44rpx;
  }
}
.float-tool {
  position: fixed;
  width: 80rpx;
  top: 200rpx;
  left: 30rpx;
  z-index: 999;
  color: #333;
  font-size: 24rpx;
  border-radius: 2rpx;
  background-color: #fff;
  box-shadow: 0px 0px 3px #888888;

  .float-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0;
    overflow: hidden;
    .iconfont {
      margin-top: 5rpx;
      font-size: 50rpx;
    }

    &::after {
      position: absolute;
      height: 1rpx;
      left: 0rpx;
      right: 0rpx;
      bottom: 0;
      content: '';
      background-color: #a1a1a1;
    }
    &:last-child {
      &::after {
        display: none;
      }
    }
  }
}

.floor-box {
  position: fixed;
  width: 100%;
  height: 780rpx;
  left: 0;
  bottom: 0;
  z-index: 999;
  color: #333;
  font-size: 28rpx;
  background-color: #fff;

  .floor-title {
    height: 90rpx;
    padding-left: 30rpx;
    color: #333;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-weight: bold;
    border-bottom: 1px solid #eee;
  }
  .close {
    position: absolute;
    top: 20rpx;
    width: 40rpx;
    height: 40rpx;
    right: 20rpx;
  }

  .floor-line {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 76rpx;
    padding-left: 30rpx;

    .check-box {
      margin-right: 30rpx;
      font-size: 40rpx;
      color: #fff;
      border: #333 solid 1rpx;

      &.check {
        color: #188833;
        border: #188833 solid 1rpx;

        // background-color: red;
      }
    }

    &.second {
      padding-left: 114rpx;
    }
  }
}
.floor-body {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  .floor-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(35, 28, 28, 0.1);
  }
}
</style>
