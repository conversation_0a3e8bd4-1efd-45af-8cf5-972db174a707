<template>
    <view class="main-container">
        <u-tabs :list="tabList" @click="changeTab" v-if="tabList.length > 1"></u-tabs>
        <view class="list-container">
            <view class="list-item" :class="{'list-item1':formName == '随手拍'}" @click="goDetail(item)"
                v-for="(item,index) in flowList" :key="index">
                <view class="title-box">
                    <view class="title">{{item.applyName}}</view>
                    <view class="status pass" :class="{'refuse': item.auditStatus == '已拒绝'}">{{item.auditStatus}}</view>
                </view>
                <view class="content-box" :class="{'content-box1':formName == '随手拍'}" v-if="formName == '随手拍'">
                    <view class="u-line-1" v-for="(ele,ind) in item.fieldLabels" :key="ind">
                        <text class="field-label">{{ele}}</text>：<text
                            class="field-value">{{item.fieldValues[ind]}}</text>
                    </view>
                </view>
                <view class="content-box" v-else>
                    <view class="u-line-1">
                        <text class="field-label">{{item.fieldLabels[0]}}</text>：<text
                            class="field-value">{{item.fieldValues[0]}}</text>
                    </view>
                    <template v-if="item.fieldLabels.length > 1">
                        <view class="u-line-1"><text class="field-label">{{item.fieldLabels[1]}}</text>：<text
                                class="field-value">{{item.fieldValues[1]}}</text></view>
                    </template>
                </view>
                <view class="time-stamp">
                    {{item.createDate}} {{item.applyPeopleName}}
                </view>
            </view>
            <!-- 底部加载，三个状态：more、loading、nomore -->
            <u-loadmore :status="loadStatus" v-if='flowList.length > 0'></u-loadmore>
            <view class="addBtn" v-if="isShowApply">
                <u-image src="@/static/images/plus-circle.svg" width="80rpx" height="80rpx" mode="aspectFit"
                    @click="goPublish"></u-image>
            </view>
            <view class="empty-status" v-if='flowList.length == 0'>
                <view class="empty-icon">
                    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                        height="150rpx"></u-image>
                    <view class="no-more">
                        <span class="txt">暂无数据</span>
                    </view>
                </view>
            </view>

        </view>

    </view>
</template>

<script setup>
    import {
        ref,
        reactive,
        nextTick,
        computed
    } from 'vue'
    import {
        onShow,
        onLoad,
        onReachBottom
    } from '@dcloudio/uni-app'
    import {
        getFlowTab,
        getFlowList
    } from '../../api/flowPage/flowPage.js'
    import {
        NO_MORE_IMG
    } from '@/common/net/staticUrl.js'
    import {
        setPoints
    } from '../../api/burialPoint.js'
    import {
        goldTask
    } from '../../api/api.js'

    const flowId = ref('') // 流程id
    const formName = ref('')
    const taskId = ref('')
    const appKey = ref('')
    onLoad((options) => {
        // 获取应用流程id
        flowId.value = options.flowId
        getFlowTypeTabs()

        // 来自金币任务
        const {
            appKey: appKeyFromOptions = ''
        } = options;
        appKey.value = appKeyFromOptions
        const {
            taskId: taskIdFromOptions = ''
        } = options;
        taskId.value = taskIdFromOptions
        if (taskId.value) {
            getGoldTask()
        }
    })
    onShow(() => {
        flowList.value = [];
        // 获取列表数据
        if (tabList.value.length > 0) {
            currentPage.value = 1;
            getFlowPage(currentTab.value);
        }
    })
    // 列表分类，根据应用类型展示的分类不同
    const tabList = ref([])
    const isShowApply = ref(false)
    const currentTab = ref('')
    const currentPage = ref(1)
    const total = ref(0)
    const loadStatus = ref('loading')
    // 流程列表数据
    const flowList = ref([])
    // 获取数据分类tab
    function getFlowTypeTabs() {
        let param = {
            flowId: flowId.value
        }
        getFlowTab(param).then(res => {
            console.log(res);
            if (res && res.success && res.data.tabList.length > 0) {
                isShowApply.value = res.data.isShowApply;
                tabList.value = res.data.tabList;
                currentTab.value = tabList.value[0].value;
                uni.setNavigationBarTitle({
                    title: res.data.formName
                });
                formName.value = res.data.formName;
                // 获取列表数据
                getFlowPage(tabList.value[0].value);
                // 埋点方法
                let reportParam = {}
                if (formName.value == '领导信箱') {
                    // 埋点-进入领导信箱页
                    reportParam = {
                        eventId: 'leader_mailbox_enter',
                        attributeValue: '/leader_mailbox'
                    }
                } else if (formName.value == '事件上报') {
                    // 埋点-进入事件上报页
                    reportParam = {
                        eventId: 'enter_event_reporting_list',
                        attributeValue: '/jimo/pages/flowPage/flowList'
                    }
                }
                setPoints(reportParam)
            }
        })
    }
    // 获取流程列表数据
    function getFlowPage(tabValue) {
        let param = {
            pageNum: currentPage.value,
            pageSize: 10,
            flowId: flowId.value,
            tabValue: tabValue
        }
        getFlowList(param).then(res => {
            if (res && res.data && res.data.records.length > 0) {
                total.value = res.data.total
                // 遍历列表数据，每个项目取都表单项的前两个用于在列表页展示
                res.data.records.forEach((item) => {
                    let fieldLabels = []
                    let fieldValues = []
                    // 解析表单项和表单值
                    let tableFields = JSON.parse(item.formJsonStr);
                    console.log('tableFields----', tableFields);
                    // 取前两项
                    if (formName.value == '随手拍') {
                        tableFields && tableFields.map((ele) => {
                            fieldLabels.push(ele.__config__.label)
                            fieldValues.push(ele.__config__.defaultValue)
                        })
                        // fieldLabels.push(tableFields[0].__config__.label)
                        // fieldValues.push(tableFields[0].__config__.defaultValue)
                        // if (tableFields.length > 1) {
                        //     fieldLabels.push(tableFields[1].__config__.label)
                        //     fieldValues.push(tableFields[1].__config__.defaultValue)
                        // }
                    } else {
                        fieldLabels.push(tableFields[0].__config__.label)
                        fieldValues.push(tableFields[0].__config__.defaultValue)
                        if (tableFields.length > 1) {
                            fieldLabels.push(tableFields[1].__config__.label)
                            fieldValues.push(tableFields[1].__config__.defaultValue)
                        }
                    }

                    // 赋值给原对象
                    item.fieldLabels = fieldLabels;
                    item.fieldValues = fieldValues;
                })
                if (currentPage.value > 1) {
                    flowList.value = [...flowList.value, ...res.data.records];
                } else {
                    flowList.value = res.data.records;
                }
                loadStatus.value = total.value === flowList.value.length ? "nomore" : "more";
            }
        }).catch((err) => {
            console.log(err);
        })
    }
    //下拉监听方法
    onReachBottom(() => {
        if (loadStatus.value == 'nomore') {
            return;
        }
        currentPage.value = currentPage.value + 1;
        getFlowPage(currentTab.value);
    });
    // 切换列表类型
    function changeTab(item) {
        flowList.value = [];
        currentTab.value = item.value;
        currentPage.value = 1;
        loadStatus.value = 'loading'
        getFlowPage(item.value);
        if (formName.value == '事件上报') {
            // 埋点-处理状态tab切换
            let param = {
                eventId: 'switch_status_tab_event',
                attributeValue: item.value
            }
            setPoints(param)
        }
    }
    // 查看审批详情
    function goDetail(flowItem) {
        if (formName.value == '事件上报') {
            // 埋点-点击事件列表内容
            let param = {
                eventId: 'click_event_list_item',
                attributeValue: flowItem.applyId
            }
            setPoints(param)
        }
        uni.navigateTo({
            url: '/jimo/pages/flowPage/flowDetail?applyId=' + flowItem.applyId + '&flowId=' + flowId
                .value + '&formName=' + formName.value
        })
    }
    // 发布
    function goPublish() {
        uni.navigateTo({
            url: '/jimo/pages/flowPage/flowPublish?flowId=' + flowId.value + "&appKey=" + appKey.value
        })
    }

    // 金币任务
    async function getGoldTask() {
        try {
            let res = await goldTask({
                taskId: taskId.value,
                taskType: '1',
                appKey: appKey.value
            });
            if (res && res.success) {
                // let goldMessage = taskId.value ? `任务完成，获得${res.data.coin}金币` : `获得${res.data.coin}金币`;
                let goldMessage = `任务完成，获得${res.data.coin}金币`
                handleToast(goldMessage);
            } else {
                console.log('获取金币失败', res)
                handleToast(res.message);
            }
        } catch (err) {
            console.error('获取金币异常', err)
            // handleToast('操作异常，请稍后再试！');
        }
    }

    function handleToast(message) {
        // 使用配置对象
        const toastConfig = {
            title: message,
            icon: 'none',
            duration: 1500,
            success: () => {
                const timer = setTimeout(() => {
                    clearTimeout(timer)
                    // 家庭任务积分获取-应用访问
                    responsibility({
                        taskType: '2',
                        appKey: appKey.value
                    })
                }, 1500)
            }
        };
        uni.showToast(toastConfig);
    }
</script>

<style lang="scss" scoped>
    .main-container {
        box-sizing: border-box;
        padding: 0 20rpx;
        padding-top: 20rpx;
        width: 100%;
        height: 100%;
        // background: linear-gradient(180deg, #e3f7f0, #eef6f6);

        .list-container {
            box-sizing: border-box;
            margin-top: 20rpx;
            width: 100%;

            .list-item {
                box-sizing: border-box;
                margin-bottom: 20rpx;
                padding: 20rpx;
                width: 100%;
                height: 248rpx;
                border-radius: 20rpx;
                background: #fff;

                .title-box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20rpx;

                    .title {
                        font-size: 30rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #333333;
                    }

                    .staus {
                        height: 40rpx;
                        font-size: 28rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        line-height: 40rpx;
                    }

                    .pass {
                        color: #04B97E;
                    }

                    .refuse {
                        color: #EB1701;
                    }
                }

                .content-box {
                    margin-bottom: 20rpx;
                    height: 100rpx;

                    .field-label {
                        font-size: 28rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #333;
                    }

                    .field-value {
                        font-size: 28rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #666;
                    }
                }

                .content-box1 {
                    height: 180rpx;
                }

                .time-stamp {
                    height: 36rpx;
                    font-size: 24rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #999999;
                    line-height: 36rpx;
                }
            }

            .list-item1 {
                height: 340rpx;
            }
        }

        .list-container:first-of-type {
            margin-top: 0;
        }
    }

    .main-container ::v-deep .u-tabs__wrapper__nav__item {
        flex: 1
    }

    .addBtn {
        position: fixed;
        bottom: 120rpx;
        right: 60rpx;
    }

    .empty-status {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 50rpx;

        .empty-icon {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .no-more {
                margin-top: 20rpx;
                display: flex;
                justify-content: center;
                margin-top: 10rpx;

                .txt {
                    text-align: center;
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #333333;
                    line-height: 37rpx;
                }
            }

        }
    }
</style>
<style>
    uni-page-body,
    page {
        background: linear-gradient(180deg, #e3f7f0, #eef6f6);
    }
</style>