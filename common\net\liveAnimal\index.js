// 模拟告警数据
const mockAlarmData = {
  records: [
    {
      id: 'WS001',
      warnGrade: '0', // 0-紧急 1-重要 2-提醒
      alarmDescribe: '土壤湿度低于阈值，当前值：15%，阈值：30%',
      type: '0', // 0-待处理 1-处理中 2-已完成
      addressDetail: '示范区A区块1号地块',
      warnTime: '2024-01-20 10:30:00'
    },
    {
      id: 'SS001',
      warnGrade: '1',
      alarmDescribe: '苗高超出正常范围，当前值：25cm，阈值：20cm',
      type: '0',
      addressDetail: '示范区B区块2号地块',
      warnTime: '2024-01-20 11:00:00'
    },
    {
      id: 'PS001',
      warnGrade: '2',
      alarmDescribe: '虫口密度超出预警值，当前值：35头/㎡，阈值：30头/㎡',
      type: '1',
      addressDetail: '示范区C区块3号地块',
      warnTime: '2024-01-20 11:30:00'
    }
  ],
  total: 3,
  size: 20,
  current: 1,
  pages: 1
}

// 模拟告警配置数据
const mockAlarmConfig = {
  id: '1',
  // 土壤墒情告警配置
  soilHumidityUp: '80',
  soilHumidityDown: '30',
  soilTemperatureUp: '35',
  soilTemperatureDown: '15',
  conductivityUp: '2',
  conductivityDown: '0.5',
  phValueUp: '7.5',
  phValueDown: '5.5',
  waterLevelUp: '200',
  waterLevelDown: '50',
  
  // 苗情监测告警配置
  seedlingHeightUp: '20',
  seedlingHeightDown: '5',
  leafAreaIndexUp: '5',
  leafAreaIndexDown: '2',
  tillersPerPlantUp: '6',
  tillersPerPlantDown: '2',
  survivalRateUp: '100',
  survivalRateDown: '85',
  pestLevelUp: '3',
  pestLevelDown: '1',
  
  // 虫情与孢子监测告警配置
  pestDensityUp: '30',
  pestDensityDown: '5',
  sporeConcentrationUp: '200',
  sporeConcentrationDown: '50',
  
  // 告警等级
  alarmGrade: '1',
  alarmGradeShow: '重要'
}

export const LiveAnimalApi = {
  // 获取告警事件数据
  getMyAlarmEventData(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 根据告警等级筛选
        let filteredRecords = [...mockAlarmData.records]
        if (params.warnGrade !== '') {
          filteredRecords = filteredRecords.filter(item => item.warnGrade === params.warnGrade)
        }
        // 根据处理状态筛选
        filteredRecords = filteredRecords.filter(item => item.type === String(params.type))
        
        resolve({
          success: true,
          data: {
            ...mockAlarmData,
            records: filteredRecords,
            total: filteredRecords.length
          }
        })
      }, 500)
    })
  },

  // 更新告警状态
  updateMyAlarm(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟更新告警状态
        params.forEach(item => {
          const alarm = mockAlarmData.records.find(a => a.id === item.id)
          if (alarm) {
            alarm.type = item.type
          }
        })
        resolve({
          success: true,
          data: true
        })
      }, 500)
    })
  },

  // 获取告警配置
  getAlarmConfig() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: mockAlarmConfig
        })
      }, 500)
    })
  },

  // 更新告警配置
  updateAlarmConfig(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        Object.assign(mockAlarmConfig, params)
        resolve({
          success: true,
          data: true
        })
      }, 500)
    })
  },

  // 添加告警配置
  addAlarmConfig(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        Object.assign(mockAlarmConfig, params)
        resolve({
          success: true,
          data: true
        })
      }, 500)
    })
  }
}