<template>
  <view
    class="formcontainer"
    :style="{ backgroundImage: `url(${LIVE_INVENTORY_BG})` }"
  >
    <u-navbar
      title="巡检打卡"
      bgColor="rgba(0,0,0,0)"
      :autoBack="true"
      :placeholder="true"
      leftIconColor="#000"
      titleStyle="font-size: 36rpx;color: #000"
    />
    <logoutbtn></logoutbtn>

    <view class="infocontainer">
      <view class="form">
        <view class="formitem">
          <view class="label">企业名称：</view>
          <view class="value">{{ detail.enterpriseName }}</view>
        </view>
        <view class="formitem">
          <view class="label">企业性质：</view>
          <view class="value">{{ detail.enterpriseNature }}</view>
        </view>
        <view class="formitem">
          <view class="label">企业规模：</view>
          <view class="value">{{ detail.enterpriseScale }}</view>
        </view>
        <view class="formitem">
          <view class="label">最新巡检时间：</view>
          <view class="value">{{ detail.lastInspectTime }}</view>
        </view>
      </view>
      <view class="form">
       
        <view class="formitem">
          <view class="label">巡检时间：</view>
          <view class="value">{{ detail.inspectTime }}</view>
        </view>
        <view class="formitem">
          <view class="label">巡检定位：</view>
          <view class="value">{{ detail.businessAddress }}</view>
        </view>
       <view class="label">检查记录：</view>
        <view class="imgcell">
          <view class="imgitem" v-for="item in detail.checkimglist" :key="item">
            <view class="img">
              <image
                :src="item"
               
              ></image>
            </view>
          </view>
        </view>
        <view class="formitem">
          <view class="label">是否存在重大隐患：</view>
          <view class="value">{{ detail.isMajorRisk=='YES' ? '是' : '否' }}</view>
        </view>
            <view class="label">
            隐患图片：
          </view>
          <view class="imgcell">
            <view
              class="imgitem"
              v-for="item in detail.dangerimglist"
              :key="item"
            >
              <view class="img">
                <image
                  :src="item"
              
                ></image>
              </view>
            </view>
          </view>     
          <view class="label"> 隐患描述： </view>
          <view class="value">{{ detail.problemDesc }}</view>

          <view class="formitem">
            <view class="label">整改期限：</view>
            <view class="value">{{ detail.rectifyDeadline }}</view>
          </view>
          <view class="formitem">
            <view class="label">上传人：</view>
            <view class="value">{{ detail.uploader }}</view>
          </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { LIVE_INVENTORY_BG } from '@/common/net/staticUrl'
import { onLoad } from '@dcloudio/uni-app'
import { AJYService } from "../../api/anquanjianguan/companyList.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const detail = ref({})

onLoad((options) => {
  const recordId = options.id
  AJYService.findInspectRecordById(recordId).then(res => {
    if (res.success) {
      detail.value = res.data
      detail.value.checkimglist = detail.value.inspectImages?detail.value.inspectImages.split(','):[]
      detail.value.dangerimglist = detail.value.riskImages?detail.value.riskImages.split(','):[]
    }
  })
})

</script>
<style scoped lang="scss">
.flexitem {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.formitem {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  ::v-deep .u-icon {
    margin-left: 20rpx;
  }
}
.value {
  color: #666;
  word-wrap: break-word;
}
::v-deep .redplaceholder {
  color: red !important;
  font-size: 28rpx;
}
.cellcomplete {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 0 26rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-family: PingFang-SC, PingFang-SC;
  color: #000000;
  .rightarrow {
    width: 30rpx;
    height: 30rpx;
  }
}
.workgetcon {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 23rpx 26rpx;
  .workgetarea {
    position: relative;
    .btngroup {
      position: absolute;
      right: 0;
      top: -46rpx;
      display: flex;
      align-items: center;
      .btn {
        width: 111rpx;
        height: 48rpx;
        border-radius: 31rpx;
        border: 0.5px solid #cccccc;
        line-height: 48rpx;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        margin-left: 7rpx;
      }
      .greenbtn {
        border: 0.5px solid #0bbd88;
        color: #0bbd88;
      }
    }
    .areacon {
      padding-top: 30rpx;
    }
  }
  .label {
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
  }
  .addWorkget {
    padding: 36rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #0bbd88;
    line-height: 45rpx;
    .plus {
      margin-right: 20rpx;
    }
  }
}
.imgcell {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .imgitem {
    width: 233rpx;
    height: 179rpx;
    border: 0.5px solid #e2e2e2;
    margin-right: 40rpx;
    margin-bottom: 20rpx;
    position: relative;

    .img {
      margin: 7rpx;
      width: 219rpx;
      height: 164rpx;
      image{
       width: 219rpx;
      height: 164rpx; 
      }
    }

    .closebtn {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: -16rpx;
      right: -16rpx;
    }
  }
}
.gridcontent {
  padding: 15rpx 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 28rpx;
  font-family: PingFang-SC, PingFang-SC;
  font-weight: 400;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 56rpx;
}
.fileitem {
  display: flex;
  flex-wrap: wrap;
  white-space: nowrap;
  position: relative;
  text {
    font-size: 24rpx;
    color: #ccc;
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .closebtn {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.imgitem {
  width: 233rpx;
  height: 179rpx;
  border: 0.5px solid #e2e2e2;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
  position: relative;
  image {
    margin: 7rpx;
    width: 219rpx;
    height: 164rpx;
  }
  .closebtn {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.uploadcon {
  width: 100%;
  overflow: hidden;
}
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 180rpx;
}
.arrow {
  top: 50rpx;
  width: 16rpx;
  height: 30rpx;
  position: absolute;
  right: 0;
}
.formcontainer {
  height: 100vh;
  .infocontainer {
    margin: 20rpx;

    height: calc(100vh - 220rpx);
    box-sizing: border-box;
    overflow: scroll;
    .form {
      padding: 0 26rpx;
      background: #ffffff;
      border-radius: 20rpx;
      padding-bottom: 26rpx;
      ::v-deep .uni-input-wrapper {
        text-align: right;
      }
      ::v-deep .u-form-item__body__left__content__required {
        position: inherit;
        left: auto;
      }
      ::v-deep .u-radio-group {
        justify-content: end;
      }
      ::v-deep .u-radio {
        margin-right: 20rpx;
      }
      &:nth-child(1) {
        margin-bottom: 26rpx;
      }
    }
  }
  ::v-deep .u-form-item {
    position: relative;
  }
  .label {
    font-size: 32rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 600;
    color: #333333;
    line-height: 45rpx;
    padding: 23rpx 0;
  }
  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  ::v-deep .u-input__content__field-wrapper__field {
    font-size: 28rpx;
    font-family: PingFang-SC, PingFang-SC;
    font-weight: 400;
    color: #b1b1b1;
    line-height: 40rpx;
  }
}
</style>
