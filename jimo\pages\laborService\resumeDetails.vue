<!-- 找工详情 -->
<template>
	<view class="my-resume" v-if="resumeInfo && isShowDetails">
		<u-navbar title="找工详情" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000" />
			
		<view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<view class="name">{{ resumeInfo.resumeName }}</view>
				<view class="info">
					<text>{{ resumeInfo.sex === 'woman' ? '女' : '男' }}</text>
					<text v-if="resumeInfo.age">｜{{ resumeInfo.age }}</text>
					<text v-if="resumeInfo.positionName">｜{{ resumeInfo.positionName }}</text>
				</view>
				<view class="money">
					<image :src="LABOR_MONEY" />
					<text>{{ resumeInfo.salaryRangeLow }}-{{ resumeInfo.salaryRangeHigh }}元</text>
				</view>
				<view class="address">
					<image :src="LABOR_DZ" />
					<view style="width: calc(100% - 46rpx);">
						{{ resumeInfo.regionPlace }}
					</view>
				</view>
				<view class="self-introduction">
					<view class="title">个人介绍</view>
					<view class="content">{{ resumeInfo.introduction }}</view>
				</view>
				<view class="certificate" v-if="resumeInfo.img">
					<view class="title">证书照片</view>
					<view class="content">
						<view v-for="item in resumeInfo.img.split(',')" :key="item.text">
							<image :src="item" />
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<view class="communication">
			<view class="btn" @click="handleCallPhone()">
				<u-icon name="phone-fill" color="#fff" size="32rpx"></u-icon>
				<text>立即沟通</text>
			</view>
		</view>
	</view>
	<view class="empty-status" v-else>
		<u-navbar title="找工详情" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000" />
		<view class="empty-icon">
			<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
			<view class="no-more">
				<span class="txt">找工信息不存在</span>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { LABOR_MONEY, LABOR_DZ, LABOR_RESUME_EDIT, NO_MORE_IMG } from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'
import {
        goldTask
    } from '../../api/api.js'
import { responsibility } from '@/common/net/my/my.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 数据加载 */
const loading = ref(false)
/** 简历详情 */
const resumeInfo = ref(null)
const taskId = ref('')
const isShowDetails = ref(true)
onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	const { resumeId } = options
	getResumeInfo(resumeId)
	LaborService.addResumeViewNumber({resumeId})
	// 来自金币任务
	const { taskId: taskIdFromOptions = '' } = options
	taskId.value = taskIdFromOptions
	getGoldTask()
})

// 金币任务-劳务用工-阅读信息
async function getGoldTask() {
	try {
		let res = await goldTask({ 
			taskId: taskId.value,
			taskType: '5',
			appKey: "laowuyonggong"
		});
		if (res && res.success) {
			let goldMessage = taskId.value ? `任务完成，获得${res.data.coin}金币` : `获得${res.data.coin}金币`;
			handleToast(goldMessage);
		} else {
			console.log('获取金币失败',res)
			if(taskId.value) {
				handleToast(res.message);
			}
		}
	} catch (err) {
		console.error('获取金币异常',err)
	}
}

function handleToast(message) {
	// 使用配置对象
	const toastConfig = {
		title: message,
		icon: 'none',
		duration: 1500,
		success: () => {
			const timer = setTimeout(() => {
				clearTimeout(timer)
				// 家庭任务积分获取-阅读信息
				responsibility({ taskType: '4' })
			}, 1500)
		}
	};
	uni.showToast(toastConfig);
}
	
/**
 * @description 获取简历详情
 * @param {type} resumeId 
 */
const getResumeInfo = (resumeId) => {
	uni.showLoading({ title: '加载中...', mask: true })
	
	LaborService.getResumeInfo({resumeId}).then(r => {
		if (r.data) {
			resumeInfo.value = r.data
			if(r.data.publishStatus != 'on' || r.data.status != 'on'){
				isShowDetails.value = false
			}else{
				isShowDetails.value = true
			}
			getWorkPlace()
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
const getWorkPlace = () => {
	const { workPlace, countyName } = resumeInfo.value
	const strList = workPlace.split(',')
	const str = strList[0].split(countyName)[0] + countyName
	
	resumeInfo.value.regionPlace = str + ' ' + strList.map(item => item.replace(new RegExp(str, 'g'), '')).join('、')
}
/**
 * @description 立即沟通
 */
const handleCallPhone = () => {
	uni.makePhoneCall({
		phoneNumber: resumeInfo.value.telephone
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}
.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 34rpx 19rpx 20rpx 21rpx;
	
	.name {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		font-size: 42rpx;
		color: #000000;
		line-height: 59rpx;
	}
	
	.info {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 26rpx;
		color: #111111;
		line-height: 37rpx;
		margin-top: 23rpx;
	}
	
	.money {
		display: flex;
		margin-top: 27rpx;
		
		image {
			width: 32rpx;
			height: 32rpx;
			margin-top: 1rpx;	
			margin-right: 12rpx;
		}
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #111111;
			line-height: 37rpx;
		}
	}
	
	.address {
		display: flex;
		margin-top: 28rpx;
		
		image {
			width: 32rpx;
			height: 32rpx;
			margin-top: 3rpx;
			margin-right: 14rpx;
		}
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 33rpx;
		}
	}
	
	.self-introduction {
		margin-top: 34rpx;
		border-top: 1rpx solid #E8E8E8;
		padding: 21rpx 7rpx 30rpx 5rpx;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}
		
		.content {
			margin-top: 11rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 48rpx;
			padding-left: 13rpx;
			white-space: pre-wrap;
		}
	}
	
	.certificate {
		margin-top: 20rpx;
		display: flow-root;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}
		
		.content {
			margin-top: 16rpx;
			
			> view {
				width: calc((100% - 20rpx) / 2);
				height: 243rpx;
				border: 1rpx solid #E3E3E3;
				padding: 8rpx;
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				float: left;
				
				&:nth-child(2n) {
					margin-right: 0 !important;
				}
				
				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
}

.communication {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	padding-bottom: calc(env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
			line-height: 50rpx;
		}
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}

.empty-status {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 50rpx;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}
</style>