<template>
  <view class="training-list" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
    <u-navbar title="活动列表" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
      <logoutbtn></logoutbtn>

    <view class="container">
      <view class="searchCont">
        <u-search v-model="keyword" placeholder="请输入活动标题" :showAction="false" :clearabled="false" bgColor="#FFFFFF" height="78rpx" @search="search" />
        <view class="status-select">
          <u-input v-model="statusText" placeholder="活动状态" readonly border="none" >
            <template #suffix>
                <u-icon name="arrow-down" @click="showStatusPicker = true" style="margin-right: 10rpx;"></u-icon>
           </template>
          </u-input>
        </view>
      </view>
      <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="getList">

        <view class="empty-status" v-if="loaded && dataList.length === 0">
          <view class="empty-icon">
            <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
            <view class="no-more">
              <span class="txt">暂无数据</span>
            </view>
          </view>
        </view>
        <view class="trainingList">
          <view class="list" v-for="item in dataList" :key="item.trainingId" @click="gotoDetail(item)">
            <view class="row row-title">
              <text class="label">活动标题：</text>
              <text class="value">{{ item.trainingTitle }}</text>
              <view class="status-btn" :class="parseStatus(item.trainingStatus)">{{ item.trainingStatus}}</view>
            </view>
            <view class="row">
              <text class="label">活动开始时间：</text>
              <text class="value">{{ item.trainingTrainingStartTime }}</text>
            </view>
            <view class="row">
              <text class="label">活动结束时间：</text>
              <text class="value">{{ item.trainingTrainingEndTime }}</text>
            </view>
            <view class="row">
              <text class="label">活动地点：</text>
              <text class="value">{{ item.trainingLocation }}</text>
            </view>
          </view>
        </view>
        <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && dataList.length === 0)"
      ></u-loadmore>
      </scroll-view>
    </view>
     <view @click="addTrain">
        <floating-button></floating-button>
      </view>
    <u-picker closeOnClickOverlay @close="showStatusPicker = false" :show="showStatusPicker" keyName="label" :columns="[statusList]" @confirm="onStatusConfirm" @cancel="showStatusPicker = false" />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { LIVE_INVENTORY_BG,NO_MORE_IMG } from '@/common/net/staticUrl.js';
import { TrainingService } from "../../api/anquanjianguan/training.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'

const keyword = ref('')
const statusText = ref('全部')
const showStatusPicker = ref(false)

// 活动状态列表
const statusList = ref([
  { label: '全部', value: '' },
  { label: '未开始', value: 'NOT_STARTED' },
  { label: '进行中', value: 'IN_PROGRESS' },
  { label: '已结束', value: 'FINISHED' },
])

const dataList = ref([])
const loadStatus = ref('more')
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(6)
const loaded = ref(false)
const statusValue = ref('')


function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }

  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    trainingTitle: keyword.value,
    trainingStatus: statusValue.value,
  }

  TrainingService.getTrainingList(params).then(res => {
    if (res.success) {
      total.value = res.data.total
      if (pageNum.value > 1) {
        dataList.value = [...dataList.value, ...res.data.records]
      } else {
        dataList.value = res.data.records
      }
      if (total.value === dataList.value.length) {
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'more'
        pageNum.value = pageNum.value + 1
      }
    } else {
      uni.showToast({
        title: res.message || '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    }
    loaded.value = true
  }).catch(() => {
    uni.showToast({
      title: '查询数据失败',
      icon: 'none',
    })
    loadStatus.value = 'more'
  })
}
function addTrain(){
  uni.navigateTo({
    url:'./trainingAdd'
  })
}
function search() {
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}

function parseStatus(statusvalue){
   return statusList.value.find(item=>item.label==statusvalue)?.value || ''

}



function onStatusConfirm(e) {
  statusText.value = e.value[0].label
  statusValue.value = e.value[0].label
  showStatusPicker.value = false
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}

function gotoDetail(item) {
  uni.navigateTo({ url: `./trainingDetail?id=${item.trainingId}` })
}

onShow(() => {
  search()
})
</script>

<style lang="scss" scoped>
.training-list {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 1135rpx;
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: PingFang-SC-Regular, PingFang-SC;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
.container {
  .searchCont {
    height: 78rpx;
    border-radius: 39rpx;
    display: flex;
    align-items: center;
    margin: 22rpx 22rpx 0 22rpx;
    box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
    background: #fff;

    .u-search {
      flex: 2;
      margin-right: 16rpx!important;
    }
    .status-select{
        flex: 1;
    }
  }

  .scroll-style {
				/* background-color: #fff; */
				height: calc(100vh - 200rpx - env(safe-area-inset-bottom));
  .trainingList {
    margin: 30rpx 16rpx 0 16rpx;
    .list {
      background: #fff;
      border-radius: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
      margin-bottom: 24rpx;
      padding: 24rpx 20rpx;
      position: relative;
      overflow: hidden;
      .row-title {
        height: 71rpx;
        background: linear-gradient(270deg, #FFFFFF 0%, #E9FFF6 100%);
        display: flex;
        align-items: center;
        border-radius: 20rpx 20rpx 0 0;
        padding-left: 11rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 0;
        position: relative;
        .label {
          font-size: 28rpx;
          color: #222;
          font-weight: 600;
        }
        .value {
          font-size: 28rpx;
          color: #0CBE88;
          font-weight: 700;
          margin-left: 12rpx;
        }
      }
      .row {
        display: flex;
        align-items: center;
        margin-bottom: 18rpx;
        .label {
          font-size: 28rpx;
          color: #999;
          font-weight: 400;
        }
        .value {
          font-size: 30rpx;
          color: #222;
          margin-left: 12rpx;
          font-weight: 500;
        }
      }
    }
  }
}
}
.status-btn {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 26rpx;
  border-radius: 0 20rpx 0 32rpx;
  padding: 0 24rpx 0 29rpx;
  height: 48rpx;
  line-height: 48rpx;
  z-index: 2;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  background: #909399;
  color: #fff;
}

// 添加所有状态的样式
  .status-btn.not_started {
    background: #fff7e6;
    color: #ff9500;
  }
  .status-btn.in_progress {
    background: #e6f2ff;
    color: #1976FF;
  }
  .status-btn.finished {
    background: #e6f7e6;
    color: #00b050;
  }
</style>
