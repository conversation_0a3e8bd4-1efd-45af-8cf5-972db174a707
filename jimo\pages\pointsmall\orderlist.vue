<template>
  <view class="orderlist">
    <scroll-view class="secondBox" scroll-y @scrolltolower="getList">
      <!-- <u-empty
        v-if="loaded === true && orderlist.length === 0"
        mode="data"
      ></u-empty> -->
      <view class="empty-status" v-if='loaded === true && orderlist.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
	  <block v-if="orderlist.length>0">
      <view
        class="accountitem u-border-bottom"
        v-for="(item, index) in orderlist"
        :key="index"
        @click="getDetail(item)"
      >
        <view class="left">
          <view class="accounttitle">
            {{ item.commodityTitle }}
          </view>
          <view class="accounttime">
            {{ item.createDate }}
          </view>
        </view>
        <view class="accountprice">
          <text>-</text>
          <text>{{ item.orderAmount }}</text>
        </view>
      </view>
	  </block>
    <u-loadmore :status="loadStatus"  v-if="!(loadStatus === 'nomore' && orderlist.length === 0)"></u-loadmore>
    </scroll-view>
    <!-- 占位块，使上一个view的marginbottom生效 -->
    <view style="height: 1rpx"></view>
  </view>
</template>
	
	<script setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { findOrderPage } from  '../../api/pointsmall/hypermarketOrder'
import { NO_MORE_IMG } from '@/common/net/staticUrl'

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
})
const orderlist = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
let sourceType = ref('1')
onLoad((option) => {
	sourceType.value = option?.sourceType || '1'
  getList()
})

function getList() {
  if(loadStatus.value == 'nomore') return;
  if (loadStatus.value == 'more') {
    loadStatus.value = 'loading'
  }
  queryParams.role = sourceType.value
  findOrderPage(queryParams)
    .then((res) => {
      if (res.success) {
        total.value = res.data.total
        if (queryParams.pageNum > 1) {
          orderlist.value = [...orderlist.value, ...res.data.records]
        } else {
          orderlist.value = res.data.records
        }
        if (total.value === orderlist.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.pageNum = queryParams.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
}
function getDetail(data) {
  uni.navigateTo({
    url: '/jimo/pages/pointsmall/orderdetail?id=' + data.orderId,
  })
}

</script>
	
<style>
page {
  background-color: #f4f8f7;
}
</style>	
<style lang="scss" scoped>
::v-deep .u-loadmore{
  padding-bottom: 60rpx;
}
.orderlist {
  margin: 20rpx;
  background-color: white;
  border-radius: 10rpx;
  height: calc(100vh - 40rpx);
  box-sizing:border-box;
  .secondBox {
    z-index: 2;

    padding: 0 25rpx;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    
    .accountitem {
      display: flex;
      align-items: center;
      padding-top: 20rpx;
      padding-bottom: 11rpx;
      .left {
        flex: 1;

        .accounttitle {
          font-size: 28rpx;

          color: #000000;
          line-height: 40rpx;
          margin-bottom: 8rpx;
        }
        .accounttime {
          font-size: 26rpx;

          color: #999999;
          line-height: 37rpx;
        }
      }
      .accountprice {
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        min-width: 160rpx;
        text-align: right;
        color: #666666;
        line-height: 40rpx;
        background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>