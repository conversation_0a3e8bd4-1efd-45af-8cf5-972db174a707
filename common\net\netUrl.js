export const API_SMS_LOGIN_URL = '/auth/smslogin' //验证码登录
export const API_LOGOUT_URL = '/auth/logout' //登出
export const API_APP_SELECT_URL = '/auth/appSelect' //获取租户信息
export const API_USERINFO_URL = '/auth/oauth/check_token' //获取用户信息
export const API_REFRESH_URL = '/auth/oauth/token' //刷新token
export const API_SWITCHJOB_URL = '/auth/jobSelect' //切换岗位
export const API_VERFICATION_URL = '/user/staffs/getVerificationCode' //获取短信验证码
export const API_FORGOT_URL = '/user/staffs/forgottenPassword' //找回密码
export const API_SYSTEM_URL = '/cms/v1.0/newVersion/forcheck' //系统检查更新
export const API_DINGLOGIN_URL = '/auth/v1.0/dingLogin' //钉钉授权登录
export const API_DINGAUTHLOGIN_URL = '/auth/v1.0/dingAuthLogin' // 钉钉授权登录authCode
export const API_DINGJSAPI_URL = '/auth/v1.0/dingJsApi' // 钉钉鉴权
export const API_WORK_URL = '/user/configs/selectByConfigCode' //工作台
/* 微信公众号授权登录 */
export const ACCREDIT_CODE_URL = '/exchange/wechat/callback' //根据code获取access_token
export const ACCREDIT_TOKEN_URL = '/auth/wechatlogin' //根据access_token换取真正的token
export const ACCREDIT_SEND_URL = '/exchange/wechat/getCaptchaByWechatBind' //发送验证码
export const ACCREDIT_BINDING_URL = '/exchange/wechat/wechatBind' //绑定手机号
/* 通讯录 */
export const ACCREDIT_ORGANIZATION_URL = '/user/orgs/upAndDown' //根组织接口(个人)
export const ACCREDIT_CONTACTS_URL = '/user/staffOrgs' //个人通讯录接口
/* 公告 */
export const NOTIFY_TOP5PAGE_URL = '/cms/v1.0/notice/selectTop5Page' //首页公告
export const NOTIFY_Tab_URL = '/user/dict/data/list/notice_type_list' //公告Tab
export const NOTIFY_LIST_URL = '/cms/v1.0/notice/selectForPage' //公告列表
export const NOTIFY_DETAILS_URL = '/cms/v1.0/notice/selectNoticeByNoticeId' //公告详情

export const ANNOUCE_TOPPAGE_URL = '/cms/v1.0/advertising/selectTopPage' //广告
export const MENU_ALL_URL = '/user/permissions/findAllMenuVoList' //首页菜单
export const MENU_PERSON_URL = '/user/personalConfig/SysPersonalConfig' //个人主页配置
export const API_KNOWLEDGE_URL = '/cms/v1/document/lib/page' //知识库
/* 微信小程序授权登录 */
export const WECHAT_CALLBACKMP_URL = '/exchange/wechat/callbackMp' //微信小程序回调
export const WECHAT_GETPHONENUMBER_URL = '/exchange/wechat/getPhoneNumber' //获取手机号
export const WECHAT_WECHATBINDMP_URL = '/exchange/wechat/wechatBindMp' //绑定微信unionId

export const API_AGREEMENT_URL = '/user/client/page' //隐私协议 用户协议
export const API_COMMONRESOURCES_URL = '/user/common/clientConfig' //未登录请求的资源

export const API_JPUSHUSER_URL = '/exchange/v1.0/jgDevice/addTagsAndAlias' //极光推送绑定用户

export const API_FILEUPLOAD_URL = '/cms/v1/files/single' // 文件上传
export const API_FILEDOWNLOAD_URL = '/cms/v1/files/downloadGetByPath' // 文件下载
export const API_LAYOUT_DESIGN = '/user/layout-design/getEnableLayoutDesign' // 工作台动态布局
export const API_DETERMINE_URL = '/user/people/join/findByTenant' // 判断首次申请加入还是编辑信息
// 首页
export const API_PAGECONFIG_URL =
  '/village/clientConfig/findOneClientConfigMobile' // 获取首页配置信息
export const API_DEFAULTPAGECONFIG_URL =
  '/village/clientConfig/findClientConfigAnonymous' // 获取默认的首页配置信息
export const API_FINDTHREE_URL =
  '/user/villageTownIndustryOverview/overview/findThree' // 干部信息情况产业前三
// 选择乡村
export const API_REGIONINFO_URL = '/user/region/findLowerListWithOpen' // 获取行政区划信息，只返回下级有开通租户的数据
export const API_REGIONVILLAGEINFO_URL =
  '/user/tenants/findVillageTenantByTownRegionCode' // 根据镇级区划获取已开通租户的村级数据
export const API_APPLYJOIN_URL = '/user/people/join/add' // 申请加入
export const API_TEAMGROUP_URL = '/user/villageOrg/findLowerList' // 队组选择
export const API_MESSAGE_URL = '/user/villageOrg/findLowerListApp' // 村镇信息获取
export const API_CODE_URL = '/user/people/join/sendCodeToHolder' // 村镇信息获取
export const API_REGIONVILLAGEINFO_SEARCH_URL =
  '/user/region/findLowerListWithQueryNameAPP' //根据名称查询已开通租户的村

// 列表页面
export const API_GETCONTENTLIST_URL =
  '/village/columnContent/findContentListMobile' // 获取列表
export const API_GETCONTENTDETAIL_URL = '/village/columnContent/findOneMobile' // 获取详情
export const API_CONTENTlIKE_URL = '/village/contentUpvote/upvote' // 内容点赞、取消点赞
export const API_COMMENTlIST_URL = '/village/contentComment/findAllComment' //内容相关的所有评论

//三务公开
export const API_COLUMNCONTENT_URL = '/village/columnContent/findOne' // 查询栏目内容详情
export const API_READADD_URL = '/village/readCount/count/add' // 新增一条阅读记录
export const API_PAGEQUICK_URL = '/village/columnContent/findContentPageQuick' // 查询栏目内容列表

// 应用列表
export const API_APPLICATIONINFOANONYMOUS_URL =
  '/village/application/findListByIdsAnonymous' // 获取默认应用信息，未登录时使用的
export const API_APPLICATIONINFO_URL = '/village/application/findListByIds' // 获取应用信息

//我的-设置
export const API_CHANGEPWD_URL = '/user/staffs/changeBySms' // 修改密码
export const API_FINDINFO_URL = '/user/peopleAdditionalInfo/findInfoByPeopleId' //查询昵称头像信息
export const API_ADDUPDATA_URL = '/user/peopleAdditionalInfo/addAndUpdate' // 修改昵称和头像
export const API_CHANGENICKNAME_URL = '/user/peopleAdditionalInfo/add' // 修改昵称

// 流程应用
export const API_GETFLOWTAB_URL = '/village/flow/tbFlowApply/findTabList' // 获取流程列表页tabs
export const API_GETFLOWLIST_URL = '/village/flow/tbFlowApply/findPage' // 获取流程列表
export const API_GETFLOWTABLE_URL = '/village/flow/tbFlow/findOne' // 获取流程配置的表单数据
export const API_GETFLOWNODEPEOPLE_URL =
  '/village/flow/tbFlowNode/findFLowNodePeople' // 获取流程配置人员列表
export const API_APPLYFLOW_URL = '/village/flow/tbFlowApply/add' // 发起一个流程
export const API_GETFLOWINFO_URL = '/village/flow/tbFlowApply/findOne' // 获取流程详情
export const API_DELFLOW_URL = '/village/flow/tbFlowApply/delete' // 删除申请
export const API_AGREE_URL = '/village/flow/tbFlowApply/agree' // 同意申请
export const API_REFUSE_URL = '/village/flow/tbFlowApply/refuse' // 拒绝申请
export const API_HANDLE_URL = '/village/flow/tbFlowApply/handle' // 办理申请
export const API_FORWARD_URL = '/village/flow/tbFlowApply/forward' // 转交申请
export const API_REPLY_URL = '/village/flow/tbFlowApply/reply' // 回复申请
export const API_REVIEW_URL = '/village/flow/tbFlowApply/review' // 审阅申请
export const API_EVENTCOUNT_URL =
  '/village/flow/tbFlowApply/findFlowApplyNumApp' // 获取代办在办已办数量
export const API_EVENTLIST_URL = '/village/flow/tbFlowApply/findFlowApplyPage' // 获取代办在办已办列表

//村镇概况
export const API_VILLAGES_URL = '/user/villageTownBasicInfo/info/findPage' // 查询所有基础信息
export const API_INDUSTRY_URL =
  '/user/villageTownIndustryOverview/overview/findPage' //查询某个产业类型的所有数据
export const API_TYPELIST_URL = '/user/villageTownIndustryType/type/findList' // 查询所有产业类型

//通讯录
export const API_ADDRESSBOOK_URL = '/user/people/addressBook' // 查询所有基础信息
export const API_VILLAGEPEOPLE_URL = '/user/people/findOneVillagePeople' // 查询通讯录页面详情

// 获取mmy
export const API_GETMMYSTR_URL = '/user/people/mmyStr'

//信息集市
export const API_ADDMOBILE_URL = '/village/productMarket/addMobile' // 发布买卖
export const API_FINDPAGEMOBILE_URL = '/village/productMarket/findPageMobile' // 浏览商品
export const API_MYRELESASE_URL =
  '/village/productMarket/findPageMyReleaseMobile' // 我的发布
export const API_MOBILEDETAIL_URL = '/village/productMarket/findInfoMobile' // 买卖详情
export const API_UPDATEMOBILE_URL = '/village/productMarket/updateMobile' // 编辑/修改
export const API_DELETEMOBILE_URL = '/village/productMarket/deleteMobile' // 删除商品

//随心问
export const API_ASKFREELY_URL = '/village/askFreely/findListMobile' // 查询列表
export const API_ASKFREELYDETAIL_URL = '/village/askFreely/findInfoMobile' // 详情

//农具租赁
export const API_ADDLEASE_URL = '/village/agriculturalToolRental/addMobile' // 发布租赁信息
export const API_BROWSELEASE_URL =
  '/village/agriculturalToolRental/findPageMobile' // 浏览农具租赁消息
export const API_RELEASELEASE_URL =
  '/village/agriculturalToolRental/findPageMyReleaseMobile' // 我的发布
export const API_LEASEDETAIL_URL =
  '/village/agriculturalToolRental/findInfoMobile' // 租赁详情
export const API_UPDATELEASE_URL =
  '/village/agriculturalToolRental/updateMobile' // 编辑/修改
export const API_DELETELEASE_URL =
  '/village/agriculturalToolRental/deleteMobile' // 删除租赁信息

// 首页搜索
export const API_GETAPPANDCONTENT_URL =
  '/village/columnContent/findAppAndContent'

// 访问记录
export const API_ADDUSERECORD_URL = '/village/appUseRecord/addMobile'

export const API_GETAFFILIATIONTENANTS_URL =
  '/user/staffs/getAffiliationTenants' // 获取当前用户所在的所有租户

// 成员审核列表
export const API_MEMBERREVIEW_URL = '/user/people/join/findPage'
// 成员审核详情
export const API_MEMBERREVIEWDETAIL_URL = '/user/people/join/findInfo'
// 同意申请
export const API_MEMBERREVIEWPASS_URL = '/user/people/join/updateStatus'
// 获取加入的村名和地址信息
export const API_GETTENANTBYID_URL = '/user/tenants/findTenantById'

//调查问卷
export const API_ADDSURVEY_URL = '/village/survey/info/add' // 保存问卷（问卷管理--新增问卷）
export const API_FINDPAGESURVEY_URL = '/village/survey/info/findPage' // 分页查询（问卷管理）
export const API_PUBLISHSURVEY_URL = '/village/survey/info/publish' // 发布问卷（问卷管理）
export const API_FINDONESURVEY_URL = '/village/survey/info/findOne' // 查看问卷（问卷管理）
export const API_UPDATESURVEY_URL = '/village/survey/info/update' // 保存问卷（问卷管理--编辑问卷）
export const API_DELETESURVEY_URL = '/village/survey/info/delete' // 删除问卷（问卷管理）
export const API_QUESTIONSURVEY_URL = '/village/survey/info/findPageSurvey' // 分页查询（问卷调查）
export const API_ANSWERSURVEY_URL = '/village/survey/info/addSurveyAnswer' // 问卷填写（问卷调查）
export const API_SELECTEDSURVEY_URL = '/village/survey/info/findOneSelected' // 查看详情（问卷调查）

//二手市场
export const API_ADDMARKET_URL = '/village/oldProductMarket/addMobile' // 发布二手商品
export const API_FINDMARKET_URL = '/village/oldProductMarket/findPageMobile' // 浏览二手商品
export const API_MYMARKET_URL =
  '/village/oldProductMarket/findPageMyReleaseMobile' // 我的发布
export const API_MARKETDETAIL_URL = '/village/oldProductMarket/findInfoMobile' // 买卖详情
export const API_UPDATEMARKET_URL = '/village/oldProductMarket/updateMobile' // 编辑/修改
export const API_DELETEMARKET_URL = '/village/oldProductMarket/deleteMobile' // 删除二手商品

//抽奖活动
export const API_GETLOTTERYINFO_URL = '/village/lottery/findInfo' // 查询抽奖活动信息
export const API_GETLOTTERYLIMIT_URL = '/village/lottery/getLotteryLimit' // 获取当前用户当日剩余抽奖次数
export const API_GETLOTTERYRECORDS_URL =
  '/village/lottery/record/findRecordsTop100' // 获取中奖滚屏Top100
export const API_DOPLAY_URL = '/village/lottery/doPlay' // 抽奖
export const API_UPDATERECIVEINFO_URL =
  '/village/lottery/record/updateRceiveInfo' // 填写领奖信息
export const API_FINDRECORDSBYSTAFF_URL =
  '/village/lottery/record/findRecordsByStaff' // 获取当前用户中奖记录
export const API_FINDRECORDSDETAIL_URL = '/village/lottery/record/findInfo' // 获取中奖记录详情
//投票
export const API_FINDLOWER_URL = '/user/region/findLowerList' // 查询行政区划接口
export const API_SIGNADD_URL = '/village/ballot/candidate/signUp' // 报名接口
export const API_CANDIDATE_URL = '/village/ballot/candidate/findCandidateList' // 列表接口
export const API_SIGNDETAIL_URL = '/village/ballot/candidate/findInfoMobile' // 候选人详情
export const API_MINEDETAIL_URL = '/village/ballot/candidate/findMyInfoMobile' // 我的详情
export const API_SIGNUPDATA_URL = '/village/ballot/candidate/updateMobile' // 修改报名
export const API_VOTE_URL = '/village/ballot/candidate/vote' // 投票

export const API_FINDINFOMOBILE_URL = '/village/activities/findInfoMobile' // 查询活动信息
export const API_ADDSTATISTICS_URL = '/village/activitiesStatistics/add' // 新增访问量
