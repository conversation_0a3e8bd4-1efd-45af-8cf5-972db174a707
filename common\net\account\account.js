import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDUPDATA_URL,
	API_FINDINFO_URL,
} from '@/common/net/netUrl.js'

// 新增和修改昵称或头像
export function addUpdata(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDUPDATA_URL,
		method: 'POST',
		params,
	})
}

//根据peopleId查询昵称和头像
export function findInfo(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FINDINFO_URL,
		method: "GET",
		params,	
	});
}
