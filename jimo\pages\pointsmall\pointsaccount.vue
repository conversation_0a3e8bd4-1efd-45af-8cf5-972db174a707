<template>
  <view class="bg">
<!--    <u-navbar
      title="积分账单"
      :autoBack="true"
      bgColor="rgb(66,206,147,0)"
      :placeholder="true"
      titleStyle="color:#fff"
      leftIconColor="#fff"
    >
    </u-navbar> -->
    <!-- 背景 -->
    <image
      class="navBg"
      mode="widthFix"
      :src="JF_BG"
    ></image>
    <view class="firstBox" v-if="summarydata">
      <view class="flexbetween">
        <view class="item">
          <view class="label">总积分</view>
          <view class="value">{{summarydata.totalPointsEarned}}</view>
        </view>
        <view class="item">
          <view class="label">可兑换积分</view>
          <view class="value">{{summarydata.redeemablePoints}}</view>
        </view>
        <view class="item">
          <view class="label">已兑换积分</view>
          <view class="value">{{summarydata.totalPointsConsump}}</view>
        </view>
      </view>
    </view>

    <scroll-view class="secondBox" scroll-y @scrolltolower="getList">
      <!-- <u-empty
        v-if="loaded === true && accountList.length === 0"
        mode="data"
      ></u-empty> -->
      <view class="empty-status" v-if='loaded === true && accountList.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
      <block v-if="accountList.length>0">
      <view
        class="accountitem u-border-bottom"
        v-for="(item, index) in accountList"
        :key="index"
        @click="goDetail(item)"
      >
        <view class="left">
          <view class="accounttitle u-line-1">
            {{ item.pointsDescribe }}
          </view>
          <view class="accounttime">
            {{ item.postingDate }}
          </view>
        </view>
        <view class="accountprice u-line-1" :class="getFlag(item)=='-'?'reducepoint':''">
          <text>{{getFlag(item)}}</text>
          <text>{{ item.pointsValue }}</text>
        </view>
      </view>
    </block>
    <u-loadmore :status="loadStatus" v-if="!(loadStatus === 'nomore' && accountList.length === 0)"></u-loadmore>

    </scroll-view>
    <!-- 占位块，使上一个view的marginbottom生效 -->
    <view style="height: 1rpx"></view>
  </view>
</template>
  
  <script setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { pointBilling,pointBillingTop } from "../../api/pointsmall/pointsmall"
import {
  DEFAULT_AVATOR_IMAGES,
  JF_BG,
  NO_MORE_IMG
} from '@/common/net/staticUrl.js'

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
})
const accountList = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')
const summarydata = ref(null)
onLoad((option) => {
  getSummary()
  getList()
})
function getFlag(data){
  if(data.pointsType=='add'){
    return '+'
  }
  else{
    if(data.pointsDescribe=='商品退款'){
      data.pointsValue = -data.pointsValue
      return '+'
    }
    return '-'
  }
 
}
function getSummary(){
  pointBillingTop()
    .then((res) => {
      if (res.success) {
        summarydata.value = res.data
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
      
      }
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
    })
}
function getList() {
  if( loadStatus.value == 'nomore') return;
  if (loadStatus.value == 'more') {
    loadStatus.value = 'loading'
  }
  pointBilling(queryParams)
    .then((res) => {

      if (res.success) {
        total.value = res.data.total
        if (queryParams.pageNum > 1) {
          accountList.value = [...accountList.value, ...res.data.records]
        } else {
          accountList.value = res.data.records
        }
        if (total.value === accountList.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.pageNum = queryParams.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
}
function goDetail(item) {
	if (item.source == 'pointApply') {
		uni.navigateTo({
		    url: '/jimo/pages/pointsManage/pointsReportDetail?pointApplyId='+item.pointApplyId
		})
	}
}
</script>
  
  <style lang="scss" scoped>
.bg {
  background-color: white;
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 1;
}

.navBg {
  width: 100%;
  position: absolute;
  /* 使用 z-index 调整上下层级需要脱离文档流 */
  top: -140rpx;
  z-index: 1;
  /* z-index 设低一点，让页面元素可以覆盖它 */
}

.firstBox {
  top: 80rpx;
  width: 100%;

  z-index: 2;
  position: absolute;
  line-height: 1;

  box-sizing: border-box;

  .flexbetween {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 9.2vw;
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .label {
        font-size: 30rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        color: #292c39;
        line-height: 42rpx;
      }
      .value {
        letter-spacing: 0;
        font-size: 43rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 600;
        color: #666666;
        line-height: 61rpx;
        background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

.secondBox {
  z-index: 2;
  position: absolute;
  top: 240rpx;
  height: calc(100% - 310rpx);
  margin: 0 25rpx;
  box-sizing: border-box;

  width: calc(100% - 50rpx);
  .accountitem {
    display: flex;
    align-items: center;
    padding-top: 20rpx;
    padding-bottom: 11rpx;
    .left {
      flex: 1;
     
      .accounttitle {
        font-size: 28rpx;

        color: #000000;
        line-height: 40rpx;
        margin-bottom: 8rpx;
      }
      .accounttime {
        font-size: 26rpx;

        color: #999999;
        line-height: 37rpx;
      }
    }
    .accountprice {
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
	  min-width: 160rpx;
      color: #666666;
      line-height: 40rpx;
      background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .reducepoint{
      background: linear-gradient(149deg, #5cdca0 0%, #1ec58b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      color: #666666;
    }
  }
}

.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
::v-deep .u-loadmore{
  padding-bottom: 60rpx;
}
</style>