@mixin get-styles($k, $c) {
  @if $k == size or $k == weight {
    font-#{$k}: #{$c};
  } @else {
    #{$k}: #{$c};
  }
}

@each $key, $child in $uni-headings {
  /* #ifndef APP-NVUE */
  .uni-#{$key} {
    @each $k, $c in $child {
      @include get-styles($k, $c);
    }
  }
  /* #endif */
  /* #ifdef APP-NVUE */
  .container .uni-#{$key} {
    @each $k, $c in $child {
      @include get-styles($k, $c);
    }
  }
  /* #endif */
}
