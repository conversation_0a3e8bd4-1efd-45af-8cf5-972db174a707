import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDMARKET_URL,
	API_FINDMARKET_URL,
	API_MYMARKET_URL,
	API_MARKETDETAIL_URL,
	API_UPDATEMARKET_URL,
	API_DELETEMARKET_URL,
} from '@/common/net/netUrl.js'

// 发布买卖
export function mobileAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDMARKET_URL,
		method: 'POST',
		params,
	})
}

//浏览商品
export function mobileFind(params) {
	return request({
		url: API_FINDMARKET_URL,
		method: "GET",
		params,
	});
}

//我的发布
export function myRelesase(params) {
	return request({
		url: API_MYMARKET_URL,
		method: "GET",
		params,
	});
}

//买卖详情
export function mobileDetail(params) {
	return request({
		url: API_MARKETDETAIL_URL,
		method: "GET",
		params,
	});
}

//编辑/修改
export function mobileUpdate(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_UPDATEMARKET_URL,
		method: 'POST',
		params,
	})
}

//删除商品
export function mobileDelete(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_DELETEMARKET_URL,
		method: 'POST',
		params,
	})
}