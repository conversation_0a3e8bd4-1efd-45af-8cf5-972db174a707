import {
        request
} from '../../request.js'


// 问卷管理列表
export function getPage(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/getPage",
                method: "POST",
                params
        });
}

//发布问卷
export function publish(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/publish",
                method: "POST",
                params
        })
}
//获取问卷详情
export function detail(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/detail",
                method: "GET",
                params
        })
}
//获取题目详情
export function getAnswersForQuestion(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/getAnswersForQuestion",
                method: "GET",
                params
        })
}

//问卷作答提交
export function submit(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/submit",
                method: 'POST',
                params,
        })
}

//删除选项
export function deleteOption(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/deleteOption",
                method: "GET",
                params
        })
}
//删除问题
export function deleteQuestion(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/deleteQuestion",
                method: "GET",
                params
        })
}
//删除问卷
export function deleteSurvey(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/deleteSurvey",
                method: "GET",
                params
        })
}
//编辑问卷
export function update(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/update",
                method: "POST",
                params
        })
}
//新增问卷
export function create(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/create",
                method: "POST",
                params
        })
}
export function getUserPageVO(params) {
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/getUserPage",
                method: "POST",
                params
        })
}
export function userSurveyDetail(params){
        params['uniContentType'] = 'json'
        return request({
                url: "/agriculture/survey/userSurveyDetail",
                method: "GET",
                params
        })  
}