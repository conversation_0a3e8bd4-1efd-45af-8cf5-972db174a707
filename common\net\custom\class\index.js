import {request,uploadFile} from "../../request.js";

//会议列表
export function getList(params){
	params["uniContentType"] = "json" ;
    return request({
        url: "/village/meetingApi/findmeetingPage",
        method: "POST",
        params
      });
}

//会议详情

export function getClassMeeting(params){
	params["uniContentType"] = "json" ;
    return request({
        url: "/village/meetingApi/getClassMeeting",
        method: "GET",
        params
      });
}
export function updateState(params){
  params["uniContentType"] = "json" ;
  return request({
      url: "/village/meetingApi/updateState",
      method: "GET",
      params
    });
}

//组织树
export function childVillages(params){
		params["uniContentType"] = "json" ;
    return request({
        url: "/dingding/villageorg/childVillages",
        method: "GET",
        params
      });
}
//人员列表
export function getChildNodeAndPopleList(parentId, tenantId){
    return request({
        url: "/dingding/villageorg/getChildNodeAndPopleList",
        method: "GET",
        params:{
			parentId, tenantId
		}
      });
}


//修改会议状态
export function finish(params){
    return request({
        url: "/village/meetingApi/finish",
        method: "GET",
        params
      });
}

//编辑会议

export function change(params){
	params["uniContentType"] = "json" ;
    return request({
        url: "/village/meetingApi/updatemeeting",
        method: "POST",
        params
      });
}



//查询村民列表

export function lookNumber(params){
	params["uniContentType"] = "json" ;
    return request({
        url: "/dingding/meetingApi/lookNumber",
        method: "POST",
        params
      });
}

//模糊查询人员  
export function seachPeople(params){
		params["uniContentType"] = "json" ;
    return request({
        url: "/village/orgApi/lookValue",
        method: "POST",
        params
      });
}
//组织人员列表接口

export function getOrgAndPeople(params){
  params["uniContentType"] = "json" ;
  return request({
      url: "/village/orgApi/getOrgAndPeople",
      method: "POST",
      params
    });
}
//点击节点返回人员列表
export function feedBack(params){
  params["uniContentType"] = "json" ;
  return request({
      url: "/village/orgApi/feedBack",
      method: "POST",
      params
    });
}
//添加会议
export function add(params){
  params["uniContentType"] = "json" ;
  return request({
      url: "/village/meetingApi/add",
      method: "POST",
      params
    });
}
//删除会议
export function deleteMeeting(params){
  params["uniContentType"] = "json" ;
  return request({
      url: "/village/meetingApi/delete",
      method: "GET",
      params
    });
}


