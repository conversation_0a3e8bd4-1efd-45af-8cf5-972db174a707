import {
    request
} from '../request.js'

//我的认养
export function recordList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/landAdopt/record/findPage',
        method: "GET",
        params,
    });
}
// 购买 
export function buy(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/landAdopt/record/add',
        method: "POST",
        params,
    });
}

//认养列表
export function adoptList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/landAdopt/adopt/findPage',
        method: "GET",
        params,
    });
}
//根据id查询详情
export function adoptFindInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/landAdopt/adopt/findInfo',
        method: "GET",
        params,
    });
}