<template name="tabBar">
  <view class="box">
    <block v-for="(item, index) in tabList" :key="index" class="item">
      <view
        class="navigator"
        :class="currentTabIndex == index ? 'on' : ''"
        @tap="switchTab(index)"
      >
        <view class="icon">
          <text
            class="iconfont"
            :class="item.icon"
            :style="[
              currentTabIndex == index
                ? { color: tintColor }
                : { color: color },
            ]"
          ></text>
          <text v-if="item.badge" class="uni_badge">{{ item.badge }}</text>
          <text v-if="item.badgeDot" class="uni_badge uni_badge_dot"></text>
        </view>
        <view
          class="text"
          :style="[
            currentTabIndex == index ? { color: tintColor } : { color: color },
          ]"
          >{{ item.text }}</view
        >
      </view>
    </block>
  </view>
</template>
<script>
export default {
  data() {
    return {
      tabList: [
        // {
        //   pagePath: 'pages/home/<USER>',
        //   text: '首页',
        //   iconPath: 'static/images/index.png',
        //   selectedIconPath: 'static/images/index_selected.png',
        // },
        {
          pagePath: 'pages/UNI/uploading/uploading',
          text: '上传事件',
          iconPath: 'static/images/category.png',
          selectedIconPath: 'static/images/category_selected.png',
        },
        // {
        //   pagePath: 'pages/UNI/warning/warning',
        //   text: '预警事件',
        //   iconPath: 'static/images/cart.png',
        //   selectedIconPath: 'static/images/cart_selected.png',
        // },
        // {
        //   pagePath: 'pages/my/my',
        //   text: '我的',
        //   iconPath: 'static/images/user.png',
        //   selectedIconPath: 'static/images/user_selected.png',
        // },
      ],
      currentTabIndex: this.current,
    }
  },
  props: {
    current: { type: [Number, String], default: 0 },
    backgroundColor: { type: String, default: '#fbfbfb' },
    color: { type: String, default: '#999' },
    tintColor: { type: String, default: '#42b983' },
  },
  methods: {
    switchTab(index) {
      this.currentTabIndex = index
      this.$emit('click', index)
    },
  },
}
</script>

<style lang="scss">
.box {
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.item {
  flex: 1;
}
</style>
