<template>
	<view class="main-container">
		<!-- 基本信息 -->
		<view class="base-info">
			<!-- <u-avatar size="132rpx" :src="infoData.headPhoto"></u-avatar> -->
			<safe-image :src="infoData.headPhoto" width='132rpx' height='132rpx' shape="circle"></safe-image>
			<view class="info">
				<view class="grid">
					<text class='villageName'>{{infoData.villageName}}></text>
					<text class='gridName'>{{infoData.gridName}}</text>
				</view>
				<view class="name">{{infoData.staffName}}</view>
			</view>
		</view>
		<!-- 事务下发 -->
		<view class="distribute" v-if="currentRole != 'villager'">
			<view class="distribute-item">
				<view class="value"> {{infoData.completionRate}} </view>
				<view class="label"> 事务下发完成率 </view>
			</view>
			<!-- 分隔线 -->
			<view class="divider"></view>
			<view class="distribute-item">
				<view class="value"> {{infoData.accumulateNum}} </view>
				<view class="label"> 累计下发事项 </view>
			</view>
		</view>
		<!-- 事项类型 -->
		<view class="matter-type" v-if="currentRole != 'villager'">
			<view class="title"> 最经常下发的事项类型 </view>
			<view class="matter-list">
				<view class="matter-item" v-for="(item,index) in infoData.matterList" :key="index">
					{{item.missionType}}
				</view>
			</view>
		</view>
		<!-- 下户走访 -->
		<view class="task-count">
			<view class="interview">
				<view class="value"> {{infoData.interviewNum}} </view>
				<view class="label"> 累计下户走访 </view>
			</view>
			<!-- 分隔线 -->
			<view class="divider"></view>
			<view class="task">
				<view class="value"> {{infoData.taskNum}} </view>
				<view class="label"> 任务完成 </view>
			</view>
			<!-- 分隔线 -->
			<view class="divider" v-if="currentRole != 'villager'"></view>
			<view class="report" v-if="currentRole != 'villager'">
				<view class="value"> {{infoData.reportNum}} </view>
				<view class="label"> 累计上报事项 </view>
			</view>
		</view>
		<view class="colleague" v-if="infoData.colleagueList.length > 0">
			<view class="title"> 与你并肩作战的同事 </view>
			<swiper class="swiper" circular :indicator-dots="colleagueListSplit.length > 1" indicator-color="#D8D8D8"
				indicator-active-color="#0CBE88" :autoplay="false" :duration="500">
				<swiper-item v-for="(colleagueList,index) in colleagueListSplit" :key="index">
					<view class="colleague-list" :class="{acfs : colleagueList.length <= 3}">
						<view class="colleague-item" v-for="(item, index) in colleagueList" :key="index"
							:class="{mb40 : colleagueList.length > 1 && index > 3 }">
							<!-- <u-avatar size="132rpx" :src="item.photo || DEFAULT_AVATOR_IMAGES"></u-avatar> -->
							<safe-image :src="item.photo || DEFAULT_AVATOR_IMAGES" width='132rpx' height='132rpx' shape="circle"></safe-image>
							<view class="colleague-name"> {{item.name}} </view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>
		<painter :palette="painterData" style="position: absolute;z-index: -9999;" widthPixels="1000" @imgOK="imgOK" @imgErr="imgErr" />
	</view>
	<view class="submit-container paddingbottom">
		<view class="save-image" @click="toSave">
			保存到相册
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, computed } from 'vue'
	import { onShow, onLoad, onUnload } from '@dcloudio/uni-app'
	import { useUserStore } from '@/store/user.js'
	import { DEFAULT_AVATOR_IMAGES } from '@/common/net/staticUrl.js'
	import {
		getCountMission,
		getMissionType,
		getCountVisit,
		getCountTask,
		getCountReport,
		getColleague,
		getGridMemberInfo
	} from '../../api/mygrid/mygrid.js'
	import PainterJson from './PainterJson.js'
	import PainterJsonVillage from './PainterJsonVillage.js'
	import { fileUpload } from '@/common/api.js'
	import {
	    useTokenStore
	} from '@/store/token.js'
	const tokenStore = useTokenStore()
	onLoad(() => {
		// getSystemInfo()
		// 获取页面数据
		getPageData()
	})
	const userStore = useUserStore()
	//获取头像
	const headPhoto = computed(() => {
		return userStore.userInfo?.headPhoto || DEFAULT_AVATOR_IMAGES
	})
	// 村名
	const villageName = computed(() => {
		return userStore.userInfo.customParam?.tenantName || ''
	})
	// 当前角色
	const currentRole = computed(() => {
		return userStore.currentRole
	})
	// 用户名
	const staffName = computed(() => {
		return userStore.userInfo.customParam.peopleInfo?.name || ''
	})
	// 分割数组，将列表数组按照每3个一组分割成二维数组
	function subGroup(arr, len) {
		var newArr = [];
		for (var i = 0; i < arr.length; i += len) {
			newArr.push(arr.slice(i, i + len));
		}
		return newArr;
	}
	// 分割后的数组，是一个二维数组
	const colleagueListSplit = computed(() => {
		return subGroup(infoData.colleagueList, 3)
	})
	const infoData = reactive({
		// 头像
		headPhoto: headPhoto.value,
		// 村名
		villageName: villageName.value,
		// 网格名
		gridName: '',
		// 用户名
		staffName: staffName.value,
		// 事务下发完成率
		completionRate: '0',
		// 累计下发事项
		accumulateNum: '0',
		// 经常下发的事项类型
		matterList: [],
		// 累计下户走访
		interviewNum: '0',
		// 任务完成
		taskNum: '0',
		// 累计上报事项
		reportNum: '0',
		// 同事列表
		colleagueList: []
	})
	// 获取页面数据
	async function getPageData() {
		// 获取网格员信息
		getGridMemberInfo().then(res => {
			console.log('获取网格员信息---', res);
			if (res.success) {
				infoData.gridName = res.data.gridName
			}
		})
		// 获取事务下发数据
		getCountMission().then(res => {
			console.log('事务下发数据---', res);
			if (res.success && !!res.data) {
				// 事务下发完成率
				infoData.completionRate = res.data.completionRate == '0' ? '0' : res.data.completionRate + '%'
				// 累计下发事项
				infoData.accumulateNum = res.data.totalTasks
			}
		})
		// 最常下发的事务类型
		getMissionType().then(res => {
			console.log('最常下发的事务类型---', res);
			// 后端处理好了，返回的数组里最多5个元素
			if (res.success && Array.isArray(res.data)) {
				infoData.matterList = res.data;
			}
		})
		// 累计下户走访数
		getCountVisit().then(res => {
			console.log('累计下户走访数---', res);
			if (res.success && !!res.data) {
				infoData.interviewNum = res.data.totalHouseholdVisitPlan || 0
			}
		})
		// 获取任务完成数
		getCountTask().then(res => {
			console.log('获取任务完成数---', res);
			if (res.success && !!res.data) {
				infoData.taskNum = res.data || 0
			}
		})
		// 获取累计上报事项
		getCountReport().then(res => {
			console.log('获取累计上报事项---', res);
			if (res.success && !!res.data) {
				infoData.reportNum = res.data.totalWorkReport || 0
			}
		})
		// 获取同事
		getColleague().then(res => {
			console.log('获取同事---', res);
			if (res.success && !!res.data) {
				infoData.colleagueList = res.data;
			}
		})
		let p = Promise.all([getGridMemberInfo(),getCountMission(),getMissionType(),getCountVisit(),getCountTask(),getCountReport(),getColleague()])
		p.then(async arr => {
			let tempData = JSON.parse(JSON.stringify(infoData))
			tempData.currentRole = currentRole.value;
			tempData.headPhoto = await loadImage(tempData.headPhoto)
			let tmpLength = tempData.colleagueList.length > 3 ? 3 : tempData.colleagueList.length
			for(let i=0;i<tmpLength;i++) {
				let item = tempData.colleagueList[i]
				if(!item?.photo) {
					item.photo = DEFAULT_AVATOR_IMAGES
				} else {
					item.photo = await loadImage(item.photo)
					console.log('item.photo',item.photo);
				}
			}
			console.log('currentRole.value',currentRole.value);
			if (currentRole.value != 'villager') {
				painterData.value = new PainterJson().palette(tempData);
			} else {
				painterData.value = new PainterJsonVillage().palette(tempData);
			}
			
		})
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: async (data) => {
					const result = await fileUpload({
						filePath: data.tempFilePath,
						formData: {
							isAnonymous: 1
						}
					});
					console.log('fileUpload', result);
					if (result.success) {
						resolve(result.data.url);
					} else {
						resolve("");
					}
				}
			})
		})
	}
	// 生成海报
	const painterData = ref('')
	const painterImg = ref('')
	function imgOK(e) {
		console.log('imgOK', e);
		painterImg.value = e.detail.path;
	}
	function imgErr(e) {
		console.log('imgErr', e);
	}
	// 1.先判断手机相册是否授权；2.授权后再保存到相册
	function toSave() {
		console.log('toSavec触发了');
		uni.showLoading({
			title: "保存中...",
			mask: true,
		});
		uni.getSetting({
			success: (res) => {
				if (!res.authSetting["scope.writePhotosAlbum"]) {
					uni.authorize({
						scope: "scope.writePhotosAlbum",
						success: () => { // 授权成功
							uni.hideLoading();
							saveImg();
						},
						fail: (res) => {
							uni.hideLoading();
							uni.showModal({
								content: '为了方便您保存图片到相册，需要您开启相册权限',
								confirmText: "去开启",
								cancelText: '取消',
								success: (res) => {
									if (res.confirm) {
										console.log("弹窗点的确认");
										uni.openSetting({
											success(res) {
												console.log("uni.openSetting执行成功",res.authSetting);
												if (res.authSetting['scope.writePhotosAlbum']) {
													console.log("已得到相册权限");
													saveImg()
												}
											},
											fail(res) {
												console.log("uni.openSetting执行失败");
											},
											complete() {
												console.log("uni.openSetting执行完成");
											}
										})
									} else {
										console.log('取消');
										uni.hideLoading();
										uni.showToast({
											title: '无法保存图片到相册，请开启添加到相册权限',
											icon: "none",
											duration: 2000
										});
									}
								}
							})
						},
					});
				} else { // 已经授权！
					uni.hideLoading();
					saveImg();
				}
			},
		});
	}
	// 保存图片到相册
	function saveImg() {
		uni.saveImageToPhotosAlbum({
			filePath: painterImg.value, // 将临时文件 保存到相册
			success: (res) => {
				uni.showToast({
					title: '图片保存成功',
					duration: 2000
				});
			},
			fail: (error) => {
				console.log(error);
			},
		});
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
	}

	.main-container {
		position: relative;
		padding: 20rpx;
		width: 100%;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.base-info {
		display: flex;
		align-items: center;
		padding: 20rpx;
		margin-bottom: 20rpx;
		height: 200rpx;
		background: linear-gradient(180deg, #F6FFFD 0%, #FFFFFF 100%);
		border-radius: 10rpx;

		.info {
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			margin-left: 20rpx;
			height: 100%;

			.grid {

				.villageName,
				.gridName {
					font-weight: 600;
					font-size: 30rpx;
					color: #000000;
				}

				.gridName {
					font-weight: 400;
				}

				.name {
					font-weight: 400;
					font-size: 28rpx;
					color: #000000;
				}
			}
		}
	}

	.distribute {
		display: flex;
		align-items: center;
		justify-content: space-evenly;
		margin-bottom: 20rpx;
		height: 182rpx;
		background: linear-gradient(180deg, #F6FFFD 0%, #FFFFFF 100%);
		border-radius: 20rpx;

		.distribute-item {
			width: 200rpx;
			height: 100rpx;
			text-align: center;

			.value {
				margin-bottom: 20rpx;
				font-weight: 600;
				font-size: 42rpx;
				color: #0BBD88;
			}

			.label {
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
			}
		}

		.divider {
			width: 1rpx;
			height: 75rpx;
			border: 1rpx solid #EAEAEA;
		}
	}

	.divider {
		width: 1rpx;
		height: 75rpx;
		border: 1rpx solid #EAEAEA;
	}

	.title {
		margin-bottom: 20rpx;
		font-weight: 500;
		font-size: 32rpx;
		color: #333333;
	}

	.matter-type {
		margin-bottom: 20rpx;
		padding: 20rpx;
		background: #FFFFFF;
		border-radius: 10rpx;

		.matter-list {
			margin-top: 10rpx;
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.matter-item {
				margin: 0 20rpx 20rpx 0;
				padding: 0 20rpx;
				min-width: 175rpx;
				height: 66rpx;
				line-height: 66rpx;
				text-align: center;
				background: #EDFCF7;
				border-radius: 33rpx;
				font-weight: 400;
				font-size: 28rpx;
				color: #0BBD88;
			}
		}
	}

	.task-count {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		margin-bottom: 20rpx;
		height: 182rpx;
		background: linear-gradient(180deg, #F6FFFD 0%, #FFFFFF 100%);
		border-radius: 20rpx;

		.interview,
		.report {
			width: 168rpx;
			height: 100rpx;
			text-align: center;
		}

		.task {
			width: 112rpx;
			height: 100rpx;
			text-align: center;
		}

		.value {
			margin-bottom: 10rpx;
			font-weight: 600;
			font-size: 42rpx;
			color: #000000;
		}

		.label {
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
		}
	}

	.colleague {
		padding: 30rpx 20rpx 0 20rpx;
		background: #FFFFFF;
		border-radius: 8rpx;

		.swiper {
			height: 250rpx;
		}
	}

	.colleague-list {
		display: flex;
		align-items: center;
		justify-content: space-around;
		flex-wrap: wrap;
		width: 100%;
		height: 100%;

		.colleague-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 33%;

			.colleague-name {
				margin-top: 14rpx;
				font-weight: 400;
				font-size: 26rpx;
				color: #666666;
			}
		}

		&::after {
			content: "";
			flex: 1;
		}
	}

	.mb40 {
		margin-bottom: 40rpx;
	}

	.acfs {
		align-content: flex-start;
		padding-top: 20rpx;
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 3;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 125rpx;
		background: #FFFFFF;
	
		.save-image {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-size: 36rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F8F8FA;
	}
</style>