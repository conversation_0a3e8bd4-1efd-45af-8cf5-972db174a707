// 防抖
import { customRef, ref } from 'vue'
const debounceRef = (data, delay = 500) => {
  // 创建定时器
  let timer = null
  // customRef 中会返回两个函数参数。一个是：track 在获取数据时收集依赖的；一个是：trigger 在修改数据时进行通知派发更新的。
  return customRef((track, trigger) => {
    return {
      get() {
        // 收集依赖
        track()
        // 返回当前数据
        return data
      },
      set(val) {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          // 修改数据
          data = val
          // 派发更新
          trigger()
        }, delay)
      },
    }
  })
}

export default debounceRef
