<template>
	<image class="top-bg" :src="VILLAGE_TOUR_GUIDE_BG" mode=""></image>
	<view class="main-conitaner paddingbottom">
		<u-navbar bgColor="transparent" placeholder :autoBack="true" title='乡村向导' leftIconColor='#333'
			titleStyle="font-size: 36rpx;color: #333" @leftClick="leftClick">
		</u-navbar>
		<view class="search-bar">
			<view class="filter" @click="changeVillage" v-if="canChange">
				<view class="label">
					位置
				</view>
				<up-icon name="arrow-down-fill" color='#646566' size="10"></up-icon>
			</view>
			<up-search placeholder="请输入姓名搜索" bgColor='#fff' :showAction='false' v-model="keyword" height='66rpx'
				@clear="searchList" @search='searchList'></up-search>
		</view>
		<scroll-view scroll-y="true" class="guide-list" @scrolltolower="loadMore" :style="scrollViewHeight">
			<view class="guide-item" v-for="(item,index) in guideList" :key="index" @click="goDetail(item)">
				<safe-image :src='item.photo' width='200rpx' height='200rpx' radius='8rpx'
					mode='scaleToFill'></safe-image>
				<view class="guide-info">
					<view class="name u-line-1">{{item.name}}</view>
					<view class="introduce u-line-2">{{item.introduce}}</view>
					<view class="address-info">
						<image style="width:24rpx;height:36rpx" :src="VILLAGE_TOUR_LOCATION_ICON" mode="aspectFit">
						</image>
						<view class="address u-line-1">服务区域：{{item.address}}</view>
						<image style="width:60rpx;height:60rpx" :src="VILLAGE_TOUR_PHONE_ICON" shape="circle"
							mode="aspectFit" @click.stop="phoneCall(item.id)"></image>
					</view>
				</view>
			</view>
			<!-- 底部加载，三个状态：loadmore、loading、nomore -->
			<u-loadmore :status="loadStatus" v-if="guideList.length > 0"></u-loadmore>
			<view class="empty-status" v-if="showEmpty">
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
			<view class="float-btn" @click="applyGuide" v-if="showApplyBtn">
				<image :src="VILLAGE_TOUR_GUIDE_ICON" class="img"></image>
				<view class="title">
					<view class="main">成为向导</view>
					<view class="sub">点击提交申请</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import { 
		onLoad, 
		onUnload, 
		onShow,
		onShareAppMessage,
		onShareTimeline
	} from '@dcloudio/uni-app'
	import {
		NO_MORE_IMG,
		VILLAGE_TOUR_GUIDE_BG,
		VILLAGE_TOUR_GUIDE_ICON,
		VILLAGE_TOUR_LOCATION_ICON,
		VILLAGE_TOUR_PHONE_ICON,
		DEFAULT_AVATOR_IMAGES
	} from '@/common/net/staticUrl.js'
	import {
		findGuideList,
		findApplyInfo,
		findGuideDetail
	} from '../../api/villageCodeTour/villageCodeTour.js'
	import {
	    useUserStore
	} from '@/store/user.js'
	import { hasLogin, objToStr } from './villageCodeTourUnit.js'
	import kvStore from '@/common/store/uniKVStore.js'

	const isLogin = kvStore.get('hasLogin', true)
	const userStore = useUserStore()
	const canChange = ref(false)
	const scanFlag = ref(false)
	const query = ref({})
	
	onLoad((options) => {
		query.value = {
			from: 'villageCodeTour',
			regionCode: options.regionCode || undefined,
			regionLevel: options.regionLevel || undefined,
			canChange: options.canChange ? '1' : '0',
		}
		if (!hasLogin('villageGuide', query.value, isLogin)) return
		
		if (options.regionCode) {
			regionCode.value = options.regionCode
		}
		if (options.regionLevel) {
			regionLevel.value = options.regionLevel
		}
		if (options.canChange == '1') {
			canChange.value = true
		}
		
		if (!!options.from && options.from == 'villageCodeTour') {
			// 扫码跳转的会携带参数from=villageCodeTour
            scanFlag.value = true
			// 扫码跳转过来的不能切换村
			canChange.value = false
        }	
		
		
		// 获取向导列表
		getList()
		// 监听切换乡村事件，切换完乡村返回后刷新数据
		uni.$on('changeVillage', (data) => {
			regionCode.value = data.regionCode;
			regionLevel.value = data.regionLevel;
			currentPage.value = 1;
			getList()
		});
	})
	onShow((options) => {
		currentPage.value = 1;
		getList()
	})
	//发送给朋友
	onShareAppMessage((from) => {
		console.log('---------------')
	    return {
	        title: '乡村向导', // 标题
	        path: `/jimo/pages/villageCodeTour/villageGuide?${objToStr(query.value)}`, // 要分享的页面
	    }
	})
	// 分享到朋友圈
	onShareTimeline(() => {
	    return {
	        title: '乡村向导', // 标题
	        path: `/jimo/pages/villageCodeTour/villageGuide?${objToStr(query.value)}`, // 要分享的页面
	    }
	})
	function leftClick() {
	    if (scanFlag.value) {
	        uni.switchTab({
	            url: '/pages/home/<USER>',
	        })
	    } else {
	        uni.navigateBack({
	            delta: 1
	        });
	    }
	}
	// 设置scrollview高度
	const scrollViewHeight = computed(() => {
		let height = 0;
		uni.getSystemInfo({
			success: (res) => {
				// scrollview高度=屏幕可用高度-状态栏高度-导航栏高度-tabs高度-margin高度-底部安全高度
				height = res.windowHeight - res.statusBarHeight - 44 - 44 - 20 - res.safeAreaInsets
					.bottom
			}
		})
		return `height: ${height}px`
	})
	const guideList = ref([]) // 向导列表
	const showEmpty = ref(false) // 是否展示暂无数据样式
	const regionCode = ref('') // 行政区划编码
	const regionLevel = ref('6') // 行政区划级别
	const keyword = ref('')
	const total = ref(0);
	const currentPage = ref(1)
	const loadStatus = ref("");
	// 是否展示申请入口,userStore中存的是当前登录人租户的regioncode,regionCode.value是当前查看的租户的
	const showApplyBtn = computed(() => {
		return userStore?.userInfo?.customParam.regionCode == regionCode.value
	})
	// 获取向导列表
	function getList() {
		uni.showLoading({
			title: '加载中',
			mask: true
		})
		let param = {
			name: keyword.value,
			regionCode: regionCode.value,
			regionLevel: regionLevel.value,
			pageNum: currentPage.value,
			pageSize: '10'
		}
		findGuideList(param).then(res => {
			if (res.success && res.data) {
				if (res.data.records.length > 0) {
					total.value = res.data.total
					if (currentPage.value > 1) {
						guideList.value = [...guideList.value, ...res.data.records];
					} else {
						guideList.value = res.data.records;
					}
					loadStatus.value = total.value == guideList.value.length ? "nomore" : "loadmore";
				} else {
					total.value = res.data.total
					guideList.value = []
					loadStatus.value = 'nomore'
				}
				if (guideList.value.length == 0) {
					showEmpty.value = true;
				} else {
					showEmpty.value = false;
				}
			}
			isLoadMore.value = false
			uni.hideLoading();
		}).catch(err => {
			console.log(err);
			uni.hideLoading();
			isLoadMore.value = false
		})
	}
	// 搜索数据
	function searchList() {
		currentPage.value = 1;
		getList();
	}
	// 加载更多
	const isLoadMore = ref(false)
	function loadMore() {
		console.log('loadMore触发了', isLoadMore.value);
		if (isLoadMore.value) {
			return;
		} else {
			isLoadMore.value = true
		}
		
		if (loadStatus.value == 'nomore') {
			isLoadMore.value = false
			return;
		}
		currentPage.value = currentPage.value + 1;
		getList()
	}
	// 切换乡村
	function changeVillage(village) {
		// 获取当前所在市的行政区划code，前4位代表省市，后面补0
		let cityRegionCode = regionCode.value.substr(0, 4) + '00000000'
		uni.navigateTo({
			url: `/jimo/pages/villageCodeTour/chooseVillage?regionCode=${cityRegionCode}`
		})
	}
	// 拨打电话
	function phoneCall(id) {
		let param = {
			id
		}
		findGuideDetail(param).then(res => {
			if (res.success) {
				uni.makePhoneCall({
					phoneNumber: res.data.phone,
					success: () => {
						console.log('拨打电话成功')
					},
					fail: () => {
						console.error('拨打电话失败')
					},
				})
			}
		})
	}
	// 查看向导详情
	function goDetail(item) {
		uni.navigateTo({
			url: '/jimo/pages/villageCodeTour/villageGuideDetail?id=' + item.id
		})
	}
	// 申请成为向导
	function applyGuide() {
		// 先查询申请信息，申请过的话跳转详情页，没申请过跳转申请页
		findApplyInfo().then(res => {
			if (res.success) {
				// 没申请过的话接口会返回空
				if (!!res.data) {
					uni.navigateTo({
						url: '/jimo/pages/villageCodeTour/applyDetail?applyInfo=' +
							encodeURIComponent(JSON.stringify(res.data))
					})
				} else {
					uni.navigateTo({
						url: '/jimo/pages/villageCodeTour/applyGuide'
					})
				}
			}
		})
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.main-conitaner {
		// padding: 0 20rpx;
	}

	.top-bg {
		position: absolute;
		top: 0;
		left: 0;
		z-index: -1;
		width: 750rpx;
		height: 500rpx;
	}

	.search-bar {
		padding: 0 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.filter {
			margin-right: 16rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 110rpx;
			height: 66rpx;
			border-radius: 30rpx;
			background: #fff;
			font-weight: 500;
			font-size: 24rpx;
			color: #333333;

			.label {
				margin-right: 10rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 24rpx;
				color: #333333;
			}
		}
	}

	.guide-list {
		position: relative;
		margin-top: 20rpx;
		padding: 20rpx;
		box-sizing: border-box;
		background-color: #fff;
	}

	.guide-item {
		margin-bottom: 20rpx;
		display: flex;
		align-items: flex-end;
		height: 200rpx;
		// width: 100%;

		.guide-info {
			flex: 1;
			margin-left: 30rpx;

			.name {
				margin-bottom: 10rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #33374D;
			}

			.introduce {
				margin-bottom: 10rpx;
				height: 64rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}

			.address-info {
				display: flex;
				align-items: center;
				height: 80rpx;

				.address {
					flex: 1;
					margin-left: 10rpx;
				}
			}
		}
	}

	.float-btn {
		position: fixed;
		right: 0;
		bottom: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 300rpx;
		height: 100rpx;
		background: #FDA203;
		border-radius: 55rpx 0rpx 0rpx 55rpx;

		.img {
			margin-right: 10rpx;
			width: 85rpx;
			height: 60rpx;
		}

		.title {

			.main,
			.sub {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 28rpx;
				color: #FFFFFF;
				line-height: 40rpx;
			}

			.sub {
				font-size: 24rpx;
				line-height: 33rpx;
			}
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #FFF;
	}
</style>