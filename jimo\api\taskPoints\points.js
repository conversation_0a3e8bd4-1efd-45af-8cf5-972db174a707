import {
    request
} from '@/common/net/request.js';

//村积分
export function findVillagePoints(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/findVillagePoints',
        method: 'POST',
		params
    })
}
//队组积分排名
export function villaeOrgPointsRank(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/villaeOrgPointsRank',
        method: 'POST',
		 params
    })
}

//家庭积分排名
export function familyPointsRank(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/familyPointsRank',
        method: 'POST',
		 params
    })
}

//一个村的积分填报
export function findPointRecord(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/findPointRecordByTenantId',
        method: 'POST',
		 params
    })
}

//新增积分
export function addPoint(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/add',
        method: 'POST',
		params
    })
}

//查询单条积分
export function findInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/findInfo',
        method: 'GET',
        params
    })
}

//查询所有的村民
export function findPeople(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/findPeopleByTenantId',
        method: 'GET',
        params
    })
}

