import {
    request
} from '@/common/net/request.js';



  export  function updateWork(params){
    
    params['uniContentType'] = 'json'
    return request({
        url: "/village/missionPeople/update",
        method: "POST",
        params
    })
  }

  export function findWorkById(params){
    return request({
        url: "/village/missionPeople/findById",
        method: "GET",
        params
    })
  }
  export function getTasks(params){
    params['uniContentType'] = 'json'
    return request({
        url: "/village/missionPeople/findOfPage",
        method: "POST",
        params
    })
  }




