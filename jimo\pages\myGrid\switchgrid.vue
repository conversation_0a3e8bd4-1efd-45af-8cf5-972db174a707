<template>
  <view>
    <gridtree
      :checkList="checkList"
      :searchIf="false"
      v-if="gridList.length > 0"
      :options="prop"
      @sendValue="confirm"
	  :orgName="villageName"
      :isCheck="true"
      :treeNone="gridList"
      :showsubmit="true"
    ></gridtree>
    <view class="empty-status" v-else>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
  </view>
</template>
<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { reactive, ref, computed } from 'vue'
import gridtree from '../../components/wgtree/swtichgrid.vue'
import { getAllGrid } from '../../api/mygrid/mygrid'
import { useUserStore } from '@/store/user.js'
import {
  NO_MORE_IMG
} from '@/common/net/staticUrl'
// import {
// 	treeNode
// } from './data.js'
const tree = ref([])
const checkList = ref([])
const backList = ref([])
const userStore = useUserStore()
const villageName = computed(() => {
	return userStore.userInfo.customParam?.tenantName || ''
})
const gridList = ref([])
const prop = ref({
  label: 'listname',
  children: 'gridList',
  multiple: false
})

onLoad((option) => {
  //tree.value = treeNode; //树形数据赋值
  getTree()
})
async function getTree() {
  try {
	console.log('getTree执行了');
	let res = await getAllGrid()
	// console.log('getAllGrid', result);
    // let res = await gridOrg()
    if (res.success) {
	  let parentdata = [Object.assign({gridName:villageName.value,gridId:'-1'},{...res.data})]
      gridList.value = getGridArray(parentdata)
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
	  console.log(e);
    uni.showToast({
      title: '获取网格失败！',
      icon: 'none',
    })
  }
}
function getGridArray(arr) {
  arr.forEach((item) => {
    if (item.gridList && item.gridList.length > 0) {
      let list = item.gridList
      item.listname = item.gridName
      item.id = item.gridId
      item.user = false
      item.children = getGridArray([...list])
    } else {
      item.user = true
      item.listname = item.gridName
      item.id = item.gridId
    }
  })
  return arr
}
//获取选中的值
function confirm(val, back) {
  if (back) {
    backConfirm(val)
    return
  }
  backList.value = val
}

// 返回上一页传参
function backConfirm(val) {
  console.log(val)
  //uni.$emit('selectSuccess',{list:val})
  //uni.navigateBack()
  uni.$emit('selectGrid', val)
  setTimeout(() => {
    uni.navigateBack({
      delta: 1,
      complete: () => {},
    })
  }, 500)
}
</script>
<style>
page {
  background-color: #f8f8fa;
}
</style>
<style scoped lang="scss">
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>