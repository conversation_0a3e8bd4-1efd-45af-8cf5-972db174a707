import {
    request
} from '@/common/net/request.js';

// 新增访问记录
export function addUseRecord(params) {
	params['uniContentType'] = 'json'
	return request({
		url:  '/village/appUseRecord/addMobile',
		method: 'POST',
		params,
	})
}

export function burialPoint(params) {
	params['uniContentType'] = 'json'
	return request({
		url: "/village/behavior/records/add",
		method: 'POST',
		params
	})
}
// 未登录埋点
export function burialPointAnonymous(params) {
	params['uniContentType'] = 'json'
	return request({
		url: "/village/behavior/records/addNotLoding  ",
		method: 'POST',
		params
	})
}

// 金币完成任务获取金币
export function goldTask(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/gold/record/completeTask',
		method: "POST",
		params
	});
}
