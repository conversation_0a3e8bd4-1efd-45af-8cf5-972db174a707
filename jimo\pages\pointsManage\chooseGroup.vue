<template>
	<view class="main-container">
		<!-- 党组级别 -->
		<view class="orgs">
			<view v-for="(item,index) in areaList" :key="index" class="org-item" @click="changeOrgLev(index)"
				:class="index == currentLevel ? 'active' : ''">{{item}}</view>
		</view>
		<!-- 党组列表 -->
		<scroll-view scroll-y="true" class="list-box" :scroll-top="scrollTop" @scroll="scroll"
			v-if="currentList.length > 0">
			<view v-for="(item,index) in currentList" :key="index" class="content">
				<view class="list-item" @click="selectVillage(item)">
					{{ item.partyOrgName}}
				</view>
				<view class="item-icon" v-if="item.flg == 'true'" @click="viewNextOrg(item)">
					<up-icon name="arrow-right" color="#000" size="20"></up-icon>
				</view>
			</view>
		</scroll-view>
		<scroll-view scroll-y="true" class="list-box" v-else>
			<u-empty mode="search" text="暂无数据"></u-empty>
		</scroll-view>
	</view>
</template>

<script setup>
	import { findPartyOrgList } from "../../api/points/points"
	import { ref, reactive, nextTick, computed, onMounted, defineEmits } from 'vue'



	// 定义要触发的事件
	const emit = defineEmits(['selectGroup'])
	onMounted((options) => {
		let param = {
			superiorId: '0'
		}
		getList(param, 0, true);
	})
	const scrollTop = ref(0); // 滚动条距离顶部的距离，重置为0可使滚动区域返回顶部
	const oldScrollTop = ref(0); // 记录滚动位置
	const areaList = ref(["请选择"]); // tags数组，依次是省-市-区-街道-村镇
	// const defaultOrgName = ref(["区县", "乡/镇", "村"]);
	const currentLevel = ref(0); // 当前选中的哪个级别
	const currentLevelKey = ref([]);
	const allData = reactive([ // 存储各个级别的列表数据
		// county: [],
		// street: [],
		// village: []
	]);
	const currentList = ref([]); // 当前展示的列表
	const currentOrg = ref([]); // 当前已选择的党组,，依次是区-街道-村镇
	const currentPage = ref(1)
	const total = ref(0)
	const loadStatus = ref('loading')


	function scroll(e) {
		//记录scroll  位置
		oldScrollTop.value = e.detail.scrollTop
	}

	// 查看子级党组
	async function viewNextOrg(item) {
		// 如果点击的是已经选过的级别，则把子集清空
		// 例如已经选完山西省-太原市-小店区，这时再点击回选择省的列表，切换省，则后面市县区都要重置
		let childLevel = currentLevel.value + 1;
		if (!!areaList.value[childLevel]) {
			areaList.value = areaList.value.slice(0, childLevel);
			currentOrg.value = currentOrg.value.slice(0, childLevel);
		}
		// 如果不是最后一级，则更改党组级别tabs的名称
		// 例：页面初始化后，党组级别默认只展示“省份”，选择一个省份之后，“省份”会被替换为当前选中的省：
		// this.$set(this.areaList, this.currentLevel, e.regionName);
		areaList.value[currentLevel.value] = item.partyOrgName
		// 保存当前选中的党组数据
		// this.$set(this.currentOrg, this.currentLevel, e);
		currentOrg.value[currentLevel.value] = item;
		// currentLevel代表当前要选择的党组级别，当前选中了之后就该选下一个级别了，所以级别要+1
		currentLevel.value++;
		// 当前选中后，要展示的下一级的数据
		let key = currentLevel.value;
		uni.showLoading({
			title: "加载中..."
		})

		// areaList.value.push(defaultOrgName.value[currentLevel.value])
		areaList.value.push('请选择')
		let param = {
			superiorId: item.partyOrgId
		}
		getList(param, key, true)
		
		//视图会发生重新渲染
		scrollTop.value = oldScrollTop.value;
		//当视图渲染结束 重新设置为0,返回滚动区域顶部
		nextTick(() => {
			scrollTop.value = 0;
		});
	}
	// 获取区域数据
	function getList(param, key, show) {
		uni.showLoading({
			title: "加载中...",
			mask: true
		})
		findPartyOrgList(param).then(res => {
			if (res.success) {
				allData[key] = res.data.list;
				if (show) {
					currentList.value = res.data.list;
				}
				uni.hideLoading();
			} else {
				uni.showToast({
					title: "请求失败",
					duration: 2000,
					icon: "none"
				})
				uni.hideLoading();
			}
		}).catch(err => {
			uni.hideLoading();
		})
	}

	// 切换党组
	function changeOrgLev(index) {
		currentLevel.value = index;
		// let key = currentLevelKey.value[index];
		currentList.value = allData[index];
	}

	// 选中目标党组,返回上一页，切换到目标党组
	function selectVillage(item) {
		console.log('selectVillage-----', item);
		let target = {
			partyOrgId: item.partyOrgId,
			partyOrgName: item.partyOrgName,
		}
		// 触发自定义事件并传递数据
		emit('selectGroup', target)
	}
</script>

<style lang="scss" scoped>
	.main-container {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		padding: 0 24rpx;
		// height: 100%;
		height: 80vh;
		// background: #F4F5F7;
		overflow: hidden;
		background: #F4F8F7;
	}

	.title {
		margin-bottom: 20rpx;
		padding: 0 13rpx;
		font-size: 32rpx;
		color: #000000;
		font-weight: 500;
		position: relative;


		&::after {
			content: "";
			position: absolute;
			left: 0rpx;
			top: 50%;
			transform: translate(0, -50%);
			width: 6rpx;
			height: 28rpx;
			border-radius: 3rpx;
			background-image: linear-gradient(180deg, #0CBE88 0%, #1CD073 100%);
		}
	}

	.mb-0 {
		margin-bottom: 0;
	}

	.list-box {
		margin-bottom: 20rpx;
		flex: 1;
		overflow-y: auto;
		background: #F4F8F7;
		border-radius: 8rpx;
	}

	.content {
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid rgba(151, 151, 151, 0.3);

		.list-item {
			flex: 1;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			min-height: 84rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			// line-height: 84rpx;
			color: #333333;
		}
		.item-icon {
			display: flex;
			justify-content: flex-end;
			align-items: center;
			width: 80rpx;
		}
	}

	.orgs {
		position: relative;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin: 20rpx 0;
		height: 40rpx;
		width: 100%;
		white-space: nowrap;
		color: #999999;
		background: #F4F8F7;

		.org-item {
			display: inline-block;
			padding-right: 10rpx;
			width: 20%;
			font-size: 28rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-family: Source Han Sans CN, Source Han Sans CN;
		}

		.active {
			color: #0CBE88;
		}
		.current-city {
			position: absolute;
			right: 0;
			bottom: 0;
			display: flex;
			align-items: center;
			.label {
				margin-left: 4rpx;
				font-size: 28rpx;
				color: #0cbe88;
			}
		}
	}

	.main-container ::v-deep .u-search {
		flex: unset;
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F4F8F7;
	}
</style>