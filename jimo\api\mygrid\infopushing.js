import { request } from '@/common/net/request.js';



export function addText(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/broadcast/addText',
		method: 'POST',
		params
	})
}


export function addMusic(params) {
	//Content-Type: multipart/form-data
	return request({
		url: '/village/broadcast/addMusic',
		method: 'POST',
		params,
		header: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export function findInfoList(params) {
	return request({
		url: '/village/broadcast/findPage',
		method: 'GET',
		params
	})
}

export function getTerminalInfoList() {
	return request({
		url: '/village/broadcast/getTerminalInfoList',
		method: 'GET'
	})
}
export function playAudio(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/broadcast/play',
		method: 'POST',
		params
	})
}
export function findInfo(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/broadcast/findInfo',
		method: 'GET',
		params
	})
}
export function findPageByVillager(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/broadcast/findPageByVillager',
		method: 'GET',
		params
	})
}

/**
 * @description 获取当前行政区划级别
 */
export function getRegionLevel () {
	return request({
		url: '/village/broadcast/checkRegionLevel',
		method: 'GET',
	})
}

/**
 * @description 获取租户村庄-树结构
 * @param {Object} params
 */
export function findTreeVillageOrg(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/user/villageOrg/findTreeVillageOrg',
		method: 'POST',
		params
	})
}

/**
 * @description 获取租户村庄-列表结构
 */
export function findListVillageOrg() {
	return request({
		url: '/village/broadcast/getVillages',
		method: 'GET',
	})
}

export function addMusicBroadcastType1(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/village/broadcast/addMusicBroadcastType1',
		method: 'POST',
		params
	})
}
