import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDLEASE_URL,
	API_BROWSELEASE_URL,
	API_RELEASELEASE_URL,
	API_LEASEDETAIL_URL,
	API_UPDATELEASE_URL,
	API_DELETELEASE_URL,
} from '@/common/net/netUrl.js'

// 租赁发布
export function leaseAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDLEASE_URL,
		method: 'POST',
		params,
	})
}

//浏览商品
export function leaseFind(params) {
	return request({
		url: API_BROWSELEASE_URL,
		method: "GET",
		params,
	});
}

//我的发布
export function myRelesase(params) {
	return request({
		url: API_RELEASELEASE_URL,
		method: "GET",
		params,
	});
}

//租赁详情
export function leaseDetail(params) {
	return request({
		url: API_LEASEDETAIL_URL,
		method: "GET",
		params,
	});
}

//编辑/修改
export function leaseUpdate(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_UPDATELEASE_URL,
		method: 'POST',
		params,
	})
}

//删除商品
export function leaseDelete(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_DELETELEASE_URL,
		method: 'POST',
		params,
	})
}