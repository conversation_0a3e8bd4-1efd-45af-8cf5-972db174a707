import {
    request
} from '../request.js';


//添加走访计划
export function addPlan(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/addMobile',
        method: 'POST',
		params
    })
}
//添加走访
export function addvisit(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitInfo/addMobile',
        method: 'POST',
		params
    })
}
//查询走访计划
export function findPlanList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/findPlanListMobile',
        method: 'GET',
        params
    })
}
//查询走访详情
export function findInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/findInfoMobile',
        method: 'GET',
        params
    })
}


//查询人员
export function findVillageCadres(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/findVillageCadresMobile',
        method: 'GET',
        params
    })
}
//删除走访计划

export function deleteVisit(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/delete',
        method: 'POST',
		params
    })
}
//修改走访计划
export function updatePlan(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/village/householdVisitPlan/updateMobile',
        method: 'POST',
		params
    })
}