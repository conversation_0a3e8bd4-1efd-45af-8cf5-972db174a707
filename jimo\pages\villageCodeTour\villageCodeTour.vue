<template>
    <view class="container paddingbottom">
        <u-navbar title="乡村一码游" bgColor="rgba(0, 0, 0, 0)" :placeholder="true" leftIconColor='#000'
            titleStyle="font-size: 36rpx;color: #000" @leftClick="leftClick" />

        <image class="bg-image" :src="VILLAGE_TOUR_BG" />

        <view class="inner-container" :style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
            <view class="village-bar">
                <view class="village-name u-line-1" @click="toChangeVillage"> {{villageName}} </view>
                <up-icon name="arrow-down-fill" color="#000000" size="14" v-if="canChange"
                    @click="toChangeVillage"></up-icon>
              
            </view>
            <view class="inner-content">
                <view class="image">
                    <u-image width="100%" height="310rpx" radius='4rpx' :src="banner.img" mode="aspectFill"
                        @click="handleToBannerLink()"></u-image>
                </view>
                <view class="tour-list">
                    <u-image width="220rpx" height="140rpx" v-for="(item, index) in tourList" :key="index"
                        :src="item.icon" mode="aspectFill" radius="8rpx" @tap="handleClick(item.key)"></u-image>
                </view>
                <view class="play-list">
                    <view class="paly-item" v-for="item in playList" @tap="handleClick(item.key)">
                        <u-image width="92rpx" height="92rpx" :src="item.icon" mode="aspectFill"></u-image>
                        <view class="txt">{{ item.name }}</view>
                    </view>
                </view>
                <view class="overview-items" :class="{'fullScreen': dataList.length}">
                    <view class="tab-list">
                        <view class="tab active" v-for="item in tabList">{{ item.name }}</view>
                    </view>
                    <!-- 无景区景点数据，且无乡村名片视频 -->
                    <view class="empty-status" v-if="!dataList.length && !villageCardInfo.businessCardVideo">
                        <view class="empty-icon">
                            <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                                height="150rpx"></u-image>
                            <view class="no-more">
                                <span class="txt">暂无数据</span>
                            </view>
                        </view>
                    </view>
                    <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="scrollToLower"
                        :lower-threshold="10" :show-scrollbar="true" v-else>
                        <view class="video-show" v-if="villageCardInfo.businessCardVideo">
                            <video :src="villageCardInfo.businessCardVideo" controls
                                :style="{ width: '100%', height: '328rpx', marginBottom: '18rpx' }" />
                        </view>
                        <view class="overview-box" v-for="item in dataList" @click="handleDetailClick(item)">
                            <safe-image v-if="item?.coverPhoto" width="325rpx" height="225rpx"
                                :src="dealImg(item.coverPhoto)" mode="aspectFill" />
                            <view class="content">
                                {{ item?.title }}
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
            <view class="float-btn" @click="goServer" v-if="showApplyBtn">
                <image :src="VILLAGE_TOUR_FWS_ICON" class="img"></image>
                <view class="title">
                    <view class="main">乡村服务商</view>
                    <view class="sub">服务商申请入驻</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
    import {
        reactive,
        ref,
        computed
    } from 'vue'
    import {
        onLoad,
        onShow,
        onShareAppMessage,
        onShareTimeline
    } from '@dcloudio/uni-app'
    import {
        onPullDownRefresh,
        onReachBottom
    } from '@dcloudio/uni-app'
    import {
        getBanner,
        getPlacePage,
        findRegionInfo,
        findRegionInfoByTenantId,
        getVillageCardInfo2,
        getTenantId
    } from '../../api/villageCodeTour/villageCodeTour.js'

    import {
        goldTask,
        addUseRecord
    } from '../../api/api.js'
    import {
        responsibility
    } from '@/common/net/my/my.js'
    import {
        VILLAGE_TOUR_BG,
        VILLAGE_TOUR_BANNER,
        VILLAGE_TOUR_PLAY,
        VILLAGE_TOUR_TRAFFIC,
        NO_MORE_IMG,
        VILLAGE_TOUR_JDMS_ICON,
        VILLAGE_TOUR_BDTC_ICON,
        VILLAGE_TOUR_MSZN_ICON,
        VILLAGE_TOUR_XCXD_ICON,
        VILLAGE_TOUR_LYLX_ICON,
        VILLAGE_TOUR_FWS_ICON,
        VILLAGE_TOUR_MAP
    } from '@/common/net/staticUrl.js'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        useMainStore
    } from '@/store/index.js'
    import kvStore from '@/common/store/uniKVStore.js'

    /** 顶部导航高度 */
    const navbarHeight = ref('')
    const userStore = useUserStore()
    const tokenStore = useTokenStore()
    const mainStore = useMainStore()

    let hasLogin = kvStore.get('hasLogin', true) //hasLogin是登录成功后在本地存储的登录标识
    hasLogin = Boolean(Number(hasLogin)) //返回布尔值


    const banner = ref({
        img: VILLAGE_TOUR_BANNER,
        title: null
    })
    const tourList = [{
            name: '酒店民宿',
            icon: VILLAGE_TOUR_JDMS_ICON,
            key: 'hotelshomestays',
        },
        {
            name: '本地特产',
            icon: VILLAGE_TOUR_BDTC_ICON,
            key: 'localspecialties',
        },
        {
            name: '美食指南',
            icon: VILLAGE_TOUR_MSZN_ICON,
            key: 'food',
        },
    ]
    const playList = [{
            name: '地图导览',
            icon: VILLAGE_TOUR_MAP,
            key: 'mapLocation'
        }, {
            name: '门票玩乐',
            icon: VILLAGE_TOUR_PLAY,
            key: 'ticketsfun'
        },
        {
            name: '交通通行',
            icon: VILLAGE_TOUR_TRAFFIC,
            key: 'traffic'
        },
        {
            name: '旅游路线',
            icon: VILLAGE_TOUR_LYLX_ICON,
            key: 'touristroutes'
        },
        {
            name: '乡村向导',
            icon: VILLAGE_TOUR_XCXD_ICON,
            key: 'guide'
        }
    ]
    const tabList = [{
        key: 'all',
        name: '景区景点',
    }, ]
    let dataList = ref([]);
    let currentPage = ref(1);
    let loadStatus = ref(false)
    let pageSize = ref(10)
    let scanFlag = ref(false)
    const taskId = ref('')
    const villageName = ref(userStore?.userInfo?.customParam?.tenantName) // 当前查看的村庄
    const regionCode = ref(userStore?.userInfo?.customParam.regionCode) // 当前查看的村庄的code,默认是当前登录租户的值
    const regionLevel = ref('3') // 默认查看的都是市级的，所以level是3
    const canChange = ref(true) // 是否可以切换村庄，如果是从应用进来的可以切换，从首页的乡村名片进来的不能切，扫码进来的也不能切
    // 是否展示申请入口,userStore中存的是当前登录人租户的regioncode,regionCode.value是当前查看的租户的
    const showApplyBtn = computed(() => {
        return userStore?.userInfo?.customParam.regionCode == regionCode.value && userStore?.userInfo
            ?.customParam.userType != 'Visitor'
    })
    // 乡村名片数据
    const villageCardInfo = ref({})
    let villageTenantId = ref('') //获取当前tenantId
    onLoad((options) => {
        navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`

        if (options?.regionCode) {
            villageName.value = options.villageName
            regionCode.value = options.regionCode
            regionLevel.value = options.regionLevel
        }
        // 如果打开时是未登录状态，则一定是扫码进入的，所以需要跳转登录页，登录成功后再跳转回来
        if (!hasLogin) {
            mainStore.login_redirect_url =
                `/jimo/pages/villageCodeTour/villageCodeTour?from=${options.from}&regionCode=${options.regionCode}&regionLevel=${options.regionLevel}&villageName=${options.villageName}`
            setTimeout(() => {
                // #ifdef H5
                uni.reLaunch({
                    url: '/pages/login/login?loginType=password'
                })
                // #endif
                // #ifndef H5
                uni.reLaunch({
                    url: '/pages/index/index',
                })
                // #endif
            }, 100)
            return;
        }
        // source=villageCard是在乡村名片跳转过来时才会带的参数
        if (!!options.source && options.source == 'villageCard') {
            villageName.value = options.villageName
            regionCode.value = options.regionCode
            regionLevel.value = options.regionLevel
            // 乡村名片跳转过来的不能切换村
            canChange.value = false
            // 获取乡村名片数据
            getVillageCardInfoData()
            // 增加活跃度
            addLiveness()
        } else if (!!options.source && options.source == 'voteDetails') {
            // source=voteDetails是投票抽奖候选人详情跳转过来
            villageName.value = options.villageName
            regionCode.value = options.regionCode
            regionLevel.value = options.regionLevel
            // 投票抽奖候选人跳转过来的不能切换村
            canChange.value = false
            // 获取乡村名片数据
            getVillageCardInfoData()
            // 增加活跃度
            addLiveness()
        } else if (!!options.from && options.from == 'villageCodeTour') {
            // 扫码跳转的会携带参数from=villageCodeTour
            scanFlag.value = true
            // 扫码跳转过来的不能切换村
            canChange.value = false
            if (options?.regionCode) {
                villageName.value = options.villageName
                regionCode.value = options.regionCode
                regionLevel.value = options.regionLevel
            }
            // 获取乡村名片数据
            getVillageCardInfoData()
            // 增加活跃度
            addLiveness()
        } else {
            // 正常点应用进来的，默认展示当前市的数据，先获取行政区划信息
            // 如果是游客，根据租户id获取市级信息
            if (userStore?.userInfo?.customParam.userType == 'Visitor') {
                getCityInfoByTenantId()
                // 游客也不能在这切换村
                // canChange.value = false
            } else {
                // 否则根据regionCode获取市级信息
                getCityInfoByRegionCode()
            }
        }

        // 来自金币任务
        const {
            taskId: taskIdFromOptions = ''
        } = options
        taskId.value = taskIdFromOptions
        if (taskId.value) {
            getGoldTask()
        }
        // 监听切换乡村事件
        uni.$on('changeVillage', (data) => {
            regionCode.value = data.regionCode;
            regionLevel.value = data.regionLevel;
            villageName.value = data.villageName
            villageTenantId.value = data.tenantId
            // 获取乡村名片数据
            getVillageCardInfoData()
           
        });
        // 查询当前村庄租户和星级
        getTenantIdInfo()
    })
    onShow(() => {
        // 如果打开时是未登录状态，则一定是扫码进入的，所以需要跳转登录页，登录成功后再跳转回来
        if (!hasLogin) {
            mainStore.login_redirect_url =
                `/jimo/pages/villageCodeTour/villageCodeTour?from=villageCodeTour&regionCode=${regionCode.value}&regionLevel=${regionLevel.value}&&villageName=${villageName.value}`
            setTimeout(() => {
                // #ifdef H5
                uni.reLaunch({
                    url: '/pages/login/login?loginType=password'
                })
                // #endif
                // #ifndef H5
                uni.reLaunch({
                    url: '/pages/index/index'
                })
                // #endif
            }, 100)
            return;
        }
        console.log('onShow触发了----');
        setTimeout(() => {
            // dataList.value = [];
            currentPage.value = 1;
            getList()
        }, 1000)

        getBanner({
            id: '1'
        }).then(r => {
            if (r.success && r.data.img) {
                banner.value = r.data
            }
        })
    })
    // 增加活跃度
    function addLiveness(appkey = 'villageCodeTour') {
        const platform = uni.getSystemInfoSync().uniPlatform
        let param = {
            appKey: appkey,
            source: platform
        }
        addUseRecord(param).then(res => {
            console.log('访问记录增加成功');
        })
    }
    // 获取乡村名片数据
    function getVillageCardInfoData() {
        villageCardInfo.value = {}
        console.log('regionLevel.value', regionLevel.value)
        if (regionLevel.value === '6' && regionCode.value) {
            const params = {
                regionCode: regionCode.value
            }
            getVillageCardInfo2(params).then(r => {
                villageCardInfo.value = r.data || {}
            })
        }
    }

    // banner图跳转
    function handleToBannerLink() {
        const url = banner.value.title
        if (!url) return
        if (/^\/jimo\//i.test(url)) {
            uni.navigateTo({
                url   
            }).catch(() => {
                uni.showToast({
                    title: '地址错误！',
                    icon: 'none'
                })
            })
        } else if (/^https:\/\//i.test(url)) {
            uni.navigateTo({
                url: '/pages/UNI/webview/webview?url=' + encodeURIComponent(url),
            })
        } else {
            uni.showToast({
                title: '打开失败!',
                icon: 'none'
            })
        }
    }
    //发送给朋友
    onShareAppMessage((from) => {
        setTimeout(() => {
            getGoldTaskShare()
        }, 2000)
        return {
            title: '乡村一码游', // 标题
            path: `/jimo/pages/villageCodeTour/villageCodeTour?regionCode=${regionCode.value}&regionLevel=${regionLevel.value}&villageName=${villageName.value}&from=villageCodeTour`, // 要分享的页面
        }
    })
    // 分享到朋友圈
    onShareTimeline(() => {
        setTimeout(() => {
            getGoldTaskShare()
        }, 2000)
        return {
            title: '乡村一码游',
            path: `/jimo/pages/villageCodeTour/villageCodeTour?regionCode=${regionCode.value}&regionLevel=${regionLevel.value}&villageName=${villageName.value}&from=villageCodeTour`, // 要分享的页面
        }
    })

    function getList() {
        //显示加载中动画
        // uni.showNavigationBarLoading();
        getPlacePage({
            'regionCode': regionCode.value,
            'regionLevel': regionLevel.value,
            'pageSize': pageSize.value,
            'pageNum': currentPage.value,
        }).then((res) => {
            //成功获取数据后隐藏加载动画
            // uni.hideNavigationBarLoading();
            const {
                success,
                data
            } = res;
            if (success) {
                if (data.records.length > 0) {
                    //如果页数>1，需要拼接返回的数据
                    if (currentPage.value > 1) {
                        dataList.value = [...dataList.value, ...data.records];
                    } else {
                        dataList.value = data.records;
                    }
                    loadStatus.value = data.records.length >= pageSize.value ? true : false
                } else {
                    dataList.value = [];
                    currentPage.value = []
                    loadStatus.value = false
                }
                //成功获取数据后结束下拉刷新
                uni.stopPullDownRefresh();
            } else {
                dataList.value = [];
                currentPage.value = []
            }
        }).catch(() => {
            // uni.hideNavigationBarLoading();
            uni.stopPullDownRefresh();
        })
    }
    //下拉刷新
    onPullDownRefresh(() => {
        // 触发下拉刷新时执行
        console.log("下拉触发");
        dataList.value = [];
        currentPage.value = 1;
        loadStatus.value = true;
        getList();
    });
    //下拉监听方法
    onReachBottom(() => {
        console.log('-------------onReachBottom')
        console.log(loadStatus.value)
        console.log('到底了到底了...');
        if (loadStatus.value) {
            currentPage.value += 1;
            getList();
        } else {
            // uni.showToast({
            //     title: '没有更多数据了',
            //     icon: 'none'
            // });
        }
    });
    // scroll-view 下拉加载更多
    function scrollToLower() {
        console.log('scrollToLower...');
        if (loadStatus.value) {
            currentPage.value += 1;
            getList();
        } else {
            // uni.showToast({
            //     title: '没有更多数据了',
            //     icon: 'none'
            // });
        }
    }
    //页面跳转
    function handleClick(key) {
        // 乡村向导跳转到乡村向导页
        if (key == 'guide') {
            uni.navigateTo({
                // 跳转乡村向导页
                url: '/jimo/pages/villageCodeTour/villageGuide?regionCode=' + regionCode.value +
                    '&regionLevel=' + regionLevel.value + '&canChange=' + Number(canChange.value)
            })
        } else if (key == 'mapLocation') {
            uni.navigateTo({
                // 跳转地图导航
                url: '/jimo/pages/villageCodeTour/mapLocation?regionCode=' + regionCode.value +
                    '&regionLevel=' + regionLevel.value
            })
        } else {
            let params = {
                parentKey: key,
                canChange: canChange.value,
                villageName: villageName.value,
                regionCode: regionCode.value,
                regionLevel: regionLevel.value
            }
            uni.navigateTo({
                //保留当前页面，跳转到应用内的某个页面
                url: `/jimo/pages/villageCodeTour/list?params=${JSON.stringify(params)}`
            })
        }
    }
    //页面跳转
    function handleDetailClick(item) {
        console.log(item)
        uni.navigateTo({
            url: '/jimo/pages/villageCodeTour/detail?parentKey=touristroutes&contentId=' + item
                .contentId
        })
    }
    //处理图片
    function dealImg(imgs) {
        if (imgs.includes('[')) {
            const imgList = JSON.parse(imgs);
            if (imgList && imgList.length > 0) {
                return imgList[0]
            }
        } else {
            return imgs;
        }
    }
    // 切换乡村
    function toChangeVillage(village) {
        if (!canChange.value) {
            return false;
        }
        // 获取当前所在市的行政区划code，前4位代表省市，后面补0
        let cityRegionCode = regionCode.value.substr(0, 4) + '00000000'
        uni.navigateTo({
            url: `/jimo/pages/villageCodeTour/chooseVillage?regionCode=${cityRegionCode}`
        })
    }

    function leftClick() {
        console.log('leftClick触发了scanFlag', scanFlag.value);
        const pages = getCurrentPages();
        console.log('currentPages----', pages.length);
        if (scanFlag.value) {
            uni.switchTab({
                url: '/pages/home/<USER>',
            })
        } else {
            // uni.navigateBack({
            // 	delta: 1
            // });
            const pages = getCurrentPages();
            if (pages.length === 1) {
                uni.switchTab({
                    url: '/pages/home/<USER>',
                })
            } else {
                uni.navigateBack();
            }
        }
        // const pages = getCurrentPages();
        // if (pages.length === 2) {
        //     uni.navigateBack({
        //         delta: 1
        //     });
        // } else if (pages.length === 1) {
        //     uni.switchTab({
        //         url: '/pages/home/<USER>',
        //     })
        // } else {
        //     uni.navigateBack({
        //         delta: 1
        //     });
        // }
    }

    // 金币任务-乡村一码游-应用访问
    async function getGoldTask() {
        try {
            const appKey = 'villageCodeTour'
            let res = await goldTask({
                taskId: taskId.value,
                taskType: '1',
                appKey
            });
            if (res && res.success) {
                let goldMessage = `任务完成，获得${res.data.coin}金币`
                handleToast(goldMessage, '2', appKey);
            } else {
                // 家庭任务积分获取-阅读信息
                responsibility({
                    taskType: '2',
                    appKey
                })
            }
        } catch (err) {
            console.error('获取金币异常', err)
        }
    }

    // 金币任务-乡村一码游-转发分享
    async function getGoldTaskShare() {
        try {
            let res = await goldTask({
                taskId: "",
                taskType: '8',
                appKey: "villageCodeTour"
            });
            if (res && res.success) {
                let goldMessage = `任务完成，获得${res.data.coin}金币`
                handleToast(goldMessage);
            } else {
                console.log('获取金币失败', res)
                // handleToast(res.message);
            }
        } catch (err) {
            console.error('获取金币异常', err)
        }
    }

    function handleToast(message, taskType, appKey) {
        // 使用配置对象
        const toastConfig = {
            title: message,
            icon: 'none',
            duration: 1500,
            success: () => {
                if (taskType) {
                    const timer = setTimeout(() => {
                        clearTimeout(timer)
                        // 家庭任务积分获取-阅读信息
                        responsibility({
                            taskType,
                            appKey
                        })
                    }, 1500)
                }
            }
        };
        uni.showToast(toastConfig);
    }
    // 跳转服务商页面
    function goServer() {
        // 获取当前所在市的行政区划code，前4位代表省市，后面补0
        // let cityRegionCode = regionCode.value.substr(0, 4) + '00000000'
        uni.navigateTo({
            url: `/jimo/pages/villageCodeTour/serviceProvider`
        })
    }
    // 获取行政区划信息
    function getCityInfoByRegionCode() {
        let param = {
            rootCode: regionCode.value,
            regionLevel: '6'
        }
        findRegionInfo(param).then(res => {
            if (res.success) {
                console.log('findRegionInfo--------', res);
                regionCode.value = res.data[0].cityNumber
                villageName.value = res.data[0].cityName
                regionLevel.value = '3'
                getList()
            }
        })
    }
    // 根据租户id获取行政区划信息
    function getCityInfoByTenantId() {
        let param = {
            tenantId: tokenStore.tenantId
        }
        findRegionInfoByTenantId(param).then(res => {
            if (res.success) {
                console.log('getCityInfoByTenantId--------', res);
                regionCode.value = res.data[0].cityNumber
                villageName.value = res.data[0].cityName
                regionLevel.value = '3'
                getList()
            }
        })
    }
    // 匹配当前村庄星级
    function handleLevel(e) {
        switch (e) {
            case 1:
                return '实习村庄'
            case 2:
                return '一星村庄'
            case 3:
                return '二星村庄'
            case 4:
                return '三星村庄'
            case 5:
                return '四星村庄'
            case 6:
                return '五星村庄'
            default:
                return
        }
    }

    // 根据regionCode查tenantId
    function getTenantIdInfo() {
        if (regionLevel.value === '6') {
            getTenantId({
                regionCode: regionCode.value
            }).then(res => {
                if (res.success) {
                    villageTenantId.value = res.data.tenantId
                }
            })
        }
    }


</script>

<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .container {
        width: 100%;
        height: 100vh;
        background: #F4F8F7;
        position: fixed;
        top: 0;
        z-index: -2;

        .bg-image {
            width: 100%;
            height: calc(358rpx + env(safe-area-inset-top));
            position: fixed;
            top: 0;
            z-index: -1;
        }

        .inner-container {
            width: 100%;
            padding: 0 20rpx;

            .village-bar {
                height: 72rpx;
                background: rgba(255, 255, 255, 0.45);
                border-radius: 20rpx;
                padding: 16rpx 20rpx;
                margin-bottom: 20rpx;
                display: flex;
                align-items: center;

                .village-name {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #000000;
                    line-height: 40rpx;
                    margin-right: 8rpx;
                }
            }

            .inner-content {
                width: 100%;
                height: calc(100% - 92rpx);
                overflow: auto;

                .image {
                    height: 301rpx;
                    margin-bottom: 24rpx;
                }

                .tour-list {
                    width: 100%;
                    height: 137rpx;
                    margin-bottom: 17rpx;
                    display: flex;
                    justify-content: space-between;
                }

                .play-list {
                    width: 100%;
                    height: 207rpx;
                    background: #FFFFFF;
                    border-radius: 20rpx;
                    padding: 33rpx 36rpx 36rpx;
                    margin-bottom: 24rpx;
                    display: flex;
                    justify-content: space-between;

                    ::v-deep .paly-item {
                        .u-image {
                            margin: 0 auto !important;
                        }

                        .txt {
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #333333;
                            line-height: 40rpx;
                            margin-top: 6rpx;
                            max-width: 460rpx;
                        }
                    }
                }

                .overview-items {
                    width: 100%;
                    // height: calc(100% - 325rpx - 154rpx - 231rpx);
                    height: calc(100% - 710rpx);
                    padding: 21rpx 18rpx;
                    background: #FFFFFF;
                    border-radius: 20rpx;

                    &.fullScreen {
                        height: 100% !important;
                    }

                    .tab-list {
                        margin-bottom: 27rpx;

                        .tab {
                            width: 170rpx;
                            text-align: center;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            font-size: 32rpx;
                            color: #000000;
                            line-height: 45rpx;

                            &.active {
                                position: relative;
                                display: flex;
                                justify-content: center;
                                height: 60rpx;

                                &:after {
                                    content: "";
                                    position: absolute;
                                    bottom: 0;
                                    left: 53rpx;
                                    width: 41rpx;
                                    height: 6rpx;
                                    background: #0BBD88;
                                    border-radius: 3rpx;
                                }
                            }
                        }
                    }

                    .empty-status {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        margin-top: 50rpx;

                        .empty-icon {
                            display: flex;
                            flex-direction: column;
                            justify-content: center;

                            .no-more {
                                margin-top: 20rpx;
                                display: flex;
                                justify-content: center;
                                margin-top: 10rpx;

                                .txt {
                                    text-align: center;
                                    height: 37rpx;
                                    font-size: 26rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #333333;
                                    line-height: 37rpx;
                                }
                            }
                        }
                    }

                    .scroll-style {
                        width: 100%;
                        height: calc(100% - 83rpx);

                        ::v-deep.overview-box {
                            width: 328rpx;
                            height: 328rpx;
                            background: #FFFFFF;
                            border-radius: 10rpx;
                            border: 1rpx solid #E0E0E0;
                            margin-bottom: 18rpx;
                            float: left;

                            image {
                                border-radius: 10rpx 10rpx 0 0 !important;
                            }

                            &:nth-child(odd) {
                                margin-right: 18rpx;
                            }

                            .content {
                                padding-left: 15rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                font-size: 28rpx;
                                color: #333333;
                                line-height: 40rpx;
                                display: -webkit-box;
                                /* 将容器以弹性盒子形式布局 */
                                -webkit-line-clamp: 2;
                                /* 限制文本显示为两行 */
                                -webkit-box-orient: vertical;
                                /* 将弹性盒子的主轴方向设置为垂直方向 */
                                overflow: hidden;
                                /* 隐藏容器中超出部分的内容 */
                                text-overflow: ellipsis;
                                /* 超出容器范围的文本显示省略号 */

                            }
                        }
                    }
                }
            }
        }

        .float-btn {
            position: fixed;
            right: 0;
            bottom: 100rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 301rpx;
            height: 105rpx;
            background: #FDA203;
            border-radius: 55rpx 0rpx 0rpx 55rpx;

            .img {
                margin-right: 20rpx;
                width: 56rpx;
                height: 63rpx;
            }

            .title {

                .main,
                .sub {
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #FFFFFF;
                    line-height: 40rpx;
                }

                .sub {
                    font-size: 24rpx;
                    line-height: 33rpx;
                }
            }
        }
    }

    .starVillage {
        width: 135rpx;
        height: 36rpx;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #f6c45e;
        font-size: 22rpx;
        line-height: 22rpx;
        padding-left: 24rpx;
    }
</style>