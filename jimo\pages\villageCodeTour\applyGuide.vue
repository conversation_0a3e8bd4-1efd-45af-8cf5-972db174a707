<template>
	<view class="main-conitaner">
		<view class="form-container">
			<up-form labelPosition="top" labelWidth='80' :model="applyInfo" :rules="rules" ref="guideInfoRef">
				<up-form-item label="姓名" prop="name" borderBottom required>
					<up-input v-model="applyInfo.name" border="none" :readonly="true"></up-input>
				</up-form-item>
				<up-form-item label="手机号" prop="phone" borderBottom required>
					<up-input v-model="applyInfo.phone" border="none" :readonly="true"></up-input>
				</up-form-item>
				<up-form-item label="服务区域" prop="address" borderBottom required>
					<up-input v-model="applyInfo.address" border="none" :readonly="true"></up-input>
				</up-form-item>
				<up-form-item label="个人照片" :labelStyle='labelStyle' prop="photo" borderBottom required>
					<u-upload :fileList="photoList" @afterRead="uploadPhoto" @delete="deletePic" name="1" :maxCount="1"
						width='118rpx' height="118rpx" :maxSize='maxSize' @oversize='oversize'>
						<image class="file-img" :src="PLUS_IMAGES"></image>
					</u-upload>
				</up-form-item>
				<up-form-item label="服务介绍" prop="introduce" required>
					<textarea class="introduce" :value="applyInfo.introduce" placeholder="请输入个人简介" maxlength='500'
						border='none' :disableDefaultPadding='true' @input="inputHandle($event, 500)"></textarea>
				</up-form-item>
				<view style="margin-top: 20rpx;">
					<u-upload :fileList="introducePhotoList" @afterRead="uploadIntroducePhotos"
						@delete="delIntroducePic" name="1" :maxCount="5" multiple width='118rpx' height="118rpx"
						:maxSize='maxSize' @oversize='oversize'>
						<image class="file-img" :src="PLUS_IMAGES"></image>
					</u-upload>
				</view>
			</up-form>
		</view>
		<view class="submit-container paddingbottom">
			<view class="submit-btn" @click="$u.throttle(submit(), 1000)">
				提交
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, reactive } from 'vue'
	import { onLoad, onUnload, onShow } from '@dcloudio/uni-app'
	import { PLUS_IMAGES } from "@/common/net/staticUrl";
	import { useUserStore } from '@/store/user.js'
	import { useTokenStore } from '@/store/token.js'
	import {
		addGuide,
		updateGuide,
		findApplyInfo
	} from '../../api/villageCodeTour/villageCodeTour.js'
	import {
		fileUpload
	} from "@/common/api";
	import { config } from '@/config/config.js'
	const userStore = useUserStore()
	const tokenStore = useTokenStore()
	const applyInfo = ref({
		id: '',
		name: userStore?.userInfo?.customParam?.peopleInfo?.name,
		phone: userStore?.userInfo?.customParam?.cellphone,
		address: userStore?.userInfo?.customParam?.tenantName,
		photo: '',
		introduce: '',
		introducePhoto: ''
	})
	const guideInfoRef = ref()
	const labelStyle = ref({
		fontFamily: 'Source Han Sans CN, Source Han Sans CN',
		fontWeight: '600',
		fontSize: '32rpx',
		color: '#333333'
	})
	const rules = reactive({
		'photo': {
			type: 'string',
			required: true,
			message: '请上传个人照片',
			trigger: ['blur', 'change'],
		},
		'introduce': {
			type: 'string',
			required: true,
			message: '请填写个人简介',
			trigger: ['blur', 'change'],
		},
	})
	const opreate = ref('add')
	const maxSize = computed(() => {
		return 5 * 1024 * 1024
	})
	onLoad((options) => {
		// 如果传了eidt则是从详情页进编辑的，要回显申请信息
		if (options.opreate == 'edit') {
			opreate.value = 'edit'
			// 获取申请信息
			getApplyInfo()
		}
	})
	const photoList = ref([]); // 个人照片
	const introducePhotoList = ref([]) // 简介图片
	// 获取申请信息
	function getApplyInfo() {
		findApplyInfo().then(res => {
			if (res.success) {
				applyInfo.value.id = res.data.id
				applyInfo.value.photo = res.data.photo
				initPhotoList(res.data.photo)
				applyInfo.value.introduce = res.data.introduce
				applyInfo.value.introducePhoto = res.data.introducePhoto
				initIntroducePhotoList(res.data.introducePhoto)
			}
		})
	}
	// 编辑时回显个人照片
	async function initPhotoList(photo) {
		if (photo.startsWith(config.ALLOWED_IMAGE_URL)) {
			uni.downloadFile({
				url: photo,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					let photoItem = {
						url: data.tempFilePath,
						type: "image"
					}
					photoList.value.push(photoItem)
					// imgSrc.value = data.tempFilePath
				}
			})
		}
	}
	// 编辑时回显个人介绍图片
	async function initIntroducePhotoList (initIntroducePhotos) {
		let photos = initIntroducePhotos.split(',');	// initIntroducePhotos是逗号分割的字符串，先转成数组再遍历
		imagesUrl.value = initIntroducePhotos.split(',');
		for (let i = 0; i < photos.length; i++) {
			let tmpUrl = await loadImage(photos[i])
			let file = {
				url: tmpUrl,
				type: "image"
			}
			introducePhotoList.value.push(file)
		}
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}
	// 获取文件的后缀名
	const getFileName = (item) => {
		let index = item.lastIndexOf(".")
		let name = item.substring(index + 1);
		return name;
	}
	// 新增个人照片
	const uploadPhoto = async (event) => {
		console.log('uploadphoto', event);
		// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
		// 格式判断
		let fileName = getFileName(event.file.url)
		if (fileName !== 'png' && fileName !== 'jpg' && fileName !== 'bmp' && fileName !== 'jpeg' &&
			fileName !== 'PNG' && fileName !== 'JPG' && fileName !== 'BMP' && fileName !== 'JPEG') {
			uni.showToast({
				title: '只允许上传jpg/png/bmp/jpeg格式的图片',
				icon: 'none'
			})
			return
		}
		let lists = [].concat(event.file);
		let fileListLen = photoList.value.length;
		lists.map((item) => {
			photoList.value.push({
				...item,
				status: 'uploading',
				message: '上传中',
			});
		});
		for (let i = 0; i < lists.length; i++) {
			const result = await handleClickImageFm(lists[i].url, 'personal');
			let item = photoList.value[fileListLen];
			photoList.value.splice(fileListLen, 1, {
				...item,
				status: 'success',
				message: '',
				url: result,
			});
			fileListLen++;
		}
	};
	// 新增简介照片
	const imagesUrl = ref([])
	const uploadIntroducePhotos = async (event) => {
		// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
		// 格式判断
		let fileName = getFileName(event.file[0].url)
		if (fileName !== 'png' && fileName !== 'jpg' && fileName !== 'bmp' && fileName !== 'jpeg' &&
			fileName !== 'PNG' && fileName !== 'JPG' && fileName !== 'BMP' && fileName !== 'JPEG') {
			uni.showToast({
				title: '只允许上传jpg/png/bmp/jpeg格式的图片',
				icon: 'none'
			})
			return
		}
		let lists = [].concat(event.file);
		let fileListLen = introducePhotoList.value.length;
		lists.map((item) => {
			introducePhotoList.value.push({
				...item,
				status: 'uploading',
				message: '上传中',
			});
		});
		for (let i = 0; i < lists.length; i++) {
			const result = await handleClickImageFm(lists[i].url, 'introduce');
			let item = introducePhotoList.value[fileListLen];
			introducePhotoList.value.splice(fileListLen, 1, {
				...item,
				status: 'success',
				message: '',
				url: result,
			});
			fileListLen++;
		}
	};
	async function handleClickImageFm(url, type) {
		try {
			const result = await fileUpload({
				filePath: url,
				// formData: {
				// 	isAnonymous: 1
				// }
			});
			if (result.success) {
				if (type == 'personal') {
					applyInfo.value.photo = result.data.url;
				} else if (type == 'introduce') {
					imagesUrl.value.push(result.data.url)
					applyInfo.value.introducePhoto = imagesUrl.value.join();
				}
			} else {
				uni.showToast({
					title: result.message,
					icon: 'none',
					duration: 2000
				});
			}
		} catch (err) {
			console.log(err);
		}
	}
	// 删除个人照片
	const deletePic = (event) => {
		photoList.value.splice(event.index, 1);
		applyInfo.value.photo = ""
	};
	// 删除简介图片
	const delIntroducePic = (event) => {
		introducePhotoList.value.splice(event.index, 1);
		imagesUrl.value.splice(event.index, 1);
		applyInfo.value.introducePhoto = imagesUrl.value.join()
	};

	function submit() {
		guideInfoRef.value.validate().then(res => {
			if (res) {
				uni.showLoading({
					title: '处理中，请稍后...',
					mask: true
				})
				// 执行新增或者编辑
				if (opreate.value == 'add') {
					excuteAdd()
				} else {
					excuteUpdate()
				}
			}
		}).catch(errors => {
			console.log(errors);
		})
	}
	// 执行新增操作
	function excuteAdd() {
		let param = {
			photo: applyInfo.value.photo,
			introduce: applyInfo.value.introduce,
			introducePhoto: applyInfo.value.introducePhoto
		}
		addGuide(param).then(res => {
			if (res.success) {
				uni.showToast({
					title: '提交成功',
					icon: 'none',
					duration: 1500,
					success: () => {
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 1500)
					}
				})
			} else {
				uni.showToast({
					title: '提交失败',
					icon: 'none'
				})
			}
			uni.hideLoading()
		}).catch(err => {
			console.log(err);
			uni.showToast({
				title: '提交失败',
				icon: 'none'
			})
			uni.hideLoading()
		})
	}
	// 执行更新操作
	function excuteUpdate() {
		console.log('applyInfo.value.introduce------', applyInfo.value.introduce.length);
		let param = {
			id: applyInfo.value.id,
			photo: applyInfo.value.photo,
			introduce: applyInfo.value.introduce,
			introducePhoto: applyInfo.value.introducePhoto
		}
		updateGuide(param).then(res => {
			if (res.success) {
				uni.showToast({
					title: '提交成功',
					icon: 'none',
					duration: 2000,
					success: () => {
						setTimeout(() => {
							uni.navigateBack({
								delta: 2
							})
						}, 2000)
					}
				})
			} else {
				uni.showToast({
					title: '提交失败',
					icon: 'none'
				})
			}
			uni.hideLoading()
		}).catch(err => {
			console.log(err);
			uni.showToast({
				title: '提交失败',
				icon: 'none'
			})
			uni.hideLoading()
		})
	}
	// 上传的图片超出大小限制
	function oversize() {
		uni.showToast({
			title: '图片大小不能超过5M，请重新选择',
			icon: 'none'
		})
	}
	// 控制文本域内容
	let _inputTimer = ref(null)

	function inputHandle(e, maxLength) {
		clearTimeout(_inputTimer.value)
		_inputTimer.value = setTimeout(() => {
			let {
				value: inputVal
			} = e.detail
			if (inputVal.length > maxLength) {
				applyInfo.value.introduce = inputVal.slice(0, maxLength)
				// fontnum.value = maxLength
			} else {
				applyInfo.value.introduce = inputVal
				// fontnum.value = inputVal.length
			}
		}, 100)
	}
</script>

<style lang="scss" scoped>
	view {
		box-sizing: border-box;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.main-conitaner {
		padding: 20rpx;
		padding-bottom: 160rpx;

		.form-container {
			padding: 10rpx 26rpx;
			background: #fff;
			border-radius: 20rpx;
		}
	}

	.file-img {
		width: 118rpx;
		height: 118rpx;
	}

	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 3;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 160rpx;
		background-color: #fff;

		.submit-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
		}
	}

	.form-container ::v-deep .u-textarea {
		padding-left: 0;
	}

	.introduce {
		width: 100%;
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F4F8F7;
	}
</style>