<template>
  <view class="infolist">
    <scroll-view class="secondBox" scroll-y @scrolltolower="getList">
      <!-- <u-empty
        v-if="loaded === true && infolist.length === 0"
        mode="data"
      ></u-empty> -->
      <view class="empty-status" v-if='loaded === true && infolist.length === 0'>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
      <block v-if="infolist.length > 0">
        <view
          class="item"
          v-for="(item, index) in infolist"
          :key="index"
          @click="gotodetail(item)"
        >
          <view class="tag" :class="getTagClass(item)">{{
            getStatusText(item)
          }}</view>

          <view class="title u-line-1">{{item.missionName}}</view>
          <view class="desc">
            <view class="workstatus"
              ><text>任务进度：</text
              ><u-line-progress
                :percentage="item.peopleSchedule"
                height="12"
                :showText="false"
              ></u-line-progress
              ><text class="percent">{{item.peopleSchedule}}%</text></view
            >
            <view class="u-line-1">完成时限：{{item.completeTime}}</view>
            <view class="u-line-1">所属网格：{{item.gridName}}</view>
            <view class="u-line-1" style="font-size: 26rpx"
              >{{item.createDate}} {{item.createByName}}下发</view
            >
          </view>
          <!-- <view class="editbtn">编辑</view> -->
        </view>
      </block>
      <u-loadmore
        :status="loadStatus"
        v-if="!(loadStatus === 'nomore' && infolist.length === 0)"
      ></u-loadmore>
    </scroll-view>
    <!-- 占位块，使上一个view的marginbottom生效 -->
    <view style="height: 1rpx"></view>
  </view>
</template>
      
      <script setup>
import { reactive, ref,defineExpose } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getTasks } from '../../api/worktask/worktask'
import { NO_MORE_IMG } from '@/common/net/staticUrl';

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
})
const infolist = ref([])
const total = ref(0)
const loaded = ref(false)
const loadStatus = ref('loading')

onLoad((option) => {
  getList()
})
defineExpose({
  refreshData
    });
function refreshData(){
   infolist.value = []
    total.value = 0
    queryParams.pageNum = 1
    loadStatus.value = 'more'
    getList()
}
function getTagClass(item) {
  if (item.peopleStatus == 0) {
    return ''
  }
  if (item.peopleStatus == 1||item.peopleStatus == 3) {
    return 'wctag'
  }
  if (item.peopleStatus == 2) {
    return 'yqtag'
  }
}
function getStatusText(item) {
  if (item.peopleStatus == 0) {
    return '待完成'
  }
  if (item.peopleStatus == 1) {
    return '已完成'
  }
  if (item.peopleStatus == 2) {
    return '已逾期'
  }
  if (item.peopleStatus == 3) {
    return '逾期完成'
  }
}
function getList() {
  if (loadStatus.value == 'nomore') return
  if (loadStatus.value == 'more') {
    loadStatus.value = 'loading'
  }
  getTasks(queryParams)
    .then((res) => {
      if (res.success) {
        total.value = res.data.total
        if (queryParams.pageNum > 1) {
          infolist.value = [...infolist.value, ...res.data.records]
        } else {
          infolist.value = res.data.records
        }
        if (total.value === infolist.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          queryParams.pageNum = queryParams.pageNum + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    })
    .catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
}
function gotodetail(item) {
  uni.navigateTo({
    url: './detail?id=' + item.peopleId,
  })
}
</script>
      
  <style>
page {
  background-color: #f4f8f7;
}
</style>	
  <style lang="scss" scoped>
::v-deep .u-loadmore {
  padding-bottom: 60rpx;
}
.infolist {
  height: calc(100vh - 40rpx);
  box-sizing: border-box;
  .secondBox {
    z-index: 2;

    box-sizing: border-box;
    width: 100%;
    height: 100%;

    .item {
      margin: 20rpx;
      background: #ffffff;
      box-shadow: 0px 4rpx 18rpx 0px rgba(0, 0, 0, 0.12);
      border-radius: 8rpx;
      padding: 10rpx 26rpx;
      position: relative;
      .title {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        color: #000000;
        line-height: 60rpx;
      }
      .editbtn {
        position: absolute;
        bottom: 22rpx;
        right: 20rpx;
        width: 108rpx;
        height: 50rpx;
        line-height: 50rpx;
        font-size: 24rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #0bbd88;
        text-align: center;
        border-radius: 25rpx;
        border: 1rpx solid rgba(11, 189, 136, 1);
      }
      .desc {
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #666666;
        line-height: 54rpx;
        .workstatus {
          display: flex;
          align-items: center;
          .percent {
            font-size: 28rpx;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            color: #0bbd88;
            min-width: 80rpx;
            text-align: right;
          }
        }
      }
      .tag {
        position: absolute;
        top: 0;
        right: 0;
        width: 128rpx;
        height: 56rpx;
        background: rgba(255, 200, 168, 0.4);
        border-radius: 0 8rpx 0 26rpx;
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        text-align: center;
        color: #ff6d1c;
        line-height: 56rpx;
      }
      .wctag {
        background: rgba(180, 242, 183, 0.4);
        color: rgba(1, 189, 93, 1);
      }
      .yqtag {
        background-color: rgba(242, 180, 180, 0.38);
        color: #e90000;
      }
    }
  }
}
.empty-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;

  .empty-icon {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .no-more {
      margin-top: 20rpx;
      display: flex;
      justify-content: center;
      margin-top: 10rpx;

      .txt {
        text-align: center;
        height: 37rpx;
        font-size: 26rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 37rpx;
      }
    }
  }
}
</style>