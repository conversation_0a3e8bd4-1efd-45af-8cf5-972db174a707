<!-- 酒店民宿编辑 -->
<template>
	<view class="service-provider">
		<u-navbar :title="`${form.contentId ? '编辑' : '新增'}${pageTitle}`" bgColor="#FFF"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<u--form labelPosition="top" labelWidth="100%"
					:model="form" :rules="rules" ref="refForm">
					<u-form-item :label="titleLabel" prop="title" :required="true">
						<u--input v-model="form.title" border="none"
							:placeholder="`请输入${titleLabel}`" maxlength="64" />
					</u-form-item>
					<u-form-item :label="`${typeName}类型`" prop="typeId" :required="true">
						<view class="form-item" @click="typeShow = true">
							<u--input v-model="form.typeName" border="none" 
								:placeholder="`请选择${typeName}类型`" readonly />
							<u-icon name="arrow-right"></u-icon>
						</view>
						<u-picker :show="typeShow" :title="`${typeName}类型`"
							:columns="typeColumns" keyName="typeName"
							@confirm="handleTypeSelect" @cancel="typeShow = false" />
					</u-form-item>
					<u-form-item :label="`${typeName2}图片/视频`" prop="coverPhoto" :required="true">
						<view class="img-upload" :class="{'diabled-upload': fileList.findIndex(o => o.status) > -1}">
							<u-upload 
								accept="media"
								:auto-upload="false" :fileList="fileList"
								:maxCount="6" :mutiple="true"
								@afterRead="(e) => afterRead(e, 1)"
								@delete="(e) => deletePic(e, 1)" />
						</view>
					</u-form-item>
					<u-form-item label="简介" prop="introduction" :required="true">
						<u--textarea v-model="form.introduction" border="none" 
							placeholder="请输入简介" maxlength="300" />
					</u-form-item>
					<u-form-item label="位置" prop="position" :required="true" v-if="positionInput">
						<u--input v-model="form.position" border="none" 
							placeholder="请输入位置" maxlength="64" />
					</u-form-item>
					<u-form-item label="价格" prop="price" v-if="priceInput">
						<u--input v-model="form.price" border="none" 
							placeholder="请输入价格(元)" />
					</u-form-item>
					<u-form-item label="咨询电话" prop="hotline">
						<u--input v-model="form.hotline" border="none" 
							placeholder="请输入咨询电话" />
					</u-form-item>
					<u-form-item label="开放时间" prop="openingStartTime" v-if="openingTimeInput">
						<view class="flex-end">
							<view @click="show1 = true">
								<u--input v-model="form.openingStartTime" border="none"
									placeholder="开始时间" readonly />
							</view>
							<view class="right-img">
								<image :src="LABOR_SWAP_RIGHT" />
							</view>
							<view @click="show2 = true">
								<u--input v-model="form.openingEndTime" border="none"
									placeholder="结束时间" readonly />
							</view>
						</view>
						<u-datetime-picker title="开放时间" :show="show1"
							v-model="form.openingStartTime" mode="time"
							@cancel="show1 = false" @confirm="show1 = false"/>
						<u-datetime-picker title="结束时间" :show="show2"
							v-model="form.openingEndTime" mode="time"
							@cancel="show2 = false" @confirm="show2 = false"/>
					</u-form-item>
					<u-form-item label="详细内容" prop="content">
						<u--textarea v-model="form.content" border="none" 
							placeholder="请输入详细内容" maxlength="300" />
						<view class="img-upload" :class="{'diabled-upload': infoFile.findIndex(o => o.status) > -1}">
							<u-upload
								accept="media"
								:auto-upload="false" :fileList="infoFile"
								:maxCount="5" :mutiple="true"
								@afterRead="(e) => afterRead(e, 2)"
								@delete="(e) => deletePic(e, 2)" />
						</view>
					</u-form-item>
				</u--form>
			</view>
		</view>
		
		<view class="submit" @click="handleSubmit()">
			<view class="btn">提交</view>
		</view>
		
		<u-toast ref="refToast"></u-toast>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { LABOR_SWAP_RIGHT } from '@/common/net/staticUrl.js'
import { fileUpload } from '@/common/api'
import { SreviceProvider, findList } from '../../api/villageCodeTour/villageCodeTour.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 类型 */
const parentKey = ref('')
/** 表单组件 */
const refForm = ref()
/** 表单 */
const form = ref({
	title: ''
})
/** 提示语显示 */
const refToast = ref(null)
// 正则表达式，匹配表情符号
const regexEmo = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]?/g
const regexEmoji = /[^\u4E00-\u9FA5|\d|\a-zA-Z|\r\n\s,.?!，。？！…—&$=()-+/*{}[\]]/g
/** 表单验证 */
const rules = reactive({
	title: {
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			if (!value) {
				callback(new Error(`请输入${titleLabel.value}`))
				return
			}
			if (regexEmoji.test(value) || regexEmoji.test(value)) {
				callback(new Error('禁止输入表情或特殊符号'))
				return
			}
			callback()
		}, trigger: ['change', 'blur'],
	},
	typeId: { 
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			if (!value) {
				callback(new Error(`请选择${typeName.value}类型`))
				return
			}
			callback()
		}, trigger: ['change', 'blur']
	},
	coverPhoto: { 
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			if (!fileList.value.length) {
				callback(new Error(`请上传${typeName2.value}图片`))
				return
			}
			console.log('--------')
			if (fileList.value.findIndex(o => o.type === 'image') === -1) {
				callback(new Error(`请上传${typeName2.value}图片`))
				refToast.value.show({ message: '请至少上传一张图片', type: 'error', icon: false })
				return
			}
			callback()
		}, trigger: ['change', 'blur']
	},
	introduction: { 
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			if (!value) {
				callback(new Error(`请输入简介`))
				return
			}
			if (regexEmoji.test(value) || regexEmoji.test(value)) {
				callback(new Error('禁止输入表情或特殊符号'))
				return
			}
			callback()
		}, trigger: ['change', 'blur']
	},
	position: { 
		type: 'string', required: true, 
		asyncValidator: (rule, value, callback) => {
			if (!value) {
				callback(new Error(`请输入位置`))
				return
			}
			if (regexEmoji.test(value) || regexEmoji.test(value)) {
				callback(new Error('禁止输入表情或特殊符号'))
				return
			}
			callback()
		}, trigger: ['change', 'blur']
	},
	price: { 
		validator: (rule, value, callback) => {
			if (value === null || value === undefined) return true
			return value && /^\d{1,5}(\.\d{1,2})?$/.test(value)
		},
		message: '价格不超过五位数保留两位小数', trigger: ['change', 'blur']
	},
	hotline: {
		validator: (rule, value, callback) => {
			const reg = /^1[3456789]\d{9}$/
			const reg2 = /^\(?\d{3,4}\)?[- ]?\d{7,8}$/
			if (value === null || value === undefined) return true
			return value && (reg.test(value) || reg2.test(value))
		},
		message: '咨询电话格式错误', trigger: ['change', 'blur']
	},
	openingStartTime: {
		validator: (rule, value, callback) => {
			if (!form.value.openingStartTime && !form.value.openingEndTime) return true
			if (!form.value.openingStartTime && form.value.openingEndTime) return false
			if (form.value.openingStartTime && !form.value.openingEndTime) return false
			return form.value.openingStartTime < form.value.openingEndTime
		},
		message: '开放时间错误', trigger: ['change', 'blur']
	},
	openingEndTime: { required: false },
	content: { 
		required: false, asyncValidator: (rule, value, callback) => {
			if (value && (regexEmoji.test(value) || regexEmoji.test(value))) {
				callback(new Error('禁止输入表情或特殊符号'))
				return
			}
			callback()
		}, trigger: ['change', 'blur']
	},
})
/** 类型数据 */
const typeData = ref([])
/** 类型显示 */
const typeShow = ref(false)
/** 开始时间选择显示 */
const show1 = ref(false)
/** 结束时间选择显示 */
const show2 = ref(false)
/** 服务类型列表 */
const typeColumns = ref([])

const formTypeName = computed(() => {
	return typeColumns.value[0]?.find(o => o.typeId === form.value.typeId).typeName
})

/** 页面标题 */
const pageTitle = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '酒店民宿'
	if (parentKey.value === 'localspecialties') return '产品'
	if (parentKey.value === 'food') return '美食'
})
/** 服务类名称标题显示 */
const titleLabel = computed(() => {
	return [
		'localspecialties'
	].findIndex(o => o === parentKey.value) > -1 ? '标题' : '名称'
})
/** 当前服务类名称 */
const typeName = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '房源'
	if (parentKey.value === 'localspecialties') return '特产'
	if (parentKey.value === 'food') return '美食'
	return ''
})
/** 当前服务类名称 */
const typeName2 = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '酒店'
	if (parentKey.value === 'localspecialties') return '特产'
	if (parentKey.value === 'food') return '美食'
	return ''
})
/** 是否展示位置输入框 */
const positionInput = computed(() => {
	return [
		'hotelshomestays', 'food'
	].findIndex(o => o === parentKey.value) > -1
})
/** 是否展示价格输入框 */
const priceInput = computed(() => {
	return [
		'hotelshomestays', 'food'
	].findIndex(o => o === parentKey.value) > -1
})
/** 是否展示开放时间输入框 */
const openingTimeInput = computed(() => {
	return [
		'food'
	].findIndex(o => o === parentKey.value) > -1
})

onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	parentKey.value = options.parentKey
	getTypesData()
	if (options.id) {
		form.value.contentId = options.id
		getInfoData()
	}
})

onMounted(() => {
	refForm.value.setRules(rules)
})
/**
 * @description 获取服务类详情
 */
const getInfoData = () => {
	const params = {
		contentId: form.value.contentId
	}
	SreviceProvider.getSreviceClassInfo(params).then(r => {
		if (r.success) {
			form.value = r.data
			// 获取类型名称
			form.value.typeName = formTypeName
			// 图片视频
			fileList.value = JSON.parse(r.data.coverPhoto).map(str => {
				const ex = str.split('.').pop().toLowerCase()
				if (ex === 'jpg' || ex === 'jpeg' || ex === 'png' || ex === 'gif') {
					return { url: str, type: 'image' }
				} else {
					return { url: str, type: 'video' }
				}
			})
			// 内容富文本处理
			extractImageUrls(form.value.content)
			form.value.content = form.value.content.replace(/<[^>]*>?/gm, '').trim()
		}
	})
}
/**
 * @description 获取富文本图片和视频
 * @param {Object} richTextContent
 */
function extractImageUrls(richTextContent) {
	const imageRegex = /<img[^>]*src="([^"]+)"[^>]*>/g;
	const videoRegex = /<video[^>]*src="([^"]+)"[^>]*>/g;
	let match, match2;
	  
	while ((match = imageRegex.exec(richTextContent)) !== null) {
		infoFile.value.push({ url: match[1], type: 'image' })
	}
	  
	while ((match2 = videoRegex.exec(richTextContent)) !== null) {
		infoFile.value.push({ url: match2[1], type: 'video' })
	}
}
/**
 * @description 获取分类列表
 */
const getTypesData = () => {
	const params = {
		parentKey: parentKey.value
	}
	findList(params).then(r => {
		if (r.success) {
			typeColumns.value = [r.data]
		}
	})
}
/**
 * @description 服务类型选择确认
 */
const handleTypeSelect = (val) => {
	form.value.typeName = val.value[0].typeName
	form.value.typeId = val.value[0].typeId
	typeShow.value = false
	refForm.value.validateField('typeId')
}
/** 酒店图片 */
const fileList = ref([])
/** 详细内容 */
const infoFile = ref([])
/**
 * @description 图片上传
 */
const afterRead = async (event, index) => {
	const { url, size, type } = event.file
	const ex = url.split('.').pop().toLowerCase()
	if (ex === 'jpg' || ex === 'jpeg' || ex === 'png' || ex === 'gif') {
		if (size > 5 * 1024 * 1024) {
			uni.showToast({ title: '图片限制上传5M', icon: 'none' })
			return
		}
	} else if (ex === 'mov' || ex === 'mp4' || ex === 'avi' || ex === 'flv') {
		if (size > 500 * 1024 * 1024) {
			uni.showToast({ title: '视频限制上传500M', icon: 'none' })
			return
		}
	} else {
		uni.showToast({ title: '文件格式错误', icon: 'none' })
		return
	}
	loadingUpload(index, url, type, true)
	const params = {
		filePath: url,
		formData: { isAnonymous: 1 }	
	}
	fileUpload(params).then(r => {
		if (r.success) {
			loadingUpload(index, r.data.url, type, false)
		}
	})
}
/**
 * @description 上传
 */
const loadingUpload = (index, url, type, loading) => {
	if (index === 1) {
		if (loading) {
			fileList.value.push({ url, type, status: 'uploading', message: '上传中' })
		} else {
			const i = fileList.value.findIndex(o => o.status)
			fileList.value[i] = { url, type }
		}
		// form.value.coverPhoto = fileList.value.map(o => o.url).join(',')
		form.value.coverPhoto = JSON.stringify(fileList.value.map(o => o.url))
		// refForm.value.validateField('coverPhoto')
	}
	if (index === 2) {
		if (loading) {
			infoFile.value.push({ url, type, status: 'uploading', message: '上传中' })
		} else {
			const i = infoFile.value.findIndex(o => o.status)
			infoFile.value[i] = { url, type }
		}
	}
}
/**
 * @description 图片删除
 */
const deletePic = (event, index) => {
	if (index === 1) {
		fileList.value.splice(event.index, 1)
		refForm.value.validateField('coverPhoto')
	}
	if (index === 2) {
		infoFile.value.splice(event.index, 1)
	}
}
/**
 * @description 提交
 */
const handleSubmit = () => {
	if (fileList.value.findIndex(o => o.status) > -1 || infoFile.value.findIndex(o => o.status) > -1) {
		uni.showToast({ title: '图片上传中...', icon: 'none' })
		return
	}
	refForm.value.validate().then(res => {
		if (res) {
			uni.showLoading({ title: '提交中...', mask: true })
			const params = {
				contentId: form.value.contentId
			}
			Object.keys(rules).forEach(key => {
				params[key] = form.value[key] || undefined
			})
			
			params.content = params.content ? `<p>${params.content}</p>` : ''
			infoFile.value.forEach(item => {
				if (item.type === 'image') {
					params.content += `<img src="${item.url}">`
				} else if (item.type === 'video') {
					params.content += `<video class="ql-video" controls="controls" type="video/mp4" src="${item.url}" width="200px" height="150px"></video>`
				}
			})
			
			const api = params.contentId ? 'updateSreviceClass' : 'addSreviceClass'
			
			SreviceProvider[api](params).then(r => {
				uni.hideLoading()
				if (r.success) {
					uni.showToast({
						type: 'success', title: '保存成功', 
						icon: 'none', duration: 1000,
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				} else {
					uni.showToast({ type: 'error', title: r.message, icon: 'none' })
				}
			}).finally(() => {
				
			})
		}
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.service-provider {
	width: 100%;
	height: 100vh;
	background: #F0F7F7;
	
	.container {
		padding: 24rpx 20rpx;
		
		::v-deep.u-form {
			background-color: #FFF;
			padding: 23rpx 26rpx;
			border-radius: 20rpx;
			
			.form-item-view {
				padding: 23rpx 19rpx 23rpx 33rpx;
				background-color: #fff;
				border-radius: 20rpx;
				margin-bottom: 20rpx;
			}
			
			.u-form-item {
				border-bottom: 1rpx solid #E0E0E0;
				
				.u-form-item__body__left__content__label, .label-solt {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 600;
					font-size: 32rpx;
					color: rgba(51, 51, 51, 1);
					line-height: 45rpx;
				}
				
				&:last-child {
					border-bottom: none;
				}
			}
			
			.form-item {
				width: 100%;
				display: flex;
				justify-content: space-between;
			}
		
			.img-upload {
				.u-upload__deletable {
					width: 40rpx !important;
					height: 40rpx !important;
					
					.u-upload__deletable__icon {
						top: 6rpx !important;
						right: 6rpx !important;
					}
				}
				
				.u-upload__wrap >view:nth-child(4) {
					margin-right: 0 !important;
				}
				
				&.diabled-upload .u-upload__button {
					display: none !important;
				}
			}
		
			.u-textarea {
				padding: 0 !important;
			}
			
			.right-img {
				padding-right: 32rpx;
				
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}
}

.submit {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #FFFFFF;
		line-height: 50rpx;
	}
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

.flex-end {
	display: flex;
	align-items: flex-end;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>