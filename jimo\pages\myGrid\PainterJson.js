export default class PainterJson {
	palette(infoData) {
		let jsonData = ({
			"width": "375px",
			"height": "630px",
			"background": "#f8f8f8",
			"views": [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "10px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "image",
					"url": infoData.headPhoto,
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "22px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.villageName + '>' + infoData.gridName,
					"css": {
						"color": "#000000",
						"background": "rgba(0,0,0,0)",
						"width": "232px",
						"height": "16.95px",
						"top": "35px",
						"left": "120px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "15px",
						"fontWeight": "bold",
						"maxLines": "2",
						"lineHeight": "16.650000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "text",
					"text": infoData.staffName,
					"css": {
						"color": "#000000",
						"background": "rgba(0,0,0,0)",
						"width": "200px",
						"height": "15.819999999999999px",
						"top": "65px",
						"left": "120px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "2",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "340px",
						"left": "9px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": infoData.interviewNum,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "23.729999999999997px",
						"top": "367px",
						"left": "39px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "21px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "23.310000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.taskNum,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "23.729999999999997px",
						"top": "367px",
						"left": "141px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "21px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "23.310000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.reportNum,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "23.729999999999997px",
						"top": "367px",
						"left": "234px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "21px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "23.310000000000002px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "rect",
					"css": {
						"background": "",
						"width": "1px",
						"height": "40px",
						"top": "372px",
						"left": "140px",
						"rotate": "0",
						"borderRadius": "4.5px",
						"borderWidth": "1px",
						"borderColor": "#EAEAEA",
						"shadow": "",
						"color": ""
					}
				},
				{
					"type": "rect",
					"css": {
						"background": "",
						"width": "1px",
						"height": "40px",
						"top": "371px",
						"left": "228px",
						"rotate": "0",
						"borderRadius": "4.5px",
						"borderWidth": "1px",
						"borderColor": "#EAEAEA",
						"shadow": "",
						"color": ""
					}
				},
				{
					"type": "text",
					"text": "累计下户走访",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "90px",
						"height": "15.819999999999999px",
						"top": "398px",
						"left": "35px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": "任务完成",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "84px",
						"height": "15.819999999999999px",
						"top": "400px",
						"left": "138px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": "累计上报事项",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "90px",
						"height": "15.819999999999999px",
						"top": "400px",
						"left": "236px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		});
		// let obj = JSON.parse(jsonData)
		let publishInfo = [{
				"type": "rect",
				"css": {
					"background": "#F6FFFD",
					"width": "355px",
					"height": "100px",
					"top": "121px",
					"left": "10px",
					"rotate": "0",
					"borderRadius": "10px",
					"shadow": "",
					"color": "#F6FFFD"
				}
			},
			{
				"type": "text",
				"text": infoData.completionRate,
				"css": {
					"color": "#0BBD88",
					"background": "rgba(0,0,0,0)",
					"width": "100px",
					"height": "23.729999999999997px",
					"top": "150px",
					"left": "44px",
					"rotate": "0",
					"borderRadius": "",
					"borderWidth": "",
					"borderColor": "#000000",
					"shadow": "",
					"padding": "0px",
					"fontSize": "21px",
					"fontWeight": "bold",
					"maxLines": "1",
					"lineHeight": "23.310000000000002px",
					// "textStyle": "fill",
					"textDecoration": "none",
					"fontFamily": "",
					"textAlign": "center"
				}
			},
			{
				"type": "text",
				"text": "事务下发完成率",
				"css": {
					"color": "#333333",
					"background": "rgba(0,0,0,0)",
					"width": "100px",
					"height": "15.819999999999999px",
					"top": "180px",
					"left": "44px",
					"rotate": "0",
					"borderRadius": "",
					"borderWidth": "",
					"borderColor": "#000000",
					"shadow": "",
					"padding": "0px",
					"fontSize": "14px",
					"fontWeight": "normal",
					"maxLines": "1",
					"lineHeight": "15.540000000000001px",
					// "textStyle": "fill",
					"textDecoration": "none",
					"fontFamily": "",
					"textAlign": "left"
				}
			},
			{
				"type": "rect",
				"css": {
					"background": "",
					"width": "1px",
					"height": "40px",
					"top": "153px",
					"left": "183px",
					"rotate": "0",
					"borderRadius": "4.5px",
					"borderWidth": "1px",
					"borderColor": "#EAEAEA",
					"shadow": "",
					"color": ""
				}
			},
			{
				"type": "text",
				"text": infoData.accumulateNum,
				"css": {
					"color": "#0BB985",
					"background": "rgba(0,0,0,0)",
					"width": "100px",
					"height": "23.729999999999997px",
					"top": "149px",
					"left": "220px",
					"rotate": "0",
					"borderRadius": "",
					"borderWidth": "",
					"borderColor": "",
					"shadow": "",
					"padding": "0px",
					"fontSize": "21px",
					"fontWeight": "bold",
					"maxLines": "1",
					"lineHeight": "23.310000000000002px",
					// "textStyle": "fill",
					"textDecoration": "none",
					"fontFamily": "",
					"textAlign": "center"
				}
			},
			{
				"type": "text",
				"text": "累计下发事项",
				"css": {
					"color": "#333333",
					"background": "rgba(0,0,0,0)",
					"width": "100px",
					"height": "15.819999999999999px",
					"top": "180px",
					"left": "221px",
					"rotate": "0",
					"borderRadius": "",
					"borderWidth": "",
					"borderColor": "#000000",
					"shadow": "",
					"padding": "0px",
					"fontSize": "14px",
					"fontWeight": "normal",
					"maxLines": "1",
					"lineHeight": "15.540000000000001px",
					// "textStyle": "fill",
					"textDecoration": "none",
					"fontFamily": "",
					"textAlign": "left"
				}
			}
		]
		console.log('jsonObj--', jsonData);
		if (infoData.currentRole != 'villager') {
			jsonData.views = [...jsonData.views, ...publishInfo]
		}
		let mostType = []
		if (infoData.matterList.length == 1) {
			mostType = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "最经常下发的事项类型：",
					"css": {
						"color": "#333333",
						"background": "rgba(0,0,0,0)",
						"width": "180px",
						"height": "18.08px",
						"top": "245px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[0].missionType,
					"id": "missionType0",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.matterList.length == 2) {
			mostType = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "最经常下发的事项类型：",
					"css": {
						"color": "#333333",
						"background": "rgba(0,0,0,0)",
						"width": "180px",
						"height": "18.08px",
						"top": "245px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[0].missionType,
					"id": "missionType0",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[1].missionType,
					"id": "missionType1",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "145px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.matterList.length >= 3) {
			mostType = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "100px",
						"top": "230px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "最经常下发的事项类型：",
					"css": {
						"color": "#333333",
						"background": "rgba(0,0,0,0)",
						"width": "180px",
						"height": "18.08px",
						"top": "245px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "left"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[0].missionType,
					"id": "missionType0",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[1].missionType,
					"id": "missionType1",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "145px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.matterList[2].missionType,
					"id": "missionType2",
					"css": {
						"color": "#0BBD88",
						"background": "#EDFCF7",
						"width": "80px",
						"height": "23.82px",
						"top": "286px",
						"left": "258px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "8px",
						"borderColor": "#EDFCF7",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		}
		if (infoData.currentRole != 'villager' && mostType.length > 0) {
			jsonData.views = [...jsonData.views, ...mostType]
		}
		let colleagueInfo = []
		if (infoData.colleagueList.length == 1) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "449px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "463px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"id": "colleague0",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.colleagueList.length == 2) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "449px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "463px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"id": "colleague0",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[1].photo,
					"id": "colleague1",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "148px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[1].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "148px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				}
			]
		} else if (infoData.colleagueList.length >= 3) {
			colleagueInfo = [{
					"type": "rect",
					"css": {
						"background": "#F6FFFD",
						"width": "355px",
						"height": "170px",
						"top": "449px",
						"left": "10px",
						"rotate": "0",
						"borderRadius": "10px",
						"shadow": "",
						"color": "#F6FFFD"
					}
				},
				{
					"type": "text",
					"text": "与你并肩作战的同事",
					"css": {
						"color": "#333333",
						"background": "",
						"width": "150px",
						"height": "18.08px",
						"top": "463px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "16px",
						"fontWeight": "bold",
						"maxLines": "1",
						"lineHeight": "17.76px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[0].photo,
					"id": "colleague0",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[0].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "30px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[1].photo,
					"id": "colleague1",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "148px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[1].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "148px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "text",
					"text": infoData.colleagueList[2].name,
					"css": {
						"color": "#333333",
						"background": "",
						"width": "76px",
						"height": "15.819999999999999px",
						"top": "584px",
						"left": "266px",
						"rotate": "0",
						"borderRadius": "10px",
						"borderWidth": "",
						"borderColor": "",
						"shadow": "",
						"padding": "0px",
						"fontSize": "14px",
						"fontWeight": "normal",
						"maxLines": "1",
						"lineHeight": "15.540000000000001px",
						// "textStyle": "fill",
						"textDecoration": "none",
						"fontFamily": "",
						"textAlign": "center"
					}
				},
				{
					"type": "image",
					"url": infoData.colleagueList[2].photo,
					"id": "colleague2",
					"css": {
						"width": "76px",
						"height": "76px",
						"top": "494px",
						"left": "266px",
						"rotate": "0",
						"borderRadius": "38px",
						"borderWidth": "",
						"borderColor": "#000000",
						"shadow": "",
						"mode": "scaleToFill"
					}
				}
			]
		}
		if (colleagueInfo.length > 0) {
			jsonData.views = [...jsonData.views, ...colleagueInfo]
		}
		return jsonData;
	}
}