{
	// 快捷登录页
	"login.fastLoginPhone": "بۇ ئاپپارات نومۇرى بىر كۇنۇپكا بىلەن تىزىملىتىش", //本机号码一键登录
	"login.fastLoginMp": "ئۈندىدار بىر كۇنۇپكا بىلەن ھوقۇق بېرىش تىزىملىتىش", //微信一键授权登录
	"login.captchaLogin": "تەكشۈرۈش نومۇرى تىزىملىتىش", //验证码登录
	"login.pwdLogin": "مەخپىي نومۇر تىزىملىتىش", //密码登录
	"login.agreedLogin": "تىزىملىتىش يەنى قوشۇلدى", //登录即同意
	"login.agreementLogin": "《خېرىدار كېلىشىمى》", //《用户协议》
	"login.gentleLogin": "ۋە", //和
	"login.PolicyLogin": "《مەخپىيەتلىك سىياسىتى》", //《隐私政策》
	"login.consentAgreement": "مۇقىملاشتۇرۇڭ «خېرىدار كېلىشىمى» ۋە «شەخسىي سىياسەت»", //请确认同意《用户协议》和《隐私政策》
	// 密码验证码登录页
	// 登录方式标题
	"login.captchaLoginTitle": "تەكشۈرۈش نومۇرى تىزىملىتىش", //验证码登录
	"login.pwdLoginTitle": "مەخپىي نومۇر تىزىملىتىش", //密码登录
	// 按钮文案
	"login.findPwdTitle": "قايتۇرغان مەخپىي نومۇر", //找回密码
	"login.loginBtn": "تىزىملىتىش", //登录
	"login.fastLogin": "بىر كۇنۇپكا بىلەن تىزىملىتىش", //一键登录
	"login.getCaptcha": "ئېرىشىش تەكشۈرۈش نومۇرى", //获取验证码
	"login.confirmAuthorization": "ئېتىراپ قىلىش ھوقۇق بېرىش", //确认授权
	"login.authorizationFailures": "ھوقۇق بېرىش مەغلۇپ بولدى", //授权失败
	"login.tips": "رويخەتكە ئېلىنمىغان يان تېلېفون نومۇرى دەلىللەش مۇۋەپپەقىيەتلىك بولغاندىن كېيىن ئاپتوماتىك رويخەتكە ئالدۇرغان", //未注册的手机号验证成功后自动注册
	//提示文字信息
	"login.phoneNumber": "تېلېفون نومۇرىڭىزنى كىرگۈزۈڭ", //请输入手机号
	"login.enterPassword": "مەخپىي نومۇر كىرگۈزۈڭ", //请输入密码
	"login.verificationCode": "دەلىللەش نومۇرىنى كىرگۈزۈڭ", //请输入验证码
	"login.writePhone": "يان تېلېفون نومۇرىنى تولدۇرۇڭ", //请填写手机号
	"login.incorrectPhone": "يان تېلېفون نومۇرى توغرا ئەمەس", //手机号码不正确
	"login.writePassword": "مەخپىي نومۇرنى تولدۇرۇڭ", //请填写密码
	"login.passwordIength": "مەخپىي نومۇر ئۇزۇنلۇقى 8 ~ 20 خانە ئارىلىقىدا", //密码长度在8~20位之间
	"login.phoneVerification": "يان تېلېفون دەلىللەش نومۇرى تولدۇرۇڭ", //请填写手机验证码
	"login.username": "سكيپە نامىنى تولدۇرۇڭ", //请填写用户名
	"login.tenantName": "ئىجارە ئالغۇچىنىڭ ئىسمىنى تولدۇرۇڭ", //请填写租户名
	"login.selectTenant": "ئىجارە ئالغۇچى تاللاڭ", //请选择租户
	"login.graphicVerification": "گرافىك دەلىللەش نومۇرى تولدۇرۇڭ", //请填写图形验证码
	"login.register": "تىزىملىتىش جەريانىدا", //登录中
	"login.succeeded": "تىزىملىتىش مۇۋەپپەقىيەتلىك بولدى", //登录成功
	"login.gainCode": "ھازىر ئېرىشىش تەكشۈرۈش نومۇرى", //正在获取验证码
	"login.bindPhone": "تېلېفون نومۇرىنى باغلاڭ", //请绑定手机号
	"login.alreadyBound": "سىز ئاللىقاچان بۇ تېلېفون نومۇرىنى باغلاپ باققان، تەكرار باغلىماڭ", //您已经绑定过该手机号，请勿重复绑定
	"login.authorizing": "ھوقۇق بېرىش جەريانىدا" //授权中



}