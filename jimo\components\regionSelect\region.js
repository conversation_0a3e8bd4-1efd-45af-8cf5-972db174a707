import { request } from '@/common/net/request.js';

export class RegionSelectApi {
	/**
	 * @description 获取行政区
	 * @param {Object} params
	 */
	static findLowerList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/user/region/findLowerList',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取行政区条件查询
	 * @param {Object} params
	 */
	static queryLowerList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/user/region/findLowerListWithQueryName',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 单个查询
	 * @param {Object} params
	 */
	static queryByRegionCode (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/findLowerListWithQueryName',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取用户区县行政区划
	 * @param {Object} params
	 */
	static getUserAreaRegionInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/findCountyNumber',
		    method: 'POST',
			params
		})
	}
}