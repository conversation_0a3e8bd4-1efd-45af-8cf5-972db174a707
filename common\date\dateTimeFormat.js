//时间戳转换日期 (yyyy-MM-dd HH:mm:ss)
export function formatDateTime(timeValue) {
    const date = new Date(timeValue);
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    let d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    let h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    let minute = date.getMinutes();
    let second = date.getSeconds()
    minute = minute < 10 ? ('0' + minute) : minute;
    second = second < 10 ? ('0' + second) : second;
    return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second;
};
//时间戳转换日期 (yyyy-MM-dd)
export function formatDateYearMonthDay(timeValue) {
    const date = new Date(timeValue);
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? ('0' + m) : m;
    let d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    return y + '-' + m + '-' + d;
};
//判断传入日期是否属于今年
function isYear(timeValue) {
    const takeNewYear = formatDateTime(new Date()).substr(0, 4); //当前时间的年份
    const takeTimeValue = formatDateTime(timeValue).substr(0, 4); //传入时间的年份
    return takeTimeValue == takeNewYear;
}
//判断传入两个日期是否属于同一年
function isSameYear(time1, time2) {
    const takeNewYear = formatDateTime(time1).substr(0, 4); //当前时间的年份
    const takeTimeValue = formatDateTime(time2).substr(0, 4); //传入时间的年份
    return takeTimeValue == takeNewYear;
}
//判断是否是今天
function isToday(date) {
    return new Date(date).toDateString() === new Date().toDateString();
}

//今日显示时分,一年内显示月日时分,当年之前显示年月日时分
export function timeChange(dateStr) {
    if (!dateStr) return '';
    const dateTime = Date.parse(dateStr)
    let returnTime = ''
    //当天
    if (isToday(dateTime)) {
        returnTime = formatDateTime(dateTime).substr(11, 5)
    }
    // 当年 
    else if (isYear(dateTime) === true) {
        returnTime = formatDateTime(dateTime).substr(5, 11);
    } else {
        // returnTime = formatDateTime(dateTime).substr(0, 10);
        returnTime = formatDateTime(dateTime).substr(0, 16);
    }
    return returnTime;
}
//判断两个时间是否是同一天
export function isSameDay(timeA, timeB) {
    // 获取两个时间的0点时间戳
    let dateA = new Date(timeA + '').setHours(0, 0, 0, 0)
    let dateB = new Date(timeB + '').setHours(0, 0, 0, 0)
    // 比较时间戳
    return dateA === dateB
}
/**
    两个时间的显示问题  
        同一天 4月7日 周四 10:00~18:00  
        同一年 10月15日 10:00~10月18日 10:00
        不同年 2022年10月15日 10:00 ~ 2023年10月18日 10:00
 * @param {Object} startTime
 * @param {Object} endTime
 */
export function dateBetweenFormat(startTime, endTime) {
    if (!startTime || !endTime) return ''
    let dateStr = '';
    const date = new Date(startTime + '')
    let year = date.getFullYear();
    let month = date.getMonth() + 1; // 获取月份（注意月份是从0开始计数的，所以需要加1）
    let day = date.getDate();
    let hours = date.getHours();
    let minutes = date.getMinutes();

    const endDate = new Date(endTime + '')
    let endYear = endDate.getFullYear();
    let endMonth = endDate.getMonth() + 1;
    let endDay = endDate.getDate();
    let endHours = endDate.getHours();
    let endMinutes = endDate.getMinutes();
    console.log(isSameDay(startTime, endTime))
    //两个时间是同一天
    if (isSameDay(startTime, endTime)) {
        const week = getChineseWeekByDate(date);
        dateStr =
            `${month}月${day}日 ${week} ${dateZeroFill(hours)}:${dateZeroFill(minutes)}~${dateZeroFill(endHours)}:${dateZeroFill(endMinutes)}`
    } else {
        //同一年  10月15日 10:00~10月18日 10:00
        if (isSameYear(startTime, endTime)) {
            dateStr =
                `${month}月${day}日 ${dateZeroFill(hours)}:${dateZeroFill(minutes)}~${endMonth}月${endDay}日 ${dateZeroFill(endHours)}:${dateZeroFill(endMinutes)}`
        } else {
            //不同年 2022年10月15日 10:00 ~ 2023年10月18日 10:00
            dateStr =
                `${year}年${month}月${day}日 ${dateZeroFill(hours)}:${dateZeroFill(minutes)}~${endYear}年${endMonth}月${endDay}日 ${dateZeroFill(endHours)}:${dateZeroFill(endMinutes)}`
        }
    }
    return dateStr
}
//补零
function dateZeroFill(str) {
    return str.toString().padStart(2, '0')
}
/*
 * 根据Date对象返回星期几
 *  @param {Date} date
 *  @return {String} "星期三"
 */
export function getChineseWeekByDate(date) {
    var numWeekDay = date.getDay();
    switch (numWeekDay) {
        case 0:
            return '周日';
        case 1:
            return '周一';
        case 2:
            return '周二';
        case 3:
            return '周三';
        case 4:
            return '周四';
        case 5:
            return '周五';
        case 6:
            return '周六';
        default:
            return '';
    }
}