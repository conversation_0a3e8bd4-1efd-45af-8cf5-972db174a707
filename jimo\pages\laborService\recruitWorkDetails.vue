<!-- 招工详情-->
<template>
    <view class="detail-container" v-if="isShowDetails">
		<u-navbar title="招工详情" border bgColor="rgba(0, 0, 0, 0)" 
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000">
		</u-navbar>
		
        <view class="main-content scrollbar-none" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
            <view class="first-view">
				<view class="title">{{ dataInfo.positionName }}</view>
				<view class="money">{{ dataInfo.salaryRange }}</view>
				<view class="other">
				    <view class="zhao">
				        <u-image :src="LABOR_ZHAO" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
				        <span class="num">招{{ dataInfo.workerNum }}人 </span>
				    </view>
				    <view class="zhao">
				        <u-image :src="LABOR_SJ" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
				        <span class="num">{{ dataInfo.deadLine }}截止投递</span>
				    </view>
				</view>
				<view class="contact-person">联系人：{{ dataInfo.contacts }}</view>
			</view>
            <u-line></u-line>
            <view class="content">
                <view class="detail-title">岗位描述</view>
                <view class="detail-content">
                    {{ dataInfo.positionDescription }}
                </view>
                <view class="address">
                    <u-image :src="LABOR_DZ" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
                    <view class="txt">{{ dataInfo.workPlace }}{{ dataInfo.address }}</view>
                </view>
            </view>
        </view>
    
		<view class="communication">
			<view class="btn" @click="handleCallPhone()">
				<u-icon name="phone-fill" color="#fff" size="32rpx"></u-icon>
				<text>立即沟通</text>
			</view>
		</view>
	</view>
	<view class="empty-status" v-else>
		<u-navbar title="招工详情" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000">
		</u-navbar>
		<view class="empty-icon">
			<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
			<view class="no-more">
				<span class="txt">招工信息不存在</span>
			</view>
		</view>
	</view>
</template>

<script setup>
import { computed, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { LABOR_ZHAO, LABOR_SJ, LABOR_DZ, LABOR_BTNBG } from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'
import {
        goldTask
    } from '../../api/api.js'
import { responsibility } from '@/common/net/my/my.js'
import { NO_MORE_IMG } from '@/common/net/staticUrl'
import { useUserStore } from '@/store/user.js'
const userStore = useUserStore()
/** 顶部导航高度 */
const navbarHeight = ref('')
const dataInfo = ref({})
const taskId = ref('')
const isShowDetails = ref(true)
const villagePeopleId = computed(() => {
	return userStore.userInfo?.customParam?.peopleInfo?.villagePeopleId
})
onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	getDataInfo(options.positionId)
	addViewNumber(options.positionId)
	// 来自金币任务
	const { taskId: taskIdFromOptions = '' } = options
	taskId.value = taskIdFromOptions
	getGoldTask()

})

// 金币任务-劳务用工-阅读信息
async function getGoldTask() {
	try {
		let res = await goldTask({ 
			taskId: taskId.value,
			taskType: '5',
			appKey: "laowuyonggong"
		});
		if (res && res.success) {
			let goldMessage = taskId.value ? `任务完成，获得${res.data.coin}金币` : `获得${res.data.coin}金币`;
			handleToast(goldMessage);
		} else {
			console.log('获取金币失败',res)
			if(taskId.value) {
				handleToast(res.message);
			}
		}
	} catch (err) {
		console.error('获取金币异常',err)
	}
}

function handleToast(message) {
	// 使用配置对象
	const toastConfig = {
		title: message,
		icon: 'none',
		duration: 1500,
		success: () => {
			const timer = setTimeout(() => {
				clearTimeout(timer)
				// 家庭任务积分获取-阅读信息
				responsibility({ taskType: '4' })
			}, 1500)
		}
	};
	uni.showToast(toastConfig);
}
	

/**
 * @description 获取招工详情
 * @param {String} postionId
 */
const getDataInfo = (positionId) => {
	const params = {
		positionId
	}
	let currentDate = new Date()
	LaborService.getRecruitWorkInfo(params).then(r => {
		if (r.success) {
			dataInfo.value = r.data
			let deadLine = r.data.deadLine.match(/^\d{4}-\d{2}-\d{2}$/) ? r.data.deadLine + 'T23:59:59' : r.data.deadLine;
			let deadLineTime = new Date(deadLine)
			let currentTime = new Date()
			if( r.data.villagePeopleId != villagePeopleId.value && (deadLineTime.getTime() < currentTime.getTime() || r.data.publishStatus !='on') ){
				// 如果浏览人不是当前人，且招聘时间已截止
				isShowDetails.value = false
			}else{
				isShowDetails.value = true
			}
		}
	})
}
/**
 * @description 添加浏览量
 * @params positionId
 */
const addViewNumber = (positionId) => {
	LaborService.addViewNumber({ positionId })
}
/**
 * @description 立即沟通
 */
const handleCallPhone = () => {
	uni.makePhoneCall({
		phoneNumber: dataInfo.value.contactsPhone
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 25rpx 20rpx;
	
	.first-view {
		padding: 20rpx 21rpx;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 42rpx;
			color: #000000;
			line-height: 59rpx;
		}
		
		.money {
			margin-top: 8rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 32rpx;
			color: #0BBD88;
			line-height: 45rpx;
		}
		
		.other {
			display: flex;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 33rpx;
			margin-top: 23rpx;
		
			.zhao {
				margin-right: 10rpx;
				display: flex;
		
				.num {
					margin-left: 10rpx;
				}
			}
		}
		
		.contact-person {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 42rpx;
			margin-top: 20rpx;
		}
	}

	.content {
		padding: 23rpx 21rpx;

		.detail-title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}

		.detail-content {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 48rpx;
			margin-top: 17rpx;
			white-space: pre-wrap;
		}

		.address {
			margin-top: 48rpx;
			display: flex;

			.txt {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				line-height: 33rpx;
				margin-left: 6rpx;
			}
		}
	}
}

.communication {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	padding-bottom: calc(env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
			line-height: 50rpx;
		}
	}
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
	.empty-status {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 50rpx;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
				}
			}
		}
	}
</style>