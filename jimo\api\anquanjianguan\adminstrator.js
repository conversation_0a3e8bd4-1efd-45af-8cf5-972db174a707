import { request } from '@/common/net/request.js';


export class AdminService {

	static getCompanyList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/findPage',
		    method: 'POST',
			params
		})
	}
	static findCompanyById(id){
    return request({
      url: '/farm/enterprise/findOne/'+id,
      method: 'GET'
    
    })
  }
  static updateCompanyDetail(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm//enterprise/update',
		    method: 'POST',
			params
		})
  }
  static deleteCompany(id){
    return request({
      url: '/farm/enterprise/delete/'+id,
      method: 'GET'
    
    })
  }
  static addCompany(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/add',
		    method: 'POST',
			params
		})
  }
  static findCompanyChecklist(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/audit/findPage',
		    method: 'POST',
			params
		})
  }
  static findAuditDetail(id){
		return request({
		    url: '/farm/enterprise/audit/'+id,
		    method: 'GET'
			
		})
  }
  static checkCompany(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/audit/audit',
		    method: 'POST',
			params
		})
  }
  static auditDetail(id){
    return request({
      url: '/farm/enterprise/audit/detail/'+id,
      method: 'GET'
    
  })
  }
  static findOverdueInspections(params){
     params['uniContentType'] = 'json'
		return request({
		    url: '/farm/overdueTask/admin/findOverdueInspections',
		    method: 'POST',
			params
		})
  }
   static findOverdueReviews(params){
     params['uniContentType'] = 'json'
		return request({
		    url: '/farm/overdueTask/admin/findOverdueReviews',
		    method: 'POST',
			params
		})
  }
   static findOverdueCredentials(params){
     params['uniContentType'] = 'json'
		return request({
		    url: '/farm/overdueTask/admin/findOverdueCredentials',
		    method: 'POST',
			params
		})
  }
  static auditReview(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectReview/auditReview',
		    method: 'POST',
			params
		})
  }
}


