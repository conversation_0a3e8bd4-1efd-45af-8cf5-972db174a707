import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDSURVEY_URL,
	API_FINDPAGESURVEY_URL,
	API_PUBLISHSURVEY_URL,
	API_FINDONESURVEY_URL,
	API_UPDATESURVEY_URL,
	API_DELETESURVEY_URL,
	API_QUESTIONSURVEY_URL,
	API_ANSWERSURVEY_URL,
	API_SELECTEDSURVEY_URL,
} from '@/common/net/netUrl.js'

// 保存问卷
export function naireSave(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDSURVEY_URL,
		method: 'POST',
		params,
	})
}

//发布问卷
export function naireAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_PUBLISHSURVEY_URL,
		method: 'POST',
		params,
	})
}

//问卷管理 -分页
export function findPage(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FINDPAGESURVEY_URL,
		method: 'POST',
		params,
	})
}

//调查问卷 -分页
export function reference(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_QUESTIONSURVEY_URL,
		method: 'POST',
		params,
	})
}