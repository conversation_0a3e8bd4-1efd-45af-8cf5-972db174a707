<template>
    <view class="container" :class="{'approver': chooseType == 'single' || chooseType == 'browser' }">
        <!-- <u-navbar title="选择成员" :title-style="{'fontSize': '36rpx'}" placeholder border auto-back>
		</u-navbar> -->
        <view class="search-box" v-if="chooseType == 'multi'">
            <u-search :placeholder="sourceType=='2'? '输入姓名搜索' : '输入姓名或门牌号搜索'" :inputStyle="inputStyle" height="60rpx" v-model="keyword"
                bgColor="#FFFFFF" :showAction="false" :clearabled="false" @custom="search()"
                @search="search()"></u-search>
			<view class="left">
				<uni-data-select
					v-model="peopleTag"
					placeholder="请选择" 
					:localdata="peopleTagData"
					clear
					@change="handleSelect('label')">
				</uni-data-select>
			</view>
			<view class="left" v-if="sourceType == '1'">
				<uni-data-select
					v-model="villageOrgName"
					placeholder="请选择" 
					:localdata="teamData"
					clear
					@change="handleSelect('label')">
				</uni-data-select>
			</view>
			<view class="left" v-else>
				<view class="choose-group" @click="showGroup = true">
					<view class="holder u-line-1">
						{{groupName}}
					</view>
					<view v-if="groupName!='请选择'"  @click.stop="clearGroup">
						<uni-icons type="clear" color="#c0c4cc" size="22" />
					</view>
					<up-icon v-else name="arrow-down-fill"></up-icon>
				</view>
			</view>
        </view>
        <scroll-view scroll-y="true" class="content-container" @scrolltolower='loadMore'>
            <template v-if="peopleList.length > 0">
                <view class="article-list">
                    <view class="article-item" v-for="(item, index) in peopleList" :key="item.villagePeopleId"
                        @click="chooseMember(item)">
                        <view class="article-radio" v-if="chooseType == 'multi'">
                            <view style="position: relative;">
                                <view class="radio-img"></view>
                                <view v-if="isChoosed(item)" class="active"></view>
                            </view>
                        </view>
                        <view class="article-content">
                            <safe-image :src="item.photo || DEFAULT_AVATOR_IMAGES" shape="circle" width="98rpx"
                                height="98rpx" />
                            <!-- <u-avatar :src="item.photo" size="98rpx"
								:default-url="DEFAULT_AVATOR_IMAGES"></u-avatar> -->
                            <view class="article-text">
                                <view class="online u-line-1" style="font-size: 32rpx;margin-bottom: 10rpx;">
                                    {{ item.name }}{{item.phone}}
                                </view>
                                <view class="online" style="flex:1;font-size: 28rpx;;color: #999999;overflow: hidden;">
                                    <view>{{ item.villageOrgName}}
                                    </view>
                                    <view style="margin-left:10rpx;">
                                        {{item.houseNumber}}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 底部加载，三个状态：more、loading、nomore -->
                <u-loadmore :status="loadStatus"></u-loadmore>
            </template>
            <view v-else class="empty-status">
                <view class="empty-icon">
                    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                        height="150rpx"></u-image>
                    <view class="no-more">
                        <span class="txt">暂无数据</span>
                    </view>
                </view>
            </view>
        </scroll-view>
        <!-- 占位块，使上一个view的marginbottom生效 -->
        <!-- <view style="height: 1rpx;"></view> -->
    </view>
    <view class="submit-container paddingbottom" v-if="chooseType == 'multi' && peopleList.length > 0">
        <view class="submit" @click="submit">
            确认
        </view>
    </view>
	
	<!-- 底部弹窗 -->
	<u-popup v-if="showGroup" :show="showGroup" mode='bottom' :closeOnClickOverlay='true' @close="showGroup = false">
		<chooseGroup @selectGroup='selectGroup'></chooseGroup>
	</u-popup>
</template>

<script setup>
    import {
        reactive,
        ref
    } from "vue";
    import {
        onLoad
    } from '@dcloudio/uni-app';
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        NO_MORE_IMG,
        DEFAULT_AVATOR_IMAGES
    } from '@/common/net/staticUrl.js'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        getTeamListByRegionCode,
        findPeople,
		findPartyPeople,
        findApproverList,
		findPartyApproverList
    } from '../../api/points/points';
	import { getDictListByKey } from '@/common/net/generalPage/common.js'
	import chooseGroup from "./chooseGroup.vue";
    const peopleList = ref([])
    const total = ref(0);
    const currentPage = ref(1)
    const isLoading = ref(false);
    const loadStatus = ref("loading");
    const chooseType = ref('multi') // 单选还是多选 选村民是多选 选审批人是单选
    const eventType = ref("") // 事件类型，办理类的工单需要这个参数来过滤人员
    const operate = ref("") // 操作，用来判断是不是选转交人，如果是的话则不能选择自己
    const inputStyle = {
        width: '180rpx',
    }
    const keyword = ref('') //关键字
    const tokenStore = useTokenStore()
    const pointApplyId = ref('') // 申报记录id
	let sourceType = ref('1')  //1为家庭 2为党员
    const userStore = useUserStore()
	let peopleTag = ref('') //人员标签
	let villageOrgName = ref('')  //队组
	let peopleTagData = ref([]) //人员标签数据
	let teamData = ref([]) //队组/党组织数据
	const groupName = ref('请选择') //党组织
	const showGroup = ref(false)
    onLoad((options) => {
		sourceType.value = options?.sourceType || '1'
		getLocaldata()
        if (!!options.choosedMembers) {
            choosedMembers.value = JSON.parse(options.choosedMembers);
        }
        if (!!options.operate) {
            operate.value = options.operate;
        }
        if (!!options.operate) {
            eventType.value = options.eventType
        }
        if (!!options.chooseType) {
            chooseType.value = options.chooseType;
        }
        if (!!options.pointApplyId) {
            pointApplyId.value = options.pointApplyId;
        }
        // 单选是选审批人 多选是选村民
        if (chooseType.value == 'multi' || chooseType.value == 'browser') {
            getPeople()
        } else if (chooseType.value == 'single') {
            getApprover()
        }
    })

    //搜索
    function search() {
        peopleList.value = []
        currentPage.value = 1
        getPeople()
    }
    // 选择村民
    function getPeople() {
        //显示加载中动画
        uni.showNavigationBarLoading();
        let paging = {
            pageNum: currentPage.value,
            pageSize: 10,
            peopleNames: keyword.value,
			peopleTag: peopleTag.value,
        }
		if(sourceType.value == '2'){
			paging.organizationName = groupName.value !== '请选择' ? groupName.value : ''
		}else{
			paging.villageOrgName = villageOrgName.value
		}
        if (!!pointApplyId.value) {
            paging.pointApplyId = pointApplyId.value
        }
		let api = sourceType.value == '2' ? findPartyPeople : findPeople
        api(paging).then(res => {
            handleView();
            if (res.success && res.data && res.data.records.length > 0) {
                total.value = res.data.total
                if (currentPage.value > 1) {
                    peopleList.value = [...peopleList.value, ...res.data.records];
                } else {
                    peopleList.value = res.data.records;
                }
                loadStatus.value = total.value === peopleList.value.length ? "nomore" : "more";
            }
        }).catch(() => {
            handleView();
            uni.showToast({
                title: '查询数据失败',
                icon: 'none'
            });
        })
    }
    // 选择审批人
    function getApprover() {
        //显示加载中动画
        uni.showNavigationBarLoading();
		let api = sourceType.value =='2' ? findPartyApproverList : findApproverList
        api().then(res => {
            handleView();
            if (res.success && res.data && res.data.length > 0) {
                peopleList.value = res.data;
                loadStatus.value = "nomore";
            }
        }).catch(() => {
            handleView();
            uni.showToast({
                title: '查询数据失败',
                icon: 'none'
            });
        })
    }

    // 请求成功后更新视图
    function handleView() {
        //成功获取数据后隐藏加载动画
        uni.hideNavigationBarLoading();
        //成功获取数据后结束下拉刷新
        uni.stopPullDownRefresh();
        isLoading.value = false;
    }

    function loadMore() {
        if (loadStatus.value == 'nomore') {
            return;
        }
        currentPage.value = currentPage.value + 1;
        getPeople()
    }

    // 选中的成员列表
    const choosedMembers = ref([]);

    // 选中成员
    function chooseMember(memberInfo) {
        // 详情页查看申报对象时只展示不能选择
        if (chooseType.value == 'browser') {
            return false
        }
        // 单选时选中即返回，多选时选中后只保存选项，需要通过按钮触发返回
        if (chooseType.value == 'single') {
            // 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
            uni.$emit('chooseApproverCallback', memberInfo);
			// 返回上一页
			uni.navigateBack({
			    delta: 1
			});
        } else {
            // 先判断当前的点击的是否已被选中，已被选中的点击是取消选中，未被选中的点击是选中
            let tmpIndex = choosedMembers.value.findIndex(member => {
                return member.peopleId == memberInfo.peopleId
            })
            // 不存在则添加
            if (tmpIndex == -1) {
                choosedMembers.value.push(memberInfo)
            } else {
                // 已存在则删除
                choosedMembers.value.splice(tmpIndex, 1)
            }
            console.log("choosedMembers.value------", choosedMembers.value);
        }
    }
    // 判断当前选项是否已选中
    function isChoosed(memberInfo) {
        let tmpObj = choosedMembers.value.find(item => {
            return item.peopleId == memberInfo.peopleId
        })
        if (!!tmpObj) {
            return true
        } else {
            return false;
        }
    }
    // 提交选中人员
    function submit() {
        // 返回参数，需要前一个页面调用uni.$on('chooseMemberCallback', (data) => { console.log(data); });
        uni.$emit('choosePeopleCallback', choosedMembers.value);
		// 返回上一页
		uni.navigateBack({
		    delta: 1
		});
    }
	function getLocaldata(){
		getDictListByKey({code:'people_tag'}).then(res => {
			if(res.success){
				peopleTagData.value = res.data.map(item => {
					return {
						text: item.dictLabel,
						value: item.dictValue
					}
				})
			}
		})
		if(sourceType.value == '1'){
			getTeamListByRegionCode({regionCode:userStore?.userInfo?.customParam?.regionCode}).then(res => {
				if(res.success){
					teamData.value = res.data.map(item => {
						return {
							text: item.regionName,
							value: item.regionName
						}
					})
				}
			})
		}
	}
	function selectGroup(item) {
		groupName.value = item.partyOrgName;
		search();
		showGroup.value = false;
	}
	function clearGroup(){
		groupName.value = '请选择'
		search();
	}
	function handleSelect(type){
		search()
	}
</script>

<style lang="scss" scoped>
    .container {
        height: calc(100vh - 165rpx - env(safe-area-inset-bottom));
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 20rpx;
        overflow: hidden;
        background: #F0F7F7;

        .search-box {
            box-sizing: border-box;
            margin-bottom: 20rpx;
            width: 100%;
            height: 80rpx;
			display: flex;
			align-items: center;
			.left{
				flex:1;
				min-width: 170rpx;
				margin-left: 10rpx;
				::v-deep .u-search__content{
					padding: 0 12rpx;
				}
				::v-deep .u-search__content__input--placeholder{
					font-size: 26rpx;
				}
				::v-deep .uni-select {
					height: 64rpx;
				}
				::v-deep .uni-select__input-placeholder{
					color: #909399;
				}
				::v-deep .uni-select__input-box{
					overflow: hidden;
				}
			}
        }

        .content-container {
            height: calc(100% - 80rpx);
            flex: 1;
            overflow-y: auto;
            box-sizing: border-box;
            display: block;
            // margin: 20rpx;
            padding: 25rpx;
            background: #fff;
            border-radius: 10rpx;
            box-shadow: 1rpx 2rpx 8rpx 6rpx rgba(79, 139, 250, 0.05);

            .article-list {
                width: 100%;

                .article-item {
                    display: flex;
                    align-items: center;
                    width: 100%;
                    height: 120rpx;
                    margin-bottom: 18rpx;
                    padding-top: 18rpx;

                    &:last-child {
                        padding-bottom: 24rpx;
                    }

                    .article-radio {
                        width: 60rpx;
                        padding-bottom: 18rpx;

                        .radio-img {
                            width: 30rpx;
                            height: 30rpx;
                            margin: auto;
                            background: #FFFFFF;
                            border: 2rpx solid #C3C3C3;
                            border-radius: 50%;
                        }

                        .active {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            width: 20rpx;
                            height: 20rpx;
                            background: #0BBD88;
                            border-radius: 50%;
                            transform: translate(-50%, -50%);
                        }
                    }

                    .article-content {
                        display: flex;
                        align-items: center;
                        height: 100%;
                        border-bottom: 1rpx solid #E0E0E0;
                        flex: 1;
                        padding-bottom: 18rpx;
                        margin-left: 25rpx;

                        .article-text {
                            margin-left: 20rpx;
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            line-height: 1.5;
                            width: 0;
                            flex: 1;
                        }
                    }
                }
            }

            .empty-status {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 50rpx 0;

                .empty-icon {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .no-more {
                        margin-top: 20rpx;
                        display: flex;
                        justify-content: center;
                        margin-top: 10rpx;

                        .txt {
                            text-align: center;
                            height: 37rpx;
                            font-size: 26rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #333333;
                            line-height: 37rpx;
                        }
                    }
                }
            }
        }
    }

    .submit-container {
        // position: fixed;
        // bottom: 0;
        // left: 0;
        // z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 125rpx;
        background: #FFFFFF;

        .submit {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 670rpx;
            height: 80rpx;
            background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
            border-radius: 48rpx;
            font-size: 36rpx;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            color: #FFFFFF;
        }
    }

    .online {
        display: flex;
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 1;
        // overflow: hidden;
        white-space: nowrap;
    }

    .addr {}

    .paddingbottom {
        padding-bottom: constant(safe-area-inset-bottom);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .approver {
        height: calc(100vh - 40rpx - env(safe-area-inset-bottom));
    }
	.choose-group {
		display: flex;
		justify-content: center;
		align-items: center;
		width:  100%;
		height: 60rpx;
		line-height: 60rpx;
		background: #fff;
		border-radius: 30rpx;
		color: #6a6a6a;
		font-size: 26rpx;
		padding: 0 10rpx 0 21rpx;
		box-sizing: border-box;
		.holder {
			margin-right: 20rpx;
		}
	}
</style>