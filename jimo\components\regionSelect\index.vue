<template>
	<u-popup :show="show" round="20rpx" mode="bottom" @close="handleClose()">
		<view class="region-select">
			<u-navbar title="选择乡村" bgColor="rgba(0, 0, 0, 0)"
				:placeholder="true" :border="true" @leftClick="show = false"
				leftIconColor='#000' titleStyle="font-size: 36rpx;color: #000"
				style="height: 235rpx;">
			</u-navbar>
			
			<view class="region-body" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
				<u-search placeholder="请输入乡村名称搜索" v-model="villageName"
					:showAction="false" height="78rpx" bgColor="#FFF" borderColor="#E4E4E4"
					@search="handleSearch()" @blur="handleSearch()" />
				
				<view class="title"><view></view>全部</view>
				
				<view class="breadcrumb" v-if="breadcrumbShow">
					<view v-for="(item, index) in breadcrumbList" :key="item.text"
						:class="{'is-active': breadcrumb === index}"
						@click="handleBreadcrumb(index)" v-show="item.regionShowName">
						{{ item.regionShowName }}
					</view>
				</view>
				
				<view class="data-list" :class="{'data-list2': !breadcrumbShow}">
					<scroll-view scroll-y :scroll-into-view="scrollIntoViewId" style="height: 100%;">
						<checkbox-group>
							<view class="data-item" v-for="item in dataList" :key="item.id"
								@click="handleRegionClick(item)" :id="`id${item.id}`">
								<text>{{ item.regionFullName || item.regionName }}</text>
								
								<label v-if="item.regionLevel > level">
									<checkbox :value="item.regionCode"
										:checked="selectRegionCode.findIndex(o => o === item.regionCode) > -1"
									  	@click.stop="handleSelectRegion(item)" />
								</label>
							</view>
						</checkbox-group>
					</scroll-view>
				</view>
			</view>
			
			<view class="submit" @click="handleSubmit()">
				<view class="btn">确认</view>
			</view>
		</view>
	</u-popup>
</template>

<script setup>
import { ref, defineExpose, defineProps, defineEmits } from 'vue'
import { RegionSelectApi } from './region.js'

const props = defineProps({
	maxCheck: {
		type: Number,
		default: () => 1
	},
	level: {
		type: Number,
		default: () => 4
	},
	/** 是否只显示当前用户所在'区/县'的行政区划 */
	isUserArea: {
		type: Boolean,
		default: () => false
	}
})

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 弹窗显示 */
const show = ref(false)
/** 用户行政区划 */
const userRegionCode = ref(null)
/** 条件查询 */
const villageName = ref('')
/** 面包屑显示 */
const breadcrumbShow = ref(true)
/** 面包屑显示 */
const breadcrumbList = ref([])
/** 面包屑选择 */
const breadcrumb = ref(0)
/** 当前选择行政区划编码 */
const selectRegionCode = ref([])
/** 当前选择行政区划 */
const selectRegion = ref([])
/** 数据列表 */
const dataList = ref([])
/** 滚动索引 */
const scrollIntoViewId = ref('')

defineExpose({
	init
})

const emit = defineEmits(['get'])

function init () {
	show.value = true
	handleReset()
	
	if (!props.isUserArea) {
		breadcrumbList.value = [{ regionShowName: '省份' }]
		getRegionData('0')
	} else {
		breadcrumbList.value = [{}, {}, {}, { regionShowName: '乡镇' }]
		breadcrumb.value = 3
		getUserAreaRegionInfo()
	}
	
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
}

const handleClose = () => {
	show.value = false
}

const handleReset = () => {
	breadcrumb.value = 0
	breadcrumbShow.value = true
	villageName.value = ''
	selectRegionCode.value = []
	selectRegion.value = []
	dataList.value = []
}
/**
 * @description 获取当前用户区县行政区划信息
 */
const getUserAreaRegionInfo = () => {
	RegionSelectApi.getUserAreaRegionInfo({}).then(r => {
		if (r.success) {
			userRegionCode.value = r.data
			getRegionData(r.data)
			breadcrumbList.value[2].regionCode = r.data
		}
	})
}
/**
 * @description 获取行政区划
 * @param {String} regionCode
 */
const getRegionData = (regionCode) => {
	uni.showLoading({
		title: '数据加载中...'
	})
	breadcrumbShow.value = true
	
	const params = {
		queryType: 'down',
		regionCode
	}
	RegionSelectApi.findLowerList(params).then(r => {
		if (r.success) {
			dataList.value = r.data
			
			scrollIntoViewId.value = `id${r.data[0].id}`
			
			// 重置选择
			selectRegionCode.value = []
			selectRegion.value = []
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 面包屑点击
 * @param {Number} index
 */
const handleBreadcrumb = (index) => {
	breadcrumb.value = index
	const regionCode = index === 0 ? '0' :  breadcrumbList.value[index - 1].regionCode
	getRegionData(regionCode)
}

/**
 * @description 条件查询
 */
const handleSearch = () => {
	if (!villageName.value) {
		getRegionData(breadcrumbList.value[breadcrumb.value - 1].regionCode)
		return
	}
	uni.showLoading({
		title: '数据加载中...'
	})
	
	breadcrumbShow.value = false
	
	selectRegionCode.value = []
	selectRegion.value = []
	
	const params = {
		regionName: villageName.value,
		rootCode: userRegionCode.value || '0'
	}
	RegionSelectApi.queryLowerList(params).then(r => {
		if (r.success) {
			dataList.value = r.data.filter(item => item.regionLevel > props.level)
		}
	}).finally(() => {
		uni.hideLoading()
	})
}

/**
 * @description 行政区划点击
 * @param {Object} region
 */
const handleRegionClick = (region) => {
	if (villageName.value || breadcrumb.value === 4) return
	
	breadcrumbList.value.splice(breadcrumb.value)
	breadcrumbList.value[breadcrumb.value] = {
		regionName: region.regionName,
		regionShowName: region.regionName,
		regionCode: region.regionCode,
		regionLevel: region.regionLevel,
		parentCode: region.parentCode
	}
	if (breadcrumb.value === 0) {
		breadcrumbList.value.push({ regionShowName: '城市' })
		breadcrumb.value = 1
	}
	else if (breadcrumb.value === 1) {
		breadcrumbList.value.push({ regionShowName: '区县' })
		breadcrumb.value = 2
	}
	else if (breadcrumb.value === 2) {
		breadcrumbList.value.push({ regionShowName: '乡镇' })
		breadcrumb.value = 3
	}
	else if (breadcrumb.value === 3) {
		breadcrumbList.value.push({ regionShowName: '村庄' })
		breadcrumb.value = 4
	}
	
	// if (breadcrumb.value < props.level) {
	// 	selectRegionCode.value = []
	// 	selectRegion.value = []
	// }
	
	getRegionData(region.regionCode)
}

/**
 * @description 行政区划选择回调
 * @param {Object} item
 */
const handleSelectRegion = (item) => {
	const index = selectRegionCode.value.findIndex(o => o === item.regionCode)
	if (index > -1) {
		selectRegionCode.value.splice(index, 1)
		selectRegion.value.splice(index, 1)
	} else {
		// 判断是否是相同区县或乡镇下
		if (selectRegion.value.length && item.parentCode !== selectRegion.value[0].parentCode) {
			selectRegionCode.value = []
			selectRegion.value = []
		}
		// 判断是否达到最大选择数量
		if (selectRegionCode.value.length === props.maxCheck) {
			selectRegionCode.value.splice(0, 1)
			selectRegion.value.splice(0, 1)
		}
		// 获取当前选择行政区划的全路径名称
		let regionFullName = ''
		if (villageName.value) {
			regionFullName = item.regionFullName
		} else {
			regionFullName = breadcrumbList.value.map(o => o.regionName).join('') + item.regionName
		}
		
		selectRegionCode.value.push(item.regionCode)
		selectRegion.value.push({
			regionFullName,
			regionName: item.regionName,
			regionCode: item.regionCode,
			regionLevel: item.regionLevel,
			parentCode: item.parentCode
		})
	}
}
/**
 * @description 提交
 */
const handleSubmit = () => {
	if (!selectRegion.value.length) {
		uni.showToast({ title: '请选择！', icon: 'none' })
	}
	const params = {
		selectRegion: selectRegion.value,
		parent: null
	}
	console.log(params)
	RegionSelectApi.queryByRegionCode({
		rootCode: selectRegion.value[0].parentCode,
		regionLevel: `${Number(selectRegion.value[0].regionLevel) - 1}`
	}).then(r => {
		if (r.success) {
			params.parent = {
				regionName: r.data[0].regionName,
				regionCode: r.data[0].regionCode
			}
			emit('get', params)
			handleClose()
		}
	})
	
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}
.region-select {
	width: 100%;
	height: 100vh;
	position: relative;
}

.region-body {
	width: 100%;
	position: fixed;
	bottom: calc(141rpx + env(safe-area-inset-bottom));
	padding: 21rpx 17rpx 21rpx 23rpx;
	
	.title {
		display: flex;
		align-items: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 600;
		font-size: 32rpx;
		color: #000000;
		line-height: 45rpx;
		margin-top: 60rpx;
		
		>view {
			width: 6rpx;
			height: 27rpx;
			background: linear-gradient(180deg, #0CBE88 0%, #1CD073 100%);
			border-radius: 3rpx;
			margin-right: 9rpx;
			margin-bottom: -3px;
		}
	}
	
	.breadcrumb {
		margin-top: 32rpx;
		padding-left: 13rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		font-size: 28rpx;
		color: #999999;
		line-height: 40rpx;
		display: flex;
		
		>view {
			margin-right: 40rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
			max-width: 110rpx;
		}
		
		.is-active {
			font-weight: 600;
			color: #0CBE88;
		}
	}
	
	.data-list {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: calc(100% - 300rpx);
		padding: 0 40rpx 20rpx 40rpx;
		
		&.data-list2 {
			height: calc(100% - 300rpx + 72rpx) !important;
		}
		
		.data-item {
			width: 100%;
			padding-bottom: 24rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 40rpx;
			border-bottom: 1rpx solid rgba(151, 151, 151, 0.3);
			margin-bottom: 23rpx;
			display: flex;
			justify-content: space-between;
		}
	}
}

.submit {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #FFFFFF;
		line-height: 50rpx;
	}
}
</style>
