<template>
    <!-- 列表的详情页面-->
    <view class="detail-container" v-if="contentInfo">
        <view class="main-content">
            <view class="title">
                {{contentInfo.contentTitle||''}}
            </view>
            <view class="other">
                <span class="author">{{contentInfo?.createByName||''}}</span>
                <span class="time">{{timeChange(contentInfo?.createDate)||''}}</span>
            </view>
            <view class="content">
                <view v-for="(content, index) in contentArr" :key="index">
                    <rich-text class="rich-text ql-editor" :nodes="content" @click="showImg(content)"></rich-text>
                    <video v-if="videoArr[index] !== null" :src="videoArr[index]" controls :style="{ width }"></video>
                </view>
            </view>
            <view class="option-btn">
                <view class="view-info" @click="handleDelete"
                    v-if="villagePeopleId && contentInfo.peopleId && villagePeopleId == contentInfo.peopleId">
                    <u-icon name="trash" size="32rpx"></u-icon>
                    <span class="comment">删除</span>
                </view>
                <!-- <view class="icon-list" v-if="listType == 'commentList'">
                    <u-icon name="chat" size="32rpx" @click="handleComment"></u-icon>
                    <span class="comment" @click="handleComment">评论 {{contentInfo.commentCount || 0}}</span>
                    <u-icon name="thumb-up-fill" v-if="contentInfo.upvoteFlag==1" color="#f00" size="32rpx"
                        @click="handleLike()"></u-icon>
                    <u-icon name="thumb-up" v-else size="32rpx" @click="handleLike()"></u-icon>
                    <span class="like" @click="handleLike()">点赞 {{contentInfo.upvoteCount || 0}}</span>
                </view> -->
            </view>
        </view>
        <view class="split-line" v-if="listType == 'commentList'"> </view>
        <view class="comment-content" v-if="listType == 'commentList'">
            <comment :contentId="contentId" ref="childRef" @getInfo="getInfo"></comment>
        </view>

    </view>
    <!-- ActionSheet 操作菜单 -->
    <u-action-sheet :actions="actionList" :title="title" :cancelText="'取消'" @select="selectClick" @close="show=false"
        :show="show" round="8rpx"></u-action-sheet>
    <u-toast ref="uToastRef"></u-toast>
</template>
<script setup>
    import Comment from './comment.vue'
    import {
        onLoad,
        onUnload
    } from '@dcloudio/uni-app'
    import {
        defineProps,
        ref,
        onMounted,
        watchEffect,
        computed,
        reactive
    } from 'vue'
    import {
        timeChange
    } from '@/common/date/dateTimeFormat.js'
    import {
        getContentDetail,
        getContentDetailAnonymous,
        deleteContent,
        addReadNum,
        addReadNumAnonymous
    } from '@/common/net/listPage/listPage.js'
    import {
        useUserStore,
    } from '@/store/user.js'
    import {
        useTokenStore
    } from '@/store/token.js'
    import {
        useMainStore
    } from '@/store/index.js'
    const uToastRef = ref(null)
    const userStore = useUserStore()
    const tokenStore = useTokenStore()
    const mainStore = useMainStore()
    const tenantId = tokenStore.tenantId
    const villagePeopleId = computed(() => {
        console.log('-----------peopleInfo')
        console.log(userStore.userInfo)
        return userStore.userInfo.customParam?.peopleInfo?.villagePeopleId || ''
    })
    let actionList = ref([])
    let title = ref('')
    let show = ref(false)
    let keyboard = ref(false);
    let currentInstance = ''
    const childRef = ref();
    let contentArr = reactive([]);
    let videoArr = reactive([]);
    // 接收父组件传入的参数
    const props = defineProps({
        // 是普通列表:list，还是带评论的列表:commentList,还是左边图片的列表 picList
        listType: {
            type: String,
            default: 'list'
        },
        // 内容id
        contentId: {
            type: String,
            required: true,
            default: ''
        }
    });
    let contentInfo = ref({})
    watchEffect(() => {
        console.log('-----------villagePeopleId')
        console.log(villagePeopleId)
        if (props.contentId) {
            getInfo()
            // addReadData()
        }
    })
    // 获取单个数据
    function getInfo() {
        //显示加载中动画
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
        if (mainStore.hasLogin) {
            getContentDetail({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    console.log(res.data);
                    contentInfo.value = res.data;
                    parseVideo()
                    //成功获取数据后隐藏加载动画
                    uni.hideLoading();
                }
            }).catch(() => {
                //成功获取数据后隐藏加载动画
                uni.hideLoading();
            })
        } else {
            getContentDetailAnonymous({
                contentId: props.contentId
            }).then((res) => {
                if (res.success) {
                    console.log(res.data);
                    contentInfo.value = res.data;
                    parseVideo()
                    //成功获取数据后隐藏加载动画
                    uni.hideLoading();
                }
            }).catch(() => {
                //成功获取数据后隐藏加载动画
                uni.hideLoading();
            })
        }

    }
    // 新增一条阅读内容
    async function addReadData() {
        console.log('-------addReadData')
        console.log(mainStore.hasLogin)
        if (mainStore.hasLogin) {
            await addReadNum({
                contentId: props.contentId
            }).finally(() => {
                getInfo()
                console.log('finally')
            })
        } else {
            await addReadNumAnonymous({
                contentId: props.contentId
            }).finally(() => {
                getInfo()
                console.log('finally')
            })
        }
    }

    // 删除
    function handleDelete() {
        title.value = '删除文章'
        show.value = true;
        actionList.value = [{
            name: '删除',
            color: '#C80000',
            fontSize: '20'
        }]
    }
    // 填写评论
    function handleComment() {
        console.log('-----------------childRef')
        console.log(childRef.value)
        childRef.value && childRef.value.handleComment()
    }

    function selectClick(obj) {
        console.log(obj)
        let content = JSON.parse(JSON.stringify(contentInfo.value))
        if (obj.name == '删除') {
            deleteContent({
                contentId: content.contentId,
            }).then((res) => {
                uni.showToast({
                    title: '删除成功!',
                    icon: 'none'
                })
                goBack()
            }).catch((e) => {
                console.log(e)
                uni.showToast({
                    title: e,
                    icon: 'none'
                });
            })
        }
    }

    // 富文本点击查看大图
    function showImg(content) {
        // 富文本
        const richContent = content;
        // 判断含有图片
        if (richContent.indexOf("src") >= 0) {
            const imgs = [];
            richContent.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function(match, capture) {
                imgs.push(capture);
            })
            uni.previewImage({
                current: imgs[0], // 当前显示图片的链接
                urls: imgs
            })
        }
    }
    //处理富文本的数据包含视频
    function parseVideo() {
        const content = contentInfo.value.contentText
        if (typeof content != 'string') {
            //不是HTML字符串格式的暂不处理
            contentArr[0] = content;
            videoArr[0] = null;
            return false;
        }

        //同步解决如果图片太大超出手机显示界面的问题
        let nodes = content.replace(/\<img/g, '<img style="max-width:98%!important;height:auto;"');
        let arr = nodes.split('</video>');
        let reg = /<video([\s\S]*)/g;

        for (let i in arr) {
            var item = arr[i];
            var urlMatch = item.match(/<video[\s\S]*src=\"(.*?)\"/);
            if (urlMatch && urlMatch.length > 1) {
                videoArr[i] = urlMatch[1];
            } else {
                videoArr[i] = null;
            }
            contentArr[i] = item.replace(reg, '');
        }
    }

    function goBack() {
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)
    }
</script>


<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .detail-container {
        width: 750rpx;
        background: #fff;
        height: 100%;

        .main-content {
            padding: 32rpx;

            .title {
                font-size: 36rpx;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #000000;
                line-height: 54rpx;
                display: -webkit-box;
                /* 将容器以弹性盒子形式布局 */
                -webkit-line-clamp: 2;
                /* 限制文本显示为两行 */
                -webkit-box-orient: vertical;
                /* 将弹性盒子的主轴方向设置为垂直方向 */
                overflow: hidden;
                /* 隐藏容器中超出部分的内容 */
                text-overflow: ellipsis;
                /* 超出容器范围的文本显示省略号 */
            }

            .other {
                height: 52rpx;
                font-size: 28rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 52rpx;

                .author {
                    margin-right: 20rpx;
                }
            }

            .content {
                padding: 20rpx 0;
                font-size: 28rpx;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 52rpx;
            }

            .option-btn {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 20rpx;
                width: 100%;
                position: relative;

                .view-info {
                    display: flex;
                    align-items: center;
                }

                .icon-list {
                    display: flex;
                    align-items: baseline;
                    line-height: 50rpx;
                    position: absolute;
                    right: 20rpx;
                    bottom: 0;
                }

                .comment {
                    margin: 0 34rpx 0 10rpx;
                    font-size: 28rpx;
                    color: #999999;
                }

                .like {
                    margin-left: 10rpx;
                    font-size: 28rpx;
                    color: #999999;
                }
            }

        }

        .split-line {
            width: 100%;
            height: 20rpx;
            background: #F9F9F9;
        }

        .comment-content {
            padding: 20rpx 32rpx;

        }
    }
</style>