// import store from '@/store/index.js';	//vuex
import Platform from '@/common/platform/ePlatform.js'
import { isDef, isObj } from '@/common/uniUtils.js'
import { refreshToken } from '@/common/api.js'
import { isStrEmpty } from '@/common/uniUtils.js'
import { config } from '@/config/config.js' //appCode,clientId,BASEURL
import { useTokenStore } from '@/store/token.js'
import { useMainStore } from '@/store/index.js'
import { useTabBarStore } from '@/store/tabbar.js'
import { useUserStore } from '@/store/user.js'
import kvStore from '@/common/store/uniKVStore.js'
let hasLogin = kvStore.get('hasLogin', true) //hasLogin是登录成功后在本地存储的登录标识
hasLogin = Boolean(Number(hasLogin)) //返回布尔值

let tokenInfo = kvStore.get('tokenInfo', true, 'OBJ')

let server_url = config.BASEURL
let token = ''
let tokenType = ''
export let imageUrl = server_url + '/auth/captcha'
var tokenStore
var mainStore
var userStore
var tabBarStore
export async function request(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
        tokenStore = useTokenStore()
        mainStore = useMainStore()
		userStore = useUserStore()
		tabBarStore = useTabBarStore()
        options.url = `${server_url}${options.url}`
        if (
          isDef(options.params) &&
          isDef(options.params.uniContentType) &&
          options.params.uniContentType == 'json'
        ) {
          options.header = {
            clientId: config.clientId,
            'Content-Type': 'application/json;charset=UTF-8',
            scope: Platform,
            ...options.header,
          }
          delete options.params.uniContentType
        } else {
          options.header = {
            clientId: config.clientId,
            'Content-Type': 'application/x-www-form-urlencoded',
            scope: Platform,
            ...options.header,
          }
        }
        if (isDef(options.params)) {
          options.data = options.params
        }
        if (mainStore.hasLogin) {
          tokenStore.value && (token = tokenStore.value)
          tokenStore.tokenType && (tokenType = tokenStore.tokenType)
          options.header['Authorization'] = `${tokenType} ${token}`
        }
        if (!isStrEmpty(mainStore.jPushregisterId)) {
          options.header['jPushregisterId'] = mainStore.jPushregisterId
        }
        // #ifdef APP-PLUS
          let plat = uni.getEnterOptionsSync()
          options.header['channel'] = plat?.channel;
		    // #endif
        options.sslVerify = false
        resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if (
          isDef(res.data) &&
          isObj(res.data) &&
          res.data.hasOwnProperty('code')
        ) {
          let code = Number(res.data.code)
          switch (code) {
            case 1:
              resolved_1(res.data)
              break
            case 0:
              resolved_1(res.data)
              break
			case 200:
			  resolved_1(res.data)
			  break  
            case 402:
              rejected_1(res.data.message)
              break
            case 401: //todo 定义token失效的代码,刷新token
			  rejected_1(res.data.message)
			  break
            default:
              rejected_1(res.data.message)
              break
          }
        } else if (!isDef(res.data)) {
          rejected_1('返回数据异常')
        } else if (isObj(res.data)) {
          //res.data.error = unauthorized 表示401
          if (res.data.error == 'invalid_token') {
            let isTokenInvalid = kvStore.get('isTokenInvalid', true) // token是否已经失效
            isTokenInvalid = Boolean(Number(isTokenInvalid)) //返回布尔值
            if (!isTokenInvalid) {
			  kvStore.set('isTokenInvalid', 1, true)
			  uni.showToast({
				title: '登录信息已失效，请重新登录',
				icon: 'none',
				success: () => {
					mainStore.logout()
					tabBarStore.logout()
					userStore.logout()
					tokenStore.clearAuthInfo()
					kvStore.set('hasLogin', 0, true)
					setTimeout(() => {
						  // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
					}, 2000)
				},
			  })
            }
          }
          resolved_1(res.data)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.request(options_1)
    })
  } catch (err_1) {
	return Promise.reject(err_1)
  }
}

export async function refresh(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
        tokenStore = useTokenStore()
        mainStore = useMainStore()
        options.url = `${server_url}${options.url}`
        options.url = `${server_url}${options.url}`
        options.data = options.params
        options.header = {
          clientId: config.clientId,
          'Content-Type': 'application/x-www-form-urlencoded',
          scope: Platform,
        }
        if (mainStore.hasLogin) {
          tokenStore.value && (token = tokenStore.value)
          tokenStore.tokenType && (tokenType = tokenStore.tokenType)
          options.header['Authorization'] = `${tokenType} ${token}`
        }
        resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if (
          isDef(res.data) &&
          isObj(res.data) &&
          res.data.hasOwnProperty('code')
        ) {
          let code = Number(res.data.code)
          switch (code) {
            case 1:
              resolved_1(res.data)
              break
            case 0:
              resolved_1(res.data)
              break
            case 402:
              rejected_1(res.data.message)
              break
            case 401: // 定义token失效的代码,刷新token
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.refreshToken
                  state.tokenType = r.tokenType
                  state.value = r.value
                  state.expiration = r.expiration
                })
              })
            default:
              rejected_1(res.data.message)
              break
          }
        } else if (!isDef(res.data)) {
          rejected_1('返回数据异常')
        } else if (isObj(res.data)) {
          //res.data.error = unauthorized 表示401
          if (res.data.error == 'unauthorized') {
             // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
          }
          resolved_1(res.data)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.request(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}

export async function uploadFile(options = {}) {
  console.log(options,'ddddddddd')
  try {
    const options_1 = await new Promise((resolved, rejected) => {
        tokenStore = useTokenStore()
        mainStore = useMainStore()
        options.url = `${server_url}${options.url}`
        if (isDef(options.params)) {
          options.filePath = options.params.filePath
          options.name = 'multipartFile'
          options.formData =Object.assign( {
            appCode: config.appCode,
          },{...options.params.formData})
        }
        options.header = {
          clientId: config.clientId,
          scope: Platform,
        }
        if (mainStore.hasLogin) {
          tokenStore.value && (token = tokenStore.value)
          tokenStore.tokenType && (tokenType = tokenStore.tokenType)
          options.header['Authorization'] = `${tokenType} ${token}`
        }
        resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        let result = JSON.parse(res.data)
        if (isDef(result) && isObj(result) && result.hasOwnProperty('code')) {
          let code = Number(result.code)
          switch (code) {
            case 1:
              resolved_1(result)
              break
            case 0:
              resolved_1(result)
              break
            case 402:
              rejected_1(result.message)
              break
            case 401: // 定义token失效的代码,刷新token
              if (options_1.url && options_1.url.endsWith('/auth/login')) {
                rejected_1(result.message)
                break
              }
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.refreshToken
                  state.tokenType = r.tokenType
                  state.value = r.value
                  state.expiration = r.expiration
                })
              })
            default:
              rejected_1(result.message)
              break
          }
        } else if (!isDef(result)) {
          rejected_1('返回数据异常')
        } else if (isObj(result)) {
          //res.data.error = unauthorized 表示401
          if (result.error == 'unauthorized') {
              // #ifdef H5
	uni.reLaunch({
		url: '/pages/login/login?loginType=captcha'
   })
   // #endif
   // #ifndef H5
   uni.reLaunch({
	   url: '/pages/index/index'
   })
   // #endif
          }
          resolved_1(result)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.uploadFile(options_1)
    })
  } catch (err_1) {
    console.log(err_1)
    return Promise.reject(err_1)
  }
}

export async function downloadFile(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
        tokenStore = useTokenStore()
        mainStore = useMainStore()
        options.url = `${server_url}${options.url}`
        options.header = {
          clientId: config.clientId,
          scope: Platform,
        }
        if (mainStore.hasLogin) {
          tokenStore.value && (token = tokenStore.value)
          tokenStore.tokenType && (tokenType = tokenStore.tokenType)
          options.header['Authorization'] = `${tokenType} ${token}`
        }
        resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if(isDef(res) && isObj(res)){
          resolved_1(res)
        } else if (!isDef(res)) {
          rejected_1('返回数据异常')
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.downloadFile(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}
