import {
    request
} from '../request.js'

// 获取农事列表
export function getFarmList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/farm/getFarmPageForLand',
        method: 'POST',
        params,
    })
}
// 获取所有地块列表
export function getLandList(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/land/findListWithTenant',
        method: 'POST',
        params,
    })
}
// 获取能种植的地块列表
// export function getPlanLandList(params) {
//     params['uniContentType'] = 'json'
//     return request({
		
//         url: '/agriculture/plant/getPlantPageByCon',
//         method: 'POST',
//         params,
//     })
// }

// 获取所有作物数据
export function getCropList(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/crop/findCropList',
        method: 'POST',
        params,
    })
}
//新增种植活动
export function addPlant(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/plant/add',
        method: 'POST',
        params,
    })
}
//新增其他活动
export function addFarm(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/farm/add',
        method: 'POST',
        params,
    })
}
//获取所有种植活动
export function getFarmPageByCon(params) {
    params['uniContentType'] = 'json'
    return request({
		
        // url: '/agriculture/plant/findPlantList',
		url: '/agriculture/plant/getPlantPageByCon',
        method: 'POST',
        params,
    })
}
//删除种植活动
export function deletePlant(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/plant/delete',
        method: 'GET',
        params,
    })
}
//删除其他活动
export function deleteAgriculture(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/farm/delete',
        method: 'GET',
        params,
    })
}
//农事管理-种植详情
export function plantDetail(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/plant/selectOne',
        method: 'GET',
        params,
    })
}
//农事管理-其他活动详情
export function farmDetail(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/farm/findById',
        method: 'GET',
        params,
    })
}
//农事管理-种植编辑
export function plantUpdate(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/plant/update',
        method: 'POST',
        params,
    })
}
//农事管理-其他活动编辑
export function farmUpdate(params) {
    params['uniContentType'] = 'json'
    return request({
		
        url: '/agriculture/farm/update',
        method: 'POST',
        params,
    })
}

