<template>
	<view class="main-container">
		<view class="top-container">
			<view class="photo">
				<safe-image :src='guideInfo.photo' width="200rpx" height='200rpx' radius='10rpx'></safe-image>
			</view>
			<view class="people-info">
				<view class="name">
					{{guideInfo.name}}
				</view>
				<view class="address-info">
					<image style="width:24rpx;height:36rpx" :src="VILLAGE_TOUR_LOCATION_ICON" mode="aspectFit">
					</image>
					<view class="address">
						服务区域：{{guideInfo.address}}
					</view>
				</view>
			</view>
		</view>
		<view class="introduce-container">
			<textarea :value="guideInfo.introduce" disabled auto-height />
			<view class="introduce-photo" v-for="(item, index) in introducePhotoList" :key="index">
				<image :src='item' style="width:100%;height: 345rpx;"></image>
			</view>
		</view>
		<view class="submit-container paddingbottom">
			<view class="submit-btn" @click="$u.throttle(phoneCall(), 1000)">
				立即沟通
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue'
	import {
		onLoad, 
		onUnload, 
		onShow,
		onShareAppMessage,
		onShareTimeline
	} from '@dcloudio/uni-app'
	import { findGuideDetail } from '../../api/villageCodeTour/villageCodeTour.js'
	import { VILLAGE_TOUR_LOCATION_ICON } from '@/common/net/staticUrl.js'
	import { hasLogin, objToStr } from './villageCodeTourUnit.js'
	import kvStore from '@/common/store/uniKVStore.js'

	const isLogin = kvStore.get('hasLogin', true)
	const query = ref({})
	
	onLoad((options) => {
		query.value = {
			from: 'villageCodeTour',
			id: options.id,
		}
		if (!hasLogin('villageGuideDetail', query.value, isLogin)) return
		
		if (!!options.id) {
			id.value = options.id
			getGuideInfo()
		}
	})
	//发送给朋友
	onShareAppMessage((from) => {
	    return {
	        title: '向导详情', // 标题
	        path: `/jimo/pages/villageCodeTour/villageGuideDetail?${objToStr(query.value)}`, // 要分享的页面
	    }
	})
	// 分享到朋友圈
	onShareTimeline(() => {
	    return {
	        title: '向导详情',
	        path: `/jimo/pages/villageCodeTour/villageGuideDetail?${objToStr(query.value)}`, // 要分享的页面
	    }
	})
	
	const id = ref('') // 向导id
	const guideInfo = ref({}) // 向导信息
	const introducePhotoList = ref([]) // 个人简介图片
	// 获取向导详情
	function getGuideInfo() {
		let param = {
			id: id.value
		}
		findGuideDetail(param).then(res => {
			if (res.success) {
				guideInfo.value = res.data
				res.data.introducePhoto.split(',').forEach(item => {
					introducePhotoList.value.push(item)
				})
			}
		})
	}
	// 拨打电话
	function phoneCall(id) {
		uni.makePhoneCall({
			phoneNumber: guideInfo.value.phone,
			success: () => {
				console.log('拨打电话成功')
			},
			fail: () => {
				console.error('拨打电话失败')
			},
		})


	}
</script>

<style lang="scss" scoped>
	.top-container {
		margin-bottom: 20rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;
		width: 750rpx;
		height: 257rpx;
		background: #FFFFFF;
		border-radius: 0rpx 0rpx 20rpx 20rpx;

		.photo {
			margin-right: 34rpx;
		}

		.people-info {
			padding-top: 90rpx;
			height: 200rpx;
			box-sizing: border-box;

			.name {
				margin-bottom: 25rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 30rpx;
				color: #33374D;
			}

			.address-info {
				display: flex;
				align-items: center;

				.address {
					margin-left: 10rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
				}
			}
		}
	}

	.introduce-container {
		padding: 20rpx;
		background: linear-gradient(180deg, #FFFFFF 0%, #EFFFF9 3%, #FFFFFF 100%);
		box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
		border-radius: 20rpx;
		.introduce-photo {
			margin-bottom: 20rpx;
		}
		.introduce-photo:first-of-type {
			margin-top: 20rpx;
		}
	}

	.submit-container {
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 3;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 160rpx;
		background-color: #fff;

		.submit-btn {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 670rpx;
			height: 80rpx;
			background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
			border-radius: 48rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 36rpx;
			color: #FFFFFF;
		}
	}

	.paddingbottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
</style>
<style>
	uni-page-body,
	page {
		height: 100%;
		background: #F8F8FA;
	}
</style>