import {
    request
} from '../request.js'
// 获取活动列表
export function getActiveityList(params) {
    params['uniContentType'] = 'json';
    console.log(params)
    if (params.actkey == 'my') {
        delete params.actkey
        return request({
            url: '/user/activityRegistration/registration/findMyActivity',
            method: 'GET',
            params,
        })
    } else {
        delete params.actkey
        delete params.applicantsId
        return request({
            url: '/user/activityManage/manage/findPage',
            method: 'GET',
            params,
        })
    }

}
//我报名的活动
export function getMyActivityList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findMyActivity',
        method: 'GET',
        params,
    })
}
//获取单个活动
export function findOne(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityManage/manage/findInfo',
        method: 'GET',
        params,
    })
}
//获取某个活动的报名表
export function getActivityEntryList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findPage',
        method: 'GET',
        params,
    })
}
//报名活动
export function entryActivity(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/add',
        method: 'POST',
        params,
    })
}
// 修改报名的状态
export function updateStatus(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/update',
        method: 'post',
        params
    })
}

//取消报名
export function cancleActivity(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/delete',
        method: 'GET',
        params,
    })
}

//获取某个活动的签到表
export function getSignListByActivityId(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findSignInList',
        method: 'GET',
        params,
    })
}
//获取某个活动的未签到列表
export function getUnSignListByActivityId(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findUnSignInList',
        method: 'GET',
        params,
    })
}
//某人是否报名某个活动
export function isBaomingActivity(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/isRegistrated',
        method: 'GET',
        params,
    })

}
//某人报名活动的报名状态
export function findRegistrationStatus(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findRegistration',
        method: 'GET',
        params,
    })
}
//查询同意人数
export function findAgreeNum(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/findAgreeNum',
        method: 'GET',
        params,
    })
}
//签到
export function activitySign(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/activityRegistration/registration/singIn',
        method: 'POST',
        params,
    })
}