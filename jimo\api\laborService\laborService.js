import { request } from '@/common/net/request.js';


export class LaborService {
	/**
	 * @description 获取招工分页数据
	 * @param {Object} params
	 */
	static getRecruitWorksPageList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/position/findPageApp',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取招工详情数据
	 * @param {Object} params
	 */
	static getRecruitWorkInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: "/village/labor/position/findOne",
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取招工详情数据
	 * @param {Object} params
	 */
	static getMyRecruitWorksPageList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: "/village/labor/position/findPageMyApp",
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 新增我的招工
	 * @param {Object} params
	 */
	static addMyRecruitWork (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: "/village/labor/position/addApp",
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 修改我的招工
	 * @param {Object} params
	 */
	static updateMyRecruitWork (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/position/updateApp',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 删除我的招工
	 * @param {Object} params
	 */
	static delMyRecruitWork (params) {
		params['uniContentType'] = 'json'
		return request({
		    url:  '/village/labor/position/deleteNewApp' ,
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 添加浏览量
	 * @param {Object} params
	 */
	static addViewNumber (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/position/addNumber',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取找工分页列表
	 * @param {Object} params
	 */
	static getResumePageList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/findResumeByCountyPage',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取我的简历信息
	 * @param {Object} params
	 */
	static getResumeInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/findOne' ,
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 获取我的简历信息
	 * @param {Object} params
	 */
	static getMyResumeInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/findMyResume' ,
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 新增我的简历信息
	 * @param {Object} params
	 */
	static addMyResumeInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/add' ,
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 修改我的简历信息
	 * @param {Object} params
	 */
	static updateMyResumeInfo (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/update',
		    method: 'POST',
			params
		})
	}
	
	/**
	 * @description 新增简历浏览量
	 * @param {Object} params
	 */
	static addResumeViewNumber (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/village/labor/resume/addNumber',
		    method: 'POST',
			params
		})
	}
}