import {
	request
} from '../request.js'

import {
	API_GETLOTTERYINFO_URL,
	API_GETLOTTERYLIMIT_URL,
	API_GETLOTTERYRECORDS_URL,
	API_DOPLAY_URL,
	API_UPDATERECIVEINFO_URL,
	API_FINDRECORDSBYSTAFF_URL,
	API_FINDRECORDSDETAIL_URL
} from '@/common/net/netUrl.js'

// ID查询抽奖信息
export function getLotteryInfoById(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETLOTTERYINFO_URL,
		method: 'GET',
		params,
	})
}

// 获取当前用户当日剩余抽奖次数
export function getLotteryLimit(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETLOTTERYLIMIT_URL,
		method: 'GET',
		params,
	})
}

// 获取中奖滚屏Top100
export function getRecordsTop100(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETLOTTERYRECORDS_URL,
		method: 'GET',
		params,
	})
}

// 抽奖
export function doPlay(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_DOPLAY_URL,
		method: 'GET',
		params,
	})
}

// 填写中奖信息
export function updateRceiveInfo(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_UPDATERECIVEINFO_URL,
		method: 'POST',
		params,
	})
}

// 获取登录人中奖记录
export function getRecordsByStaff(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FINDRECORDSBYSTAFF_URL,
		method: 'GET',
		params,
	})
}

// ID查询中奖记录详情
export function getRecordDetail(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FINDRECORDSDETAIL_URL,
		method: 'GET',
		params,
	})
}