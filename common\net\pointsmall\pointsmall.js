import {
    request
} from '../request.js';


export function findOfPage(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/commodity/findOfPage",
      method: "POST",
      params
    });
  }
  
  
  export function findOne(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/commodity/findOne",
      method: "GET",
      params
    });
  }
  export function findAddressList(){
    return request({
        url: "/village/exchangeAddress/findList",
        method: "GET"
    })
  }

  export function pointBilling(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/user/tbFamilyPointsRecord/record/pointBilling",
      method: "POST",
      params
    });
  }
  export function pointBillingTop(){
    let params = {}
    params['uniContentType'] = 'json'
    return request({
      url: "/user/tbFamilyPointsRecord/record/pointBillingTop",
      method: "POST",
      params
    });
  }
