<!-- 招工详情-我的-->
<template>
    <view class="detail-container">
		<u-navbar title="招工详情" border bgColor="rgba(0, 0, 0, 0)" 
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000">
		</u-navbar>
		
        <view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
            <view class="scrollbar-none">
				<view class="first-view">
					<view class="title">{{ dataInfo.positionName }}</view>
					<view class="money">{{ dataInfo.salaryRange }}</view>
					<view class="zhao">
					    <u-image :src="LABOR_ZHAO" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
					    <span class="num">招{{ dataInfo.workerNum }}人 </span>
					</view>
					<view class="zhao">
					    <u-image :src="LABOR_SJ" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
					    <span class="num">{{ dataInfo.deadLine }}截止投递</span>
					</view>
					<view class="contact-person">联系人：{{ dataInfo.contacts }}</view>
				</view>
				<u-line></u-line>
				<view class="content">
				    <view class="detail-title">岗位描述</view>
				    <view class="detail-content">
				        {{ dataInfo.positionDescription }}
				    </view>
				</view>
				<u-line></u-line>
				<view class="address">
					<view class="address-title">招工地址</view>
					<view class="address-content">
					  <u-image :src="LABOR_DZ" mode="aspectFill" width="32rpx" height="32rpx"></u-image>
					  <view class="txt">{{ dataInfo.workPlace }}{{ dataInfo.address }}</view>
					</view>
				</view>
				<view class="eye">
					<u-icon name="eye" color="#000" size="26rpx"></u-icon>
					<text>{{ dataInfo.number }}</text>
				</view>
			</view>
        </view>
    
		<view class="operate">
			<view class="btn" @click="operateShow = true">
				管理
			</view>
		</view>
		
		<u-popup :show="operateShow" round="20rpx" mode="bottom" @close="operateShow = false">
			<view class="operate-view">
				<view class="edit-btn" @click="goEdit()">编辑</view>
				<u-line></u-line>
				<view class="del-btn" @click="handleDel()">删除</view>
				<u-line></u-line>
				<view class="close-btn" @click="operateShow = false">取消</view>
				<u-line></u-line>
			</view>
		</u-popup>
		
		<u-popup :show="delShow" round="20rpx" mode="center" closeable
			:safeAreaInsetBottom="false" @close="delShow = false">
			<view class="del-body">
				<text class="tip">确认删除此招工信息吗？</text>
				<view class="btn-view">
					<view class="cancel" @click="delShow = false">取消</view>
					<view class="confirm" @click="handleConfim()">确定</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { LABOR_ZHAO, LABOR_SJ, LABOR_DZ, LABOR_BTNBG } from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 招工详情 */
const dataInfo = ref({})
/** 管理操作 */
const operateShow = ref(false)
/** 删除提示 */
const delShow = ref(false)

onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	if (options && options.positionId) {
		dataInfo.value.positionId = options.positionId
		getDataInfo()
	}
})

onShow(() => {
	getDataInfo()
})

/**
 * @description 获取招工详情
 */
const getDataInfo = () => {
	if (!dataInfo.value.positionId) return
	const params = {
		positionId: dataInfo.value.positionId
	}
	LaborService.getRecruitWorkInfo(params).then(r => {
		if (r.success) {
			dataInfo.value = r.data
		}
	})
}

/**
 * @description 去编辑
 */
const goEdit = () => {
	uni.navigateTo({
		url: `/jimo/pages/laborService/recruitWorkAddEdit?positionId=${dataInfo.value.positionId}`
	})
	operateShow.value = false
}
/**
 * @description 删除
 */
const handleDel = () => {
	operateShow.value = false
	delShow.value = true
}
/**
 * @description 删除确定
 */
const handleConfim = () => {
	const params = [dataInfo.value.positionId]
	LaborService.delMyRecruitWork(params).then(r => {
		if (r.success) {
			uni.showToast({
				title: '删除成功', icon: 'none', duration: 1000,
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1000)
		}
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 25rpx 20rpx;
	background: #F0F7F7;
	
	>view {
		background-color: #fff;
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		padding: 26rpx 20rpx;
	}
	
	.first-view {
		padding: 0 8rpx;
		
		.title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 42rpx;
			color: #000000;
			line-height: 59rpx;
		}
		
		.money {
			margin-top: 19rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 26rpx;
			color: #111111;
			line-height: 37rpx;
		}
		
		.zhao {
			display: flex;
			margin-top: 19rpx;
			
			image {
				width: 32rpx;
				height: 32rpx;
			}
				
			.num {
				margin-left: 10rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 33rpx;
			}
		}
		
		.contact-person {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 42rpx;
			margin-top: 20rpx;
			margin-bottom: 30rpx;
		}
	}

	.content {
		padding: 23rpx 10rpx;

		.detail-title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}

		.detail-content {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 48rpx;
			margin-top: 17rpx;
			white-space: pre-wrap;
		}
	}

	.address {
		padding: 20rpx 6rpx;

		.address-title {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			line-height: 60rpx;
		}
		
		.address-content {
			display: flex;
			margin-top: 21rpx;
			
			.txt {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 33rpx;
				margin-left: 14rpx;
			}
		}
	}
	
	.eye {
		display: flex;
		padding-left: 10rpx;
		margin-top: 56rpx;
		
		text {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 33rpx;
			margin-left: 10rpx;
		}
	}
}

.operate {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	padding-bottom: calc(env(safe-area-inset-bottom));
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 201rpx;
		height: 80rpx;
		border-radius: 48rpx;
		border: 1rpx solid #0BBD88;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #0BBD88;
		float: right;
		margin-right: 20rpx;
	}
}

.operate-view {
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 400;
	font-size: 28rpx;
	color: #000000;
	line-height: 45rpx;
	text-align: center;
	
	.edit-btn {
		padding: 19rpx 0 12rpx;
	}
	.del-btn {
		padding: 28rpx 0 12rpx;
	}
	.close-btn {
		padding: 28rpx 0 12rpx;
	}
}

.del-body {
	width: 650rpx;
	text-align: center;
	padding-top: 83rpx;
	padding-bottom: 52rpx;
		
	.tip {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 32rpx;
		color: #111111;
		line-height: 55rpx;
	}
	
	.btn-view {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;
		
		view {
			width: 276rpx;
			height: 80rpx;
			border-radius: 41rpx;
			line-height: 80rpx;
		}
		
		.cancel {
			background: #FFFFFF;
			border: 2rpx solid #E5E5E5;
			margin-right: 23rpx;
			
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #555555;
		}
		
		.confirm {
			background: #0CBE88;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>