import {
    request
} from '../request.js';


export function statisticsQuota(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsQuota",
      method: "POST",
      params
    });
  }
  
  
  export function statisticsMission(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsMissionByStatus",
      method: "POST",
      params
    });
  }
  export function gridOrg(){
    return request({
        url: "/village/grid/gridOrg",
        method: "GET"
      });
  }
  export function statisticsMissionByType(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/statisticsMissionByType",
      method: "POST",
      params
    });
  }
  export function gridCompleteSort(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/gridCompleteSort",
      method: "POST",
      params
    });
  }
  export function findEventNumApp(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/flow/tbFlowApply/findEventNumApp",
      method: "POST",
      params
    });
  }
  export function gridOrgAndMemberApp(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/grid/gridOrgAndMemberApp",
      method: "POST",
      params
    });
  }
