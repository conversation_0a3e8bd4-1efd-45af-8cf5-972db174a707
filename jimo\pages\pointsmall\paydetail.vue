<template>
  <view>
    <view class="orderdetail" v-if="commoditydetail">
      <view class="paystatus u-border-bottom">
        <view class="status">支付成功</view>
        <view class="tip">请尽快到商品兑换点兑换</view>
      </view>
      <view class="gooddetail">
        <view class="img">
          <view class="imgbox">
            <image
              :src="goodDetailImg"
              mode="widthFix"
            ></image>
          </view>
        </view>
        <view class="info">
          <view class="title u-line-2">
            {{ commoditydetail.commodityTitle }}
          </view>
          <view class="priceline"  v-if="orderdetail">
            <text class="price">{{ orderdetail.orderAmount }}</text>
            <text class="label">积分</text>
          </view>
        </view>
      </view>
      <view class="erweima">
        <view class="qrcode" >
      <view v-for="(row, rowI) in modules" :key="rowI" style="display: flex;flex-direction: row;">
        <view v-for="(col, colI) in row" :key="colI">
          <view v-if="col.isBlack" style="width: 8rpx;height: 8rpx;background-color: black;">
            <!-- 黑色码点 -->
          </view>
          <view v-else style="width: 8rpx;height: 8rpx;background-color: white;">
            <!-- 白色码点 -->
          </view>
        </view>
      </view>
    </view>
        <!-- <u-qrcode ref="qr" canvas-id="qrpaydetail" :value="qrCode" :size="qrCodeSize"></u-qrcode> -->
        <!-- <image
          class="ma"
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQAQAAAACoxAthAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QAAd2KE6QAAAAJcEhZcwAAFxEAABcRAcom8z8AAAIzSURBVHja7dtBjsIwDAVQoy5YcoQepUdrj9aj9Ahdsqiawf6JExCjtoFhWHwLjSrIm5XlJE4q4XAICQkJybcR8TiH4Rx/GPHNSZ+nPIKEpIpc4jAdfxVpQlikAwQ5xQEzCUktyTl5lf42ftEvOkvO1ohlJgnJ/xD9NDpSxzc5k0lI3kW0ZqZqOTb7MpmEZAfJMzImYlvh3Qpgt+rCb2MSJyHZJB5e+vxz8pxEkJDUkIcYPCdtIdc+/k5CUkM0IZGWIn3Q6VWwirsVvdWU/Z2FhKSKzLHuaXukIIjWZlgSkpdIsMHISVNoxGlg8bYa1CAh+RSZ87zsq764gZU4uLWaegkkJDVEn665Cxewe23iP8HOol19+UdCcpxYRUPd6/H1kgpgOkVFWpYbXhKS/USjmF57nKIumSAnJyEhqSR4AkRajqnuBXR67/okJCSHiS7hsDvADGuPXSj2sHH3KnfbBBKS4yTuQyXf7ggrCckbSD4VtRbckOremLISdW8SEpLPEXRIMD5Oyp7J2L3G8/rHpgoJyU6CDomXSo0m56Tk5LzPZBKS3aSMeO9oKWBxdXwmIakiOfN8fJOavelim9+fJCGpIZeck4PRLu1bu5SQT6ZXEpIjBDkJ0i/p5L04TZDciCMheYlo+K02P00IZbOXhOQlgtInfsEj3Zy0M4Vf3rEiIfkbgkAmD+n0oUtkimepzydxEpJt4uGvyYf4/guqZXrNSp5vRkhItsjBICEhIfkq8gOBgVJNdgC9OAAAAABJRU5ErkJggg=="
        ></image> -->
        <view class="code" v-if="orderdetail">{{orderdetail.exchangeVote}}</view>
      </view>
    </view>
    <view class="orderdetailbtn" @click="gotoOrderDetail">订单详情</view>
    <view class="blank"></view>
    <view class="bottom u-border-top">
      <view class="btn pjBtn" @click="payOverHandler">完成</view>
      <view class="paddingbottom"></view>
    </view>
  </view>
</template>
  
  <script setup>
import { reactive, ref } from 'vue'
import {
        useTokenStore
    } from '@/store/token.js'
const orderId = ref('')
const commoditydetail = ref(null)
const orderdetail = ref(null)
const qrCode = ref('')
const tokenStore = useTokenStore()
import { onLoad } from '@dcloudio/uni-app'
import { findOne } from "../../api/pointsmall/pointsmall"
import {
  findOrderById,
} from  '../../api/pointsmall/hypermarketOrder'
import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js';
import { loadImage } from '@/common/getImage.js'
const qrCodeSize = '253'
const modules = ref([])
const goodDetailImg = ref('')
onLoad((option) => {
 // qrCode.value = '/jimo/pages/pointsmall/exchangedetail?id='+ option.oid+"&tenantId="+tokenStore.tenantId
  orderId.value = option.oid
  const qr = new UQRCode();
    qr.data = '/jimo/pages/pointsmall/exchangedetail?id='+ option.oid+"&tenantId="+tokenStore.tenantId
    qr.make();
   modules.value = qr.modules;
  getGoodDetail(option.cid)
  getOrderDetail()
})
async function getGoodDetail(commodityId) {
  try {
    const res = await findOne({ id: commodityId })
    if (res.success) {
      let detail = res.data
      commoditydetail.value = detail
	  goodDetailImg.value = await loadImage(detail.commodityPicture.split(',')[0])
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
async function getOrderDetail(){
  try {
    const res = await findOrderById({ id: orderId.value })
    if (res.success) {
      let detail = res.data
      orderdetail.value = detail
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '接口获取失败！',
      icon: 'none',
    })
  }
}
function gotoOrderDetail(){
  uni.redirectTo({
  url: '/jimo/pages/pointsmall/orderdetail?id='+orderId.value
});

}

function prePage(step) {
  let num = step ? 1 + step : 2
  let pages = getCurrentPages()
  let prePage = pages[pages.length - num]
    ? pages[pages.length - num]
    : pages[pages.length - num + 1]
  // #ifdef H5
  return prePage
  // #endif
  return prePage.$vm
}
function payOverHandler() {
  let page = prePage(2)
  const route  = page.route||page.__route__
  if (page&&route.indexOf('jimo/pages/pointsmall/pointsmall')>-1) {
    uni.navigateBack({
      delta: 2,
    })
  }
  else{
    uni.navigateBack({
      delta: 2,
    })
  }
}
</script>
<style>
page {
  background-color: white;
}
</style>
  <style lang="scss" scoped>
.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  //display: flex;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;

  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    flex: 1;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .pjBtn {
    background: #3cc16c;
    color: #fff;
  }
  .graybtn {
    background: #bdbdbd;
    color: #fff;
  }
}
.paddingbottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.blank {
  width: 100%;
  height: 120rpx;
}
.orderdetailbtn {
  height: 80rpx;
  border-radius: 40rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  text-align: center;
  margin: 0 40rpx;
  margin-top: 80rpx;
  width: calc(100% - 80rpx);
  color: #0bbd88;
  border: 1px solid #0bbd88;
}
.orderdetail {
  margin: 20rpx;
  border-radius: 8rpx;
  background-color: white;
  .paystatus {
    padding: 30rpx 25rpx;
    .status {
      font-size: 32rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
      color: #333333;
      line-height: 48rpx;
      margin-bottom: 13rpx;
    }
    .tip {
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      color: #333333;
      line-height: 40rpx;
    }
  }

  .gooddetail {
    margin: 20rpx;
    padding: 23rpx 20rpx;
    display: flex;
    align-items: center;
    background: #f8f8f8;
    border-radius: 10rpx;

    .img {
      min-width: 241rpx;
      height: 185rpx;
      background-color: white;
      border: 0.5px solid #ccc;
      .imgbox {
        margin: 8rpx;
        box-sizing: border-box;

        overflow: hidden;
        position: relative;
        width: calc(100% - 16rpx);
        height: calc(100% - 16rpx);

        image {
          box-sizing: border-box;
          width: 100%;
          left: 0;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    .info {
      padding: 10rpx 20rpx;
      .title {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #333333;
        line-height: 52rpx;
        margin-bottom: 10rpx;
      }
      .priceline {
        display: flex;
        align-items: center;
        .price {
          font-size: 43rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 600;
          color: #666666;
          line-height: 61rpx;
          background: linear-gradient(149deg, #fe823a 0%, #fe4836 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-right: 10rpx;
        }
        .label {
          font-size: 30rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          color: #999999;
          line-height: 42rpx;
        }
      }
    }
  }
  .erweima {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    .ma {
      width: 405rpx;
      height: 405rpx;
      border: 4rpx solid #ffffff;
    }
    .code {
      margin-top: 15rpx;
      margin-bottom: 40rpx;
      font-size: 36rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 600;
      color: #000000;
      line-height: 50rpx;
    }
  }
}
</style>