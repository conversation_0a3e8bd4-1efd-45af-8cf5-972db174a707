<template>
  <view class="container">
              <logoutbtn></logoutbtn>

    <view class="columnList">
      <view class="cover-text">
        <u-navbar
          bgColor="transparent"
          placeholder
          title="巡检企业"
          :autoBack="true"
          leftIconSize="24"
          leftIconColor="#000"
          :title-style="titleStyle"
        >
          <template #right>
            <view class="nav-right-icons">
              <view class="nav-icon" @click="gotoTraining" v-if="!isadmin">
                <u-icon name="calendar-fill" size="24" color="#fef0f0"></u-icon>
              </view>
              <view class="nav-icon" @click="gotoRemind">
                <u-icon name="bell-fill" size="24" color="#fef0f0"></u-icon>
              </view>
            </view>
          </template>
        </u-navbar>
      </view>
      <view class="column-content">
        <view class="bg-img">
          <u-image
            :src="RENRAl_BG"
            mode="aspectFill"
            width="750rpx"
            height="417rpx"
          ></u-image>
        </view>
      </view>
      <view class="mt-search">
        <u-search
          placeholder="请输入企业名称"
          v-model="keyword"
          :inputStyle="inputStyle"
          :showAction="false"
          @search="search"
        ></u-search>
      </view>
      <!-- 企业列表 -->
      <scroll-view class="company-list" scroll-y="true" @scrolltolower="scrolltolower">
        <view class="empty-status" v-if="dataList.length === 0">
          <view class="empty-icon">
            <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
            <view class="no-more">
              <span class="txt">暂无数据</span>
            </view>
          </view>
        </view>
        <template v-else>
          <view class="company-item" v-for="item in dataList" :key="item.enterpriseId" @click="gotoDetail(item.enterpriseId)">
            <view class="content">
              <view class="des">{{ item.enterpriseName }}</view>
              <view class="type-tag">{{ item.enterpriseNature }}</view>
              <view class="bottom">
                <text>成立时间：{{ item.establishDate }}</text>
               
              </view>
            </view>
          </view>
        </template>
        <u-loadmore  :status="loadStatus" v-if="!(loadStatus === 'nomore' && dataList.length === 0)"></u-loadmore>
      </scroll-view>
      <view @click="addCompany">
        <floating-button></floating-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { NO_MORE_IMG, RENRAl_BG } from '@/common/net/staticUrl.js'
import { onShow,onLoad } from '@dcloudio/uni-app'
import { AJYService } from "../../api/anquanjianguan/companyList"
import {
        useUserStore
    } from '@/store/user.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'
    const userStore = useUserStore()
 
const titleStyle = {
  height: '50rpx',
  fontSize: '36rpx',
  fontFamily: 'Source Han Sans CN, Source Han Sans CN',
  color: '#000',
  fontWeight: 600,
  lineHeight: '50rpx',
}
const inputStyle = {
  width: '500rpx',
}
const keyword = ref('')
const dataList = ref([])
const loadStatus = ref('more')
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const loaded = ref(false)
const isadmin = ref(false)
onLoad(() => { 
  isadmin.value = userStore.userInfo.authorityList.find(item=>item.roleId=='town_admin')
})
function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }
  AJYService.getCompanyList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    enterpriseName: keyword.value
  }).then(res => {
    console.log(res)
    if (res.success) {
      total.value = res.data.total
      if (pageNum.value > 1) {
        dataList.value = [...dataList.value, ...res.data.records]
      } else {
        dataList.value = res.data.records
      }
      console
      if (total.value === dataList.value.length) {
        loadStatus.value = 'nomore'
      } else {
        loadStatus.value = 'more'
        pageNum.value = pageNum.value + 1
      }
    } else {
      uni.showToast({
        title: res.message || '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    }
    loaded.value = true
  }).catch((e) => {
    console.log(e)
    uni.showToast({
      title: '查询数据失败',
      icon: 'none',
    })
    loadStatus.value = 'more'
  })
}

function search() {
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
}

function scrolltolower() {
  getList()
}

function gotoDetail(id) {
  uni.navigateTo({
    url: './companyDetail?id=' + id
  })
}

function gotoRemind() {
  if(isadmin.value){
    uni.navigateTo({
      url: '/jimo/anquanjianguan/adminstrator/remindList'
    })
  }
  else{


  uni.navigateTo({
    url: './remindList'
  })
}
}

function gotoTraining() {
  uni.navigateTo({
    url: './trainingList'
  })
}

function addCompany() {
  uni.navigateTo({
    url: './companyAdd'
  })
}

onShow(() => {
  pageNum.value = 1
  dataList.value = []
  total.value = 0
  loaded.value = false
  loadStatus.value = 'more'
  getList()
})

const getStatusText = (status) => {
  switch (status) {
    case 'check':
      return '待巡检'
    case 'rectify':
      return '待整改'
    case 'completed':
      return '已完成'
    default:
      return '未知'
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  background: #fafafa;
  padding-bottom: 20rpx;
  height: 100vh;
  box-sizing: border-box;
  .bg-img {
    z-index: 99;
    width: 100%;
    height: 417rpx;
    position: relative;
    z-index: 1;
  }
  .column-content {
    position: fixed;
    top: 0rpx;
    z-index: 99;
  }
}
.columnList {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}
.cover-text {
  z-index: 100;
  position: fixed;
  top: 0rpx;
  left: 30rpx;
  display: flex;
  flex-direction: column;

  .nav-right-icons {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .nav-icon {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
.company-list {
  position: absolute;
  top: 420rpx;
  left: 0;
  width: 100%;
  padding: 20rpx 0 0 0;
  background: #fafafa;
  height: calc(100vh - 420rpx);
  box-sizing: border-box;
  
}
.mt-search {
  position: absolute;
  left: 0;
  right: 0;
  top: 360rpx;
  z-index: 200;
  padding: 0 32rpx;
  box-sizing: border-box;
  ::v-deep .u-search {
  border-radius: 40rpx !important;
  box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
  background: #fff!important;
}
::v-deep .u-search__content{
  background: #fff!important;
}
::v-deep .u-search__content__input{
  background-color: #fff!important;
}
}
.company-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
  .content {
    position: relative;
    padding: 24rpx;
    .des {
      font-size: 28rpx;
      color: #333;
      line-height: 40rpx;
      margin-bottom: 16rpx;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .type-tag {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 16rpx;
    }
    .bottom {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #999;
      .status-tag {
        margin-left: 20rpx;
        padding: 4rpx 16rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        &.check {
          color: #FF9500;
          background: rgba(255, 149, 0, 0.1);
        }
        &.rectify {
          color: #FF3B30;
          background: rgba(255, 59, 48, 0.1);
        }
        &.completed {
          color: #34C759;
          background: rgba(52, 199, 89, 0.1);
        }
      }
    }
  }
}
.empty-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  .empty-icon {
    text-align: center;
    .no-more {
      margin-top: 20rpx;
      color: #999;
      font-size: 28rpx;
    }
  }
}
</style> 