<template>
  <view class="training-add" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}">
    <u-navbar title="新增活动" bgColor="rgba(0, 0, 0, 0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
      <logoutbtn></logoutbtn>

    <view class="container">
      <view class="form-content">
        <u--form ref="formRef" :model="form" labelPosition="top" :label-width="150" :labelStyle="{fontWeight: 'bold', paddingBottom: '10rpx'}" :rules="rules">
          
          <u-form-item label="活动标题" prop="trainingTitle" required>
            <u--input v-model="form.trainingTitle" placeholder="请输入活动标题" border="none" />
          </u-form-item>

          <u-form-item label="活动开始时间" prop="trainingTrainingStartTime" @click.stop="showStartDatePicker = true" required>
            <u--input v-model="form.trainingTrainingStartTime" placeholder="请选择活动开始时间" border="none" readonly />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>

          <u-form-item label="活动结束时间" prop="trainingTrainingEndTime" @click.stop="showEndDatePicker = true" required>
            <u--input v-model="form.trainingTrainingEndTime" placeholder="请选择活动结束时间" border="none" readonly />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>

          <u-form-item label="活动地点" prop="trainingLocation" required>
            <u--input v-model="form.trainingLocation" placeholder="请输入活动地点" border="none" />
          </u-form-item>

          <u-form-item label="参与企业" prop="enterpriseIds" @click.stop="gotoCompanyList" required>
            <u--input v-model="enterpriseNames" placeholder="请选择参与企业" border="none" readonly />
            <u-icon name="arrow-down"></u-icon>
          </u-form-item>

          <u-form-item label="活动内容" prop="trainingContent">
            <u-textarea v-model="form.trainingContent" placeholder="请输入活动内容描述" border="none" maxlength="500"></u-textarea>
          </u-form-item>
        
          
          <view class="image-section">
            <view class="section-title">活动图片</view>
            <view class="imgcell">
              <template v-if="form.imageUrl">
                <view class="imgitem" v-for="(img, imgIndex) in form.imageUrl.split(',')" :key="imgIndex">
                  <view class="img">
                    <image :src="img"></image>
                  </view>
                  <image class="closebtn" :src="CLOSE_BTN" @click="deleteActivityImage(imgIndex)"></image>
                </view>
              </template>
              <image :src="IMG_ADD" style="width: 80rpx; height: 80rpx" @click="uploadActivityImage" />
            </view>
          </view>
          
        </u--form>
      </view>

      <view class="bottom">
        <view class="button-box">
          <view class="btn bg-gray" @click="goback">取消</view>
          <view class="btn" @click="submitForm">提交</view>
        </view>
      </view>
      
      <!-- 选择器 -->
      <u-datetime-picker closeOnClickOverlay @close="showStartDatePicker = false" :show="showStartDatePicker" @confirm="onStartDateConfirm" format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showStartDatePicker = false" />
      <u-datetime-picker closeOnClickOverlay @close="showEndDatePicker = false" :show="showEndDatePicker" @confirm="onEndDateConfirm" format="YYYY-MM-DD HH:mm:ss" mode="datetime" @cancel="showEndDatePicker = false" />
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onReady, onLoad } from '@dcloudio/uni-app'
import { LIVE_INVENTORY_BG, IMG_ADD, CLOSE_BTN } from '@/common/net/staticUrl.js'
import { API_FILEUPLOAD_URL } from '@/common/net/netUrl.js'
import { uploadFile } from '@/common/net/request.js'
import { TrainingService } from '../../api/anquanjianguan/training.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'

// 企业验证函数
const validateCompany = (rule, value, callback) => {
  if(!value.length){
    callback(new Error('请选择参与企业'))
  }else{
    callback()
  }
}

// 开始时间验证函数
const validateStartTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error('活动开始时间不能为空'))
    return
  }

  if (form.trainingTrainingEndTime) {
    const startTime = new Date(value)
    const endTime = new Date(form.trainingTrainingEndTime)

    if (startTime >= endTime) {
      callback(new Error('活动开始时间不能大于或等于结束时间'))
      return
    }
  }

  callback()
}

// 结束时间验证函数
const validateEndTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error('活动结束时间不能为空'))
    return
  }

  if (form.trainingTrainingStartTime) {
    const startTime = new Date(form.trainingTrainingStartTime)
    const endTime = new Date(value)

    if (endTime <= startTime) {
      callback(new Error('活动结束时间不能小于或等于开始时间'))
      return
    }
  }

  callback()
}

const form = reactive({
  trainingTitle: '',
  trainingContent: '',
  trainingTrainingStartTime: '',
  trainingTrainingEndTime: '',
  trainingLocation: '',
  imageUrl: '',
  enterpriseIds: []
})

const formRef = ref(null)
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)
const isdisabled = ref(false)

// 企业名称显示用
const enterpriseNames = ref('')


const rules = ref({
  trainingTitle: [{ required: true, message: '活动标题不能为空', trigger: ['blur', 'change'] }],
  trainingTrainingStartTime: {
    validator: validateStartTime,
    trigger: ['blur', 'change']
  },
  trainingTrainingEndTime: {
    validator: validateEndTime,
    trigger: ['blur', 'change']
  },
  trainingLocation: [{ required: true, message: '活动地点不能为空', trigger: ['blur', 'change'] }],
  enterpriseIds: {
    validator: validateCompany
  }
})

function goback() {
  uni.navigateBack({ delta: 1 })
}

function submitForm() {
  formRef.value.validate().then(async (valid) => {
    if (isdisabled.value) return
    isdisabled.value = true

    // 使用真实API调用
    TrainingService.addTraining(form).then(res => {
      isdisabled.value = false
      if (res.success) {
        uni.showToast({ title: '提交成功！', icon: 'success' })
        goback()
      } else {
        uni.showToast({ title: res.message || '提交失败', icon: 'none' })
      }
    }).catch(() => {
      isdisabled.value = false
      uni.showToast({ title: '提交失败！',icon: 'none'  })
      goback()
    })
  }).catch(() => {
    uni.showToast({ title: '请完善表单信息', icon: 'none' })
  })
}

// 开始时间确认
function onStartDateConfirm(e) {
  form.trainingTrainingStartTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showStartDatePicker.value = false

  // 如果结束时间已选择，重新验证结束时间
  if (form.trainingTrainingEndTime) {
    formRef.value.validateField('trainingTrainingEndTime')
  }
}

// 结束时间确认
function onEndDateConfirm(e) {
  form.trainingTrainingEndTime = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
  showEndDatePicker.value = false

  // 如果开始时间已选择，重新验证开始时间
  if (form.trainingTrainingStartTime) {
    formRef.value.validateField('trainingTrainingStartTime')
  }
}

// 跳转到企业列表页面
function gotoCompanyList(){
  uni.navigateTo({
    url: '/jimo/anquanjianguan/SMSNotification/columncontent'
  })
}

// 上传活动图片
function uploadActivityImage() {
  uni.chooseImage({
    count: 9 - (form.imageUrl ? form.imageUrl.split(',').length : 0),
    extension: ['.jpg', '.png', '.gif'],
    sizeType: ['original', 'compressed'],
    sourceType: ['camera', 'album'],
    success: async (res) => {
      const tempFilePaths = res.tempFilePaths
      uni.showLoading({ mask: true })
      let list = await uploadFileList(tempFilePaths)
      const arr = form.imageUrl ? form.imageUrl.split(',') : []
      form.imageUrl = [...arr, ...list].join(',')
      console.log(form.imageUrl, '活动图片上传结果')
      uni.hideLoading()
    }
  })
}

// 批量上传文件函数
async function uploadFileList(files) {
  return new Promise(async (resolve, reject) => {
    let filelist = []
    for (let item of files) {
      try {
        let res = await uploadFile({
          url: API_FILEUPLOAD_URL,
          method: 'POST',
          params: { filePath: item },
        })
        if (res.success) {
          let imgdata = res.data.url
          filelist.push(imgdata)
        } else {
          uni.showToast({
            title: res.message,
            icon: 'none',
            duration: 2000,
          })
        }
      } catch (e) {
        uni.showToast({
          title: '上传失败',
          icon: 'none',
          duration: 2000,
        })
      }
    }
    resolve(filelist)
  })
}

// 删除活动图片
function deleteActivityImage(imageIndex) {
  let arr = form.imageUrl.split(',')
  arr.splice(imageIndex, 1)
  form.imageUrl = arr.length ? arr.join(',') : ''
}

onReady(() => {
  formRef.value.setRules(rules)
})

onLoad(()=>{
  uni.$on('selectcompanys', (res) => {
    console.log(res)
    form.enterpriseIds = res.map(item=>item.enterpriseId).join()
    enterpriseNames.value = res.map(item=>item.enterpriseName).join('、')
  })
})
</script>

<style lang="scss" scoped>
.training-add {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 1135rpx;
  background-color: #f8f9fa;
}

.container {
  padding: 20rpx;
   height: calc(100vh - 240rpx);
  .form-content {
    background: linear-gradient(180deg, #FFFFFF 0%, #F5F6F6 100%);
    border-radius: 50rpx 50rpx 0 0;
    padding: 40rpx;
    margin-bottom: 120rpx;
     height: calc(100vh - 240rpx);
    overflow: scroll;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
   
    box-sizing: border-box;
  }
}

.image-section {
  margin-top: 30rpx;
  
  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }
}

.imgcell {
  padding: 20rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  
  .imgitem {
    width: 233rpx;
    height: 179rpx;
    border: 0.5px solid #e2e2e2;
    margin-right: 40rpx;
    margin-bottom: 20rpx;
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;

    .img {
      width: 100%;
      height: 100%;
      
      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .closebtn {
      width: 40rpx;
      height: 40rpx;
      position: absolute;
      top: 0rpx;
      right: 0rpx;
    }
  }
}

.bottom {
  position: fixed;
  width: 100%;
  background: #fff;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 20rpx 38rpx;
  z-index: 100;
  box-sizing: border-box;
  
  .btn {
    line-height: 80rpx;
    border-radius: 40rpx;
    width: 45%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
    background: linear-gradient(135deg, #0CBE88 0%, #3ADB97 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(12, 190, 136, 0.3);
  }
  
  .bg-gray {
    background: #aaa;
    box-shadow: 0 4rpx 12rpx rgba(170, 170, 170, 0.3);
  }
}

.button-box {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
</style>
