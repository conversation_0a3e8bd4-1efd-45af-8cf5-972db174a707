import {
    request
} from '../request.js'

//新增声控记录
export function add(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/voicePublish/add',
        method: "POST",
        params,
    });
}
//获取声控列表
export function findPage(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/voicePublish/findPage',
        method: 'GET',
        params,
    })
}
//获取设备列表
export function findDeviceScope(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/agriculture/voicePublish/findDeviceScope',
        method: 'GET',
        params,
    })
}