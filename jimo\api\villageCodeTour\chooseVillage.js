import { request } from '@/common/net/request.js'
// 获取行政区划信息
export function getRegionInfo(params) {
	console.log(params)
	params['uniContentType'] = 'json'
	return request({
		url: '/user/region/findLowerListWithOpen',
		method: 'POST',
		params,
	})
}
// 获取镇级区划下已开通租户的村
export function getVillageInfo(params) {
	console.log(params)
	params['uniContentType'] = 'json'
	return request({
		url: '/user/tenants/findVillageTenantByTownRegionCode',
		method: 'GET',
		params,
	})
}
// 根据名称查询已开通租户的村
export function getVillageByName(params) {
	params['uniContentType'] = 'json'
	return request({
		url: '/user/region/findLowerListWithQueryNameAPP',
		method: 'POST',
		params,
	})
}