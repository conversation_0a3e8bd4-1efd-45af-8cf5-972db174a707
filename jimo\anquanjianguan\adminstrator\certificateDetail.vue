<template>
  <view class="certificate-detail" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}">
    <u-navbar title="证件详情" bgColor="rgba(0, 0, 0, 0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
    <logoutbtn></logoutbtn>

    <view class="container">
      <view class="detail-content">
        <!-- 企业基本信息 -->
        <view class="section">
          <view class="section-title">企业基本信息</view>
          <view class="info-card">
            <view class="info-row">
              <text class="label">企业名称</text>
              <text class="value">{{ detail.enterpriseName || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="label">企业性质</text>
              <text class="value">{{ detail.enterpriseNature || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="label">企业规模</text>
              <text class="value">{{ detail.enterpriseScale || '暂无' }}</text>
            </view>
            <!-- <view class="info-row">
              <text class="label">涉及重点行业</text>
              <text class="value">{{ industryCategoryLabel || '暂无' }}</text>
            </view> -->
            <view class="info-row">
              <text class="label">所属社区</text>
              <text class="value">{{ detail.communityName || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="label">安监员</text>
              <text class="value">{{ detail.supervisorName || '暂无' }}</text>
            </view>
          </view>
        </view>

        <!-- 证件信息 -->
        <view class="section">
          <view class="section-title">证件信息</view>
          <view class="info-card">
            <view class="info-row">
              <text class="label">证件名称</text>
              <text class="value">{{ detail.credentialName || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="label">证件状态</text>
              <text class="value status-text" :class="getStatusClass(detail.status)">{{ detail.status || '暂无' }}</text>
            </view>
            <view class="info-row">
              <text class="label">开始日期</text>
              <text class="value">{{ formatDate(detail.startDate) }}</text>
            </view>
            <view class="info-row">
              <text class="label">到期日期</text>
              <text class="value">{{ formatDate(detail.endDate) }}</text>
            </view>
            <view class="info-row" v-if="detail.overdueDays">
              <text class="label">已到期天数</text>
              <text class="value overdue-days">{{ detail.overdueDays }}天</text>
            </view>
          </view>
        </view>

        <!-- 证件图片 -->
        <view class="section" v-if="detail.credentialImage">
          <view class="section-title">证件图片</view>
          <view class="image-gallery">
            <view class="image-item" v-for="(img, index) in imageList" :key="index" @click="previewImage(index)">
              <image :src="img" mode="aspectFill" class="credential-image"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed} from 'vue'
import {onLoad } from '@dcloudio/uni-app'
import { LIVE_INVENTORY_BG } from '@/common/net/staticUrl.js'
import logoutbtn from '@/jimo/components/logoutbtn.vue'
import { getDicts } from '@/common/net/contacts/contacts.js'

const detail = ref({})
const industryCategoryLabel = ref('')

// 计算属性：处理图片列表
const imageList = computed(() => {
  if (!detail.value.credentialImage) return []
  return detail.value.credentialImage.split(',').filter(img => img.trim())
})

onLoad(async(options) => {
      let industryCategoriesresponse = await getDicts('industryCategories')
    let industryCategorieslist = industryCategoriesresponse.data
   
  if (options.detail) {
    try {
      detail.value = JSON.parse(decodeURIComponent(options.detail))
      console.log('证件详情数据:', detail.value)
        if (!detail.value.industryCategory) {
         industryCategoryLabel.value = ''
      }
      else{
        industryCategoryLabel.value = industryCategorieslist.find(item=>item.dictValue==detail.value.industryCategory).dictLabel
      }
    } catch (e) {
      console.error('解析详情数据失败:', e)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none'
      })
    }
  }
})

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return '暂无'
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (e) {
    return dateStr
  }
}

// 获取状态样式类
function getStatusClass(status) {
  if (!status) return ''
  if (status.includes('已过期')) return 'status-expired'
  if (status.includes('即将到期')) return 'status-warning'
  return ''
}

// 预览图片
function previewImage(index) {
  uni.previewImage({
    urls: imageList.value,
    current: index
  })
}
</script>

<style lang="scss" scoped>
.certificate-detail {
  width: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 1135rpx;
  background-color: #f8f9fa;
}

.container {
  padding: 20rpx;
   
  .detail-content {
    background: linear-gradient(180deg, #FFFFFF 0%, #F5F6F6 100%);
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(12, 190, 136, 0.08);
    backdrop-filter: blur(15rpx);
     height: calc(100vh - 120rpx);
box-sizing: border-box;
overflow: auto;
  }
}

.section {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    padding-left: 16rpx;
    border-left: 6rpx solid #0CBE88;
  }
}

.info-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(12, 190, 136, 0.1);
  
  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      flex: 1;
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
      min-width: 200rpx;
      flex-shrink: 0;
    }
    
    .value {
      font-size: 30rpx;
      color: #333;
      font-weight: 600;
      word-break: break-all;
      text-align: right;
      
      &.status-text {
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        font-size: 26rpx;
        
        &.status-expired {
          color: #FF5252;
          background: rgba(255, 82, 82, 0.1);
          border: 1rpx solid rgba(255, 82, 82, 0.3);
        }
        
        &.status-warning {
          color: #FFA726;
          background: rgba(255, 167, 38, 0.1);
          border: 1rpx solid rgba(255, 167, 38, 0.3);
        }
      }
      
      &.overdue-days {
        color: #FF5252;
        font-weight: 700;
      }
    }
  }
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  
  .image-item {
    width: 200rpx;
    height: 150rpx;
    border-radius: 12rpx;
    overflow: hidden;
    border: 2rpx solid rgba(12, 190, 136, 0.1);
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.95);
    }
    
    .credential-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
