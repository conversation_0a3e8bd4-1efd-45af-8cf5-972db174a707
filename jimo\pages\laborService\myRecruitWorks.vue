<!-- 我的招工 -->
<template>
	<view class="my-recruit-works">
		<u-navbar title="我的招工" border bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000" />
		
		<view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
			<u-list height="100%" @scrolltolower="scrolltolower1">
				<view class="work-item" v-for="item in dataList1" :key="item.text"
					@click="goRecruitWorkDetails(item)">
					<view class="first">
						<view>{{ item.positionName }}</view>
						<view>{{ item.salaryRange }}</view>
					</view>
					<view class="second ellipsis-2">{{ item.positionDescription }}</view>
					<view class="third">{{ item.workPlace }}</view>
				</view>
				<u-empty text="暂无招工信息" :icon="NO_MORE_IMG" v-if="!dataList1.length" />
			</u-list>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { NO_MORE_IMG } from '@/common/net/staticUrl.js'
import { LaborService } from '../../api/laborService/laborService.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 招工数据列表 */
const dataList1 = ref([])
const dataOver = ref(false)
/** 招工数据分页 */
const page1 = ref({
	pageNum: 1,
	pageSize: 20
})

onShow(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	page1.value.pageNum = 1
	dataList1.value = []
	dataOver.value = false
	
	getDataList1()
})

/**
 * @description 获取招工分页数据
 */
const getDataList1 = () => {
	uni.showLoading({
		title: '数据加载中...'
	})
	
	const params = {
		...page1.value
	}
	LaborService.getMyRecruitWorksPageList(params).then(r => {
		if (r.success) {
			if (r.data.records.length === 0) {
				dataOver.value = true
				return
			}
			dataList1.value.push(...r.data.records)
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 招工-下拉加载
 */
const scrolltolower1 = () => {
	if (dataOver.value) return
	page1.value.pageNum += 1
	getDataList1()
}
/**
 * @description 招工详情
 * @param {type} item
 */
const goRecruitWorkDetails = (item) => {
	uni.navigateTo({
		url: `/jimo/pages/laborService/myRecruitWorkDetails?positionId=${item.positionId}`
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	background: #F0F7F7;
	padding: 20rpx;
	
	.work-item {
		width: 100%;
		padding: 22rpx 19rpx 28rpx 27rpx;
		margin-bottom: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		position: relative;
		
		.first {
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			font-size: 32rpx;
			line-height: 45rpx;
			display: flex;
			justify-content: space-between;
			
			view:nth-child(1) {
				color: #000000;
			}
			view:nth-child(2) {
				color: #0BBD88;
			}
		}
		
		.second {
			width: 531rpx;
			margin-top: 14rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			line-height: 42rpx;
		}
		
		.second2 {
			display: flex;
			
			view:nth-child(1) {
				width: 77rpx;
				height: 40rpx;
				border-radius: 3rpx;
				border: 1rpx solid #9C9C9C;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 15rpx;
			}
		}
		
		.third {
			width: 100%;
			margin-top: 13rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			line-height: 33rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
}

.ellipsis-2 {
	-webkit-line-clamp: 2;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>