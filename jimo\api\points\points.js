import {
    request
} from '@/common/net/request.js';

//村积分
export function findVillagePoints(params) {
	params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/findVillagePoints',
        method: 'POST',
		params
    })
}
//队组积分排名
export function villaeOrgPointsRank(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/villaeOrgPointsRank',
        method: 'POST',
		 params
    })
}

//家庭积分排名
export function familyPointsRank(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPoints/points/familyPointsRank',
        method: 'POST',
		 params
    })
}

//一个村的积分填报
export function findPointRecord(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/findPointRecordByTenantId',
        method: 'POST',
		 params
    })
}

//新增积分
export function addPoint(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/add',
        method: 'POST',
		params
    })
}

//查询单条积分
export function findInfo(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/user/tbFamilyPointsRecord/record/findInfo',
        method: 'GET',
        params
    })
}

//查询所有的村民
export function findPeople(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/findVillagerPage',
        method: 'POST',
        params
    })
}
// 查询所有党员
export function findPartyPeople(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/findVillagerPage',
        method: 'POST',
        params
    })
}

//查询积分标准列表
export function findPointsStandList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/standard/findPage',
        method: 'POST',
        params
    })
}
//查询党员积分标准列表
export function findPartyPointsStandList(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/partyPoint/standard/findPage',
        method: 'POST',
        params
    })
}
//查询积分标准详情
export function findPointsStandDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/standard/findInfo',
        method: 'POST',
        params
    })
}
//查询党员积分标准详情
export function findPartyPointsStandDetail(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/partyPoint/standard/findInfo',
        method: 'get',
        params
    })
}
//查询积分标准历史详情
export function findPointsStandDetailHistory(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/findStandardInfo',
        method: 'POST',
        params
    })
}
//查询党员积分标准历史详情
export function findPartyPointsStandDetailHistory(params) {
    params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/findStandardInfo',
        method: 'POST',
        params
    })
}
//查询审批人列表
export function findApproverList() {
    return request({
        url: '/village/point/apply/findApproverList',
        method: 'POST',
    })
}
//查询审批人列表（党员）
export function findPartyApproverList() {
    return request({
        url: '/village/point/apply/party/findApproverList',
        method: 'POST',
    })
}

//查询提交人和抄送人
export function findApplicantSendPeople() {
    return request({
        url: '/village/point/apply/findApplicantSendPeople',
        method: 'POST',
    })
}
//查询提交人和抄送人（党员）
export function findPartyApplicantSendPeople() {
    return request({
        url: '/village/point/apply/party/findApplicantSendPeople',
        method: 'POST',
    })
}

//新增申报
export function addApply(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/add',
        method: 'POST',
		params
    })
}
//新增申报
export function addPartyApply(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/add',
        method: 'POST',
		params
    })
}
//积分填报申请列表
export function findPageMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/findPageMobile',
        method: 'POST',
		params
    })
}
//党员积分填报申请列表
export function findPartyPageMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/findPageMobile',
        method: 'POST',
		params
    })
}
//积分申报详情
export function findInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/findInfoMobile',
        method: 'POST',
		params
    })
}
//党员积分申报详情
export function findPartyInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/findInfoMobile',
        method: 'POST',
		params
    })
}
//删除积分申报
export function deleteInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/delete',
        method: 'POST',
		params
    })
}
//删除党员积分申报
export function deletePartyInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/delete',
        method: 'POST',
		params
    })
}
//审批同意拒绝-批量
export function auditInfoMobileList(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/auditList',
        method: 'POST',
		params
    })
}
//审批同意拒绝-批量（党员）
export function auditPartyInfoMobileList(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/auditList',
        method: 'POST',
		params
    })
}
//审批同意拒绝
export function auditInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/audit',
        method: 'POST',
		params
    })
}
//审批同意拒绝（党员）
export function auditPartyInfoMobile(params) {
	 params['uniContentType'] = 'json'
    return request({
        url: '/village/point/apply/party/audit',
        method: 'POST',
		params
    })
}
//判断能否发起申报
export function isAddBtnShow() {
    return request({
        url: '/village/point/apply/isAddBtnShow',
        method: 'POST',
    })
}
//判断能否发起申报（党员）
export function isPartyAddBtnShow() {
    return request({
        url: '/village/point/apply/party/isAddBtnShow',
        method: 'POST',
    })
}

// 查询是否为党员积分申报人
export function isDeclarantInfo() {
    return request({
        url: '/village/point/apply/party/isAddBtnShowByApplicant',
        method: 'POST',
    })
}
//根据regionCode获取队组
export function getTeamListByRegionCode(params) {
    params['uniContentType'] = 'json';
    return request({
        url: '/village/columnContent/findVillageOrgListByRegionCode',
        method: 'POST',
        params
    })
}
// 党组织列表获取
export function findPartyOrgList(params) {
	params['uniContentType'] = 'json'
  return request({
    url: '/village/partyPointRecord/record/partyInfoList',
    method: 'get',
	params
  })
}

// 查询当前是否为抄送人(党员)
export function getIsChaoSongren(){
	return request({
		url:'/village/point/node/party/isSendPeople',
		method:'get'
	})
}
// 查询当前是否为审批人(党员)
export function getIsApprover(){
	return request({
		url:'/village/point/node/party/isApprover',
		method:'get'
	})
}