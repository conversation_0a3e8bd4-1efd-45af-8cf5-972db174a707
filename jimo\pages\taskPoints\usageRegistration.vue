<template>
	<view class="content-boxes">
		<u-datetime-picker ref="datetimePicker" :show="show" v-model="pickerDate" mode="year-month"
			@cancel="show = false"></u-datetime-picker>
		<!-- @confirm="getReportList" -->
		<scroll-view class="rBox" @scrolltolower="scrolltolower" scroll-y="true" show-scrollbar="false">
			<block v-if="rList.length>0">
				<view v-for="(item,index) in rList" :key="index" class="rItem">
					<view class="name"> {{item.pointsUsePeopleName}}</view>
					<view class="center">
						<view class="des">{{item.pointsUsageDesc}}</view>
						<view class="point">{{item.pointsValue}}</view>
					</view>
					<view class="bottom">
						<text> {{item.createDate}}</text><text> {{item.peopleName}} 提交</text>
					</view>
				</view>
			</block>
			<view class="empty-status" v-else>
				<view class="empty-icon">
					<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
						height="150rpx"></u-image>
					<view class="no-more">
						<span class="txt">暂无数据</span>
					</view>
				</view>
			</view>
			<!-- <view class="nomore">没有更多了</view> -->
		</scroll-view>


		<view @click="gotoReport">
			<floating-button> </floating-button>
		</view>
	</view>
</template>

<script setup>
	import {
		NO_MORE_IMG
	} from "@/common/net/staticUrl";
	import {
		onMounted,
		reactive,
		ref,
		computed,
		watch
	} from 'vue'
	import {
		onShow,

	} from '@dcloudio/uni-app'
	import {
		taskVillage,
	} from '@/common/net/my/my.js'

	const datetimePicker = ref(null);
	const show = ref(false)

	onShow(() => {
		getReportList()
	})
	let currentPage = ref(1);
	let loadStatus = ref(false)
	let pageSize = ref(10)
	let total = ref(0)

	function scrolltolower() {
		console.log("scrolltolower...")
		if (loadStatus.value) {
			currentPage.value += 1;
			getReportList();
		} else {

		}

	}
	const rList = ref([])

	function getReportList() {
		//显示加载中动画
		uni.showNavigationBarLoading();
		show.value = false
		let params = {
			"pageSize": pageSize.value,
			"pageNum": currentPage.value,
		}
		taskVillage(params).then(res => {
			//成功获取数据后隐藏加载动画
			uni.hideNavigationBarLoading();
			const {
				success,
				data
			} = res;
			if (success) {
				total.value = data.total;
				if (data.records.length > 0) {
					//如果页数>1，需要拼接返回的数据
					if (currentPage.value > 1) {
						rList.value = [...rList.value, ...data.records];
					} else {
						rList.value = data.records;
					}
					loadStatus.value = data.records.length >= pageSize.value ? true : false
					console.log("rList.value===", rList.value, loadStatus.value)
				} else {

				}
				//成功获取数据后结束下拉刷新
				uni.stopPullDownRefresh();
			} else {}
		}).catch(() => {
			uni.hideNavigationBarLoading();
			uni.stopPullDownRefresh();
		})

	}

	function gotoReport() {
		uni.navigateTo({
			url: '/jimo/pages/taskPoints/report'
		})

	}
</script>

<style lang="scss" scoped>
	.content-boxes {
		background: #F0F7F7;
		//width: 100vw;
		height: calc(100vh - 40rpx);
		padding: 20rpx;
	}

	.top {
		//height: 200rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;

		.left {
			width: 70%;

		}

		.right {
			width: 20%;
			height: 52rpx;
			border-radius: 8rpx;
			font-size: 26rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #6D6D6D;
			line-height: 52rpx;
			display: flex;
			justify-content: space-around;
			height: 68rpx;
			line-height: 68rpx;
			/* width: 83px; */
			background: #ffffff;
			border-radius: 30rpx;
			padding: 0 12rpx;
		}
	}

	.rBox {
		background: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx 0;
		height: calc(100vh - 200rpx);
		width: calc(100% - 40rpx);
		//overflow-y: auto;
		font-family: Source Han Sans CN, Source Han Sans CN;
		color: #000000;
		padding: 20rpx;

		.rItem {
			border-bottom: 1rpx solid #E0E0E0;
			padding: 22rpx 0;

			.name {
				font-size: 32rpx;
				font-weight: 600;
				line-height: 45rpx;
			}

			.center {
				display: flex;
				justify-content: space-between;

				.des {
					width: 80%;
					font-size: 28rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #000000;
					line-height: 40rpx;
					margin: 20rpx 0;
					overflow: hidden;
					text-overflow: ellipsis;
					word-break: break-all;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 10;
					overflow: hidden;
					/* 隐藏容器中超出部分的内容 */
					text-overflow: ellipsis;
					/* 超出容器范围的文本显示省略号 */
					word-break: break-all;

				}

				.point {
					text-align: right;
					width: 20%;
					font-weight: 600;
					color: #FF8641;
					margin: 20rpx 0;
				}
			}

			.bottom {
				font-size: 26rpx;
				font-weight: 400;
				color: #999999;
				line-height: 37rpx;

				:nth-child(1) {
					margin-right: 20rpx;
				}
			}
		}
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #d5d5d6;
					line-height: 37rpx;
				}
			}
		}
	}

	.nomore {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 37rpx;
		font-size: 26rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #999999;
		line-height: 37rpx;
		padding-top: 20rpx;
	}
</style>