<template>
    <view class="container" v-if="title" :style="containerStyle">
        <view class="inner-container">
            <u-navbar bgColor="transparent" placeholder :title="title" leftIconSize="24"
                :title-style="titleStyle" @leftClick="leftClick">
            </u-navbar>
            <view class="tab-list">
                <u-tabs :list="tabList" :current="current" lineWidth="30" lineColor="#49D895"
                    :activeStyle="activeTabStyle" :inactiveStyle="inactiveTabStyle" :itemStyle="itemStyle"
                    @click="handleTabClick" keyName="typeName">
                </u-tabs>
            </view>
			<!-- 酒店民宿\美食指南\本地特产展示检索项 -->
			<template v-if="['hotelshomestays','food','localspecialties'].indexOf(parentKey) > -1">
				<view class="u-flex searchSection">
					<view class="locationItem" @click="selectVillage">
						<u--input :modelValue="villageName" placeholder="位置" border="none" fontSize="28rpx" readonly :placeholderStyle="{color:'#6D6D6D'}"
							:suffixIcon="!canChange ? '' : 'arrow-down-fill'"
							:suffixIconStyle="locationShow ? suffixUpStyle : suffixDownStyle"></u--input>
					</view>
					<view v-if="parentKey != 'localspecialties'" class="priceItem u-m-l-20" @click="priceShow = true">
						<u--input :modelValue="priceEnd && priceEnd!=1500 ? `¥0-${priceEnd}` : ''" placeholder="价格" border="none" fontSize="28rpx" readonly
							:placeholderStyle="{color:'#6D6D6D'}" :color="priceEnd!=1500 ? '#0BBD88': '#6D6D6D'" suffixIcon="arrow-down-fill" 
							:suffixIconStyle="priceShow ? suffixUpStyle : suffixDownStyle"></u--input>
					</view>
					<view v-if="parentKey != 'food'" class="inputItem u-m-l-20">
						<u-search v-model="keyword" placeholder="请输入名称" :showAction="false" :clearabled="false" bgColor="#FFFFFF"
							placeholderColor="#6D6D6D" searchIconColor="#3d3d3d" height="60rpx" @custom="search()" @search="search()">
						</u-search>
					</view>
				</view>
				<template v-if="priceShow">
					<view class="priceMask" @click="priceShow = false"></view>
					<view class="pricePopUp" :style="{top:`calc(${navbarHeight} + 176rpx)`}">
						<view class="cont">
							<view class="u-m-b-20 priceStyle">价格<text>¥0-{{priceValue}}</text></view>
							<u-slider v-model="priceValue" min='0' max='1500' activeColor='#0BBD88' inactiveColor='#F5F5F5' 
								height="6rpx">
								<template #default>
									<view class="sliderStyle">
										<image :src="VILLAGE_TUOR_SLIDER" style="width: 24rpx;height: 24rpx;"></image>
									</view>
								</template>
							</u-slider>
							<view class="priceLabel">
								<text>¥0</text>
								<text>¥1500以上</text>
							</view>
							<view class="u-flex btnCont">
								<view class="btn remake" @click="priceReset">重置</view>
								<view class="u-m-l-30 btn sub" @click="priceSubmit">确定</view>
							</view>
						</view>
					</view>
				</template>
				
			</template>
            <template v-if="dataList.length>0">
                <!-- 旅游路线 -->
                <template v-if="parentKey == 'touristroutes'">
                    <scroll-view class="hotel-scroll-style" scroll-y="true" @scrolltolower="scrolltolower">
                        <view class="overview">
                            <view class="overview-box" v-for="item in dataList" @click="handleClick(item)">
                                <safe-image v-if="item?.img" radius="3" width="707rpx" height="280rpx"
                                    :src="item.img" mode="aspectFill"></safe-image>
                                <view class="content">
                                    {{item?.title}}
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </template>
                <!-- 酒店民宿 -->
                <template v-else-if="parentKey == 'hotelshomestays'">
                    <view class="hotel-boxes hotelHeight">
                        <scroll-view class="scroll-style scrollHeight" scroll-y="true" @scrolltolower="scrolltolower">
                            <view class="hotel-box" v-for="item in dataList" @click="handleClick(item)">
                                <safe-image v-if="item?.img" radius="3" width="200rpx" height="300rpx"
                                    :src="item.img" mode="aspectFill"></safe-image>
                                <view class="content">
                                    <view class="con-title">{{item?.title}}</view>
                                    <view class="desc">{{item.introduction}}</view>
                                    <view class="position">
                                        <view class="image">
                                            <u-image radius="3" width="20rpx" height="24rpx"
                                                src="/jimo/static/images/position.png"
                                                mode="aspectFill"></u-image>
                                        </view>
                                        <view class="text"> {{item.position}}</view>
                                    </view>
                                    <view class="bottom-part">
                                        <view class="price-part">
                                            <view class="money">¥</view>
                                            <view class="price">
                                                {{dealPrice(item.price)}}
                                            </view>
                                            <view class="txt">起</view>
                                        </view>
                                       <u-image v-if="item.isHotline == 1" @tap.stop="handlePhone(item.contentId)" width="52rpx" height="52rpx"
                                            mode="aspectFill"
											:src="VILLAGE_TOUR_PHONE_ICON">
                                        </u-image>
                                    </view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </template>
                <!-- 门票玩乐 -->
                <template v-else-if="parentKey == 'ticketsfun'">
                    <view class="hotel-boxes">
                        <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="scrolltolower">
                            <view class="hotel-box" v-for="item in dataList" @click="handleClick(item)">
                                <safe-image v-if="item?.img" width="200rpx" height="300rpx" radius="3"
                                    :src="item.img" mode="aspectFill"></safe-image>
                                <view class="content">
                                    <view class="con-title">{{item?.title}}</view>
                                    <!-- <view class="desc">{{item.introduction}}</view> -->
                                    <view class="position">
                                        <view class="image">
                                            <u-image radius="3" width="20rpx" height="24rpx"
                                                src="/jimo/static/images/position.png"
                                                mode="aspectFill"></u-image>
                                        </view>
                                        <view class="text"> {{item.position}}</view>
                                    </view>
                                    <view class="bottom-part">
                                        <view class="price-part">
                                            <view class="money">¥</view>
                                            <view class="price">
                                                {{dealPrice(item.price)}}
                                            </view>
                                            <view class="txt">起</view>
                                        </view>
                                        <!-- <u-image @tap.stop="handlePhone(item.hotline)" width="52rpx" height="52rpx"
                                        mode="aspectFill" src="@/jimo/static/images/askFreely/phone.png">
                                    </u-image> -->
                                    </view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </template>
                <!-- 美食指南 -->
                <template v-else-if="parentKey == 'food'">
                    <view class="food-boxes">
                        <scroll-view class="scroll-style scrollHeight foods-scroll" scroll-y="true" @scrolltolower="scrolltolower">
                            <view class="foods-scroll">
                                <view class="food-box" v-for="item in dataList" @click="handleClick(item)">
                                    <safe-image v-if="item?.img" radius="3" width="336rpx" height="336rpx"
                                        :src="item.img" mode="aspectFill"></safe-image>
                                    <view class="con-title">{{item?.title}}</view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </template>
                <!-- 交通通行 -->
                <template v-else-if="parentKey == 'traffic'">
                    <view class="traffic-boxes">
                        <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="scrolltolower">
                            <view class="traffic-box" v-for="item in dataList" @click="handleClick(item)">
                                <view class="con-title">
                                    <view class="title">{{item?.title}}</view>
                                    <view class="price-part">
                                        <view class="money">¥</view>
                                        <view class="price">
                                            {{dealPrice(item.price)}}
                                        </view>
                                        <view class="txt">起</view>
                                    </view>
                                </view>
                                <view class="con-time-part">
                                    <view class="con-time">
                                        <view class="time">开放时间：{{item.openingStartTime}}-{{item.openingEndTime}}
                                        </view>
                                        <view class="desc">{{item.position}}</view>
                                    </view>
                                    <view class="con-tel">
                                        <u-image v-if="item.isHotline == 1" @tap.stop="handlePhone(item.contentId)" width="52rpx" height="52rpx"
                                            mode="aspectFill"
                                            :src="VILLAGE_TOUR_PHONE_ICON">
                                        </u-image>
                                    </view>

                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </template>
                <!-- 本地特产 -->
                <template v-if="parentKey == 'localspecialties'">
                    <view class="local-boxes">
                        <scroll-view class="scroll-style scrollHeight" scroll-y="true" @scrolltolower="scrolltolower">
                            <view class="local-scroll">
                                <view class="local-box" v-for="item in dataList" @click="handleClick(item)">
                                    <safe-image v-if="item?.img" width="338rpx" height="225rpx"
                                        :src="item.img" mode="aspectFill"></safe-image>
                                    <view class="content">
                                        <view class="con-title">{{item?.title}}</view>
                                        <view class="desc">{{item.introduction}}</view>
                                    </view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </template>

            </template>
            <view class="empty-status" v-else>
                <view class="empty-icon">
                    <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx"
                        height="150rpx"></u-image>
                    <view class="no-more">
                        <span class="txt">暂无数据 </span>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
    import {
        reactive,
        ref,
        computed
    } from 'vue'
    import {
        onLoad,
        onShow,
        onShareAppMessage,
        onShareTimeline
    } from '@dcloudio/uni-app'
    import {
        onPullDownRefresh,
        onReachBottom
    } from '@dcloudio/uni-app'
    import {
        findListByRegionCode,
		findOneContentNew,
		findContentPageNew
    } from '../../api/villageCodeTour/villageCodeTour.js'
    import {
        VILLAGE_TOUR_BG,
        NO_MORE_IMG,
		VILLAGE_TUOR_SLIDER,
		VILLAGE_TOUR_PHONE_ICON
    } from '@/common/net/staticUrl.js'
    import {
        useUserStore
    } from '@/store/user.js'
	import { hasLogin, objToStr } from './villageCodeTourUnit.js'
	import kvStore from '@/common/store/uniKVStore.js'
	
	const isLogin = kvStore.get('hasLogin', true)
	const userStore = useUserStore()
    let current = ref(0)
    let title = ref('')
    let parentKey = ref('touristroutes')
    const tourList = {
        touristroutes: '旅游路线',
        hotelshomestays: '酒店民宿',
        ticketsfun: '门票玩乐',
        traffic: '交通通行',
        food: '美食指南',
        localspecialties: '本地特产'
    }
    const containerStyle = {
        backgroundImage: "url(" + VILLAGE_TOUR_BG + ")",
        backgroundSize: '750rpx 358rpx',
        backgroundRepeat: 'no-repeat'
    }
    const titleStyle = {
        height: '50rpx',
        fontSize: '36rpx',
        fontFamily: 'Source Han Sans CN, Source Han Sans CN',
        color: '#000',
        fontWeight: 600,
        lineHeight: '50rpx',
    }

    // tab选中菜单的样式
    const activeTabStyle = {
        color: '#000000',
        fontWeight: '500',
        fontSize: '32rpx',
    }
    // tab未选中菜单的样式
    const inactiveTabStyle = {
        color: '#333333',
        fontWeight: '500',
    }
    // tab菜单item样式
    const itemStyle = {
        paddingLeft: '30rpx',
        paddingRight: '30rpx',
        paddingBottom: '28rpx',
        height: '34rpx',
        fontSize: '28rpx',
        fontFamily: 'Source Han Sans CN, Source Han Sans CN',
        fontWeight: 600,
    }
    let tabList = ref([])
    let activeKey = ref('')
    let dataList = ref([]);
    let currentPage = ref(1);
    let loadStatus = ref(false)
    let pageSize = ref(10)
    let status = ref(false)
	const keyword = ref('')
	const villageName = ref(userStore?.userInfo?.customParam.tenantName) // 当前查看的村庄
	let regionLevel = ref(3) //行政区划级别,默认市
	let regionCode = ref(userStore?.userInfo?.customParam.regionCode)	// 当前查看的村庄的code,默认是当前登录租户的值  //行政区划编码
	let canChange = ref(true) // 是否可以切换村庄，如果是从应用进来的可以切换，从首页的乡村名片进来的不能切，扫码进来的也不能切
	let locationShow = ref(false)
	const suffixUpStyle = ref({
		fontSize: '24rpx',
		color: '#0BBD88',
		transform:'rotate(180deg)',
		transition: 'all .4s',
	})
	const suffixDownStyle = ref({
		fontSize: '24rpx',
		color: '#D8D8D8',
		transform:'rotate(0)',
		transition: 'all .4s',
	})
	let priceShow = ref(false)
	let priceValue = ref(1500)
	let priceEnd = ref(1500)
	const navbarHeight = computed(() => {
		return `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	})
	const query = ref({})
	const scanFlag = ref(false)
    onLoad((options) => {
		let params = options.params ? JSON.parse(options.params) : {}
		// 判断是否登录
		query.value = {
			from: 'villageCodeTour',
			params: JSON.stringify({
				parentKey: params.parentKey,
				canChange: params.canChange,
				villageName: params.villageName,
				regionCode: params.regionCode,
				regionLevel: params.regionLevel,
			})
		}
		if (!hasLogin('list', query.value, isLogin)) return
		
		if (!!options.from && options.from == 'villageCodeTour') {
			// 扫码跳转的会携带参数from=villageCodeTour
		    scanFlag.value = true
		}
		
        if (params.parentKey) {
            parentKey.value = params.parentKey;
            title.value = tourList[params.parentKey]
        }
		canChange.value = params.canChange  //是否可以切换位置
		villageName.value = params.villageName
		regionCode.value = params.regionCode
		regionLevel.value = params.regionLevel
        getTypeList()
		// 监听切换乡村事件
		uni.$on('changeVillage', (data) => {
			regionCode.value = data.regionCode;
			regionLevel.value = data.regionLevel;
			villageName.value = data.villageName
			current.value = 0
			getTypeList()
		});
    })
    onShow(() => {
        console.log(current.value)
        dataList.value = [];
        currentPage.value = 1;
        getTypeList()
    })
	
	function leftClick() {
		console.log('leftClick触发了scanFlag',scanFlag.value);
		const pages = getCurrentPages();
		console.log('currentPages----', pages.length);
	    if (scanFlag.value) {
	        uni.switchTab({
	            url: '/pages/home/<USER>',
	        })
	    } else {
	        const pages = getCurrentPages();
	        if (pages.length === 1) {
	            uni.switchTab({
	                url: '/pages/home/<USER>',
	            })
	        } else {
	            uni.navigateBack();
	        }
	    }
	}
    //获取分类列表
    function getTypeList() {
        findListByRegionCode({
            parentKey: parentKey.value,
			regionLevel: regionLevel.value,
			regionCode: regionCode.value,
        }).then((res) => {
            if (res.success) {
                tabList.value = res.data;
                const firstData = res.data && res.data[current.value];
                activeKey.value = firstData?.typeName || '';
                getList()
            }
        })
    }
    //
	const total = ref(0)
    function getList() {
        //显示加载中动画
        // uni.showNavigationBarLoading();
        findContentPageNew({
            'pageSize': pageSize.value,
            'pageNum': currentPage.value,
            'typeName': activeKey.value,
			'title': keyword.value,
			'priceStart':0,
			'priceEnd':priceValue.value == 1500 ? '' : priceValue.value,
			'regionLevel':regionLevel.value,
			'regionCode':regionCode.value
        }).then((res) => {
            //成功获取数据后隐藏加载动画
            // uni.hideNavigationBarLoading();
            const {
                success,
                data
            } = res;
            if (success) {
                if (data.records.length > 0) {
					total.value = res.data.total;
                    //如果页数>1，需要拼接返回的数据
                    if (currentPage.value > 1) {
                        dataList.value = [...dataList.value, ...data.records];
                    } else {
                        dataList.value = data.records;
                    }
                    loadStatus.value = total.value == dataList.value.length ? false : true
                } else {
                    dataList.value = [];
                    loadStatus.value = false
                }

                //成功获取数据后结束下拉刷新
                uni.stopPullDownRefresh();
            } else {
                dataList.value = [];
                currentPage.value = 1
            }
        }).catch(() => {
            // uni.hideNavigationBarLoading();
            uni.stopPullDownRefresh();
        })
    }
    //下拉刷新
    onPullDownRefresh(() => {
        // 触发下拉刷新时执行
        console.log("下拉触发");
        dataList.value = [];
        currentPage.value = 1;
        loadStatus.value = true;
        getList();
    });
    //下拉监听方法
    onReachBottom(() => {
        console.log('-------------onReachBottom')
        console.log(loadStatus.value)
        console.log('到底了到底了...');
        if (loadStatus.value) {
            currentPage.value += 1;
            getList();
        } else {
            // uni.showToast({
            //     title: '没有更多数据了',
            //     icon: 'none'
            // });
        }
    });
    // scroll-view 下拉加载更多
    function scrollToLower() {
        console.log('scrollToLower...');
        if (loadStatus.value) {
            currentPage.value += 1;
            getList();
        } else {
            status.value = true
            // uni.showToast({
            //     title: '没有更多数据了',
            //     icon: 'none'
            // });
        }
    }
    //发送给朋友
    onShareAppMessage((from) => {
        return {
            title: title.value, // 标题
            path: `/jimo/pages/villageCodeTour/list?${objToStr(query.value)}`, // 要分享的页面
        }
    })
    // 分享到朋友圈
    onShareTimeline(() => {
        return {
            title: title.value,
            path: `/jimo/pages/villageCodeTour/list?${objToStr(query.value)}`, // 要分享的页面
        }
    })

    function dealPrice(value) {
        value = value.replace(/[^\d.]/g, '');
        value = value.replace(/^\./g, '');
        value = value.replace(/\.{2,}/g, '.');
        value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
        value = value.replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        if (value.length > 8) {
            value = value.substring(0, 8);
        }
        if (value > 100000) {
            value = '99999';
        }
        return value;
    }
    //切换tab
    function handleTabClick(item) {
        console.log(item)
        current.value = item.index
        activeKey.value = item.typeName;
        currentPage.value = 1;
        dataList.value = [];
        getList()
    }
    //页面跳转
    function handleClick(item) {
        console.log(item)
        // uni.navigateTo({
        //     //保留当前页面，跳转到应用内的某个页面
        //     url: '/jimo/pages/villageCodeTour/detail?parentKey=' + parentKey.value + '&contentId=' + item
        //         .contentId
        // })
        let pages = getCurrentPages(); //获取所有页面栈实例列表
        let nowPage = pages[pages.length - 1]; //当前页页面实例
        uni.navigateTo({
            url: '/jimo/pages/villageCodeTour/detail?parentKey=' + parentKey.value + '&activeKey=' +
                activeKey.value + '&contentId=' + item
                .contentId,
            events: {
                // workListFn 定义事件在通信页面执行
                workListFn: (data) => {
                    console.log(data)
                    nowPage.$vm.getList();
                }
            }
        })
    }
    //处理图片
    function dealImg(imgs) {
        if (imgs.includes('[')) {
            const imgList = JSON.parse(imgs);
            if (imgList && imgList.length > 0) {
                return imgList[0]
            }
        } else {
            return imgs;
        }
    }
    //唤起手机
    async function handlePhone(value) {
        console.log('-----------value')
        console.log(value)
		let param = {
			contentId : value
		}
		let phoneNum = ''
		const res = await findOneContentNew(param)
		if ( res.success ) {
			phoneNum = res.data.hotline
		} else {
			uni.showToast({
				title: '获取手机号失败',
				icon: 'none',
				duration: 2000
			})
			return false;
		}
        // #ifdef H5
        window.open(`tel:${phoneNum}`)
        // #endif
        //#ifdef MP-WEIXIN

        uni.makePhoneCall({
            phoneNumber: phoneNum, //item.phone,
            success: (res) => {
                console.log(res);
            },
        });
        //#endif
        //#ifdef APP-PLUS
        plus.device.dial(value, true);
        //#endif
    }
	function search(){
		dataList.value = [];
		currentPage.value = 1;
		loadStatus.value = true;
		getList()
	}
	function priceReset(){
		priceValue.value = 1500
	}
	function priceSubmit(){
		priceEnd.value = priceValue.value
		search()
		priceShow.value = false
	}
	function selectVillage(){
		if(!canChange.value) return
		// 获取当前所在市的行政区划code，前4位代表省市，后面补0
		let cityRegionCode = regionCode.value.substr(0, 4) + '00000000'
		uni.navigateTo({
			url: `/jimo/pages/villageCodeTour/chooseVillage?regionCode=${cityRegionCode}`
		})
	}
</script>

<style lang="scss" scoped>
    view {
        box-sizing: border-box;
    }

    .container {
        width: 750rpx;
        height: 100vh;
        background: #F4F8F7;
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

        .scroll-style {
            background-color: #fff;
            height: calc(100vh - 270rpx - env(safe-area-inset-bottom));
        }

        .hotel-scroll-style {
            background-color: #F4F8F7;
            height: calc(100vh - 250rpx - env(safe-area-inset-bottom));
        }
		.scrollHeight{
			background-color: #fff;
			height: 100%;
		}

        .inner-container {
            width: 750rpx;
            overflow-y: auto;

            .tab-list {
                margin: 18rpx;
            }

            .overview {
                margin: 16rpx 20rpx;
                width: 710rpx;
                display: flex;
                flex-direction: column;

                .overview-box {
                    width: 710rpx;
                    height: 369rpx;
                    background: #FFFFFF;
                    box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                    border-radius: 20rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 24rpx;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .content {
                        padding: 6rpx 18rpx;
                        width: 710rpx;
                        height: 48rpx;
                        font-size: 34rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 500;
                        color: #33374D;
                        line-height: 48rpx;
                        // display: -webkit-box;
                        justify-content: center;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        -o-text-overflow: ellipsis;
                    }


                }
            }

            .hotel-boxes {
                margin-top: 16rpx;
                height: calc(100vh - 210rpx - env(safe-area-inset-bottom));
                width: 750rpx;
                // height: 1226rpx;
                background: #FFFFFF;
                box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                border-radius: 10rpx 10rpx 0rpx 0rpx;
                padding: 25rpx;

                .hotel-box {
                    display: flex;
                    margin-bottom: 22rpx;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .content {
                        margin-left: 34rpx;
                        position: relative;

                        .con-title {
                            margin-top: 6rpx;
                            width: 464rpx;
                            height: 84rpx;
                            font-size: 30rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 600;
                            color: #33374D;
                            line-height: 42rpx;
                            word-break: break-all;
                            display: -webkit-box;
                            /* 将容器以弹性盒子形式布局 */
                            -webkit-line-clamp: 2;
                            /* 限制文本显示为两行 */
                            -webkit-box-orient: vertical;
                            /* 将弹性盒子的主轴方向设置为垂直方向 */
                            overflow: hidden;
                            /* 隐藏容器中超出部分的内容 */
                            text-overflow: ellipsis;
                            /* 超出容器范围的文本显示省略号 */
                        }

                        .desc {
                            margin-top: 8rpx;
                            width: 464rpx;
                            height: 84rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 42rpx;
                            word-break: break-all;
                            display: -webkit-box;
                            /* 将容器以弹性盒子形式布局 */
                            -webkit-line-clamp: 2;
                            /* 限制文本显示为两行 */
                            -webkit-box-orient: vertical;
                            /* 将弹性盒子的主轴方向设置为垂直方向 */
                            overflow: hidden;
                            /* 隐藏容器中超出部分的内容 */
                            text-overflow: ellipsis;
                            /* 超出容器范围的文本显示省略号 */
                        }

                        .position {
                            display: flex;
                            width: 464rpx;
                            margin-top: 14rpx;
                            height: 42rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 500;
                            color: #333333;
                            line-height: 42rpx;

                            .image {
                                margin-top: 9rpx;
								::v-deep .u-image__image{
									display: block;
								}
                            }

                            .text {
                                margin-left: 10rpx;
                                width: 432rpx;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                word-break: break-all;
                                white-space: nowrap;
                            }
                        }

                        .bottom-part {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            margin-top: 14rpx;
                            display: flex;
                            justify-content: space-between;
                            width: 464rpx;

                            .price-part {
                                display: flex;

                                .price {
                                    max-width: 165rpx;
                                    height: 50rpx;
                                    font-size: 36rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 600;
                                    color: #666666;
                                    line-height: 50rpx;
                                    background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                                    -webkit-background-clip: text;
                                    -webkit-text-fill-color: transparent;
                                }

                                .money {
                                    height: 50rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 600;
                                    color: #666666;
                                    line-height: 53rpx;
                                    background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                                    -webkit-background-clip: text;
                                    -webkit-text-fill-color: transparent;
                                }

                                .txt {
                                    width: 24rpx;
                                    height: 42rpx;
                                    font-size: 24rpx;
                                    font-family: Source Han Sans CN, Source Han Sans CN;
                                    font-weight: 400;
                                    color: #999999;
                                    line-height: 50rpx;
                                }
                            }

                        }
                    }
                }
            }
			.hotelHeight{
				height: calc(100vh - 312rpx - env(safe-area-inset-bottom));
			}
            .food-boxes {
                margin-top: 16rpx;
                height: calc(100vh - 312rpx - env(safe-area-inset-bottom));
                border-radius: 10rpx 10rpx 0rpx 0rpx;
                padding: 16rpx 26rpx 0;
                background-color: #fff;

                .foods-scroll {
                    display: flex;
                    flex-wrap: wrap;
                    background-color: none;
                }

                .food-box {
                    position: relative;
                    width: 336rpx;
                    height: 336rpx;
                    margin-bottom: 24rpx;

                    &:nth-child(2n) {
                        margin-left: 25rpx;
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &:nth-last-child(2) {
                        margin-bottom: 0;
                    }

                    .con-title {
                        position: absolute;
                        bottom: 16rpx;
                        width: 320rpx;
                        left: 17rpx;
                        height: 42rpx;
                        font-size: 30rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 600;
                        color: #FFFFFF;
                        line-height: 42rpx;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                        white-space: nowrap;
                    }
                }
            }

            .traffic-boxes {
                margin-top: 16rpx;
                width: 750rpx;
                height: calc(100vh - 210rpx - env(safe-area-inset-bottom));
                // height: 1226rpx;
                background: #FFFFFF;
                box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                border-radius: 10rpx 10rpx 0rpx 0rpx;
                padding: 16rpx 24rpx 20rpx 28rpx;

                .traffic-box {
                    width: 698rpx;
                    height: 182rpx;
                    background: #FFFFFF;
                    box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                    border-radius: 10rpx;
                    padding: 16rpx 24rpx 24rpx;

                    .con-title {
                        display: flex;
                        justify-content: space-between;

                        .title {
                            width: 464rpx;
                            height: 42rpx;
                            font-size: 30rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 500;
                            color: #33374D;
                            line-height: 42rpx;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            word-break: break-all;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            /* 超出容器范围的文本显示省略号 */
                        }

                        .price-part {
                            display: flex;

                            .price {
                                max-width: 165rpx;
                                height: 50rpx;
                                font-size: 36rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #666666;
                                line-height: 50rpx;
                                background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .money {
                                height: 50rpx;
                                font-size: 24rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 600;
                                color: #666666;
                                line-height: 53rpx;
                                background: linear-gradient(149deg, #FE823A 0%, #FE4836 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                            }

                            .txt {
                                width: 24rpx;
                                height: 42rpx;
                                font-size: 24rpx;
                                font-family: Source Han Sans CN, Source Han Sans CN;
                                font-weight: 400;
                                color: #999999;
                                line-height: 50rpx;
                            }
                        }

                    }

                    .con-time-part {
                        width: 660rpx;
                        display: flex;
                        justify-content: space-between;

                        .con-time {
                            width: 464rpx;
                            height: 84rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 42rpx;

                            .desc {
                                text-overflow: ellipsis;
                                overflow: hidden;
                                word-break: break-all;
                                white-space: nowrap;
                            }
                        }

                        .con-tel {
                            margin-top: 35rpx;
                        }

                    }
                }
            }

            .local-boxes {
                margin-top: 16rpx;
                width: 750rpx;
                background-color: #fff;
                // box-shadow: 0rpx 2rpx 24rpx 0rpx rgba(0, 0, 0, 0.06);
                height: calc(100vh - 312rpx - env(safe-area-inset-bottom));
                border-radius: 10rpx 10rpx 0rpx 0rpx;
                padding: 16rpx 22rpx;

                .local-scroll {
                    display: flex;
                    flex-wrap: wrap;
                    background-color: none;
                }

                .local-box {
                    width: 340rpx;
                    height: 382rpx;
                    background: #FFFFFF;
                    border-radius: 10rpx;
                    border: 1rpx solid #E0E0E0;
                    margin-bottom: 24rpx;

                    &:nth-child(2n) {
                        margin-left: 24rpx;
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &:nth-last-child(2) {
                        margin-bottom: 0;
                    }

                    .content {
                        padding: 0 14rpx 23rpx;

                        .con-title {
                            width: 310rpx;
                            height: 80rpx;
                            font-size: 28rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #333333;
                            line-height: 40rpx;
                            word-break: break-all;
                            display: -webkit-box;
                            /* 将容器以弹性盒子形式布局 */
                            -webkit-line-clamp: 2;
                            /* 限制文本显示为两行 */
                            -webkit-box-orient: vertical;
                            /* 将弹性盒子的主轴方向设置为垂直方向 */
                            overflow: hidden;
                            /* 隐藏容器中超出部分的内容 */
                            text-overflow: ellipsis;
                            /* 超出容器范围的文本显示省略号 */
                        }

                        .desc {
                            margin-top: 12rpx;
                            width: 297rpx;
                            height: 42rpx;
                            font-size: 24rpx;
                            font-family: Source Han Sans CN, Source Han Sans CN;
                            font-weight: 400;
                            color: #999999;
                            line-height: 42rpx;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            word-break: break-all;
                            white-space: nowrap;
                        }
                    }
                }
            }

        }

        .empty-status {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-top: 50rpx;

            .empty-icon {
                display: flex;
                flex-direction: column;
                justify-content: center;

                .no-more {
                    margin-top: 20rpx;
                    display: flex;
                    justify-content: center;
                    margin-top: 10rpx;

                    .txt {
                        text-align: center;
                        height: 37rpx;
                        font-size: 26rpx;
                        font-family: Source Han Sans CN, Source Han Sans CN;
                        font-weight: 400;
                        color: #333333;
                        line-height: 37rpx;
                    }
                }

            }
        }


    }
	.searchSection{
		padding: 0 20rpx;
		.locationItem{
			background: #fff;
			border-radius: 30rpx;
			height: 60rpx;
			flex:1;
			padding: 0 10rpx 0 16rpx;
			display: flex;
			align-items: center;
			::v-deep .u-input__content__field-wrapper__field{
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		.priceItem{
			background: #fff;
			border-radius: 30rpx;
			height: 60rpx;
			flex:1;
			padding: 0 10rpx 0 16rpx;
			display: flex;
			align-items: center;
		}
		.inputItem{
			flex: 2;
		}
	}
	
	.priceMask{
		position: fixed;
		left: 0;
		right: 0;
		top:0;
		bottom: 0;
		z-index:2;
		width: 750rpx;
		height: 100vh;
		background: rgba($color:#000, $alpha: 0);
	}
	.pricePopUp{
		position: fixed;
		left:0;
		right:0;
		z-index:3;
		.cont{
			padding: 20rpx 40rpx 40rpx;
			background: #fff;
			.priceStyle{
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 30rpx;
				color: #666666;
				line-height: 42rpx;
				text-align: left;
				font-style: normal;
				text{
					color:#0BBD88;
				}
			}
			.btnCont{
				.btn{
					width: 328rpx;
					height: 80rpx;
					border-radius: 40rpx;
					line-height: 80rpx;
					text-align: center;
				}
				.remake{
					background: #F7F7F7;
					color:#555;
				}
				.sub{
					background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
					color:#fff;
				}
			}
		}
	}
	
	.sliderStyle{
		width: 56rpx;
		height: 56rpx;
		border-radius: 50%;
		transform: translate(-100%,-50%);
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 10rpx 0rpx rgba(0,0,0,0.12);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	::v-deep .u-slider-inner{
		padding: 20rpx 0  10rpx !important;
	}
	.priceLabel{
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
		text{
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			line-height: 33rpx;
			font-style: normal;
		}
	}
</style>