<template>
	<view class="standard-container">
		<view class="standard-item" :class="{selected : item.pointStandardId == selectedId}" v-for="(item, index) in standardList" :key="index" @click="selectStandard(item)">
			<view class="standard-name u-line-1">
				{{item.title}}
			</view>
			<view class="standard-desc u-line-2">
				{{item.content}}
			</view>
			<view class="standard-info">
				<view class="points-info">
					<view class="points-type">
						{{item.addSubtractedStateName}}
					</view>
					<view class="points-range">
						{{item.pointScope}}
					</view>
				</view>
				<view class="detail" @click.stop='goStandardDetail(item)'>
					详情
				</view>
			</view>
		</view>
		<!-- 底部加载，三个状态：more、loading、nomore -->
		<u-loadmore :status="loadStatus" v-if='standardList.length > 0'></u-loadmore>
		<view class="empty-status" v-if='standardList.length == 0'>
			<view class="empty-icon">
				<u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
				<view class="no-more">
					<span class="txt">暂无数据</span>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, reactive, nextTick, computed, defineExpose } from 'vue'
	import { onShow, onLoad, onReachBottom } from '@dcloudio/uni-app'
	import { NO_MORE_IMG } from '@/common/net/staticUrl.js'
	import { findPointsStandList,findPartyPointsStandList } from "../../api/points/points"

	const standardList = ref([]) // 评分标准列表
	const currentPage = ref(1)
	const total = ref(0)
	const loadStatus = ref('loading')
	const selectedId = ref('') // 已选择的标准id,用于修改标准时的回显
	let sourceType = ref('1')  //1为家庭 2为党员
	import { useUserStore } from '@/store/user.js'
	const userStore = useUserStore()
	const tenantId = userStore.userInfo.customParam.tenantId
	onLoad((options) => {
		sourceType.value = options?.sourceType || '1'
		if (!!options.selectedId) {
			selectedId.value = options.selectedId
		}
		getStandardData()
	})
	// 获取评分标准列表数据
	function getStandardData() {
		let param = {
			pageNum: currentPage.value,
			pageSize: 10,
			tenantId: tenantId
		}
		uni.showLoading({
			title: '加载中...',
			mask: true,
		})
		let api = sourceType.value == '2' ? findPartyPointsStandList : findPointsStandList
		api(param).then(res => {
			if (res.success && res.data && res.data.records.length > 0) {
				total.value = res.data.total;
				if (currentPage.value > 1) {
					standardList.value = [...standardList.value, ...res.data.records];
				} else {
					standardList.value = res.data.records;
				}
				loadStatus.value = total.value === standardList.value.length ? "nomore" : "more";
			}
			uni.hideLoading()
		}).catch((err) => {
			console.log(err);
			uni.hideLoading()
		})
	}
	//下拉监听方法
	onReachBottom(() => {
		if (loadStatus.value == 'nomore') {
			return;
		}
		currentPage.value = currentPage.value + 1;
		getStandardData();
	});
	// 查看标准详情
	function goStandardDetail(item) {
		uni.navigateTo({
			url: `/jimo/pages/pointsManage/pointsStandardDetail?pointStandardId=${item.pointStandardId}&sourceType=${sourceType.value}`
		})
	}
	// 选择标准
	function selectStandard(item) {
		// 返回参数，需要前一个页面调用uni.$on('selectStandardCallback', (data) => { console.log(data); });
		uni.$emit('selectStandardCallback', item);
		// 返回上一页
		uni.navigateBack({
			delta: 1
		});
	}
</script>

<style lang="scss" scoped>
	.standard-container {
		box-sizing: border-box;
		padding: 20rpx;
		width: 100%;
		height: 100%;

		.standard-item {
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			padding: 30rpx 20rpx;
			margin-bottom: 20rpx;
			width: 100%;
			height: 280rpx;
			background: #FFFFFF;
			border-radius: 8rpx;

			.standard-name {
				width: 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 32rpx;
				color: #000000;
			}

			.standard-desc {
				width: 100%;
				height: 84rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #666666;
			}

			.standard-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 100%;

				.points-info {
					display: flex;
				}
			}

			.points-type {
				margin-right: 10rpx;
				width: 80rpx;
				height: 40rpx;
				line-height: 40rpx;
				background: #FE7B3A;
				border-radius: 18rpx 18rpx 18rpx 0rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #FFFFFF;
				text-align: center;
			}

			.points-range {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 32rpx;
				color: #FF6D1C;
			}

			.detail {
				width: 108rpx;
				height: 50rpx;
				line-height: 50rpx;
				background: #fff;
				color: #0CBE88;
				border: 1px solid #0CBE88;
				border-radius: 30rpx;
				text-align: center;
			}
		}
		.selected {
			background: #e6e6e6;
		}
	}

	.empty-status {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 50rpx 0;

		.empty-icon {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.no-more {
				margin-top: 20rpx;
				display: flex;
				justify-content: center;
				margin-top: 10rpx;

				.txt {
					text-align: center;
					height: 37rpx;
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #d5d5d6;
					line-height: 37rpx;
				}
			}
		}
	}
</style>
<style>
	uni-page-body,
	page {
		background: #F0F7F7;
	}
</style>