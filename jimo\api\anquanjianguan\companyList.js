import { request } from '@/common/net/request.js';

export class AJYService {

	static getCompanyList (params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/enterprise/findPage',
		    method: 'POST',
			params
		})
	}
  static inspectedRecordList(params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectRecord/findPage',
		    method: 'POST',
			params
		})
	}
   static inspectwillRecordList(params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectRecord/findInspectable',
		    method: 'POST',
			params
		})
	}
  static inspectReviewList(params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectReview/findPage',
		    method: 'POST',
			params
		})
	}
  static addInspectRecord(params) {
		params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectRecord/add',
		    method: 'POST',
			params
		})
	}
  static findInspectRecordById(id){
     return request({
      url: '/farm/inspectRecord/findOne/'+id,
      method: 'GET'
    
    })
  }
  static findInspectReviewById(id){
     return request({
      url: '/farm/inspectReview/findOne/'+id,
      method: 'GET'
    
    })
  }
  static submitReview(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/inspectReview/submitReview',
		    method: 'POST',
			params
		})
  }
  static findExpiringCredentials(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/overdueTask/findExpiringCredentials',
		    method: 'POST',
			params
		})
  }
  static uploadYYZZ(params){
    params['uniContentType'] = 'json'
		return request({
		    url: '/farm/farmland/enterprise/ocr/bizLicense',
		    method: 'POST',
			params
		})
  }

}



// 短信通知模拟接口
export function getNotificationList(params) {
  // 生成模拟数据
  const allRecords = Array.from({length: 40}).map((_, i) => {
    return {
      id: i + 1,
      title: i % 2 == 0 ? '系统升级通知' : '巡检任务通知',
      content: i % 2 == 0 ? '根据版本更新迭代，对巡检打卡…' : '各企业巡检任务已下发，请各位…',
      time: `2025-07-${String((i % 28) + 1).padStart(2, '0')} 12:13:10`,
      company: i % 2 == 0 ? '联通产互' : '联通产互、中芯国际'
    }
  });
  // 过滤标题
  let records = allRecords;
  if (params.title) {
    records = records.filter(item => item.title.includes(params.title));
  }
  // 过滤日期
  if (params.date) {
    records = records.filter(item => item.time.startsWith(params.date));
  }
  // 分页
  const pageNum = params.pageNum || 1;
  const pageSize = params.pageSize || 10;
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;
  const pageRecords = records.slice(start, end);
  return Promise.resolve({
    success: true,
    data: {
      total: records.length,
      records: pageRecords
    }
  });
}


