import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_FINDLOWER_URL,
	API_SIGNADD_URL,
	API_CANDIDATE_URL,
	API_SIGNDETAIL_URL,
	API_MINEDETAIL_URL,
	API_SIGNUPDATA_URL,
	API_VOTE_URL,
} from '@/common/net/netUrl.js'

//查询行政区划接口
export function findLower(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FINDLOWER_URL,
		method: 'POST',
		params,
	})
}

//报名接口
export function signAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_SIGNADD_URL,
		method: 'POST',
		params,
	})
}

//列表查询
export function signFind(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_CANDIDATE_URL,
		method: 'POST',
		params,
	})
}

//候选人详情
export function signdetail(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_SIGNDETAIL_URL,
		method: 'POST',
		params,
	})
}

//我的详情
export function minedetail(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_MINEDETAIL_URL,
		method: 'POST',
		params,
	})
}

//修改报名
export function signUpdata(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_SIGNUPDATA_URL,
		method: 'POST',
		params,
	})
}

//投票
export function signVote(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_VOTE_URL,
		method: 'POST',
		params,
	})
}