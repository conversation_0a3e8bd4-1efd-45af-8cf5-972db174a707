<template>
	<view class="detailcon">
		<view class="topinfo">
			<view class="cell">
				<view class="label" v-if="workform.name">汇报人</view>
				<view class="value">{{workform.name}}</view>
			</view>
			<view class="cell">
				<view class="label" v-if="workform.gridName">所属网格</view>
				<view class="value">{{workform.gridName}}</view>
			</view>
			<view class="cell">
				<view class="label">完成进度</view>
				<u-line-progress :percentage="workform.peopleSchedule" height="12" :showText="false"></u-line-progress>
				<text class="percent">{{workform.peopleSchedule}}%</text>
			</view>

			<view class="cell">
				<view class="label" v-if="workform.peopleContent">完成情况</view>
				<view class="value">{{workform.peopleContent}}</view>
			</view>
			<view class="cell">
				<view class="label" v-if="imglist.length > 0">图片</view>
				<view class="value">
					<view class="imgbox" v-for="(item,index) in imglist" :key="item">
						<view class="imgcon">
							<!-- <image @click="previewHandler(index,imglist)" :src="item"></image> -->
							<safe-image width='219rpx' height='165rpx' :imgIndex='index' :src="item"></safe-image>
						</view>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>
<script setup>
	import {
		onLoad,
		onUnload
	} from '@dcloudio/uni-app'
	import {
		reactive,
		ref,
		computed
	} from 'vue'
	import {
		findWorkById
	} from '../../api/worktask/worktask'
	import { useTokenStore } from '@/store/token.js'
	// import { loadImage } from '@/common/getImage.js'
	const tokenStore = useTokenStore()
	const id = ref('')
	const workform = ref({})
	let imglist = ref([])

	onLoad((option) => {
		if (option.id) {
			id.value = option.id
			getList()
		}
		uni.$on('clickImg', (index) => {
			previewPics(index)
		})
	})
	onUnload(() => {
		uni.$off('clickImg')
	})

	function getList() {
				let obj = {
					id: id.value
				}
				findWorkById(obj).then((res) => {
					if (res.success) {
						workform.value = res.data
						imglist.value = workform.value.peoplePicture.length > 0 ? workform.value
							.peoplePicture.split(',') : []
					} else {
						uni.showToast({
							title: res.message,
							icon: 'none',
						})
					}
				})
	}

	function previewHandler(index, imgs) {
		console.log(index, imgs)
		uni.previewImage({
			current: index,
			urls: imgs,
			referrerPolicy: 'origin', // 必填，否则会受到图床的防盗链影响
			success() {
				console.log('预览成功')
			},
		})
	}
	//预览图片
	const previewPics = async (index) => {
		let pics = []
		for(let i=0; i<imglist.value.length;i++) {
			let a = await loadImage(imglist.value[i])
			pics.push(a)
		}
		Promise.all(pics).then((result) => {
		})
		uni.previewImage({
			urls: pics,
		})
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}
</script>
<style>
	page {
		background: #f0f7f7;
	}
</style>
<style scoped lang="scss">
	.blank {
		width: 100%;
		height: 100rpx;
	}

	.cellcomplete {
		margin: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 0 26rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		color: #000000;

		.rightarrow {
			width: 30rpx;
			height: 30rpx;
		}
	}

	.label {
		font-size: 28rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		color: #999999;
		line-height: 40rpx;
		margin-bottom: 12rpx;
	}

	.workgetcon {
		margin: 20rpx;
		background: #ffffff;
		border-radius: 20rpx;
		padding: 23rpx 26rpx;

		.workgetarea {
			position: relative;

			.btngroup {
				position: absolute;
				right: 0;
				top: -46rpx;
				display: flex;
				align-items: center;

				.btn {
					width: 111rpx;
					height: 48rpx;
					border-radius: 31rpx;
					border: 0.5px solid #cccccc;
					line-height: 48rpx;
					text-align: center;
					font-size: 26rpx;
					color: #999999;
					margin-left: 7rpx;
				}

				.greenbtn {
					border: 0.5px solid #0bbd88;
					color: #0bbd88;
				}
			}

			.areacon {
				padding-top: 30rpx;
			}
		}

		.worklabel {
			font-size: 28rpx;

			color: #999999;
			line-height: 40rpx;
		}

		.addWorkget {
			padding: 36rpx 0;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 600;
			color: #0bbd88;
			line-height: 45rpx;

			.plus {
				margin-right: 20rpx;
			}
		}
	}

	.imgcell {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.gridcontent {
		padding: 15rpx 20rpx;
		background: #f8f8f8;
		border-radius: 8rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		font-size: 28rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 400;
		margin-bottom: 16rpx;
		color: #333333;
		line-height: 56rpx;
	}

	.fileitem {
		display: flex;
		flex-wrap: wrap;
		white-space: nowrap;
		position: relative;

		text {
			font-size: 24rpx;
			color: #ccc;
			white-space: nowrap;
			flex: 1;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.closebtn {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.imgitem {
		width: 233rpx;
		height: 179rpx;
		border: 0.5px solid #e2e2e2;
		margin-right: 40rpx;
		margin-bottom: 20rpx;
		position: relative;

		image {
			margin: 7rpx;
			width: 219rpx;
			height: 164rpx;
		}

		.closebtn {
			width: 40rpx;
			height: 40rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
		}
	}

	.arrow {
		top: 50rpx;
		width: 16rpx;
		height: 30rpx;
		position: absolute;
		right: 0;
	}

	.cell {
		padding-top: 25rpx;
		margin-bottom: 15rpx;

		.percent {
			font-size: 28rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #0bbd88;
			min-width: 80rpx;
			text-align: right;
		}

		.value {
			font-size: 32rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			color: #000000;
			line-height: 45rpx;
			display: flex;
			flex-wrap: wrap;

			.progresscon {
				width: 100%;
				display: flex;
				align-items: center;

				::v-deep .u-slider {
					width: calc(100% - 80rpx)
				}

				.percent {
					font-size: 28rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #0bbd88;
					min-width: 80rpx;
					text-align: right;
				}
			}

			.imgbox {
				width: 233rpx;
				height: 179rpx;
				border: 0.5px solid #e2e2e2;
				overflow: hidden;
				margin-right: 20rpx;
				margin-bottom: 20rpx;

				.imgcon {
					margin: 7rpx;
					width: 219rpx;
					height: 165rpx;
					overflow: hidden;
					position: relative;

					image {
						width: 100%;
						height: 100%;
						// position: absolute;
						// top: 50%;
						// transform: translateY(-50%);
					}
				}
			}
		}
	}

	.detailcon {
		width: 100%;

		.topinfo {
			margin: 20rpx;
			background: #ffffff;
			border-radius: 8rpx;
			padding: 23rpx 26rpx;
			position: relative;

			.title {
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
				line-height: 60rpx;
			}

			.tag {
				position: absolute;
				right: 0;
				top: 0;
				width: 128rpx;
				height: 62rpx;
				background: rgba(255, 200, 168, 0.4);
				border-radius: 0rpx 8rpx 0rpx 26rpx;
				line-height: 62rpx;
				font-size: 28rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #ff6d1c;
				text-align: center;
			}

			.wctag {
				background: rgba(180, 242, 183, 0.4);
				color: rgba(1, 189, 93, 1);
			}

			.yqtag {
				background-color: rgba(242, 180, 180, 0.38);
				color: #e90000;
			}

		}
	}
</style>