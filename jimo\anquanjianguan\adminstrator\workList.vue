<template>
    <view class="remind-list" :style="{backgroundImage: `url(${LIVE_INVENTORY_BG})`}" >
      <u-navbar title="工作记录" bgColor="rgba(0,0,0,0)" :autoBack="true" :placeholder="true" leftIconColor="#000" titleStyle="font-size: 36rpx;color: #000" />
      <logoutbtn></logoutbtn>

      <view class="container">
        <u-tabs :list="tabsList" lineWidth="78rpx" :itemStyle="{ width: '50%', height: '86rpx' }" @click="handleTabClick" :current="tabActive" />

        <scroll-view class="scroll-style" scroll-y="true" @scrolltolower="getList">
  
          <view class="empty-status" v-if="loaded && dataList.length == 0">
            <view class="empty-icon">
              <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
              <view class="no-more">
                <span class="txt">暂无数据</span>
              </view>
            </view>
          </view>
          <view class="assetList">
           <template v-if="tabActive==0">
            <view class="list" v-for="item in dataList" :key="item.id" @click="gotoDetail(item)">
              <view class="row row-title">
                <text class="label">企业名称：</text>
                <text class="value">{{ item.enterpriseName }}</text>
              </view>
              <view class="row">
                <text class="label">企业性质：</text>
                <text class="value">{{ item.enterpriseNature }}</text>
              </view>
              <view class="row" >
                <text class="label">巡检时间：</text>
                <text class="value">{{ item.inspectTime }}</text>
              </view>
            
            </view>
          </template>
          <template v-else>
            <view class="list" v-for="item in dataList" :key="item.id" @click="gotoDetail(item)">
              <view class="row row-title">
                <text class="label">企业名称：</text>
                <text class="value">{{ item.enterpriseName }}</text>
                <view class="status-btn" :class="'review-'+item.reviewStatus">{{ getCheckTypeText(item.reviewStatus)  }}</view>
              </view>
              <view class="row">
                <text class="label">企业性质：</text>
                <text class="value">{{ item.enterpriseNature }}</text>
              </view>
            
              <view class="row" >
                <text class="label">隐患时间：</text>
                <text class="value">{{ item.inspectTime }}</text>
              </view>
              <view class="row">
                <text class="label">整改期限：</text>
                <text class="value">{{ item.rectifyDeadline }}</text>
              </view>
            </view>
          </template>
          </view>
     
          <u-loadmore
          :status="loadStatus"
          v-if="!(loadStatus == 'nomore' && dataList.length == 0)"
        ></u-loadmore>
        </scroll-view>
      </view>
    </view>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'
  import { LIVE_INVENTORY_BG,NO_MORE_IMG } from '@/common/net/staticUrl.js'
import { AJYService } from "../../api/anquanjianguan/companyList.js";
import logoutbtn from '@/jimo/components/logoutbtn.vue'
  const tabsList = ref([
    { name: '巡检打卡' },
    { name: '整改复核' }
  ])
   const checkTypeList = ref([
  { text: '重大隐患', value: 'PENDING', color: '#FF8C42' },
  { text: '已完成', value: 'COMPLETED', color: '#2ED573' },
  { text: '已移交执法', value: 'TRANSFERRED', color: '#5352ED' },
  { text: '延期整改', value: 'DELAYED', color: '#FFA726' },
])
  const tabActive = ref(0)
  
  const dataList = ref([])
  const loadStatus = ref('more')
  const total = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(6)
  const loaded = ref(false)
  
  function getList() {
  if (loadStatus.value === 'nomore') return
  if (loadStatus.value === 'more') {
    loadStatus.value = 'loading'
  }
  let api = ''
  let  params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      enterpriseId: enterpriseId.value,
    }
  if(tabActive.value==0){
      api = 'inspectedRecordList'
  
  }
  else{
   api = 'inspectReviewList'
   params.reviewStatus = ''
  
  }
  AJYService[api](params).then(res => {
      console.log(res,'7777777777')
      if (res.success) {
        total.value = res.data.total
        if (pageNum.value > 1) {
          dataList.value = tabActive.value==0?[...dataList.value, ...res.data.records]:[...dataList.value, ...res.data.records.filter(item=>item.reviewStatus!='PENDING')]
        } else {
          dataList.value = tabActive.value==0?res.data.records:res.data.records.filter(item=>item.reviewStatus!='PENDING')
        }
        if (total.value == dataList.value.length) {
          loadStatus.value = 'nomore'
        } else {
          loadStatus.value = 'more'
          pageNum.value = pageNum.value + 1
        }
      } else {
        uni.showToast({
          title: res.message || '查询数据失败',
          icon: 'none',
        })
        loadStatus.value = 'more'
      }
      loaded.value = true
    }).catch(() => {
      uni.showToast({
        title: '查询数据失败',
        icon: 'none',
      })
      loadStatus.value = 'more'
    })
  }
  
function getCheckTypeText(type) {
  if(type=='') return '重大隐患'
  const typeItem = checkTypeList.value.find(item => item.value === type)

  return typeItem ? typeItem.text : '未知'
}
  
  
  function handleTabClick(item) {
    if(tabActive.value==item.index) return;
  
    tabActive.value = item.index
    pageNum.value = 1
    dataList.value = []
    total.value = 0
    loaded.value = false
    loadStatus.value = 'more'
    getList()
  }

  function gotoDetail(item) {
    if (tabActive.value == 0) {
      uni.navigateTo({ url: `./companyCheckDetail?id=${item.recordId}` })
    } else {
      uni.navigateTo({ url: `./companyChangeDetail?id=${item.reviewId}` })
    }
  }
  const enterpriseId = ref('')
  onLoad((options) => {
    enterpriseId.value = options.id
    tabActive.value = options.tab || 0
    getList()
    console.log(NO_MORE_IMG)
  })
  </script>
  
  <style lang="scss" scoped>
  .remind-list {
    width: 100%;
    min-height: 100vh;
    background-repeat: no-repeat;
    background-size: 100% 1135rpx;
  }
  .empty-status {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50rpx 0;
  
    .empty-icon {
      display: flex;
      flex-direction: column;
      justify-content: center;
  
      .no-more {
        margin-top: 20rpx;
        display: flex;
        justify-content: center;
        margin-top: 10rpx;
  
        .txt {
          text-align: center;
          height: 37rpx;
          font-size: 26rpx;
          font-family: PingFang-SC-Regular, PingFang-SC;
          font-weight: 400;
          color: #333333;
          line-height: 37rpx;
        }
      }
    }
  }
  .container {
    ::v-deep.u-tabs {
              padding: 0 25rpx;
              
              .u-tabs__wrapper__nav__line {
                  // width: 78rpx !important;
                  height: 8rpx !important;
                  background: linear-gradient( 117deg, #0CBE88 0%, #3ADB97 100%) !important;
                  border-radius: 5rpx !important;
              }
          }
    .searchCont {
      height: 78rpx;
      border-radius: 39rpx;
      display: flex;
      align-items: center;
      margin: 22rpx 22rpx 0 22rpx;
      box-shadow: 0 4rpx 16rpx rgba(12,190,136,0.08);
      background: #fff;
     
      .u-search {
        flex: 2;
        margin-right: 16rpx!important;
      }
      .status-select{
          flex: 1;
      }
    }
    .filterSection {
      display: flex;
      align-items: center;
      margin: 20rpx 22rpx 0 22rpx;
      .select {
        flex: 1;
        min-width: 178rpx;
        height: 68rpx;
        background: #FFFFFF;
        border-radius: 34rpx;
        margin-right: 8rpx;
        box-shadow: 0 2rpx 8rpx rgba(12,190,136,0.08);
        display: flex;
        align-items: center;
        padding: 0 20rpx;
        .u--input {
          width: 100%;
          font-size: 28rpx;
          color: #333;
        }
        .u-icon {
          margin-left: 8rpx;
          color: #999;
        }
      }
    }
    .scroll-style {
              /* background-color: #fff; */
              height: calc(100vh - 20rpx - env(safe-area-inset-bottom));
    .assetList {
      margin: 30rpx 16rpx 0 16rpx;
      .list {
        background: #fff;
        border-radius: 20rpx;
        box-shadow: 0 2rpx 12rpx rgba(12,190,136,0.06);
        margin-bottom: 24rpx;
        padding: 24rpx 20rpx;
        position: relative;
        overflow: hidden;
        .row-title {
          height: 71rpx;
          background: linear-gradient(270deg, #FFFFFF 0%, #E9FFF6 100%);
          display: flex;
          align-items: center;
          border-radius: 20rpx 20rpx 0 0;
          padding-left: 11rpx;
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 600;
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 0;
          position: relative;
          .label {
            font-size: 28rpx;
            color: #222;
            font-weight: 600;
          }
          .value {
            font-size: 28rpx;
            color: #0CBE88;
            font-weight: 700;
            margin-left: 12rpx;
          }
          .status-btn {
            position: absolute;
            right: 0;
            top: 0;
            background: #909399;
            color: #fff;
            font-size: 26rpx;
            border-radius: 0 20rpx 0 32rpx;
            padding: 0 24rpx 0 29rpx;
            height: 48rpx;
            line-height: 48rpx;
            z-index: 2;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
             &.review-COMPLETED {
              color: #2ED573;
              background: rgba(46, 213, 115, 0.1);
              border: 1rpx solid rgba(46, 213, 115, 0.3);
            }
            &.review-TRANSFERRED {
              color: #5352ED;
              background: rgba(83, 82, 237, 0.1);
              border: 1rpx solid rgba(83, 82, 237, 0.3);
            }
            &.review-DELAYED {
              color: #FFA726;
              background: rgba(255, 167, 38, 0.1);
              border: 1rpx solid rgba(255, 167, 38, 0.3);
            }
            &.review-REJECTED {
              color: #FF5252;
              background: rgba(255, 82, 82, 0.1);
              border: 1rpx solid rgba(255, 82, 82, 0.3);
            }
          }
        }
        .row {
          display: flex;
          align-items: center;
          margin-bottom: 18rpx;
          .label {
            font-size: 28rpx;
            color: #999;
            font-weight: 400;
          }
          .value {
            font-size: 30rpx;
            color: #222;
            margin-left: 12rpx;
            font-weight: 500;
          }
        }
      }
    }
  }
  }
  </style> 