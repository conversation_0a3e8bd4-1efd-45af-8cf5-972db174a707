import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'

import {
	API_COLUMNCONTENT_URL,
	API_READADD_URL,
	API_PAGEQUICK_URL,
} from '@/common/net/netUrl.js'

// 新增一条阅读记录
export function readAdd(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_READADD_URL,
		method: 'POST',
		params,
	})
}

// 查询栏目内容列表
export function pagequick(params) {
	return request({
		url: API_PAGEQUICK_URL,
		method: "GET",
		params,
	});
}

// 查询栏目内容详情
export function columnContent(params) {
	return request({
		url: API_COLUMNCONTENT_URL,
		method: "GET",
		params,
	});
}