import {
	request
} from '../request.js'
import {
	isDef
} from '@/common/uniUtils.js'
import {
	API_ADDRESSBOOK_URL,
	API_VILLAGEPEOPLE_URL,
} from '@/common/net/netUrl.js'

// 信息获取
export function addressBook(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_ADDRESSBOOK_URL,
		method: 'POST',
		params,
	})
}

//详情页面
export function villagePeople(params) {
	return request({
		url: API_VILLAGEPEOPLE_URL,
		method: "GET",
		params,
	});
}

//查询字典获取标签
export function getDicts(dictCode) {
	return request({
		url: "/user/dict/data/list/" + dictCode,
		method: "get"
	});
}