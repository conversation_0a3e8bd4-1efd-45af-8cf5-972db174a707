<!-- 酒店民宿 -->
<template>
	<view class="hotel-homestay">
		<u-navbar :title="pageTitle" bgColor="rgba(0, 0, 0, 0)"
			:autoBack="true" :placeholder="true" leftIconColor='#000' 
			titleStyle="font-size: 36rpx;color: #000;" />
			
		<view class="container" :style="{height: `calc(100% - ${navbarHeight} - env(safe-area-inset-bottom))`}">
			<view class="search-view">
				<u-search :placeholder="`请输入${typeName}名称`" shape="round" :showAction="false"
					v-model="name" @change="handleSearch()"></u-search>
				<uni-data-select
					v-model="type" :localdata="typeData"
					:placeholder="`${typeName2}类型`" clear
					@change="handleSearch">
				</uni-data-select>
			</view>
			<view class="content scrollbar-none">
				<view class="item" v-for="item in dataList" :key="item.typeId"
					@click="goAddServiceInfo(item.contentId)">
					<view class="first">
						<view class="name ellipsis">{{ item.title }}</view>
						<view class="type" v-if="typeData.find(o => o.value === item.typeId)"
							:class="`type${typeData.find(o => o.value === item.typeId).sort}`">
							{{ typeData.find(o => o.value === item.typeId).text }}
						</view>
					</view>
					<view class="info ellipsis-2">{{ item.introduction }}</view>
				</view>
				
				<u-empty text="暂无数据" marginTop="400rpx" :icon="NO_MORE_IMG" v-if="!dataList.length" />
			</view>
			
		</view>
		
		<view @click="goAddServiceType">
			<floating-button />
		</view>
		
		<u-popup :show="show" mode="center" :safeAreaInsetBottom="false" closeable @close="show = false">
			<view class="del-body">
				<text class="tip">
					服务商权限{{ disable === '0' ? '被禁用' : examineType !== '1' ? '未通过' : '' }}，无法发布
				</text>
				<view class="btn-view">
					<view class="cancel" @click="show = false">取消</view>
					<view class="confirm" @click="show = false">确定</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { SreviceProvider, findList } from '../../api/villageCodeTour/villageCodeTour.js'
import { NO_MORE_IMG } from '@/common/net/staticUrl.js'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 类型 */
const parentKey = ref('')
/** 服务商是否禁用 */
const disable = ref('')
/** 服务商审核状态 */
const examineType = ref('')
/** 名称 */
const name = ref('')
/** 类型 */
const type = ref('')
/** 类型数据 */
const typeData = ref([])
/** 数据 */
const dataList = ref([])
/** 分页 */
const page = ref({
	pageNum: 1,
	pageSize: 20
})
/** 显示禁用提示 */
const show = ref(false)

/** 页面标题 */
const pageTitle = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '酒店民宿'
	if (parentKey.value === 'localspecialties') return '本地特产'
	if (parentKey.value === 'food') return '美食指南'
	return ''
})
/** 当前服务类名称 */
const typeName = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '酒店'
	if (parentKey.value === 'localspecialties') return '特产'
	if (parentKey.value === 'food') return '美食'
	return ''
})
/** 当前服务类名称 */
const typeName2 = computed(() => {
	if (parentKey.value === 'hotelshomestays') return '房源'
	if (parentKey.value === 'localspecialties') return '特产'
	if (parentKey.value === 'food') return '美食'
	return ''
})

onLoad((options) => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	parentKey.value = options.parentKey
	disable.value = options.disable
	examineType.value = options.examineType
})

onShow(() => {
	getTypesData()
	handleSearch()
})

/**
 * @description 获取分类列表
 */
const getTypesData = () => {
	const params = {
		parentKey: parentKey.value
	}
	findList(params).then(r => {
		if (r.success) {
			typeData.value = r.data.map((o, index) => {
				return { 
					sort: index,
					text: o.typeName,
					value: o.typeId
				}
			})
		}
	})
}
/**
 * @description 条件查询
 */
const handleSearch = () => {
	dataList.value = []
	page.value.pageNum = 1
	getDataList()
}
/**
 * @description 获取数据列表
 */
const getDataList = () => {
	uni.showLoading({ title: '加载中...', mask: true })
	const params = {
		...page.value,
		title: name.value,
		typeId: type.value,
		parentKey: parentKey.value
	}
	SreviceProvider.getSreviceClassPageList(params).then(r => {
		if (r.success) {
			dataList.value.push(...r.data.records)
		}
	}).finally(() => {
		uni.hideLoading()
	})
}
/**
 * @description 新增服务类数据
 */
const goAddServiceType = () => {
	if (disable.value === '0' || examineType.value !== '1') {
		show.value = true
		return
	}
	uni.navigateTo({
	    url: `/jimo/pages/villageCodeTour/serviceClassEdit?parentKey=${parentKey.value}`
	})
}
/**
 * @description 去服务类数据详情
 */
const goAddServiceInfo = (id) => {
	uni.navigateTo({
	    url: `/jimo/pages/villageCodeTour/serviceClassInfo` + 
		`?parentKey=${parentKey.value}&id=${id}&disable=${disable.value}&examineType=${examineType.value}`
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.hotel-homestay {
	width: 100%;
	height: 100vh;
	background: #F0F7F7;
	
	::v-deep.search-view {
		display: flex;
		justify-content: space-between;
		
		.u-search >view {
			height: 66rpx !important;
			background: #FFFFFF !important;
			border-radius: 33rpx !important;
			
			input {
				background: #FFFFFF !important;
			}
		}
		
		.uni-stat__select {
			width: 191rpx !important;
			margin-left: 20rpx;
			
			.uni-select {
				height: 66rpx !important;
				
				.uni-select__selector {
					left: unset !important;
					right: 0;
					
					.uni-popper__arrow_bottom {
						left: unset !important;
						right: 25%;
					}
				}
			}
		}
	}
	
	.container {
		padding: 0 20rpx;
		
		.item {
			padding: 19rpx 26rpx 24rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			margin-top: 17rpx;
			
			.first {
				display: flex;
				align-items: center;
				
				.name {
					width: 470rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 600;
					font-size: 32rpx;
					color: #000000;
					line-height: 60rpx;
				}
				
				.type {
					width: 136rpx;
					height: 40rpx;
					border-radius: 8rpx;
					margin-left: 52rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 28rpx;
					line-height: 40rpx;
					text-align: center;
					
					&.type0 {
						background: rgba(245, 186, 24, 0.1);
						color: #F5BA18;
					}
					
					&.type1 {
						background: rgba(61, 114, 255, 0.1);
						color: #3D72FF;
					}
					
					&.type2 {
						background: rgba(35, 194, 79, 0.1);
						color: #009116;
					}
					
					&.type3 {
						background: rgba(255, 102, 0, 0.1);
						color: #F5222D;
					}
				}
			}
			
			.info {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 26rpx;
				color: #999999;
				line-height: 37rpx;
			}
		}
	}
}

.del-body {
	width: 650rpx;
	text-align: center;
	padding-top: 83rpx;
	padding-bottom: 52rpx;
		
	.tip {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 32rpx;
		color: #111111;
		line-height: 55rpx;
	}
	
	.btn-view {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;
		
		view {
			width: 276rpx;
			height: 80rpx;
			border-radius: 41rpx;
			line-height: 80rpx;
		}
		
		.cancel {
			background: #FFFFFF;
			border: 2rpx solid #E5E5E5;
			margin-right: 23rpx;
			
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 400;
			font-size: 28rpx;
			color: #555555;
		}
		
		.confirm {
			background: #0CBE88;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}


.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 表示最多显示两行文本 */
  overflow: hidden;
  text-overflow: ellipsis;
}


// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>