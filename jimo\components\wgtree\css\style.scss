.flex_between_center {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.checkbox {
	position: relative;
	margin-left: 10rpx;
	margin-right: 0px;

	.color {
		color: #0BBD88;
		background-color: #0BBD88;
	}

	.txt {
		font-size: 44rpx;
		width: 100%;
		height: 100%;
		display: flex;
		border-radius: 50%;
	}
}

.checkBorder {
	border: 1px solid #ecdee4;
}

.header {
	width: 100%;
	position: fixed;
	background-color: #fff;
	z-index: 9999;

	.title {
		height: 90rpx;
		padding: 0 32rpx;
		line-height: 90rpx;
		font-size: 30rpx;
		background-color: #f5f5f5;
		color: #606064;
	}
}

.iconclass {
	display: inline-block;
	margin: 0 12rpx;
	color: #D0D4DB;
	font-size: 28rpx;
}

.container-list {
	overflow-y: scroll;
	overflow-x: hidden;
	padding-bottom: 160rpx;
	padding-top: 200rpx;

	.common {
		background-color: #fff;
		border-bottom: 1rpx solid #f4f4f4;
		padding-left: 10rpx;

		.content {
			display: flex;
			align-items: center;
			// height: 60rpx;
			width: 100%;
			padding: 15rpx 0;
			position: relative;
			font-size: 32rpx;

			.lable-text {
				padding: 20rpx 0;
				margin-left: 16rpx;
				font-size: 30rpx;
				color: #5b5757;
				width: 500rpx;
				word-break: break-all;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.lefe {
					width: 36rpx;
					height: 36rpx;
					margin-right: 17rpx;
				}

				text {
					flex: 1;
				}
			}

			.right {
				position: absolute;
				right: 30rpx;
				color: #babdc3;
				font-size: 32rpx;
			}

			.userinfo {
				padding: 24rpx 27rpx 12rpx 27rpx;
				display: flex;
				align-items: center;
				width: 100%;

				.avatar {
					width: 96rpx;
					height: 96rpx;
					border-radius: 50%;
					overflow: hidden;
					margin-right: 7rpx;
				}

				.userdesc {
					display: flex;
					align-items: center;
					flex: 1;
					margin-left: 17rpx;
				}

				.phone {
					width: 52rpx;
					height: 52rpx;
					margin-left: 30rpx;
				}

				.username {
					font-size: 26rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #333333;
					line-height: 37rpx;
					margin-bottom: 11rpx;
					flex: 1;
					display: flex;
					align-items: center;

					.tag {
						padding:7rpx;
						margin-top: -60rpx;
						margin-left: 10rpx;

						font-size: 24rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						color: #0BBD88;
						position:relative;
						&::after{
							content: '';
							position: absolute;
							left: 50%;
							top: 50%;
							width: 100%;
							height: 100%;
							transform: translate(-50%,-50%);
							border-radius: 10rpx 10rpx 10rpx 0rpx;
							border: 0.5px solid #7CD2B9;
						}
					}
				}

				.position {
					font-size: 24rpx;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					color: #999999;
					line-height: 33rpx;
				}
			}
		}
	}
}

.active {
	color: #0BBD88 !important;
}

.none {
	color: #666666;
}

.icon-selected {
	color: #0BBD88 !important;
	font-size: 40rpx !important;
}

.icons {
	color: #0BBD88 !important;
	font-size: 40rpx !important;
}

.inline-item {
	display: inline-block
}

.content-item {
	display: flex;
	position: relative;
	align-items: center;
}

.box_sizing {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.btn {
	position: fixed;
	bottom: 0;
	padding: 10px;
	background-color: #fff;
	width: 100%;

	.sureBtn {
		background-color: #0BBD88;
		color: #fff;
	}
}