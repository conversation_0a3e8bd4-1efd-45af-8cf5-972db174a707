<template>
    <view class="content-boxes">
        <view class="tab-box">
            <u-tabs :list="tabList" @click="changeTab" :current="currentTabNum" :itemStyle='itemStyle'
                :activeStyle='activeStyle' lineColor='#0BBD88'></u-tabs>
        </view>
        <view class="top">
            <view class="left" v-if="showSearch">
                <u-search :placeholder="sourceType =='2' ? '请输入党员姓名或门牌号' : '请输入农户姓名或门牌号'" bgColor="#ffffff" v-model="peopleName" :showAction="false"
                    :clearabled="false" @search="handleResetList" @focus='focusSearch'> </u-search>
            </view>
			<view class="left">
				<uni-data-select
					v-model="peopleTag"
					placeholder="请选择" 
					:localdata="peopleTagData"
					clear
					@change="handleSelect('label')">
				</uni-data-select>
			</view>
			<view class="left" v-if="sourceType == '1'">
				<uni-data-select
					v-model="teamId"
					placeholder="请选择" 
					:localdata="teamData"
					clear
					@change="handleSelect('label')">
				</uni-data-select>
			</view>
			<view class="left" v-else>
				<view class="choose-group" @click="showGroup = true">
					<view class="holder u-line-1">
						{{groupName}}
					</view>
					<view v-if="teamId"  @click.stop="clearGroup">
						<uni-icons type="clear" color="#c0c4cc" size="22" />
					</view>
					<up-icon v-else name="arrow-down-fill"></up-icon>
				</view>
			</view>
            <view class="right" @click="openDatePicker">
                {{ queryDate }}
                <u-icon name="arrow-down" color="#6D6D6D" size="32rpx"></u-icon>
				<u-datetime-picker ref="datetimePicker" :show="show" v-if="show" 
					v-model="pickerDate" mode="year-month" @cancel="show = false" />
            </view>
        </view>
		<view class="top" v-if="showManagerBtn">
			<view class="text-btn" @click="checkFlag = !checkFlag">
				{{ checkFlag ? '取消' : '管理' }}
			</view>
		</view>
		<view class="data-content" v-if="rList.length">
			<!-- 数据列表 -->
			<scroll-view class="rBox" @scrolltolower="scrolltolower" scroll-y="true"
			    show-scrollbar="false" :class="(currentTabNum === 0 && checkFlag) ? 'rBox-check' : ''">
				<u-checkbox-group v-model="checkPointApplyIds" @change="handleIsCheckAll">
					<view v-for="(item, index) in rList" :key="index" class="rItem">
						<view class="check-box" v-if="currentTabNum === 0 && checkFlag">
							<u-checkbox :name="item.pointApplyId" />
						</view>
						<view class="content" @click="toReportDetail(item)">
							<view class="name u-line-1"> {{ item.peopleNames }}</view>
							<view class="center">
							    <view class="des">{{ item.content }}</view>
							    <view class="point">{{ item.applyPoint }}</view>
							</view>
							<view class="bottom">
							    <text> {{ item.applyDate }}</text><text> {{ item.applyPeopleName }} </text>
							</view>
							<view class="status">
							    {{ item.pointApplyStatusName }}
							</view>
						</view>
					</view>
				</u-checkbox-group>
			    
			    <!-- 底部加载，三个状态：loadmore、loading、nomore -->
			    <u-loadmore :status="loadStatus"></u-loadmore>
			</scroll-view>
			<!-- 多选审批操作 -->
			<view class="check-audit" v-if="currentTabNum === 0 && checkFlag">
				<u-checkbox-group v-model="checkAllApplyIds" @change="handleCheckAll">
					<u-checkbox name="all" label="全选" />
				</u-checkbox-group>
				
				<view class="audit-btn">
					<u-button type="success" plain text="拒绝" @click="handleAudit('rejected')" />
					<u-button type="success" text="同意" @click="handleAudit('passed')" />
				</view>
			</view>
		</view>
        
        <!-- 暂无数据 -->
		<view class="empty-status" v-if="!rList.length">
            <view class="empty-icon">
                <u-image :src="NO_MORE_IMG" shape="circle" mode="aspectFit" width="300rpx" height="150rpx"></u-image>
                <view class="no-more">
                    <span class="txt">暂无数据</span>
                </view>
            </view>
        </view>
		<!-- 新增 加号 -->
        <view v-if="canApply && !checkFlag" @click="gotoReport">
            <floating-button></floating-button>
        </view>
		<!-- 审批意见 -->
		<u-popup v-if="showRemark" :show="showRemark" mode="bottom" :customStyle="popupStyle" @close="popclosed"
			:overlay='false' @open="showRemark = true" :safeAreaInsetBottom="true" :zIndex='998'>
			<view class="pop-container">
				<div class="comment-container">
					<view class="comment-title">确认{{currentOperateName}}</view>
					<view class="textarea-box">
						<u--textarea v-model="remarkData" placeholder="请输入审批意见" count />
					</view>
					<view class="submit-btn" @click="submit">确认{{currentOperateName}}</view>
				</div>
			</view>
		</u-popup>
		<!-- 遮罩层 -->
		<u-overlay :show="showOverlay" :zIndex='997' @click="hideOverlay"></u-overlay>
		<u-popup v-if="showGroup" :show="showGroup" mode='bottom' :closeOnClickOverlay='true' @close="showGroup = false">
			<chooseGroup @selectGroup='selectGroup'></chooseGroup>
		</u-popup>
    </view>
</template>

<script setup>
    import {
        NO_MORE_IMG
    } from "@/common/net/staticUrl";
    import {
        ref,
        computed,
        watch
    } from 'vue'
    import {
        onLoad,
		onShow,
    } from '@dcloudio/uni-app'
    import {
		checkUserIsParty,
        findPageMobile,
		findPartyPageMobile,
        isAddBtnShow,
		isPartyAddBtnShow,
		auditInfoMobileList,
		auditPartyInfoMobileList,
		getTeamListByRegionCode,
		getIsChaoSongren,
		getIsApprover,
		isDeclarantInfo
	} from '../../api/points/points'
	import { getDictListByKey } from '@/common/net/generalPage/common.js'
    import {
        useUserStore
    } from '@/store/user.js'
    import {
        setPoints
    } from '../../api/burialPoint.js'
	import chooseGroup from "./chooseGroup.vue";
	const groupName = ref('请选择')
    const peopleName = ref("")
    const datetimePicker = ref(null);
    const show = ref(false)
    const userStore = useUserStore()
    const canApply = ref(false) // 是否可以发起申报
	const showGroup = ref(false)
    let pickerDate = ref(Number(new Date()))
    const queryDate = computed(() => {
        if (!!pickerDate.value) {
            let dateTime = new Date(pickerDate.value);
            // 分别获取年月日、时分秒，三元判断长度不够的进行补0操作
            let year = dateTime.getFullYear();
            let month = (dateTime.getMonth() + 1 < 10 ? '0' + (dateTime.getMonth() + 1) : dateTime.getMonth() +
                1); // 这里需要注意的是月份是0~11，所以需要 + 1 操作
            let day = dateTime.getDate() < 10 ? '0' + dateTime.getDate() : dateTime.getDate();
            let hour = dateTime.getHours() < 10 ? '0' + dateTime.getHours() : dateTime.getHours();
            let minutes = dateTime.getMinutes() < 10 ? '0' + dateTime.getMinutes() : dateTime.getMinutes();
            let second = dateTime.getSeconds() < 10 ? '0' + dateTime.getSeconds() : dateTime.getSeconds();
            //按指定格式拼接年月日、时分秒
            let time = year + '-' + month
            return time
        } else {
            return '当月'
        }

    })
	const showManagerBtn = computed(() => {
		// 如果是家庭积分
		if (sourceType.value == '1') {
			return currentTabNum.value === 0 && rList.value.length && userStore.currentRole !== 'villager'
		} else if (sourceType.value == '2') {
			return currentTabNum.value === 0 && isApprover.value == '1' && rList.value.length
		}
	})
	let peopleTag = ref('') //人员标签
	let teamId = ref('')  //队组/党组织
	let peopleTagData = ref([]) //人员标签数据
	let teamData = ref([]) //队组/党组织数据
	let sourceType = ref('1')  //1为家庭积分，2为党员积分
	let isParty = ref(false)
	let isSendPeople = ref(null) //是否为抄送人
	let isApprover = ref('') // 是否为审批人
	let isDeclarant = ref(null) //是否人申报人
	let showSearch = computed(() =>{
		// (家庭积分):
		return (sourceType.value == '1' && userStore.currentRole !== 'villager') ||
			(sourceType.value == '2' && (isSendPeople.value==1 || isApprover.value == 1 || isDeclarant.value))
	})
    watch(queryDate, (value, oldValue) => {
        if (!!value) {
            rList.value = []
            getReportList()
        }
    })

    onLoad(async(option) => {
		sourceType.value = option?.sourceType || '1'
		getUserInfo()
		await findChaoSongInfo()
		await findisDeclarantInfo()
		getLocaldata()
        getReportList()
        getApplyFlag()
        // 埋点-进入积分填报页
        let param = {
            eventId: 'enter_integral_reporting_page',
            attributeValue: '/jimo/pages/pointsManage/pointsReport'
        }
        setPoints(param)
        // 埋点-进入积分申报列表页
        param = {
            eventId: 'enter_integral_reporting_list',
            attributeValue: '/jimo/pages/pointsManage/pointsReport'
        }
        setPoints(param)
		await findIsApprover()
    })
	onShow(() => {
		handleResetList()
	})
	// 查询是否为党员
	function getUserInfo(){
		checkUserIsParty().then(res=>{
			isParty.value = res.data
		})
	}
	function getLocaldata(){
		getDictListByKey({code:'people_tag'}).then(res => {
			if(res.success){
				peopleTagData.value = res.data.map(item => {
					return {
						text: item.dictLabel,
						value: item.dictValue
					}
				})
			}
		})
		if(sourceType.value == '2'){
			// findPartyOrgList().then(res => {
			// 	if(res.success){
			// 		teamData.value = res.data.map(item => {
			// 			return {
			// 				text: item.partyOrgName,
			// 				value: item.partyOrgId
			// 			}
			// 		})
			// 	}
			// })
		}else{
			getTeamListByRegionCode({regionCode:userStore?.userInfo?.customParam?.regionCode}).then(res => {
				if(res.success){
					teamData.value = res.data.map(item => {
						return {
							text: item.regionName,
							value: item.regionCode
						}
					})
				}
			})
		}
	}
    let currentPage = ref(1);
    let loadStatus = ref("loading")
    let pageSize = ref(10)

    function scrolltolower() {
        if (loadStatus.value == 'nomore') {
            return;
        }
        currentPage.value += 1;
        getReportList();
    }
    const rList = ref([])
	
	function handleResetList () {
		checkPointApplyIds.value = []
		currentPage.value = 1
		rList.value = []
		getReportList()
	}

    function getReportList() {
        //显示加载中动画
        // uni.showNavigationBarLoading();
        show.value = false
        let params = {
            tabValue: currentTab.value,
            selectDate: queryDate.value,
            peopleNames: peopleName.value,
            pageSize: pageSize.value,
            pageNum: currentPage.value,
            role: userStore.currentRole,
			peopleTag: peopleTag.value,
        }
		if(sourceType.value == '2'){
			params.organizationId = teamId.value
		}else{
			params.villageOrgId = teamId.value
		}
		let api = sourceType.value == '2' ? findPartyPageMobile : findPageMobile
        api(params).then(res => {
            //成功获取数据后隐藏加载动画
            // uni.hideNavigationBarLoading();
            const {
                success,
                data
            } = res
            if (success) {
                //如果页数>1，需要拼接返回的数据
                if (currentPage.value > 1) {
                    rList.value = [...rList.value, ...data.records];
                } else {
                    rList.value = data.records;
                }
				// 获取数据后判断是否全选
				handleIsCheckAll(checkPointApplyIds.value)
                loadStatus.value = data.total == rList.value.length ? "nomore" : "loadmore"
                console.log("rList.value==", rList.value.length, loadStatus.value)
                //成功获取数据后结束下拉刷新
                uni.stopPullDownRefresh();
            } else {
                rList.value = [];
                currentPage.value = []
            }
        }).catch(() => {
            uni.hideNavigationBarLoading();
            uni.stopPullDownRefresh();
        })

    }

	/** 是否开启多选 */
	const checkFlag = ref(false)
	/** 多选数据 */
	const checkPointApplyIds = ref([])
	/** 是否全选 */
	const checkAllApplyIds = ref([])
	/**
	 * @description 全选操作
	 */
	const handleCheckAll = (values) => {
		checkAllApplyIds.value = values
		const isCheckAll = checkAllApplyIds.value[0] === 'all'
		if (isCheckAll) {
			checkPointApplyIds.value = rList.value.map(item => item.pointApplyId)
		} else {
			checkPointApplyIds.value = []
		}
	}
	/**
	 * @description 判断当前数据是否全选
	 */
	const handleIsCheckAll = (values) => {
		checkPointApplyIds.value = values
		if (rList.value.length === checkPointApplyIds.value.length) {
			checkAllApplyIds.value = ['all']
		} else {
			checkAllApplyIds.value = []
		}
	}
	const showRemark = ref(false) // 展示操作意见弹窗
	const showOverlay = ref(false)// 展示遮罩
	const currentOperate = ref('passed') // 当前操作 同意还是拒绝
	const currentOperateName = ref('同意') // 当前操作 同意还是拒绝
	const remarkData = ref('') // 审批意见
	const popupStyle = ref({
		width: '100%',
		borderTopLeftRadius: '8rpx',
		borderTopRightRadius: '8rpx'
	})
	/**
	 * @description 审批
	 * @param auditType
	 */
	const handleAudit = (auditType) => {
		if (!checkPointApplyIds.value.length) {
			uni.showToast({ title: "请选择", icon: 'none' })
			return
		}
		
		currentOperate.value = auditType
		currentOperateName.value = auditType === 'passed' ? '同意' : '拒绝'
		// 埋点-点击积分申报审核按钮
		const param = auditType === 'passed' ? {
			eventId : 'approve_integral_reporting',
			attributeValue: 'approve_btn'
		} : {
			eventId : 'reject_integral_reporting',
			attributeValue: 'reject_btn'
		}
		setPoints(param)
		
		// 展示弹窗
		showOverlay.value = true
		showRemark.value = true
	}
	
	// 提交审批
	function submit() {
		let params = checkPointApplyIds.value.map(id => {
			return {
				pointApplyId : id,
				auditType: currentOperate.value,
				remark: remarkData.value
			}
		})
		let api = sourceType.value == '2' ? auditPartyInfoMobileList : auditInfoMobileList
		if (currentOperate.value == 'passed') {
			uni.showModal({
				title: '提示',
				content: '确定同意吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						api(params).then(res => {
							if (res.success) {
								// 埋点-积分申报同意-成功
								let param = {
									eventId : 'integral_reporting_approved',
									attributeValue: 'success'
								}
								setPoints(param)
							} else {
								// 埋点-积分申报同意-失败
								let param = {
									eventId : 'integral_reporting_approved',
									attributeValue: 'fail'
								}
								setPoints(param)
							}
							handleRes(res)
						}).catch((err) => {
							// 埋点-积分申报同意-失败
							let param = {
								eventId : 'integral_reporting_approved',
								attributeValue: 'fail'
							}
							setPoints(param)
						})
					} else if (res.cancel) {}
				}
			});
		} else if (currentOperate.value == 'rejected') {
			uni.showModal({
				title: '提示',
				content: '拒绝后积分申报将终止，确定拒绝吗',
				success: function(res) {
					if (res.confirm) {
						uni.showLoading({
							title: '处理中...',
							mask: true,
						})
						api(params).then(res => {
							if (res.success) {
								// 埋点-积分申报拒绝-成功
								let param = {
									eventId : 'integral_reporting_rejected',
									attributeValue: 'success'
								}
								setPoints(param)
							} else {
								// 埋点-积分申报拒绝-失败
								let param = {
									eventId : 'integral_reporting_rejected',
									attributeValue: 'fail'
								}
								setPoints(param)
							}
							handleRes(res)
						}).catch((err) => {
							// 埋点-积分申报拒绝-失败
							let param = {
								eventId : 'integral_reporting_rejected',
								attributeValue: 'fail'
							}
							setPoints(param)
						})
					} else if (res.cancel) {}
				}
			});
		}
	}
	// 处理接口返回结果
	function handleRes(res) {
		showOverlay.value = false
		showRemark.value = false
		remarkData.value = ''
		checkFlag.value = false
		uni.hideLoading();
		
		if (res.success) {
			uni.showToast({ title: "处理成功", icon: 'none', duration: 2000 })
			currentPage.value = 1
			handleResetList()
		} else {
			uni.showLoading({
				title: res.message || '处理失败，请稍后再试',
				mask: true
			})
		}
	}
	
	// 点击遮罩关闭
	function hideOverlay() {
		remarkData.value = ''
		showOverlay.value = false
		showRemark.value = false
		fontnum.value = 0
	}
	
    function gotoReport() {
        // 埋点-点击申报按钮
        let param = {
            eventId: 'click_report_button',
            attributeValue: 'report_btn'
        }
        setPoints(param)
        uni.navigateTo({
            url: `/jimo/pages/pointsManage/report?sourceType=${sourceType.value}`
        })
    }

    function toReportDetail(item) {
        console.log("要跳转", item)
        // 埋点-点击申报列表内容
        let param = {
            eventId: 'click_reporting_item',
            attributeValue: item.pointApplyId
        }
        setPoints(param)
        uni.navigateTo({
            url: `/jimo/pages/pointsManage/pointsReportDetail?pointApplyId=${item.pointApplyId}&sourceType=${sourceType.value}`
        })
    }
    const tabList = computed(() => {
        const list = [{
                "name": "待处理",
                "value": "pending"
            },
            {
                "name": "已处理",
                "value": "processed"
            },
            {
                "name": "已发起",
                "value": "initiated"
            }
        ]
        if ( (sourceType.value == '1' && userStore.currentRole !== 'villager') || (sourceType.value == '2' && isSendPeople.value == 1)) {
            list.push({
                "name": "抄送我的",
                "value": "copied"
            })
        }
        return list
    })
    const currentTab = ref('pending')
    const currentTabNum = ref(0)
    const itemStyle = ref({
        height: '80rpx',
        fontWeight: '500',
        fontSize: '32rpx',
        background: '#fff'
    })
    const activeStyle = ref({
        color: '#0BBD88'
    })
    // 切换列表类型
    function changeTab(item) {
        // 埋点-处理状态tab切换
        let param = {
            eventId: 'switch_status_tab',
            attributeValue: item.value
        }
        setPoints(param)
        rList.value = []
        currentPage.value = 1
        currentTab.value = item.value;
        currentTabNum.value = item.index;
        getReportList()
    }
    // 根据状态值获取状态名
    function getStatusName(status) {
        if (status == 'pass') {
            return '已通过'
        } else if (status == 'refuse') {
            return '已拒绝'
        } else if (status == 'wait') {
            return '待审批'
        }
        return '已通过'
    }
    // 查询是否可以发起申报
    function getApplyFlag() {
		let api = sourceType.value == '2' ? isPartyAddBtnShow : isAddBtnShow
        api().then(res => {
            if (res.success) {
				if(sourceType.value == '2'){
					canApply.value = isParty.value || res.data
				}else{
					canApply.value = userStore.currentRole === 'villager' ?
					    userStore.userInfo.customParam.peopleInfo.isVillager === '1' : res.data
				}
            }
        }).catch(err => {
            console.log(err);
        })
    }
    // 打开时间选择器
    function openDatePicker() {
        show.value = true;
        // 埋点-点击时间筛选框
        let param = {
            eventId: 'click_time_filter_frame',
            attributeValue: 'time_filter'
        }
        setPoints(param)
    }
    // 点击搜索框
    function focusSearch() {
        // 埋点-点击搜索框
        let param = {
            eventId: 'click_farmer_search',
            attributeValue: 'farmer_search'
        }
        setPoints(param)
    }
	function handleSelect(type){
		handleResetList()
	}
	// 查询是否为抄送人(党员)
	async function findChaoSongInfo(){
		try{
			let res = await getIsChaoSongren()
			if(res.success){
				isSendPeople.value = res.data
			}else{
				uni.showToast({
					title:e,
					icon:'none'
				})
			}
		}catch(e){
			uni.showToast({
				title:e,
				icon:'none'
			})
		}
	}
	// 查询是否为审批人(党员)
	async function findIsApprover(){
		try{
			let res = await getIsApprover()
			if(res.success){
				isApprover.value = res.data
			}else{
				uni.showToast({
					title:e,
					icon:'none'
				})
			}
		}catch(e){
			uni.showToast({
				title:e,
				icon:'none'
			})
		}
	}
	// 查询是否为申报人
	async function findisDeclarantInfo(){
		try{
			let res = await isDeclarantInfo()
			if(res.success){
				isDeclarant.value = res.data
			}else{
				uni.showToast({
					title:e,
					icon:'none'
				})
			}
		}catch(e){
			uni.showToast({
				title:e,
				icon:'none'
			})
		}
	}
	function selectGroup(item) {
		teamId.value = item.partyOrgId;
		groupName.value = item.partyOrgName;
		handleResetList();
		showGroup.value = false;
	}
	function clearGroup(){
		teamId.value = ''
		groupName.value = '请选择'
		handleResetList();
	}
</script>

<style lang="scss" scoped>
    view {
        box-sizing: border-box;
        font-family: Source Han Sans CN, Source Han Sans CN;
    }

    .content-boxes {
        background: #F0F7F7;
        height: 100vh;

        ::v-deep.tab-box {
            width: 100%;

            .u-tabs__wrapper__nav__item {
                flex: 1 !important;
            }
        }
    }

    .top {
        //height: 200rpx;
        box-sizing: border-box;
        padding: 20rpx 14rpx;
        padding-bottom: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .left {
            // width: 70%;
			min-width: 170rpx;
			flex:1;
			margin-right: 10rpx;
			::v-deep .u-search__content{
				padding: 0 12rpx;
			}
			::v-deep .u-search__content__input--placeholder{
				font-size: 26rpx;
			}
			::v-deep .uni-select {
				height: 64rpx;
			}
			::v-deep .uni-select__input-placeholder{
				color: #909399;
			}
			::v-deep .uni-select__input-box{
				overflow: hidden;
			}
        }

        .right {
            float: right;
            // width: calc(30% - 20rpx);
			width: 180rpx;
            height: 68rpx;
            border-radius: 8rpx;
            font-size: 26rpx;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            color: #6D6D6D;
            display: flex;
            justify-content: space-around;
            line-height: 68rpx;
            background: #ffffff;
            border-radius: 30rpx;
            padding: 0 12rpx;
        }
		
		.text-btn {
			margin-left: 20rpx;
		}
    }
	
	.data-content {
		width: 100%;
		height: calc(100vh - 80rpx - 90rpx - 60rpx);
		position: relative;
		flex: 1;
		padding-bottom: env(safe-area-inset-bottom);
		
		.rBox {
			box-sizing: border-box;
			border-radius: 20rpx;
			overflow-y: hidden;
			font-family: Source Han Sans CN, Source Han Sans CN;
			color: #000000;
			padding: 20rpx;
			height: 100%;
			
			&.rBox-check {
				height: calc(100% - 160rpx + env(safe-area-inset-bottom));
			}
		
			.rItem {
				width: 100%;
				position: relative;
				margin-bottom: 20rpx;
				padding: 20rpx;
				background: #fff;
				border-radius: 8rpx;
				display: flex;
				
				.check-box {
					width: 60rpx;
					display: flex;
					align-items: center;
				}
				
				.content {
					width: 100%;
					
					.name {
						font-size: 32rpx;
						font-weight: 600;
						line-height: 45rpx;
						width: 80%;
					}
					
					.center {
						display: flex;
						justify-content: space-between;
						align-items: center;
					
						.des {
							width: 80%;
							font-size: 28rpx;
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							color: #000000;
							line-height: 40rpx;
							margin: 20rpx 0;
							overflow: hidden;
							text-overflow: ellipsis;
							word-break: break-all;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
					
						}
					
						.point {
							text-align: right;
							width: 20%;
							font-weight: 600;
							color: #FF8641;
					
						}
					}
					
					.bottom {
						font-size: 26rpx;
						font-weight: 400;
						color: #999999;
						line-height: 37rpx;
					
						:nth-child(1) {
							margin-right: 20rpx;
						}
					}
					
					.status {
						position: absolute;
						top: 0;
						right: 0;
						width: 128rpx;
						height: 56rpx;
						background: rgba(180, 242, 183, 0.38);
						border-radius: 0rpx 8rpx 0rpx 26rpx;
						// opacity: 0.38;
						line-height: 56rpx;
						text-align: center;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						font-size: 28rpx;
						color: #01BD5D;
					}
				}
			}
		}	
	
		::v-deep.check-audit {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 160rpx;
			padding-bottom: env(safe-area-inset-bottom);
			background-color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-left: 40rpx;
			
			.audit-btn {
				display: flex;
				
				.u-button {
					width: 150rpx;
					margin-right: 40rpx;
					border-radius: 15rpx;
					height: 60rpx;
				}
			}
		}
	}

    .empty-status {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 50rpx 0;

        .empty-icon {
            display: flex;
            flex-direction: column;
            justify-content: center;

            .no-more {
                margin-top: 20rpx;
                display: flex;
                justify-content: center;
                margin-top: 10rpx;

                .txt {
                    text-align: center;
                    height: 37rpx;
                    font-size: 26rpx;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;
                    color: #d5d5d6;
                    line-height: 37rpx;
                }
            }
        }
    }

	.pop-container {
		padding: 20rpx;
	
		.comment-container {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
	
			.comment-title {
				margin-bottom: 20rpx;
				font-size: 30rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				color: #000000;
			}
	
			::v-deep.textarea-box {
				position: relative;
				margin-bottom: 20rpx;
				width: 100%;
				
				.u-textarea {
					height: 200rpx;
					padding-bottom: 40rpx;
				}
			}
	
			.submit-btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 80rpx;
				background: linear-gradient(117deg, #3ADB97 0%, #0CBE88 100%);
				border-radius: 40rpx;
				font-size: 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				color: #FFFFFF;
			}
		}
	}
	.choose-group {
		display: flex;
		justify-content: center;
		align-items: center;
		width:  100%;
		height: 100%;
		background: #fff;
		border-radius: 30rpx;
		color: #6a6a6a;
		font-size: 26rpx;
		padding: 0 10rpx 0 21rpx;
		box-sizing: border-box;
		.holder {
			margin-right: 20rpx;
		}
	}
</style>