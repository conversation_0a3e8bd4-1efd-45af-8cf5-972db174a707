<template>
	<view class="form-container">
		<view class="form-box">
			<block v-for="(item, index) in formData" :key="index">
				<view class="form-item flex-row--c">
					<!-- 单行文本框 -->
					<view class="line itembox" v-if="['el-input'].includes(item.__config__.tag)">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							<text v-if="item.__config__.showLabel">
								{{ item.__config__.label }}
							</text>
						</view>
						<!-- 多行输入框 -->
						<template v-if="item.type == 'textarea'">
							<textarea :placeholder="item.placeholder" class="textarea-box" :maxlength="-1" placeholder-class="plaClass"
								:value="item.__config__.defaultValue" @input="inputVal($event,index)" :disable-default-padding='true'
								:disabled="isPreview" :auto-height="true" />
						</template>
						<!-- 普通输入框 -->
						<view class="line-right" v-else>
							<input type="text" class="input" placeholder-class="plaClass"
								:placeholder="item.placeholder" :maxlength="-1" :value="item.__config__.defaultValue"
								:disabled="isPreview" @input="inputVal($event,index)" />
						</view>

					</view>
					<!-- 单选框el-radio-group -->
					<view class="line-col itembox" v-else-if="item.__config__.tag === 'el-radio-group'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.__config__.label }}
							<text style="font-size: 20rpx; color: #9e9e9e; margin-left: 23rpx; width: 140rpx">(单选)
							</text>
						</view>
						<view class="line-bottom-select">
							<view v-for="(radioItem,radioIndex) in item.__slot__.options" :key="radioIndex">
								<radio-group class="radio-group" @change='radioChange($event, item,index)'>
									<label class="radio">
										<radio style="transform: scale(0.7)" :value="radioItem.label"
											:checked="radioItem.label == item.__config__.defaultValue"
											:disabled="isPreview" />
										{{ radioItem.label }}
									</label>
								</radio-group>
							</view>

						</view>
					</view>
					<!-- 多选框el-checkbox-group -->
					<view class="line-col itembox" v-else-if="item.__config__.tag === 'el-checkbox-group'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.__config__.label }}
							<text style="font-size: 20rpx; color: #9e9e9e; margin-left: 23rpx; width: 140rpx">(多选)
							</text>
						</view>
						<view class="line-bottom-select">
							<u-checkbox-group @change="checkboxGroupChange($event,item)"
								v-model="item.__config__.defaultValue">
								<u-checkbox icon-size="35rpx" label-size="25rpx"
									:checked='isChecked(checkboxItem,item.__config__.defaultValue)'
									v-for="(checkboxItem, checkboxIndex) in item.__slot__.options" :key="checkboxIndex"
									:name="checkboxItem.label" :label="checkboxItem.label" :disabled="isPreview">
								</u-checkbox>
							</u-checkbox-group>
						</view>
					</view>

					<!-- 日期选择 el-date-picker -->
					<view class="line itembox" v-else-if="item.__config__.tag === 'el-date-picker'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.__config__.label }}
						</view>
						<view @tap="showSelectDate(index)" class="line-right">
							<view style="width: 90%; height: 100%;">
								<template v-if="item.__config__.defaultValue" class="input">
									{{item.__config__.defaultValue}}
								</template>
								<template v-else class="plaClass">
									{{item.placeholder}}
								</template>
							</view>

							<view class="select-icon" v-if="!isPreview"></view>
						</view>
						<u-datetime-picker :show="dateshow" mode="date" @confirm="selectDate" v-model="defaultDate"
							@cancel="dateshow = false"></u-datetime-picker>
					</view>

					<!-- 时间选择 el-time-picker -->
					<view class="line itembox" v-else-if="item.__config__.tag === 'el-time-picker'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.__config__.label }}
						</view>
						<view class="line-right" @tap="showSelectTime(index)">
							<view style="width: 90%; height: 100%;">
								<template v-if="item.__config__.defaultValue" class="input">
									{{item.__config__.defaultValue}}
								</template>
								<template v-else class="plaClass">
									{{item.placeholder}}
								</template>
							</view>

							<view class="select-icon" v-if="!isPreview"></view>
						</view>
						<u-datetime-picker :show="timeshow" mode="time" @confirm="selectTime" v-model="defaultDate"
							@cancel="timeshow = false"></u-datetime-picker>
					</view>

					<!-- 开关 el-switch-->
					<view class="line itembox" v-else-if="['el-switch'].includes(item.__config__.tag)">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							<text v-if="item.__config__.showLabel">
								{{ item.__config__.label }}
							</text>
						</view>
						<view>
							<!-- v-model="item.__config__.defaultValue" -->
							<switch checked="item.__config__.defaultValue" :disabled="isPreview"
								@change="swichChange($event,item)" :activeValue="item.active-value"
								:inactiveValue="item.inactive-value" style="transform:scale(0.7)" />
						</view>
					</view>
					<!-- 滑块el-slider -->
					<view class="line itembox" v-else-if="['el-slider'].includes(item.__config__.tag)">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							<text v-if="item.__config__.showLabel">
								{{ item.__config__.label }}
							</text>
						</view>
						<view style="width: 100%;">
							<slider :value="item.__config__.defaultValue" @change="sliderChange($event,item)"
								:min="item.min" :max="item.max" show-value :disabled="isPreview" />
						</view>
					</view>
					<!-- 步进器el-input-number -->
					<view class="line itembox" v-else-if="item.__config__.tag == 'el-input-number'">
						<view class="line-left">
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							<text v-if="item.__config__.showLabel">
								{{ item.__config__.label }}
							</text>
						</view>
						<view style="width: 100%;">
							<u-number-box :min="item.min" :max="item.max" v-model="item.__config__.defaultValue"
								v-if="!isPreview"></u-number-box>
							<view class="line-right" v-else>{{item.__config__.defaultValue}}</view>
						</view>
					</view>
					<!-- 下拉选择-->
					<view class="line itembox" v-else-if="['el-select'].includes(item.__config__.tag)">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.__config__.required && !isPreview">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							<text v-if="item.__config__.showLabel">
								{{ item.__config__.label }}
							</text>
						</view>
						<view class="line-right" @tap="toshowSelect(index)">
							<view style="width: 90%; height: 100%;">
								<template v-if="item.__config__.defaultValue" class="input">
									{{item.__config__.defaultValue}}
								</template>
								<view v-else class="plaClass">
									{{item.placeholder}}
								</view>
							</view>
							<view class="select-icon" v-if="!isPreview"></view>
						</view>
						<u-picker :show="selectshow" :columns="columns" :immediateChange="true" @confirm="selectConfirm" @cancel="selectCancle"
							keyName='label'>
						</u-picker>
					</view>
					<!-- 上传图片 -->
					<view class="img-box  flex-col-l" v-else-if="item.__config__.tag == 'el-upload'">
						<view style="font-size:26rpx" class="line-left">
							<!-- <text class="colorRed" v-if="item.rules.verify">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.__config__.label }}
						</view>
						<view class="img-upload p30">
							<template v-if="!isPreview">
								<u-upload :auto-upload="false" :fileList="item.__config__.defaultValue"
									:deletable='!isPreview' :disabled="isPreview" :maxCount="3" :mutiple="true"
									width="150rpx" height="150rpx" @afterRead="afterRead($event, index)"
									@delete="deletePic($event, index)" @clickPreview='previewPic'></u-upload>
							</template>
							<template v-else>
								<view class="image-box">
									<view class="image-item" v-for="(image,imageIndex) in item.__config__.defaultValue"
										:key="imageIndex">
										<!-- <u-image :show-loading="true" :src="image.url" width="80rpx" height="80rpx"
											@click="previewPics(index)"></u-image> -->
										<safe-image :showLoading="true" :src="image.url" width="80rpx" height="80rpx" :imgIndex='index'></safe-image>
									</view>
								</view>
							</template>
						</view>
					</view>

					<!-- 单选框 -->
					<view class="line-col" v-else-if="item.type === 'radio'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.rules.verify">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.label }}
							<text style="font-size: 20rpx; color: #9e9e9e; margin-left: 23rpx; width: 140rpx">(单选)
							</text>
						</view>
						<view class="line-bottom-select">
							<u-radio-group :currentIndex="index" v-model="item.rules.value"
								@change="radioChange($event, item)">
								<u-radio icon-size="35" label-size="25" shape="circle"
									v-for="(radioItem, radioIndex) in item.list" :key="radioIndex"
									:label="radioItem.label" :name="radioItem.value" :disabled="radioItem.disabled">
									{{ radioItem.label }}
								</u-radio>
							</u-radio-group>
						</view>
					</view>
					<!-- 多选框 -->
					<view class="line-col" v-else-if="item.type == 'checkbox'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.rules.verify">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.label }}
							<text style="font-size: 20rpx; color: #9e9e9e; margin-left: 23rpx; width: 140rpx">(多选)
							</text>
						</view>
						<view class="line-bottom-select">
							<u-checkbox-group @change="checkboxGroupChange($event,item)">
								<u-checkbox icon-size="35" label-size="25" v-model="checkboxItem.checked"
									v-for="(checkboxItem, checkboxIndex) in item.list" :key="checkboxIndex"
									:name="checkboxItem.value" :disabled="checkboxItem.disabled">{{ checkboxItem.name }}
								</u-checkbox>
							</u-checkbox-group>
						</view>
					</view>

					<!-- 手机输入框 -->
					<view class="line" v-else-if="item.type === 'mobile'">
						<view class="line-left">
							<!-- <text class="colorRed" v-if="item.rules.verify">*</text> -->
							<text class="num" v-if="num">{{ index + 1 }}.</text>
							{{ item.label }}
						</view>
						<view class="line-right">
							<input type="number" v-model="item.rules.value" :placeholder="item.placeholder"
								@input="inputVal(index)" class="input" :maxlength="11" />
							<view style="width:250rpx">
								<u-button v-if="item.oneKeyPhone" size="mini" type="primary" open-type="getPhoneNumber"
									@getphonenumber="getphonenumber" :disabled="item.disabled">一键获取
								</u-button>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script setup>
	// import mixins from "./mixins";
	import { ref, reactive, computed, getCurrentInstance, watch, onMounted, onBeforeUnmount } from 'vue'
	import { onReady, onShow, onHide, onLoad, onUnload } from '@dcloudio/uni-app'
	import { formatDateTime } from '@/common/date/dateTimeFormat.js'
	import { fileUpload } from '@/common/api.js'
	import {
		useTokenStore
	} from '@/store/token.js'
	const tokenStore = useTokenStore()
	// const submitData = ref("")
	// const selectBox = ref([])
	// const currentSelectIndex = ref("")
	// const currentSelectValue=ref("")
	// const codeFont=ref("获取验证码") 
	// const wait=ref(60)
	// const isSend = ref(false)
	// const sendColor =ref("color:#ff5b01") 
	onMounted(() => {
		uni.$on('clickImg', (index) => {
			console.log('clickImg触发了');
			previewPics(index)
		})
	})
	onBeforeUnmount(() => {
		uni.$off('clickImg')
	})
	let { ctx: that, proxy } = getCurrentInstance()
	const dateParams = ref({
		year: true,
		month: true,
		day: true,
		hour: false,
		minute: false,
		second: false,
	})
	const timeParams = ref({
		year: false,
		month: false,
		day: false,
		hour: true,
		minute: true,
		second: true,
		// 选择时间的时间戳
		// timestamp: true,
	})
	const dateshow = ref(false)
	const timeshow = ref(false)
	const index = ref(0)
	const selectshow = ref(false)
	const curIndex = ref(0)
	const columns = ref([])

	const props = defineProps({
		//是否展示序号
		num: {
			type: Boolean,
			default: false,
		},
		// modelValue: {
		// 	type: Array || Object,
		// 	default: () => {
		// 		return [];
		// 	},
		// },
		tableFields: {
			type: Array,
			default: () => {
				return [];
			},
		},
		isPreview: {
			type: Boolean,
			default: false,
		},
		copyData: {
			type: Object,
			default: () => {
				return {};
			},
		},
	})
	const emit = defineEmits(['update:modelValue', "selectEventType", "updateModelValue"])
	// const formData = computed({
	// 	get() {
	// 		// checkbox回显
	// 		// props.modelValue.forEach(item => {
	// 		// 	if (item.type === "checkbox") {
	// 		// 		item.list.forEach(item2 => {
	// 		// 			item2.checked = item.rules.value.includes(item2.value)
	// 		// 		})
	// 		// 	}
	// 		// })
	// 		console.log("props.modelValue--------", props.tableFields);
	// 		return props.tableFields
	// 	},
	// 	set(nval) {
	// 		// emit("update:modelValue", nval);
	// 		emit("updateModelValue", nval);
	// 	}
	// })
	const formData = ref({})
	watch(() => props.tableFields, (newVal, oldVal) => {
		if (newVal) {
			formData.value = props.tableFields
		}
	}, {
		immediate: true
	})
	//input输入框的值传给父组件
	function inputVal(event, index) {
		formData.value[index].__config__.defaultValue = event.detail.value;
		emit("updateModelValue", formData.value);
	}

	function showSelectDate(index) {
		if (props.isPreview) return;
		curIndex.value = index
		dateshow.value = true
	}

	function showSelectTime(index) {
		if (props.isPreview) return;
		curIndex.value = index
		timeshow.value = true
	}
	const defaultDate = Number(new Date())
	function selectDate(date) {
		console.log("formData.value------", formData.value);
		// 时间戳转换为日期格式
		let formatDate = formatDateTime(date.value)
		formData.value[curIndex.value].__config__.defaultValue = formatDate.substr(0, 10)
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
		dateshow.value = false
	}

	function selectTime(time) {
		console.log("formData.value------", formData.value);
		formData.value[curIndex.value].__config__.defaultValue = time.value
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
		timeshow.value = false
	}
	//单选 点击触发
	function radioChange($event, item, index) {
		console.log("得到的", $event, item, index)
		item.__config__.defaultValue = $event.detail.value
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
		// that.$forceUpdate()
	}
	//复选框 点击触发
	function checkboxGroupChange($event, item) {
		item.__config__.defaultValue = $event;
		// this.formData[index].__config__.defaultValue
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
	}
	//开关选择器点击触发
	function swichChange($event, item) {
		item.__config__.defaultValue = $event.detail.value;
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
	}
	//滑动选择器选择触发
	function sliderChange($event, item) {
		item.__config__.defaultValue = $event.detail.value
		// emit("update:modelValue", formData);
		emit("updateModelValue", formData.value);
	}

	function toshowSelect(index) {
		if (props.isPreview) return;
		formData.value[index].__config__.border = true
		let options = formData.value[index].__slot__.options
		columns.value = [options]
		curIndex.value = index
		selectshow.value = true
		// that.$forceUpdate()
	}
	// 单选 下拉框点击确定
	function selectConfirm(item) {
		formData.value[curIndex.value].__config__.defaultValue = item.value[0].label
		emit("updateModelValue", formData.value);
		selectshow.value = false
		if (formData.value[curIndex.value].__config__.label == '事件类型') {
			emit('selectEventType', item.value[0].value);
		}
	}

	function selectCancle() {
		selectshow.value = false
	}
	const fileList = ref([]);
	const beforeRead = (event, index) => {
		console.log('beforeRead---', index);
	}
	// 新增图片
	const afterRead = async (event, index) => {
		console.log('选择图片---', event, index);
		// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
		let lists = [].concat(event.file);
		let fileListLen = formData.value[index].__config__.defaultValue.length;
		lists.map((item) => {
			formData.value[index].__config__.defaultValue.push({
				...item,
				status: 'uploading',
				message: '上传中',
			});
		});
		for (let i = 0; i < lists.length; i++) {
			const result = await uploadFilePromise(lists[i].url);
			let item = formData.value[index].__config__.defaultValue[fileListLen];
			formData.value[index].__config__.defaultValue.splice(fileListLen, 1, {
				...item,
				status: 'success',
				message: '',
				url: result,
			});
			fileListLen++;
		}
		console.log("formData.value--------", formData.value);
		// formData.value[index].__config__.defaultValue = fileList
	};
	// 上传图片
	const uploadFilePromise = (url) => {
		console.log('uploadFilePromise----', url);
		return new Promise((resolve, reject) => {
			fileUpload({
				filePath: url
			}).then((res) => {
				console.log('上传图片的回调---', res);
				if (res.success) {
					resolve(res.data.url);
				} else {
					uni.showToast({
						title: res.message,
						icon: 'none',
						duration: 2000
					});
				}
			}).catch((err) => {
				uni.showToast({
					title: err,
					icon: 'none',
					duration: 2000
				});
			})
		});
	};
	// 删除图片
	const deletePic = (event, index) => {
		console.log('删除图片-----',event,index);
		formData.value[index].__config__.defaultValue.splice(event.index, 1);
	}
	// 发布时的预览图片
	const previewPic = (event) => {
		uni.previewImage({
			urls: event.url,
		})
	}
	// 办理和查看详情时的预览图片
	const previewPics = async (index) => {
		console.log('previewPics触发了', index, formData.value[index].__config__.defaultValue);
		let pics = []
		// formData.value[index].__config__.defaultValue.forEach((item) => {
		// 	pics.push(item.url)
		// })
		// uni.previewImage({
		// 	urls: pics
		// })
		for(let i=0; i<formData.value[index].__config__.defaultValue.length;i++) {
			let imgUrl = await loadImage(formData.value[index].__config__.defaultValue[i].url)
			pics.push(imgUrl)
		}
		Promise.all(pics).then((result) => {
		})
		uni.previewImage({
			urls: pics
		})
	}
	function loadImage(src) {
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: src,
				header: {
					'Authorization': 'Bearer ' + tokenStore.value
				},
				//下载地址，后端接口获取的链接
				success: (data) => {
					resolve(data.tempFilePath)
				}
			})
		})
	}

	function valify(item) {
		if (item.__config__.regTag == "mobile") {
			return uni.$u.test.mobile(item.__config__.defaultValue);
		}
		if (item.__config__.regTag == "idCard") {
			return uni.$u.test.idCard(item.__config__.defaultValue);
		}
		return false;
	}
	//校验
	function $vervify() {
		return new Promise((resolve, reject) => {
			var relus = []
			let reformData = formData.value.concat()
			reformData = reformData.reverse()
			reformData.forEach((item) => {
				let errLabel = ""
				let errFlag = false

				if (item.__config__.regTag) {
					let res = valify(item)
					if (!res) {
						uni.showToast({
							title: "【" + item.__config__.label + "】输入格式错误",
							duration: 2000,
							icon: "none",
						});
						reject(false)
					}
				}
				if (item.__config__.required && !item.__config__.defaultValue && item.__config__
					.tag !== 'el-switch') {
					if (item.__config__.tag == 'el-slider' && item.__config__.defaultValue == 0) {
						return;
					}
					uni.showToast({
						title: "请填写【" + item.__config__.label + "】",
						duration: 2000,
						icon: "none",
					});
					reject(false)
				}
				if(item.__config__.tag == 'el-upload' && item.__config__.required && item.__config__.defaultValue.length == 0) {
					uni.showToast({
						title: "请上传【" + item.__config__.label + "】",
						duration: 2000,
						icon: "none",
					});
					reject(false)
				}
			});
			resolve($submitForm())
		})
	}

	// 提交序列化的表单
	function $submitForm() {
		const tmpFormData = formData.value
		let submitData = {};
		for (let i = 0; i < tmpFormData.length; i++) {
			if (tmpFormData[i].type === 'file') {
				submitData[tmpFormData[i].rules.name] = tmpFormData[i].rules.fileList;
				break;
			}
			if (tmpFormData[i].__config__.tag === 'el-slider' && !tmpFormData[i].__config__.defaultValue) { //滑块
				tmpFormData[i].__config__.defaultValue = 0
			}
			submitData[tmpFormData[i].__vModel__] = tmpFormData[i].__config__.defaultValue;
		}

		return submitData;
	}
	// 复选框回显
	function isChecked(checkBox, checkedList) {
		if (checkedList.length == 0) {
			return false
		} else {
			let obj = checkedList.find((item) => {
				return item.label == checkBox.label
			})
			if (!!obj) {
				return true
			} else {
				return false
			}
		}
	}
	defineExpose({
		$vervify
	})
</script>

<style lang="scss" scoped>
	.img-box {
		padding-top: 30rpx;

		.img-upload {
			padding-top: 30rpx;
		}
	}

	// 弹框
	.select-modal {
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 300000;

		.select-bg {
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.3);
		}

		.select-box {
			position: absolute;
			left: 0;
			bottom: -1000upx;
			width: 100%;
			background: #fff;
			max-height: 50%;
			// height: 0%;
			overflow: auto;

			.select-title {
				display: flex;
				justify-content: space-between;
				height: 80rpx;
				background: #f5f5f5;
				line-height: 80rpx;
				padding: 0 30rpx;
			}

			.select-item {
				font-size: 28rpx;
				color: #333;
				border-bottom: 1px solid #eee;
				height: 75rpx;
				line-height: 75rpx;
				padding: 0 30rpx;

				i {
					display: block;
					float: right;
					border-bottom: 4rpx solid #1ca032;
					border-right: 4rpx solid #1ca032;
					width: 12rpx;
					height: 26rpx;
					transform: rotate(45deg);
					margin-top: 24.5rpx;
				}
			}
		}

	}

	// 下拉icon
	.select-icon {
		float: right;
		border-bottom: 2rpx solid #999;
		border-right: 2rpx solid #999;
		width: 16rpx;
		height: 16rpx;
		transform: rotate(-45deg);
		margin-right: 30rpx;
	}

	.color-999 {
		color: #999;
	}

	.form-box ::v-deep .plaClass {
		text-align: left;
		color: #B1B1B1;
		font-size: 26rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
	}

	.form-container {
		.form-box {
			width: 100%;
			box-sizing: border-box;
		}

		.colorRed {
			color: red;
			padding: 0 10rpx;
		}

		.line-right {
			flex: 1;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: relative;
			font-size: 26rpx;
			// color: #B1B1B1;
			font-family: Source Han Sans CN, Source Han Sans CN;
			.plaClass{
				color: #B1B1B1;
			}
		}

		.line-bottom-select {
			// padding-top: 40rpx;
		}

		.line-bottom-textarea {
			padding-top: 20rpx;

			textarea {
				min-height: 100rpx;
			}
		}

		.line-left {
			display: flex;
			min-width: 22%;
			align-items: center;
			margin-bottom: 20rpx;
			height: 100%;
			letter-spacing: 0.5px;
			font-size: 28rpx;
			// font-weight: 550;
			color: #000000;
			box-sizing: border-box;
			font-weight: 600;
		}

		.textarea-box {
			// display: flex;
			width: 90%;
			// border-bottom: 1px solid #ededed;
			//padding-top: 20rpx;
			// align-items: center;
			// padding: 0 20px;
			font-size: 26rpx;
			font-family: Source Han Sans CN, Source Han Sans CN;
			textarea {
				padding-left: 32rpx;
				// height: 100rpx;
				color: #333;
			}
		}

		.line {
			padding: 27rpx 0;
			// min-height: 100rpx;
			display: flex;
			// align-items: center;
			width: 100%;
			margin: 10rpx auto;
			border-bottom: 1px solid #ededed;
			overflow: hidden;
			flex-flow: column;

			.input {
				width: 90%;
				// padding-right: 30rpx;
				// padding-left: 30rpx;
				height: 100%;
				width: 100%;
				text-align: left;
				font-size: 26rpx;
				color: #333;
				border: none;
				overflow: hidden;
				text-overflow: ellipsis;
				outline: none;
				font-family: Source Han Sans CN, Source Han Sans CN;
			}
		}

		.form-item:last-of-type .line {
			border-bottom: none;
		}

		.line-col {
			padding: 27rpx 0;
			// min-height: 100rpx;
			display: flex;
			flex-direction: column;
			width: 100%;
			margin: 0 auto;
			border-bottom: 1px solid #ededed;
			overflow: hidden;

			.input {
				padding-right: 20rpx;
				height: 100%;
				width: 100%;
				text-align: left;
				font-size: 26rpx;
				color: #333;
				border: none;
				overflow: hidden;
				text-overflow: ellipsis;
				outline: none;
			}
		}

		.num {
			margin-right: 8rpx;
		}


	}

	.itembox {
		background-color: #ffffff;
		margin-bottom: 20rpx;
	}

	.picker {
		width: 100%;
		box-sizing: border-box;
		padding: 50rpx;
		text-align: center;
		margin-top: 50rpx;
	}

	.image-box {
		display: flex;

		.image-item {
			margin-right: 20rpx;
		}
	}

	.line-bottom-select ::v-deep .u-checkbox {
		margin-right: 20rpx;
	}
</style>