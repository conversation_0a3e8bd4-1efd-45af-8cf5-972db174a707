import {
    request
} from '../request.js';


export function addText(params) {
    params['uniContentType'] = 'json'
    return request({
      url: "/village/broadcast/addText",
      method: "POST",
      params
    });
  }
  
  
  export function addMusic(params) {
   //Content-Type: multipart/form-data
    return request({
      url: "/village/broadcast/addMusic",
      method: "POST",
      params,
      header:{'Content-Type': 'multipart/form-data'}
    });
  }
  export function findInfoList(params){
    return request({
        url: "/village/broadcast/findPage",
        method: "GET",
        params
    })
  }

  export function getTerminalInfoList(){
    return request({
      url: "/village/broadcast/getTerminalInfoList",
      method: "GET"
    });
  }
  export function playAudio(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/broadcast/play",
      method: "POST",
      params
    });
  }
  export function findInfo(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/broadcast/findInfo",
      method: "GET",
      params
    });
  }
  export function findPageByVillager(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/broadcast/findPageByVillager",
      method: "GET",
      params
    });
  }

  export function findTreeVillageOrg(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/user/villageOrg/findTreeVillageOrg",
      method: "POST",
      params
    });
  }
  export function addMusicBroadcastType1(params){
    params['uniContentType'] = 'json'
    return request({
      url: "/village/broadcast/addMusicBroadcastType1",
      method: "POST",
      params
    });
  }

