import {
	request
} from '@/common/net/request.js'
import {
	API_GETFLOWTAB_URL,
	API_GETFLOWLIST_URL,
	API_GETFLOWTABLE_URL,
	API_GETFLOWNODEPEOPLE_URL,
	API_APPLYFLOW_URL,
	API_GETFLOWINFO_URL,
	API_DELFLOW_URL,
	API_AGREE_URL,
	API_REFUSE_URL,
	API_HANDLE_URL,
	API_FORWARD_URL,
	API_REPLY_URL,
	API_REVIEW_URL
} from '@/common/net/netUrl.js'

// 获取流程列表tabs
export function getFlowTab(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETFLOWTAB_URL,
		method: 'POST',
		params,
	})
}

// 获取流程列表数据
export function getFlowList(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETFLOWLIST_URL,
		method: 'POST',
		params,
	})
}

// 获取流程表单数据
export function getFlowTableFields(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETFLOWTABLE_URL,
		method: 'POST',
		params,
	})
}

// 获取流程人员数据
export function getFlowNodePeople(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETFLOWNODEPEOPLE_URL,
		method: 'POST',
		params,
	})
}

// 发起审批
export function applyFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_APPLYFLOW_URL,
		method: 'POST',
		params,
	})
}

// 获取审批详情
export function getFlowDetail(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_GETFLOWINFO_URL,
		method: 'POST',
		params,
	})
}

// 删除自己发起的申请
export function delFlowDetail(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_DELFLOW_URL,
		method: 'POST',
		params,
	})
}

// 同意申请
export function agreeFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_AGREE_URL,
		method: 'POST',
		params,
	})
}

// 拒绝申请
export function refuseFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_REFUSE_URL,
		method: 'POST',
		params,
	})
}

// 办理申请
export function handleFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_HANDLE_URL,
		method: 'POST',
		params,
	})
}

// 转交申请
export function forwardFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_FORWARD_URL,
		method: 'POST',
		params,
	})
}

// 回复申请
export function replyFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_REPLY_URL,
		method: 'POST',
		params,
	})
}

// 审阅申请
export function reviewFlow(params) {
	params['uniContentType'] = 'json'
	return request({
		url: API_REVIEW_URL,
		method: 'POST',
		params,
	})
}

// 工作上报-审阅流程中增加转阅人
export function updateReferPeople(params){
	params['uniContentType'] = 'json'
	return request({
		url: '/village/flow/tbFlowApply/addFlowApplyReferPeople',
		method: 'POST',
		params
	})
}

// 工作上报-判断当前流程是否申请人或审阅人
export function getIsApplyOrAudit(params){
	params['uniContentType'] = 'json'
	return request({
		url: '/village/flow/tbFlowApply/isApplyOrAudit',
		method: 'POST',
		params
	})
}