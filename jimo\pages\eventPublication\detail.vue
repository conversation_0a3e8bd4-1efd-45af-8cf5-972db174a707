<template>
<view class="detailcon">
  <view class="topinfo">
    <!-- <view class="title">{{workform.missionName}} </view> -->
    <view class="tag" :class="getTagClass()">{{getStatusText()}}</view>
    <view class="cell">
      <view class="label">工作标题</view>
      <view class="value">{{workform.missionName}}</view>
    </view>
    <view class="cell">
      <view class="label">工作类别</view>
      <view class="value">{{selectDictLabel(typelist,workform.missionType)}}</view>
    </view>
    <view class="cell">
      <view class="label">工作要求</view>
      <view class="value">{{workform.missionContent}}</view>
    </view>

    <view class="cell">
      <view class="label">完成时限</view>
      <view class="value">{{workform.completeTime}}</view>
    </view>
    <view class="cell">
      <view class="label">图片</view>
      <view class="value">
        <view class="imgbox" v-for="(item,index) in imglist" :key="item">
          <view class="imgcon" >
            <!-- <image @click="previewHandler(index,imglist)"
              :src="item"
            ></image> -->
			<safe-image :src="item" :imgIndex='index' width='219rpx' height='165rpx'></safe-image>
          </view>
        </view>
  
      </view>
    </view>
      <!--  #ifdef  APP-PLUS -->
    <!-- <view class="cell">
      <view class="label">附件</view>
        <view>
      <view v-for="(item,index) in filelist" :key="item" class="fileitem">
        <image
          :src="FILE_ADD"
          mode="widthFix"
          style="width: 48rpx; height: 48rpx; margin-right: 18rpx"
        ></image>
        <text>{{ missionAnnexNames[index] }}</text>
     
      </view>
      </view>
  </view> -->
    
    <!--  #endif -->
    <view class="cell" >
        <view class="label">下发网格</view>

      <view class="gridcontent" v-for="(item,index) in gridmemberlist" :key="index">
        <view>网格名称：{{item.gridname}}</view>
        <view>网格级别：{{item.gridlevel}}</view>
        <view>处理人：{{item.users?.join(',')}}</view>
      </view>
    </view>
  </view>
 
    <view
      class="cellcomplete"
      @click="gotocompleteHandler"
     
    >
      <view class="worklabel">完成情况</view>
      <u-icon name="arrow-right" class="rightarrow"></u-icon>
    </view>
    <view class="workgetcon" v-if="flag==0">
      <view class="worklabel">工作小结</view>
      <view class="addWorkget" v-if="!showinput" @click="addworkget"
        ><text class="plus">+</text><text>添加工作小结</text></view
      >
      <view class="workgetarea" v-else>
        <view class="btngroup">
          <view class="btn" v-show="showsave" @click="canclehandler">取消</view>
          <view class="btn greenbtn" v-show="showedit" @click="editHadnler">编辑</view>
          <view class="btn greenbtn" v-show="showsave" @click="savehandler">保存</view>
        </view>
        <view class="areacon">
          <u-textarea
            v-model.trim="workform.missionSummary"
            placeholder="请输入内容"
            :maxlength="200"
            :count="true"
            :disabled="showedit"
            :height="90"
			:cursorSpacing="140"
          ></u-textarea>
        </view>
      </view>
    </view>

    <view class="blank"></view>

  </view>
</template>
<script setup>
let initSummary = ''
import { onLoad,onUnload } from '@dcloudio/uni-app'
import { reactive, ref, computed } from 'vue'
import {
  FILE_ADD,
} from '@/common/net/staticUrl'
import {
  updateEvent,
  findOneInfo,
} from '../../api/eventPublication/eventPublication.js'
import { getDicts } from '@/common/net/contacts/contacts.js'
import { useTokenStore } from '@/store/token.js'
const tokenStore = useTokenStore()
const workform = ref({})
let imglist = ref([])
let filelist = ref([])
const showinput = ref(false)
const showedit = ref(false)
const showsave = ref(false)
const missionId = ref('')
const typelist = ref([])
const issubmiting = ref(false)
const flag = ref(0)
let missionAnnexNames = ref([])


const gridmemberlist = computed(() => {
  if (workform.value.gridInfo) {
    let infojson = JSON.parse(workform.value.gridInfo)
	if(workform.value.gridInfo.includes('tenantId')) {
		 //获取数组中有多少个type
		 let gridtypes = []
		
		 infojson.map((item, index) => {
		   if (gridtypes.indexOf(item.gridId) === -1) {
		     gridtypes.push(item.gridId)
		   }
		 })
		
		 //一个包含多个list的结果对象
		 let obj = []
		
		 // 根据type生成多个数组
		 gridtypes.map((typeItem, typeIndex) => {
		   infojson.map((arrItem, arrIndex) => {
		     if (arrItem.gridId == typeItem) {
		       obj[typeIndex] = obj[typeIndex] || []
		       obj[typeIndex].push(arrItem)
		     }
		   })
		 })
		 let arr = []
		 obj.forEach((item) => {
		   let newobj = {}
			  newobj.gridlevel = item[0].path.length + 1 + '级'
		   newobj.gridname = item[0].path[item[0].path.length - 1].listname
		   newobj.users = item.map((user) => user.name)
		   arr.push(newobj)
		 })
		 return arr;
	}
    return infojson 
  }
  return []
})
function editHadnler() {
  showedit.value = false
  showsave.value = true
}
function addworkget(){
  console.log('fffffff','ddddddddd')
  showinput.value = true;
  showsave.value = true
}
onLoad((option) => {
  flag.value = option.flag
  initSummary = ''
  getDicts('task_type').then((response) => {
    let list = response.data
    list.forEach((item) => {
      item.name = item.dictLabel
    })
    typelist.value = list
  })

    missionId.value = option.id
    findDetail()

  uni.$on('selectSuccess', (res) => {
    console.log(res.list)
    let arr = []

    res.list.forEach((item) => {
      let name = item.listname
      const { memberId, gridId, villagePeopleId } = item
      let obj = { memberId, name, gridId, villagePeopleId }
      arr.push(obj)
    })
    workform.value.gridMemberList = arr
    workform.value.gridInfo = JSON.stringify(res.list)
  })
  uni.$on('clickImg', (index) => {
  	previewPics(index)
  })
})
onUnload(() => {
	uni.$off('clickImg')
})
async function goSub() {
  if(issubmiting.value) return
  issubmiting.value = true
 
  try {
    let res = await updateEvent({missionSummary:workform.value.missionSummary,missionId:missionId.value})
   
    console.log(res)
    if (res.success) {
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          complete: () => {
            issubmiting.value = false
            uni.showToast({
              title: '发布成功!',
              icon: 'none',
            })
          },
        })
      }, 500)
    } else {
      issubmiting.value = false
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    issubmiting.value = false
    uni.showToast({
      title: '发布失败！',
      icon: 'none',
    })
  }
}
function canclehandler() {
  workform.value.missionSummary = initSummary
  showedit.value = true
  showsave.value = false
}
function savehandler() {
  showedit.value = true
  showsave.value = false
  goSub()
}
function getTagClass(){
  if(workform.value.missionStatus==0){
    return ''
  }
  if(workform.value.missionStatus==1||workform.value.missionStatus==3){
    return 'wctag'
  }
  if(workform.value.missionStatus==2){
    return 'yqtag'
  }
}
function getStatusText(){
  if(workform.value.missionStatus==0){
    return '待完成'
  }
  if(workform.value.missionStatus==1){
    return '已完成'
  }
  if(workform.value.missionStatus==2){
    return '已逾期'
  }
  if (workform.value.missionStatus==3) {
    return '逾期完成'
  }
}
async function findDetail() {
  try {
    let res = await findOneInfo({ id: missionId.value })
    if (res.success) {
      workform.value = res.data
      imglist.value =
        workform.value.missionPicture.length > 0
          ? workform.value.missionPicture.split(',')
          : [],
		  console.log('imglist.value----------', imglist.value);
      filelist.value =
        workform.value.missionAnnex.length > 0
          ? workform.value.missionAnnex.split(',')
          : []

      showinput.value = workform.value.missionSummary.length > 0
      showedit.value = workform.value.missionSummary.length > 0 ? true : false
      showsave.value = false
      initSummary = workform.value.missionSummary
    } else {
      uni.showToast({
        title: res.message,
        icon: 'none',
      })
    }
  } catch (e) {
    uni.showToast({
      title: '查询事务详情失败！',
      icon: 'none',
    })
  }
}
function selectDictLabel(datas, value) {
  var actions = []
  Object.keys(datas).some((key) => {
    if (datas[key].dictValue == '' + value) {
      actions.push(datas[key].dictLabel)
      return true
    }
  })
  return actions.join('')
}


function gotocompleteHandler() {
  uni.navigateTo({
    url: './eventCompleteStatus?id=' + workform.value.missionId,
  })
}


function previewHandler(index, imgs) {
  console.log(index, imgs)
  uni.previewImage({
    current: index,
    urls: imgs,
    referrerPolicy: 'origin', // 必填，否则会受到图床的防盗链影响
    success() {
      console.log('预览成功')
    },
  })
}
//预览图片
const previewPics = async (index) => {
	let pics = []
	for(let i=0; i<imglist.value.length;i++) {
		let a = await loadImage(imglist.value[i])
		pics.push(a)
	}
	Promise.all(pics).then((result) => {
	})
	uni.previewImage({
		urls: pics
	})
}
function loadImage(src) {
	return new Promise((resolve, reject) => {
		uni.downloadFile({
			url: src,
			header: {
				'Authorization': 'Bearer ' + tokenStore.value
			},
			//下载地址，后端接口获取的链接
			success: (data) => {
				resolve(data.tempFilePath)
			}
		})
	})
}
</script>
<style>
page {
  background: #f0f7f7;
}
</style>
<style scoped lang="scss">
.blank {
  width: 100%;
  height: 100rpx;
}
.cellcomplete {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 0 26rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  font-family: Source Han Sans CN, Source Han Sans CN;
  color: #000000;
  .rightarrow {
    width: 30rpx;
    height: 30rpx;
  }
}
.label {
        font-size: 28rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #999999;
        line-height: 40rpx;
        margin-bottom: 12rpx;
      }
.workgetcon {
  margin: 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 23rpx 26rpx;
  .workgetarea {
    position: relative;
    .btngroup {
      position: absolute;
      right: 0;
      top: -46rpx;
      display: flex;
      align-items: center;
      .btn {
        width: 111rpx;
        height: 48rpx;
        border-radius: 31rpx;
        border: 0.5px solid #cccccc;
        line-height: 48rpx;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        margin-left: 7rpx;
      }
      .greenbtn {
        border: 0.5px solid #0bbd88;
        color: #0bbd88;
      }
    }
    .areacon {
      padding-top: 30rpx;
    }
  }
  .worklabel {
    font-size: 28rpx;

    color: #999999;
    line-height: 40rpx;
  }
  .addWorkget {
    padding: 36rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 600;
    color: #0bbd88;
    line-height: 45rpx;
    .plus {
      margin-right: 20rpx;
    }
  }
}
.imgcell {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.gridcontent {
  padding: 15rpx 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 28rpx;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  margin-bottom: 16rpx;
  color: #333333;
  line-height: 56rpx;
}
.fileitem {
  display: flex;
  flex-wrap: wrap;
  white-space: nowrap;
  position: relative;
  text {
    font-size: 24rpx;
    color: #ccc;
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .closebtn {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.imgitem {
  width: 233rpx;
  height: 179rpx;
  border: 0.5px solid #e2e2e2;
  margin-right: 40rpx;
  margin-bottom: 20rpx;
  position: relative;
  image {
    margin: 7rpx;
    width: 219rpx;
    height: 164rpx;
  }
  .closebtn {
    width: 40rpx;
    height: 40rpx;
    position: absolute;
    top: -16rpx;
    right: -16rpx;
  }
}
.arrow {
  top: 50rpx;
  width: 16rpx;
  height: 30rpx;
  position: absolute;
  right: 0;
}
.cell {
      padding-top: 25rpx;
      margin-bottom: 15rpx;
      

      .value {
        font-size: 32rpx;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #000000;
        line-height: 45rpx;
        display: flex;
        flex-wrap: wrap;
        .progresscon {
            width: 100%;
          display: flex;
          align-items: center;
          ::v-deep .u-slider{
            width: calc(100% - 80rpx)
          }
          .percent {
            font-size: 28rpx;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            color: #0bbd88;
            min-width: 80rpx;
            text-align: right;
          }
        }

        .imgbox {
          width: 233rpx;
          height: 179rpx;
          border: 0.5px solid #e2e2e2;
          overflow: hidden;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          .imgcon {
            margin: 7rpx;
            width: 219rpx;
            height: 165rpx;
            overflow: hidden;
            position: relative;

            image {
              width: 100%;
              height: 100%;
              // position: absolute;
              // top: 50%;
              // transform: translateY(-50%);
            }
          }
        }
      }
    }
.detailcon {
  width: 100%;
  .topinfo {
    margin: 20rpx;
    background: #ffffff;
    border-radius: 8rpx;
    padding: 23rpx 26rpx;
    position: relative;
    .title {
      font-size: 32rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #000000;
      line-height: 60rpx;
    }
    .tag {
      position: absolute;
      right: 0;
      top: 0;
      width: 128rpx;
      height: 62rpx;
      background: rgba(255, 200, 168, 0.4);
      border-radius: 0rpx 8rpx 0rpx 26rpx;
      line-height: 62rpx;
      font-size: 28rpx;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      color: #ff6d1c;
      text-align: center;
    }
    .wctag {
        background: rgba(180, 242, 183, 0.4);
        color: rgba(1, 189, 93, 1);
      }
      .yqtag {
        background-color: rgba(242, 180, 180, 0.38);
        color: #e90000;
      }
  
  }
}
</style>