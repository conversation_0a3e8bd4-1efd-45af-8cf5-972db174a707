<!-- 我的简历-编辑 -->
<template>
	<view class="my-resume-edit">
		<u-navbar title="我的简历" border bgColor="rgba(0, 0, 0, 0)"
			:auto-back="true" :placeholder="true" leftIconColor='#000'
			titleStyle="font-size: 36rpx;color: #000" />
		
		<view class="main-content" :style="{height: `calc(100% - ${navbarHeight} - 141rpx - env(safe-area-inset-bottom))`}">
			<view class="w-100 h-100 scrollbar-none">
				<u--form labelPosition="top" labelWidth="300rpx"
					:model="form" :rules="rules" ref="refForm">
					<!-- 求职姓名 -->
					<u-form-item label="求职姓名" prop="resumeName" :required="true">
						<u--input v-model="form.resumeName" border="none"
							placeholder="请输入姓名" maxlength="30" />
					</u-form-item>
					<!-- 求职岗位 -->
					<u-form-item label="求职岗位" prop="positionName" :required="true">
						<view class="form-item" @click="showWorkTypes = true">
							<u--input v-model="form.positionName" border="none" placeholder="请选择求职岗位" readonly />
							<u-icon name="arrow-down"></u-icon>
						</view>
						<!-- 职位名称选择 -->
						<u-picker :show="showWorkTypes" title="职位名称"
							:columns="workTypesColumns" @confirm="selectWorkTypes" 
							@cancel="showWorkTypes = false" />
					</u-form-item>
					<!-- 期望薪资 -->
					<u-form-item label="期望薪资" prop="salaryRange" :required="true">
						<view class="form-item" @click="showSalary = true">
							<u--input v-model="form.salaryRange" border="none" placeholder="请选薪资下限上限范围" readonly />
							<u-icon name="arrow-down"></u-icon>
						</view>
						<!-- 期望薪资选择 -->
						<u-picker :show="showSalary" title="期望薪资"
							:columns="salaryColumns" @confirm="selectSalary"
							@cancel="showSalary = false" />
					</u-form-item>
					<!-- 联系电话 -->
					<u-form-item label="联系电话" prop="telephone" :required="true">
						<u--input v-model="form.telephone" border="none" 
							placeholder="请输入联系电话" maxlength="11" />
					</u-form-item>
					<!-- 求职地点 -->
					<u-form-item label="求职地点" prop="workPlace" :required="true">
						<view class="form-item" @click="goRegionSelect()">
							<u--input v-model="form.workPlace" border="none" placeholder="请选择省市区县" readonly />
							<u-icon name="arrow-down"></u-icon>
						</view>
						<!-- 工作地点选择 -->
						<region-select ref="refRegionSelect" :maxCheck="5" @get="handleRegionSelect" />
					</u-form-item>
					<!-- 性别 -->
					<u-form-item label="性别" prop="sex">
						<view class="form-item" @click="showSex = true">
							<u--input v-model="form.sexName" border="none" placeholder="请选择性别" readonly />
							<u-icon name="arrow-down"></u-icon>
						</view>
						<!-- 职位名称选择 -->
						<u-picker :show="showSex" title="性别选择"
							:columns="sexColumns" @confirm="selectSex" 
							@cancel="showSex = false" />
					</u-form-item>
					<!-- 年龄 -->
					<u-form-item label="年龄" prop="age">
						<u--input v-model="form.age" border="none" placeholder="请输入年龄" type="number" />
					</u-form-item>
					<!-- 个人介绍 -->
					<u-form-item label="个人介绍" prop="introduction">
						<u--input v-model="form.introduction" border="none" 
							placeholder="请输入个人介绍" maxLength="200" />
					</u-form-item>
					<!-- 证书照片 -->
					<u-form-item label="证书照片" prop="img">
						<view class="img-upload">
							<u-upload :auto-upload="false" :fileList="fileList" :maxCount="5"
									  :mutiple="true" width="150rpx" height="150rpx"
									  @afterRead="afterRead" @delete="deletePic" />
						</view>
					</u-form-item>
					<!-- 公开简历 -->
					<u-form-item>
						<view class="label-solt">
							<view>
								<view class="label">公开简历</view>
								<u-switch v-model="form.status" active-color="#0BBD88" 
									active-value="on" inactive-value="off"></u-switch>
							</view>
							
							<text>开启后在找工信息展示，别人可浏览此简历</text>
						</view>
					</u-form-item>
				</u--form>
			</view>
		</view>
		
		<view class="submit" @click="handleSubmit()">
			<view class="btn">保存</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { fileUpload } from '@/common/api'
import { LaborService } from '../../api/laborService/laborService.js'
import RegionSelect from '../../components/regionSelect/index.vue'

/** 顶部导航高度 */
const navbarHeight = ref('')
/** 表单ref */
const refForm = ref()
/** 表单 */
const form = ref({
	resumeName: '',
	positionName: '',
	salaryRange: '',
	telephone: '',
	workPlace: '',
	sex: 'man',
	sexName: '男',
	status: 'off'
})
/** 表单验证 */
const rules = reactive({
	resumeName: {
		type: 'string', required: true, max: 30,
		message: '姓名不能为空并且字数不能超过30个', trigger: ['blur', 'change']
	},
	positionName: { 
		required: true, message: '请选择求职岗位', trigger: ['blur', 'change']
	},
	salaryRange: { 
		required: true, message: '请选薪资下限上限范围', trigger: ['blur', 'change']
	},
	telephone: [
		{
			type: 'string', required: true, max: 11,
			message: '请输入联系电话且不超过11位', trigger: ['blur', 'change'],
		},
		{
			validator: (rule, value, callback) => {
			    return uni.$u.test.mobile(value)
			},
			message: '手机号码格式不正确', trigger: ['change', 'blur'],
		}
	],
	workPlace: { 
		required: true, message: '请选择省市区县', trigger: ['blur', 'change']
	},
	sex: { required: false },
	age: { 
		required: false, type: 'number', message: '请输入正确的年龄', trigger: ['blur', 'change']
	},
	introduction: { required: false },
	img: { required: false },
	status: { required: false },
})

onLoad(() => {
	navbarHeight.value = `${uni.getSystemInfoSync().statusBarHeight + 44}px`
	
	getMyResumeInfo()
	setSalaryColumns()
})

const getMyResumeInfo = () => {
	uni.showLoading({ title: '加载中...', mask: true })
	
	LaborService.getMyResumeInfo({}).then(r => {
		if (r.data) {
			form.value = r.data
			if (r.data.salaryRangeLow && r.data.salaryRangeHigh) {
				form.value.salaryRange = `${r.data.salaryRangeLow}-${r.data.salaryRangeHigh}元`
			}
			if (r.data.img) {
				imagesUrl.value =  r.data.img.split(',')
				fileList.value = imagesUrl.value.map(item => {
					return { url: item }
				})
			} else {
				imagesUrl.value = []
				fileList.value = []
			}
		} else {
			uni.showToast({
				title: '请填写您的简历', 
				icon: 'none', duration: 1000,
			})
		}
	}).finally(() => {
		uni.hideLoading()
	})
}

/** 职位名称选择 */
const showWorkTypes = ref(false)
const workTypesColumns = ref([[
	'计算机', '厨师', '老师', '木工技师', '瓦工技师', '管道工技师', '绿化工技师',
	'林木种苗技师', '抚育间伐技师', '特岗护理技师', '闸门运行技师', '果树工技师', '其他'
]])
const selectWorkTypes = (val) => {
	form.value.positionName = val.value[0]
	showWorkTypes.value = false
	refForm.value.validateField('positionName')
}

/** 期望薪资选择 */
const showSalary = ref(false)
const salaryColumns = ref([])
const setSalaryColumns = () => {
	const salary = []
	for (let i = 1; i < 10; i++) {
		salary.push(i * 100)
	}
	for (let i = 1; i < 10; i++) {
		salary.push(i * 1000)
	}
	for (let i = 1; i < 11; i++) {
		salary.push(i * 10000)
	}
	salaryColumns.value = [salary, salary]
}
const selectSalary = ({ value }) => {
	let salaryRangeLow, salaryRangeHigh
	if (value[0] > value[1]) {
		salaryRangeLow = value[1]
		salaryRangeHigh = value[0]
	} else {
		salaryRangeLow = value[0]
		salaryRangeHigh = value[1]
	}
	
	form.value.salaryRange = `${salaryRangeLow}-${salaryRangeHigh}元`
	form.value.salaryRangeLow = salaryRangeLow
	form.value.salaryRangeHigh = salaryRangeHigh
	showSalary.value = false
	refForm.value.validateField('salaryRange')
}

/** 求职地点选择 */
const refRegionSelect = ref()
const goRegionSelect = () => {
	refRegionSelect.value.init()
}
const handleRegionSelect = ({ selectRegion, parent }) => {
	form.value.workPlace = selectRegion.map(item => item.regionFullName).join(',')
	
	if (selectRegion.length === 1) {
		form.value.countyName = selectRegion[0].regionName
	} else if (selectRegion.length > 1) {
		form.value.countyName = parent.regionName
	}
	
	refForm.value.validateField('workPlace')
}

/** 性别选择 */
const showSex = ref(false)
const sexColumns = ref([['男', '女']])
const selectSex = ({ value }) => {
	form.value.sexName = value[0]
	form.value.sex = value[0] === '男' ? 'man' : 'woman'
	// refForm.value.validateField('sex')
	showSex.value = false
}

/** 证书照片 */
const fileList = ref([])
/**
 * @description 图片上传
 * @param event
 * @return {Promise<void>}
 */
const afterRead = async (event) => {
	let lists = [].concat(event.file)
	let fileListLen = fileList.value.length
	lists.map((item) => {
		fileList.value.push({
			...item,
			status: 'uploading',
			message: '上传中',
		})
	})
	console.log('fileList', fileList)
	for (let i = 0; i < lists.length; i++) {
		const result = await handleClickImage(lists[i].url)
		let item = fileList.value[fileListLen]
		fileList.value.splice(fileListLen, 1, {
			...item,
			status: 'success',
			message: '',
			url: result,
		});
		fileListLen++
	}
}
const imagesUrl = ref([])
const handleClickImage = async (url) => {
	try {
		const result = await fileUpload({
			filePath: url,
			formData: { isAnonymous: 1 }	
		});
		if (result.success) {
			imagesUrl.value.push(result.data.url)
			form.value.img = imagesUrl.value.join(',')
			// refForm.value.validateField('img')
		} else {
			uni.showToast({
				title: result.message,
				icon: 'none',
				duration: 2000
			})
		}
	} catch (err) {
		uni.showToast({
			title: err,
			icon: 'none',
			duration: 2000
		});
	}
}
const deletePic = (event) => {
	fileList.value.splice(event.index, 1);
	imagesUrl.value.splice(event.index, 1);
	form.value.img = imagesUrl.value.join(',')
	// refForm.value.validateField('img')
}

/**
 * @description 提交
 */
const handleSubmit = () => {
	refForm.value.validate().then(res => {
		if (res) {
			uni.showLoading({ title: '提交中...', mask: true })
			const params = {
				resumeId: form.value.resumeId,
				countyName: form.value.countyName
			}
			Object.keys(rules).forEach(key => {
				if (key === 'salaryRange') {
					params.salaryRangeLow = form.value.salaryRangeLow
					params.salaryRangeHigh = form.value.salaryRangeHigh
				} else {
					params[key] = form.value[key]
				}
			})
			let api = params.resumeId ? 'updateMyResumeInfo' : 'addMyResumeInfo'
			
			LaborService[api](params).then(r => {
				if (r.success) {
					uni.showToast({ 
						type: 'success', title: '保存成功', 
						icon: 'none', duration: 1000,
					})
					setTimeout(() => {
						uni.hideLoading()
						uni.navigateBack()
					}, 1000)
				} else {
					uni.showToast({ type: 'error', title: r.message, icon: 'none' })
					setTimeout(() => {
						uni.hideLoading()
					},1000)
				}
			}).catch(() => {
				uni.hideLoading()
			})
		}
	})
}
</script>

<style lang="scss" scoped>
view {
	box-sizing: border-box;
}

.main-content {
	position: fixed;
	left: 0;
	width: 100%;
	padding: 23rpx 20rpx;
	background: #F0F7F7;
	z-index: 11;
	
	::v-deep.u-form {
		padding: 23rpx 19rpx 23rpx 33rpx;
		background-color: #fff;
		
		.u-form-item {
			border-bottom: 1rpx solid #E0E0E0;
			
			.u-form-item__body__left__content__label, .label-solt {
				width: 100%;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 600;
				font-size: 32rpx;
				color: rgba(51, 51, 51, 1);
				line-height: 45rpx;
			}
			
			&:last-child {
				border-bottom: none;
			}
		}
		
		.form-item {
			width: 100%;
			display: flex;
			justify-content: space-between;
		}
		
		.label-solt {
			>view {
				width: 100%;
				display: flex;
				justify-content: space-between;
				margin-bottom: 23rpx;
			}
			
			text {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #B1B1B1;
				line-height: 40rpx;
			}
		}
	}
}

.submit {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: calc(141rpx + env(safe-area-inset-bottom));
	padding-bottom: calc(env(safe-area-inset-bottom));
	display: flex;
	justify-content: center;
	background-color: #fff;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.06);
	background-size: 100% 100%;
	padding-top: 40rpx;
	
	.btn {
		width: 670rpx;
		height: 80rpx;
		background: linear-gradient( 117deg, #3ADB97 0%, #0CBE88 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: 500;
		font-size: 36rpx;
		color: #FFFFFF;
		line-height: 50rpx;
	}
}

.w-100 {
	width: 100%;
}
.h-100 {
	height: 100%;
}

// 隐藏滚动条
.scrollbar-none {
    scrollbar-width: none; /* firefox */
    -ms-overflow-style: none; /* IE 10+ */
    overflow-x: hidden;
    overflow-y: auto;

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
    }
}
</style>
